import{r as V,X as j,h as M,y as b,Q as t,A as r,I as l,al as d,z as _,V as z,H as v,K as x,M as i,P as E,a6 as I}from"./vendor-DmFBDimT.js";import{T as O,L as k}from"./LbButton-BtU4V_Gr.js";import{L as P}from"./LbCover-Ctxlz8o1.js";import{_ as R}from"./index-C9Xz1oqp.js";import{E as p}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const D={class:"page"},A={class:"page-main"},H={__name:"DistributionSet",setup(J){const f=V(),g=V(!1),o=j({fx_check:1,cash_one:0,cash_two:0,apply_condition:1,min_amount:0,settlement_type:1,settlement_day:1,min_withdraw:0,withdraw_fee:0,poster_image:"",agreement:""}),U={fx_check:[{required:!0,message:"请选择是否开启分销商审核",trigger:"change"}],cash_one:[{required:!0,message:"请输入一级分销提成比例",trigger:"blur"}],cash_two:[{required:!0,message:"请输入二级分销提成比例",trigger:"blur"}],apply_condition:[{required:!0,message:"请选择分销商申请条件",trigger:"change"}],settlement_type:[{required:!0,message:"请选择佣金结算方式",trigger:"change"}],min_withdraw:[{required:!0,message:"请输入最低提现金额",trigger:"blur"}],withdraw_fee:[{required:!0,message:"请输入提现手续费",trigger:"blur"}]},w=async()=>{try{const e=await(await fetch("/api/distribution/config")).json();e.code===200?Object.assign(o,e.data):p.error(e.message||"获取配置失败")}catch(s){console.error("获取分销配置失败:",s),p.error("获取配置失败")}},L=s=>{s&&s.length>0&&(o.poster_image=s[0].url)},q=async()=>{try{await f.value.validate(),g.value=!0;const e=await(await fetch("/api/distribution/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?p.success("保存成功"):p.error(e.message||"保存失败")}catch(s){console.error("保存失败:",s),p.error("保存失败")}finally{g.value=!1}},C=()=>{f.value.resetFields(),w()};return M(()=>{w()}),(s,e)=>{const m=d("el-radio"),y=d("el-radio-group"),n=d("el-form-item"),u=d("el-input-number"),N=d("el-option"),S=d("el-select"),T=d("el-input"),B=d("el-form"),F=d("el-card");return _(),b("div",D,[t(O),r("div",A,[t(F,null,{header:l(()=>e[11]||(e[11]=[r("div",{class:"card-header"},[r("span",null,"分销设置")],-1)])),default:l(()=>[t(B,{onSubmit:e[10]||(e[10]=z(()=>{},["prevent"])),model:o,rules:U,ref_key:"formRef",ref:f,"label-width":"150px",class:"config-form"},{default:l(()=>[t(n,{label:"分销商审核",prop:"fx_check"},{default:l(()=>[t(y,{modelValue:o.fx_check,"onUpdate:modelValue":e[0]||(e[0]=a=>o.fx_check=a)},{default:l(()=>[t(m,{value:1},{default:l(()=>e[12]||(e[12]=[i("开启")])),_:1,__:[12]}),t(m,{value:0},{default:l(()=>e[13]||(e[13]=[i("关闭")])),_:1,__:[13]})]),_:1},8,["modelValue"]),e[14]||(e[14]=r("div",{class:"form-tip"},"开启后，用户申请成为分销商需要管理员审核",-1))]),_:1,__:[14]}),t(n,{label:"一级分销提成比例",prop:"cash_one"},{default:l(()=>[t(u,{modelValue:o.cash_one,"onUpdate:modelValue":e[1]||(e[1]=a=>o.cash_one=a),min:0,max:100,precision:2,style:{width:"200px"}},null,8,["modelValue"]),e[15]||(e[15]=r("span",{style:{"margin-left":"8px"}},"%",-1)),e[16]||(e[16]=r("div",{class:"form-tip"},"直接邀请用户下单的佣金比例",-1))]),_:1,__:[15,16]}),t(n,{label:"二级分销提成比例",prop:"cash_two"},{default:l(()=>[t(u,{modelValue:o.cash_two,"onUpdate:modelValue":e[2]||(e[2]=a=>o.cash_two=a),min:0,max:100,precision:2,style:{width:"200px"}},null,8,["modelValue"]),e[17]||(e[17]=r("span",{style:{"margin-left":"8px"}},"%",-1)),e[18]||(e[18]=r("div",{class:"form-tip"},"间接邀请用户下单的佣金比例",-1))]),_:1,__:[17,18]}),t(n,{label:"分销商申请条件",prop:"apply_condition"},{default:l(()=>[t(y,{modelValue:o.apply_condition,"onUpdate:modelValue":e[3]||(e[3]=a=>o.apply_condition=a)},{default:l(()=>[t(m,{value:1},{default:l(()=>e[19]||(e[19]=[i("无条件申请")])),_:1,__:[19]}),t(m,{value:2},{default:l(()=>e[20]||(e[20]=[i("需要邀请码")])),_:1,__:[20]}),t(m,{value:3},{default:l(()=>e[21]||(e[21]=[i("需要消费满额")])),_:1,__:[21]})]),_:1},8,["modelValue"])]),_:1}),o.apply_condition===3?(_(),v(n,{key:0,label:"消费满额要求",prop:"min_amount"},{default:l(()=>[t(u,{modelValue:o.min_amount,"onUpdate:modelValue":e[4]||(e[4]=a=>o.min_amount=a),min:0,precision:2,style:{width:"200px"}},null,8,["modelValue"]),e[22]||(e[22]=r("span",{style:{"margin-left":"8px"}},"元",-1))]),_:1,__:[22]})):x("",!0),t(n,{label:"佣金结算方式",prop:"settlement_type"},{default:l(()=>[t(y,{modelValue:o.settlement_type,"onUpdate:modelValue":e[5]||(e[5]=a=>o.settlement_type=a)},{default:l(()=>[t(m,{value:1},{default:l(()=>e[23]||(e[23]=[i("订单完成后立即结算")])),_:1,__:[23]}),t(m,{value:2},{default:l(()=>e[24]||(e[24]=[i("每月固定时间结算")])),_:1,__:[24]}),t(m,{value:3},{default:l(()=>e[25]||(e[25]=[i("手动结算")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),o.settlement_type===2?(_(),v(n,{key:1,label:"结算日期",prop:"settlement_day"},{default:l(()=>[t(S,{modelValue:o.settlement_day,"onUpdate:modelValue":e[6]||(e[6]=a=>o.settlement_day=a),placeholder:"请选择结算日期"},{default:l(()=>[(_(),b(E,null,I(28,a=>t(N,{key:a,label:`每月${a}号`,value:a},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})):x("",!0),t(n,{label:"最低提现金额",prop:"min_withdraw"},{default:l(()=>[t(u,{modelValue:o.min_withdraw,"onUpdate:modelValue":e[7]||(e[7]=a=>o.min_withdraw=a),min:0,precision:2,style:{width:"200px"}},null,8,["modelValue"]),e[26]||(e[26]=r("span",{style:{"margin-left":"8px"}},"元",-1)),e[27]||(e[27]=r("div",{class:"form-tip"},"分销商申请提现的最低金额",-1))]),_:1,__:[26,27]}),t(n,{label:"提现手续费",prop:"withdraw_fee"},{default:l(()=>[t(u,{modelValue:o.withdraw_fee,"onUpdate:modelValue":e[8]||(e[8]=a=>o.withdraw_fee=a),min:0,max:100,precision:2,style:{width:"200px"}},null,8,["modelValue"]),e[28]||(e[28]=r("span",{style:{"margin-left":"8px"}},"%",-1)),e[29]||(e[29]=r("div",{class:"form-tip"},"提现时收取的手续费比例",-1))]),_:1,__:[28,29]}),t(n,{label:"分销海报",prop:"poster_image"},{default:l(()=>[t(P,{fileList:o.poster_image?[o.poster_image]:[],onSelectedFiles:L,fileType:"image",tips:"建议尺寸: 750 * 1334"},null,8,["fileList"]),e[30]||(e[30]=r("div",{class:"form-tip"},"分销商推广时使用的海报图片",-1))]),_:1,__:[30]}),t(n,{label:"分销协议",prop:"agreement"},{default:l(()=>[t(T,{modelValue:o.agreement,"onUpdate:modelValue":e[9]||(e[9]=a=>o.agreement=a),type:"textarea",rows:8,placeholder:"请输入分销协议内容",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(n,null,{default:l(()=>[t(k,{type:"primary",onClick:q,loading:g.value,size:"default"},{default:l(()=>e[31]||(e[31]=[i(" 保存设置 ")])),_:1,__:[31]},8,["loading"]),t(k,{onClick:C,style:{"margin-left":"12px"},size:"default"},{default:l(()=>e[32]||(e[32]=[i(" 重置 ")])),_:1,__:[32]})]),_:1})]),_:1},8,["model"])]),_:1})])])}}},Y=R(H,[["__scopeId","data-v-ca32a69d"]]);export{Y as default};
