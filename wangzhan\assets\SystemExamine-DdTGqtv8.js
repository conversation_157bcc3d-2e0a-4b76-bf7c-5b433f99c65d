import{T as W,L as g}from"./LbButton-BtU4V_Gr.js";import{_ as Y}from"./index-C9Xz1oqp.js";import{E as p,q as Z}from"./element-fdzwdDuf.js";import{r as w,X as V,h as ee,y as T,Q as t,A as n,I as a,al as d,ar as te,z as b,O as i,M as r,J as ae,H as F,K as oe}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const se={class:"lb-system-examine"},le={class:"page-main"},ne={class:"upload-file-wrap"},re={class:"upload-file-content"},ie={class:"card-header"},de={key:0},ue={key:1},pe={class:"table-operate"},ce={class:"pagination-section"},me={__name:"SystemExamine",setup(_e){const z=w(!1),S=w(!1),D=w([]),h=w(!1),k=w(),x=w(),c=V({key:"",version:"",content:""}),u=V({page:1,pageSize:10,total:0}),m=V({id:"",version:"",content:"",status:1,submit_time:"",audit_time:"",audit_reason:""}),$={key:[{required:!0,message:"请上传代码密钥文件",trigger:"blur"}],version:[{required:!0,message:"请输入版本号",trigger:"blur"}],content:[{required:!0,message:"请输入版本描述",trigger:"blur"}]},y=async(s=1)=>{S.value=!0,u.page=s;try{const e=new URLSearchParams({page:u.page,pageSize:u.pageSize}),f=await(await fetch(`/api/system/examine/list?${e}`)).json();f.code===200?(D.value=f.data.list||[],u.total=f.data.total||0):p.error(f.message||"获取数据失败")}catch(e){console.error("获取审核记录失败:",e),p.error("获取数据失败")}finally{S.value=!1}},U=s=>({1:"warning",2:"success",3:"danger"})[s]||"info",j=s=>({1:"审核中",2:"审核通过",3:"审核拒绝"})[s]||"未知",C=(s,e)=>{if(!s)return"";const l=new Date(s);return e===1?l.toLocaleDateString():l.toLocaleTimeString()},E=()=>{x.value.click()},N=s=>{const e=s.target.files[0];e&&(c.key=e.name)},I=async()=>{try{await k.value.validate(),z.value=!0;const e=await(await fetch("/api/system/examine/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)})).json();e.code===200?(p.success("提交审核成功"),B(),y()):p.error(e.message||"提交失败")}catch(s){console.error("提交审核失败:",s),p.error("提交失败")}finally{z.value=!1}},B=()=>{Object.assign(c,{key:"",version:"",content:""}),k.value&&k.value.clearValidate(),x.value&&(x.value.value="")},O=async s=>{try{const l=await(await fetch(`/api/system/examine/detail/${s.id}`)).json();l.code===200?(Object.assign(m,l.data),h.value=!0):p.error(l.message||"获取详情失败")}catch(e){console.error("获取审核详情失败:",e),p.error("获取详情失败")}},R=async s=>{try{await Z.confirm(`确定要重新提交版本 "${s.version}" 的审核吗？`,"重新提交确认",{confirmButtonText:"确定提交",cancelButtonText:"取消",type:"warning"});const l=await(await fetch(`/api/system/examine/resubmit/${s.id}`,{method:"PUT"})).json();l.code===200?(p.success("重新提交成功"),y()):p.error(l.message||"提交失败")}catch(e){e!=="cancel"&&(console.error("重新提交失败:",e),p.error("提交失败"))}},q=s=>{u.pageSize=s,y(1)},P=s=>{y(s)};return ee(()=>{y()}),(s,e)=>{const l=d("el-form-item"),f=d("el-input"),K=d("el-form"),L=d("el-card"),_=d("el-table-column"),M=d("el-tag"),J=d("el-table"),A=d("el-pagination"),v=d("el-descriptions-item"),H=d("el-descriptions"),Q=d("el-dialog"),X=te("loading");return b(),T("div",se,[t(W),n("div",le,[t(L,{class:"upload-card",shadow:"never"},{header:a(()=>e[6]||(e[6]=[n("div",{class:"card-header"},[n("span",null,"微信小程序审核上传")],-1)])),default:a(()=>[t(K,{model:c,rules:$,ref_key:"uploadFormRef",ref:k,"label-width":"140px",class:"config-form"},{default:a(()=>[t(l,{label:"代码上传密钥",prop:"key"},{default:a(()=>[n("div",ne,[n("div",re,i(c.key||"未选择文件"),1),t(g,{size:"small",type:"primary",onClick:E},{default:a(()=>e[7]||(e[7]=[r(" 上传密钥 ")])),_:1,__:[7]})]),n("input",{type:"file",accept:".txt,.key",style:{display:"none"},onChange:N,ref_key:"keyFileInput",ref:x},null,544)]),_:1}),t(l,{label:"版本号",prop:"version"},{default:a(()=>[t(f,{modelValue:c.version,"onUpdate:modelValue":e[0]||(e[0]=o=>c.version=o),placeholder:"请输入版本号",maxlength:"30","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(l,{label:"版本描述",prop:"content"},{default:a(()=>[t(f,{modelValue:c.content,"onUpdate:modelValue":e[1]||(e[1]=o=>c.content=o),type:"textarea",rows:4,placeholder:"请输入版本描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(l,null,{default:a(()=>[t(g,{type:"primary",onClick:I,loading:z.value},{default:a(()=>e[8]||(e[8]=[r(" 提交审核 ")])),_:1,__:[8]},8,["loading"]),t(g,{onClick:B,style:{"margin-left":"10px"}},{default:a(()=>e[9]||(e[9]=[r(" 重置 ")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1}),t(L,{class:"record-card",shadow:"never"},{header:a(()=>[n("div",ie,[e[11]||(e[11]=n("span",null,"审核记录",-1)),t(g,{type:"primary",onClick:y},{default:a(()=>e[10]||(e[10]=[r("刷新")])),_:1,__:[10]})])]),default:a(()=>[ae((b(),F(J,{data:D.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:a(()=>[t(_,{prop:"id",label:"ID",width:"80"}),t(_,{prop:"version",label:"版本号",width:"120"}),t(_,{prop:"content",label:"版本描述","min-width":"200"}),t(_,{prop:"status",label:"审核状态",width:"120"},{default:a(o=>[t(M,{type:U(o.row.status),size:"small"},{default:a(()=>[r(i(j(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"submit_time",label:"提交时间",width:"170"},{default:a(o=>[n("div",null,i(C(o.row.submit_time,1)),1),n("div",null,i(C(o.row.submit_time,2)),1)]),_:1}),t(_,{prop:"audit_time",label:"审核时间",width:"170"},{default:a(o=>[o.row.audit_time?(b(),T("div",de,[n("div",null,i(C(o.row.audit_time,1)),1),n("div",null,i(C(o.row.audit_time,2)),1)])):(b(),T("span",ue,"-"))]),_:1}),t(_,{prop:"audit_reason",label:"审核意见","min-width":"200"},{default:a(o=>[n("span",null,i(o.row.audit_reason||"-"),1)]),_:1}),t(_,{label:"操作",width:"150",fixed:"right"},{default:a(o=>[n("div",pe,[t(g,{size:"mini",type:"primary",onClick:G=>O(o.row)},{default:a(()=>e[12]||(e[12]=[r(" 查看详情 ")])),_:2,__:[12]},1032,["onClick"]),o.row.status===3?(b(),F(g,{key:0,size:"mini",type:"success",onClick:G=>R(o.row)},{default:a(()=>e[13]||(e[13]=[r(" 重新提交 ")])),_:2,__:[13]},1032,["onClick"])):oe("",!0)])]),_:1})]),_:1},8,["data"])),[[X,S.value]]),n("div",ce,[t(A,{"current-page":u.page,"onUpdate:currentPage":e[2]||(e[2]=o=>u.page=o),"page-size":u.pageSize,"onUpdate:pageSize":e[3]||(e[3]=o=>u.pageSize=o),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:P},null,8,["current-page","page-size","total"])])]),_:1})]),t(Q,{modelValue:h.value,"onUpdate:modelValue":e[5]||(e[5]=o=>h.value=o),title:"审核详情",width:"60%"},{footer:a(()=>[t(g,{onClick:e[4]||(e[4]=o=>h.value=!1)},{default:a(()=>e[14]||(e[14]=[r("关闭")])),_:1,__:[14]})]),default:a(()=>[t(H,{column:2,border:""},{default:a(()=>[t(v,{label:"版本号"},{default:a(()=>[r(i(m.version),1)]),_:1}),t(v,{label:"审核状态"},{default:a(()=>[t(M,{type:U(m.status),size:"small"},{default:a(()=>[r(i(j(m.status)),1)]),_:1},8,["type"])]),_:1}),t(v,{label:"提交时间"},{default:a(()=>[r(i(m.submit_time),1)]),_:1}),t(v,{label:"审核时间"},{default:a(()=>[r(i(m.audit_time||"未审核"),1)]),_:1}),t(v,{label:"版本描述",span:"2"},{default:a(()=>[r(i(m.content),1)]),_:1}),t(v,{label:"审核意见",span:"2"},{default:a(()=>[r(i(m.audit_reason||"无"),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},be=Y(me,[["__scopeId","data-v-f3f908c9"]]);export{be as default};
