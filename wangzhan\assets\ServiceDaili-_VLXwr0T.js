import{E as u,x as fe,q as _e}from"./element-fdzwdDuf.js";import{T as ve,L as v}from"./LbButton-BtU4V_Gr.js";import{L as be}from"./LbImage-CnNh5Udj.js";import{L as he}from"./LbPage-DnbiQ0Ct.js";import{_ as ye,a as h}from"./index-C9Xz1oqp.js";import{r as c,X as E,c as Ce,h as Ve,y as U,Q as a,A as p,I as o,al as d,J as we,ar as ze,H as M,z as C,M as g,O as z,K as Ge,u as Se}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const xe={class:"service-daili"},Le={class:"content-container"},ke={class:"search-form-container"},De={class:"table-container"},Ue={key:1,class:"no-data"},Ne={class:"time-column"},Te={class:"table-operate"},Ie={key:0,class:"upload-progress"},Fe={class:"dialog-footer"},Be={__name:"ServiceDaili",setup(Re){const G=c(!1),S=c(!1),x=c([]),L=c(0),N=c(),T=c(),b=c(!1),y=c([]),m=c(0),k=c(!1),s=E({pageNum:1,pageSize:10,name:"",address:"",tel:"",serviceCate:"",isGrounding:null}),t=E({id:null,name:"",address:"",tel:"",serviceCate:"",img:"",longitude:null,latitude:null,isGrounding:1}),$={name:[{required:!0,message:"请输入服务点名称",trigger:"blur"}],address:[{required:!0,message:"请输入服务点地址",trigger:"blur"}],tel:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},q=Ce(()=>t.id?"编辑服务点":"新增服务点"),_=async l=>{l&&(s.pageNum=1),G.value=!0;try{const e={pageNum:s.pageNum,pageSize:s.pageSize};s.name&&(e.name=s.name),s.address&&(e.address=s.address),s.tel&&(e.tel=s.tel),s.serviceCate&&(e.serviceCate=s.serviceCate),s.isGrounding!==null&&s.isGrounding!==""&&(e.isGrounding=s.isGrounding),console.log("🔍 查询参数:",e);const i=await h.service.agentList(e);if(console.log("📋 服务点列表数据:",i),i.code==="200"){const r=i.data;x.value=(r.list||[]).map(V=>({...V,statusLoading:!1})),L.value=r.totalCount||r.total||0,console.log("📊 处理后的数据:",{list:x.value,total:L.value,pageNum:r.pageNum,pageSize:r.pageSize})}else console.error("❌ API响应错误:",i),u.error(i.message||i.msg||"获取数据失败")}catch(e){console.error("获取服务点列表失败:",e),u.error("获取数据失败")}finally{G.value=!1}},A=()=>{_(1)},P=()=>{s.name="",s.address="",s.tel="",s.serviceCate="",s.isGrounding=null,N.value?.resetFields(),_(1)},H=()=>{D(),b.value=!0},O=l=>{D(),t.id=l.id,t.name=l.name||"",t.address=l.address||"",t.tel=l.tel||"",t.serviceCate=l.serviceCate||"",t.img=l.img||"",t.longitude=l.longitude||null,t.latitude=l.latitude||null,t.isGrounding=l.isGrounding||1,l.img&&(y.value=[{name:"image",url:l.img}]),b.value=!0},j=async l=>{try{await _e.confirm("确定要删除这个服务点吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await h.service.agentDelete({id:l.id});e.code==="200"?(u.success("删除成功"),_()):u.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除服务点失败:",e),u.error("删除失败"))}},J=async l=>{const e=l.isGrounding===1?-1:1;l.statusLoading=!0;try{const i=await h.service.agentStatus({id:l.id,status:l.isGrounding});i.code==="200"?(u.success("状态更新成功"),await _()):(u.error(i.message||i.msg||"状态更新失败"),l.isGrounding=e)}catch(i){console.error("状态更新失败:",i),u.error("状态更新失败"),l.isGrounding=e}finally{l.statusLoading=!1}},K=async()=>{try{await T.value.validate(),S.value=!0;let l;t.id?l=await h.service.agentUpdate(t):l=await h.service.agentAdd(t),l.code==="200"?(u.success(t.id?"更新成功":"新增成功"),b.value=!1,_()):u.error(l.message||"操作失败")}catch(l){console.error("提交失败:",l),u.error("操作失败")}finally{S.value=!1}},Q=l=>{s.pageSize=l,I(1)},I=l=>{s.pageNum=l,_()},W=l=>(console.log("📋 图片上传前验证:",l),l.type.indexOf("image/")===0?(console.log("✅ 图片验证通过"),!0):(u.error("只能上传图片文件!"),!1)),X=async(l,e)=>{console.log("🖼️ 图片文件变更:",l,e),l.status==="ready"&&await Z(l)},Y=(l,e)=>{console.log("🗑️ 移除图片:",l),t.img="",m.value=0},Z=async l=>{console.log("📤 开始上传图片:",l);try{k.value=!0,m.value=0;const e=new FormData;e.append("multipartFile",l.raw),console.log("📦 FormData创建完成:",e);const i=await h.upload.uploadFile(e,r=>{m.value=Math.round(r.loaded*100/r.total),console.log("📊 上传进度:",m.value+"%")});if(console.log("✅ 图片上传成功:",i),i.code===200||i.code==="200")t.img=i.data.url||i.data.fileUrl||i.data,u.success("图片上传成功"),y.value=[{name:l.name,url:t.img,status:"success"}],console.log("💾 图片URL已保存到表单:",t.img);else throw new Error(i.message||i.msg||"上传失败")}catch(e){console.error("❌ 图片上传失败:",e),u.error("图片上传失败: "+(e.message||"未知错误")),y.value=[],t.img=""}finally{k.value=!1,m.value=0}},ee=()=>{D()},D=()=>{t.id=null,t.name="",t.address="",t.tel="",t.serviceCate="",t.img="",t.longitude=null,t.latitude=null,t.isGrounding=1,y.value=[],m.value=0,k.value=!1},le=l=>l?new Date(l).toLocaleDateString("zh-CN"):"",ae=l=>l?new Date(l).toLocaleTimeString("zh-CN",{hour12:!1}):"";return Ve(()=>{_(1)}),(l,e)=>{const i=d("el-input"),r=d("el-form-item"),V=d("el-option"),te=d("el-select"),oe=d("el-col"),ne=d("el-row"),F=d("el-form"),f=d("el-table-column"),se=d("el-tag"),ie=d("el-switch"),re=d("el-table"),de=d("el-icon"),ue=d("el-upload"),me=d("el-progress"),B=d("el-input-number"),R=d("el-radio"),ce=d("el-radio-group"),pe=d("el-dialog"),ge=ze("loading");return C(),U("div",xe,[a(ve,{title:"服务点管理"}),p("div",Le,[p("div",ke,[a(F,{ref_key:"searchFormRef",ref:N,model:s,inline:!0,class:"search-form"},{default:o(()=>[a(ne,{gutter:20},{default:o(()=>[a(oe,{span:24},{default:o(()=>[a(r,{label:"服务点名称",prop:"name"},{default:o(()=>[a(i,{size:"default",modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=n=>s.name=n),placeholder:"请输入服务点名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(r,{label:"状态",prop:"isGrounding"},{default:o(()=>[a(te,{size:"default",modelValue:s.isGrounding,"onUpdate:modelValue":e[1]||(e[1]=n=>s.isGrounding=n),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:o(()=>[a(V,{label:"上架",value:1}),a(V,{label:"下架",value:-1})]),_:1},8,["modelValue"])]),_:1}),a(r,null,{default:o(()=>[a(v,{size:"default",type:"primary",icon:"Search",onClick:A},{default:o(()=>e[11]||(e[11]=[g(" 搜索 ")])),_:1,__:[11]}),a(v,{size:"default",icon:"RefreshLeft",onClick:P},{default:o(()=>e[12]||(e[12]=[g(" 重置 ")])),_:1,__:[12]}),a(v,{size:"default",type:"primary",icon:"Plus",onClick:H},{default:o(()=>e[13]||(e[13]=[g(" 新增服务点 ")])),_:1,__:[13]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),p("div",De,[we((C(),M(re,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:o(()=>[a(f,{prop:"id",label:"ID",width:"60"}),a(f,{prop:"img",label:"服务点图片",width:"120"},{default:o(n=>[a(be,{src:n.row.img,width:"80",height:"50"},null,8,["src"])]),_:1}),a(f,{prop:"name",label:"服务点名称","min-width":"150"}),a(f,{prop:"address",label:"地址","min-width":"200"}),a(f,{prop:"tel",label:"联系电话",width:"120"}),a(f,{prop:"serviceCate",label:"主营业务","min-width":"150"},{default:o(n=>[n.row.serviceCate?(C(),M(se,{key:0,size:"default"},{default:o(()=>[g(z(n.row.serviceCate),1)]),_:2},1024)):(C(),U("span",Ue,"暂无业务"))]),_:1}),a(f,{label:"状态",width:"100"},{default:o(n=>[a(ie,{modelValue:n.row.isGrounding,"onUpdate:modelValue":w=>n.row.isGrounding=w,"active-value":1,"inactive-value":-1,loading:n.row.statusLoading,onChange:w=>J(n.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),a(f,{prop:"createTime",label:"创建时间","min-width":"120"},{default:o(n=>[p("div",Ne,[p("p",null,z(le(n.row.createTime)),1),p("p",null,z(ae(n.row.createTime)),1)])]),_:1}),a(f,{label:"操作",width:"200",fixed:"right"},{default:o(n=>[p("div",Te,[a(v,{size:"default",type:"primary",onClick:w=>O(n.row)},{default:o(()=>e[14]||(e[14]=[g(" 编辑 ")])),_:2,__:[14]},1032,["onClick"]),a(v,{size:"default",type:"danger",onClick:w=>j(n.row)},{default:o(()=>e[15]||(e[15]=[g(" 删除 ")])),_:2,__:[15]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ge,G.value]])]),a(he,{page:s.pageNum,"page-size":s.pageSize,total:L.value,onHandleSizeChange:Q,onHandleCurrentChange:I},null,8,["page","page-size","total"])]),a(pe,{title:q.value,modelValue:b.value,"onUpdate:modelValue":e[10]||(e[10]=n=>b.value=n),width:"600px",onClose:ee},{footer:o(()=>[p("span",Fe,[a(v,{onClick:e[9]||(e[9]=n=>b.value=!1)},{default:o(()=>e[19]||(e[19]=[g("取消")])),_:1,__:[19]}),a(v,{type:"primary",onClick:K,loading:S.value},{default:o(()=>e[20]||(e[20]=[g(" 确定 ")])),_:1,__:[20]},8,["loading"])])]),default:o(()=>[a(F,{ref_key:"formRef",ref:T,model:t,rules:$,"label-width":"100px"},{default:o(()=>[a(r,{label:"服务点名称",prop:"name"},{default:o(()=>[a(i,{modelValue:t.name,"onUpdate:modelValue":e[2]||(e[2]=n=>t.name=n),placeholder:"请输入服务点名称"},null,8,["modelValue"])]),_:1}),a(r,{label:"服务点地址",prop:"address"},{default:o(()=>[a(i,{modelValue:t.address,"onUpdate:modelValue":e[3]||(e[3]=n=>t.address=n),placeholder:"请输入服务点地址"},null,8,["modelValue"])]),_:1}),a(r,{label:"联系电话",prop:"tel"},{default:o(()=>[a(i,{modelValue:t.tel,"onUpdate:modelValue":e[4]||(e[4]=n=>t.tel=n),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),a(r,{label:"主营业务",prop:"serviceCate"},{default:o(()=>[a(i,{modelValue:t.serviceCate,"onUpdate:modelValue":e[5]||(e[5]=n=>t.serviceCate=n),placeholder:"请输入主营业务"},null,8,["modelValue"])]),_:1}),a(r,{label:"服务点图片",prop:"img"},{default:o(()=>[a(ue,{class:"image-upload",action:"#","auto-upload":!1,"on-change":X,"on-remove":Y,"before-upload":W,"file-list":y.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:o(()=>e[16]||(e[16]=[p("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:o(()=>[a(de,null,{default:o(()=>[a(Se(fe))]),_:1})]),_:1},8,["file-list"]),m.value>0&&m.value<100?(C(),U("div",Ie,[a(me,{percentage:m.value,"show-text":!0},null,8,["percentage"]),p("p",null,"上传中... "+z(m.value)+"%",1)])):Ge("",!0)]),_:1}),a(r,{label:"经度",prop:"longitude"},{default:o(()=>[a(B,{modelValue:t.longitude,"onUpdate:modelValue":e[6]||(e[6]=n=>t.longitude=n),precision:6,placeholder:"请输入经度",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(r,{label:"纬度",prop:"latitude"},{default:o(()=>[a(B,{modelValue:t.latitude,"onUpdate:modelValue":e[7]||(e[7]=n=>t.latitude=n),precision:6,placeholder:"请输入纬度",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(r,{label:"状态",prop:"isGrounding"},{default:o(()=>[a(ce,{modelValue:t.isGrounding,"onUpdate:modelValue":e[8]||(e[8]=n=>t.isGrounding=n)},{default:o(()=>[a(R,{value:1},{default:o(()=>e[17]||(e[17]=[g("上架")])),_:1,__:[17]}),a(R,{value:-1},{default:o(()=>e[18]||(e[18]=[g("下架")])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},Oe=ye(Be,[["__scopeId","data-v-cd007b66"]]);export{Oe as default};
