import{E as d,P as g,O as L,N,q as x}from"./element-fdzwdDuf.js";import{T as E,L as V}from"./LbButton-BtU4V_Gr.js";import{L as M}from"./LbUeditor-Dh_pKaDh.js";import{_ as S,a as T}from"./index-C9Xz1oqp.js";import{r as w,X as z,h as F,y as O,Q as t,A as o,I as l,al as n,z as j,u,M as U}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const I={class:"lb-system-transaction"},A={class:"page-main"},Q={class:"label-with-tooltip"},X={class:"input-with-unit"},G={class:"label-with-tooltip"},H={class:"label-with-tooltip"},J={class:"rules-section"},K={class:"editor-container"},W={class:"action-buttons"},Y={__name:"SystemTransaction",setup(Z){const m=w(!1),f=w(),a=z({companyPay:2,overTime:1440,maxDay:7,timeUnit:30,tradingRules:""}),B={companyPay:[{required:!0,message:"请选择转账方式",trigger:"change"}],overTime:[{required:!0,message:"请输入订单超时时间",trigger:"blur"},{type:"number",min:1,max:10080,message:"订单超时时间必须在1-10080分钟之间",trigger:"blur"}],maxDay:[{required:!0,message:"请选择最长预约时间",trigger:"change"}],timeUnit:[{required:!0,message:"请选择时长单位",trigger:"change"}]},k=async()=>{try{console.log("💰 开始获取交易设置配置...");const s=await T.sys.getTradeSettings();s.code==="200"?(Object.assign(a,s.data||{}),console.log("✅ 交易设置配置获取成功")):d.error(s.message||"获取配置失败")}catch(s){console.error("❌ 获取交易设置配置失败:",s),d.error("获取配置失败，请稍后重试")}},C=async()=>{try{await f.value.validate(),await x.confirm("确定要保存交易设置配置吗？","确认保存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.value=!0,console.log("💾 开始保存交易设置配置...");const s=await T.sys.updateTradeSettings(a);s.code==="200"?(d.success("交易设置配置保存成功"),console.log("✅ 交易设置配置保存成功")):d.error(s.message||"保存失败")}catch(s){s!=="cancel"&&(console.error("❌ 保存交易设置配置失败:",s),d.error("保存失败，请稍后重试"))}finally{m.value=!1}},P=async()=>{try{await x.confirm("确定要重置所有配置吗？此操作将恢复到默认设置！","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),Object.assign(a,{companyPay:2,overTime:1440,maxDay:7,timeUnit:30,tradingRules:""}),f.value.clearValidate(),d.success("配置已重置")}catch{}};return F(()=>{k()}),(s,e)=>{const v=n("el-radio"),R=n("el-radio-group"),c=n("el-form-item"),p=n("el-icon"),_=n("el-tooltip"),q=n("el-input-number"),r=n("el-option"),y=n("el-select"),D=n("el-form"),b=n("el-card");return j(),O("div",I,[t(E),o("div",A,[t(b,{class:"config-card",shadow:"never"},{header:l(()=>e[5]||(e[5]=[o("div",{class:"card-header"},[o("span",null,"交易基础设置")],-1)])),default:l(()=>[t(D,{model:a,ref_key:"configFormRef",ref:f,"label-width":"140px",class:"config-form",rules:B},{default:l(()=>[t(c,{label:"转账方式",prop:"companyPay"},{default:l(()=>[t(R,{modelValue:a.companyPay,"onUpdate:modelValue":e[0]||(e[0]=i=>a.companyPay=i),class:"payment-radio-group"},{default:l(()=>[t(v,{label:1,class:"payment-radio"},{default:l(()=>e[6]||(e[6]=[o("span",{class:"radio-label"},"企业转账",-1)])),_:1,__:[6]}),t(v,{label:2,class:"payment-radio"},{default:l(()=>e[7]||(e[7]=[o("span",{class:"radio-label"},"商家转账",-1)])),_:1,__:[7]})]),_:1},8,["modelValue"])]),_:1}),t(c,{prop:"overTime"},{label:l(()=>[o("div",Q,[e[8]||(e[8]=o("span",null,"订单超时时间",-1)),t(_,{content:"订单未支付超时时间，超时将自动取消订单，单位：分钟",placement:"top",effect:"dark"},{default:l(()=>[t(p,{class:"tooltip-icon"},{default:l(()=>[t(u(g))]),_:1})]),_:1})])]),default:l(()=>[o("div",X,[t(q,{modelValue:a.overTime,"onUpdate:modelValue":e[1]||(e[1]=i=>a.overTime=i),min:1,max:10080,step:1,"controls-position":"right",class:"number-input"},null,8,["modelValue"]),e[9]||(e[9]=o("span",{class:"unit-label"},"分钟",-1))])]),_:1}),t(c,{prop:"maxDay"},{label:l(()=>[o("div",G,[e[10]||(e[10]=o("span",null,"最长预约时间",-1)),t(_,{content:"客户预约项目选择时间时可选择的时间期限",placement:"top",effect:"dark"},{default:l(()=>[t(p,{class:"tooltip-icon"},{default:l(()=>[t(u(g))]),_:1})]),_:1})])]),default:l(()=>[t(y,{modelValue:a.maxDay,"onUpdate:modelValue":e[2]||(e[2]=i=>a.maxDay=i),placeholder:"请选择最长预约时间",class:"select-input"},{default:l(()=>[t(r,{value:3,label:"3天"},{default:l(()=>e[11]||(e[11]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"3天"),o("span",{class:"option-desc"},"用户可提前3天预约")],-1)])),_:1,__:[11]}),t(r,{value:5,label:"5天"},{default:l(()=>e[12]||(e[12]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"5天"),o("span",{class:"option-desc"},"用户可提前5天预约")],-1)])),_:1,__:[12]}),t(r,{value:7,label:"7天"},{default:l(()=>e[13]||(e[13]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"7天"),o("span",{class:"option-desc"},"用户可提前7天预约")],-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),t(c,{prop:"timeUnit"},{label:l(()=>[o("div",H,[e[14]||(e[14]=o("span",null,"服务时长单位",-1)),t(_,{content:"划分工作时间的时间单位",placement:"top",effect:"dark"},{default:l(()=>[t(p,{class:"tooltip-icon"},{default:l(()=>[t(u(g))]),_:1})]),_:1})])]),default:l(()=>[t(y,{modelValue:a.timeUnit,"onUpdate:modelValue":e[3]||(e[3]=i=>a.timeUnit=i),placeholder:"请选择服务时长单位",class:"select-input"},{default:l(()=>[t(r,{value:30,label:"半小时"},{default:l(()=>e[15]||(e[15]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"半小时"),o("span",{class:"option-desc"},"30分钟为一个单位")],-1)])),_:1,__:[15]}),t(r,{value:60,label:"一小时"},{default:l(()=>e[16]||(e[16]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"一小时"),o("span",{class:"option-desc"},"60分钟为一个单位")],-1)])),_:1,__:[16]}),t(r,{value:120,label:"两小时"},{default:l(()=>e[17]||(e[17]=[o("div",{class:"option-content"},[o("span",{class:"option-label"},"两小时"),o("span",{class:"option-desc"},"120分钟为一个单位")],-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",J,[e[18]||(e[18]=o("div",{class:"section-title"},[o("span",null,"交易规则设置")],-1)),o("div",K,[t(M,{modelValue:a.tradingRules,"onUpdate:modelValue":e[4]||(e[4]=i=>a.tradingRules=i),height:"50vh",placeholder:"请输入交易规则内容..."},null,8,["modelValue"])])])]),_:1}),t(b,{class:"config-card",shadow:"never"},{default:l(()=>[o("div",W,[t(V,{type:"primary",onClick:C,loading:m.value,size:"large"},{default:l(()=>[t(p,null,{default:l(()=>[t(u(L))]),_:1}),e[19]||(e[19]=U(" 保存配置 "))]),_:1,__:[19]},8,["loading"]),t(V,{onClick:P,size:"large",style:{"margin-left":"15px"}},{default:l(()=>[t(p,null,{default:l(()=>[t(u(N))]),_:1}),e[20]||(e[20]=U(" 重置配置 "))]),_:1,__:[20]})])]),_:1})])])}}},ae=S(Y,[["__scopeId","data-v-c1f61ba3"]]);export{ae as default};
