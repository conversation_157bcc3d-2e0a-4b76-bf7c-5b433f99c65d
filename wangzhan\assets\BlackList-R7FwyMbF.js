import{g as W,r as m,X as I,h as X,y as j,Q as l,A as k,J as G,ar as Y,H as p,I as o,al as i,az as Z,z as c,u as ee,M as s,O as N,K as h}from"./vendor-DmFBDimT.js";import{E as d,u as le}from"./element-fdzwdDuf.js";import{T as ae,L as v}from"./LbButton-BtU4V_Gr.js";import{L as te}from"./LbPage-DnbiQ0Ct.js";import{_ as oe}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const ne={class:"blacklist-page"},se={class:"content-container"},re={class:"table-container"},ie={class:"permission-status"},de={__name:"BlackList",setup(ue){const{proxy:V}=W();Z();const T=m(!1),B=m([]),L=m(0),g=m(!1),b=m(!1),C=m(!1),z=m(!1),w=m({}),f=I({pageNum:1,pageSize:10}),n=I({coachId:"",banTx:0,banTk:0,banDl:0}),u=I({coachId:"",text:""}),U=a=>typeof a=="string"?a.split(",")[0]:a,P=a=>{w.value=a,n.coachId=a.id,n.banTx=a.banTx||0,n.banTk=a.banTk||0,n.banDl=a.banDl||0,g.value=!0},A=async()=>{try{C.value=!0;const a=await V.$api.technician.blackPermission({coachId:n.coachId,banTx:n.banTx,banTk:n.banTk,banDl:n.banDl});a.code==="200"?(d.success("权限设置成功"),g.value=!1,y()):d.error(a.msg||"权限设置失败")}catch(a){console.error("权限设置失败:",a),d.error("权限设置失败")}finally{C.value=!1}},E=a=>{w.value=a,u.coachId=a.id,u.text="",b.value=!0},H=async()=>{if(!u.text.trim()){d.warning("请输入移除原因");return}try{z.value=!0;const a=await V.$api.technician.addBlack({coachId:u.coachId,text:u.text,status:0});a.code==="200"?(d.success("移除黑名单成功"),b.value=!1,y()):d.error(a.msg||"移除黑名单失败")}catch(a){console.error("移除黑名单失败:",a),d.error("移除黑名单失败")}finally{z.value=!1}},M=a=>{f.pageSize=a,f.pageNum=1,y()},R=a=>{f.pageNum=a,y()},y=async()=>{try{T.value=!0;const a=await V.$api.technician.blackList(f);a.code==="200"?(B.value=a.data.list||[],L.value=a.data.totalCount||0):d.error(a.msg||"获取黑名单列表失败")}catch(a){console.error("获取黑名单列表失败:",a),d.error("获取黑名单列表失败")}finally{T.value=!1}};return X(()=>{y()}),(a,e)=>{const r=i("el-table-column"),S=i("el-avatar"),q=i("el-icon"),x=i("el-tag"),J=i("el-table"),_=i("el-form-item"),D=i("el-switch"),$=i("el-form"),F=i("el-dialog"),K=i("el-input"),O=Y("loading");return c(),j("div",ne,[l(ae,{title:"黑名单管理"}),k("div",se,[k("div",re,[G((c(),p(J,{data:B.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:o(()=>[l(r,{prop:"id",label:"ID",width:"80",align:"center"}),l(r,{prop:"selfImg",label:"师傅头像",width:"80",align:"center"},{default:o(t=>[t.row.selfImg?(c(),p(S,{key:0,src:U(t.row.selfImg),size:50,shape:"circle"},null,8,["src"])):(c(),p(S,{key:1,size:50,shape:"circle"},{default:o(()=>[l(q,null,{default:o(()=>[l(ee(le))]),_:1})]),_:1}))]),_:1}),l(r,{prop:"coachName",label:"师傅姓名",width:"120"}),l(r,{prop:"sex",label:"性别",width:"80",align:"center"},{default:o(t=>[l(x,{type:t.row.sex===0?"primary":"success",size:"small"},{default:o(()=>[s(N(t.row.sex===0?"男":t.row.sex===1?"女":"未知"),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"mobile",label:"手机号",width:"150"}),l(r,{prop:"idCode",label:"身份证号",width:"180"}),l(r,{prop:"address",label:"详细地址","min-width":"150","show-overflow-tooltip":""}),l(r,{prop:"createTime",label:"加入黑名单时间",width:"180"}),l(r,{label:"权限状态",width:"200",align:"center"},{default:o(t=>[k("div",ie,[t.row.banTx?(c(),p(x,{key:0,type:"danger",size:"small"},{default:o(()=>e[8]||(e[8]=[s("限制提现")])),_:1,__:[8]})):h("",!0),t.row.banTk?(c(),p(x,{key:1,type:"danger",size:"small"},{default:o(()=>e[9]||(e[9]=[s("限制退款")])),_:1,__:[9]})):h("",!0),t.row.banDl?(c(),p(x,{key:2,type:"danger",size:"small"},{default:o(()=>e[10]||(e[10]=[s("限制登录")])),_:1,__:[10]})):h("",!0),!t.row.banTx&&!t.row.banTk&&!t.row.banDl?(c(),p(x,{key:3,type:"success",size:"small"},{default:o(()=>e[11]||(e[11]=[s("无限制")])),_:1,__:[11]})):h("",!0)])]),_:1}),l(r,{label:"操作",width:"200",align:"center",fixed:"right"},{default:o(t=>[l(v,{size:"small",type:"primary",onClick:Q=>P(t.row)},{default:o(()=>e[12]||(e[12]=[s(" 权限设置 ")])),_:2,__:[12]},1032,["onClick"]),l(v,{size:"small",type:"success",onClick:Q=>E(t.row)},{default:o(()=>e[13]||(e[13]=[s(" 移除黑名单 ")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[O,T.value]])]),l(te,{page:f.pageNum,"page-size":f.pageSize,total:L.value,onHandleSizeChange:M,onHandleCurrentChange:R},null,8,["page","page-size","total"])]),l(F,{modelValue:g.value,"onUpdate:modelValue":e[4]||(e[4]=t=>g.value=t),title:"权限设置",width:"500px"},{footer:o(()=>[l(v,{onClick:e[3]||(e[3]=t=>g.value=!1)},{default:o(()=>e[14]||(e[14]=[s("取消")])),_:1,__:[14]}),l(v,{type:"primary",onClick:A,loading:C.value},{default:o(()=>e[15]||(e[15]=[s(" 确认设置 ")])),_:1,__:[15]},8,["loading"])]),default:o(()=>[l($,{model:n,"label-width":"100px"},{default:o(()=>[l(_,{label:"师傅姓名"},{default:o(()=>[k("span",null,N(w.value.coachName),1)]),_:1}),l(_,{label:"限制提现"},{default:o(()=>[l(D,{modelValue:n.banTx,"onUpdate:modelValue":e[0]||(e[0]=t=>n.banTx=t),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),l(_,{label:"限制退款"},{default:o(()=>[l(D,{modelValue:n.banTk,"onUpdate:modelValue":e[1]||(e[1]=t=>n.banTk=t),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),l(_,{label:"限制登录"},{default:o(()=>[l(D,{modelValue:n.banDl,"onUpdate:modelValue":e[2]||(e[2]=t=>n.banDl=t),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(F,{modelValue:b.value,"onUpdate:modelValue":e[7]||(e[7]=t=>b.value=t),title:"移除黑名单",width:"500px"},{footer:o(()=>[l(v,{onClick:e[6]||(e[6]=t=>b.value=!1)},{default:o(()=>e[16]||(e[16]=[s("取消")])),_:1,__:[16]}),l(v,{type:"primary",onClick:H,loading:z.value},{default:o(()=>e[17]||(e[17]=[s(" 确认移除 ")])),_:1,__:[17]},8,["loading"])]),default:o(()=>[l($,{model:u,"label-width":"100px"},{default:o(()=>[l(_,{label:"师傅姓名"},{default:o(()=>[k("span",null,N(w.value.coachName),1)]),_:1}),l(_,{label:"移除原因",required:""},{default:o(()=>[l(K,{modelValue:u.text,"onUpdate:modelValue":e[5]||(e[5]=t=>u.text=t),type:"textarea",rows:4,placeholder:"请输入移除原因",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ge=oe(de,[["__scopeId","data-v-09b50e83"]]);export{ge as default};
