import{ax as B,r as u,c as E,X as I,h as R,al as t,y as z,z as A,Q as o,A as s,I as l,M as d,O as _}from"./vendor-DmFBDimT.js";import{T as D,L as h}from"./LbButton-BtU4V_Gr.js";import{_ as F,m as V,a as M}from"./index-C9Xz1oqp.js";import{E as g}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const q={class:"security-view"},U={class:"content-container"},Z={class:"tips-content"},$={__name:"SecurityView",setup(O){const v=B(),c=u(!1),p=u(),x=E(()=>v.state.auth.userInfo),P=u(""),y=u(""),a=I({newPassword:"",confirmPassword:""}),C={newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(n,e,r)=>{e!==a.newPassword?r(new Error("两次输入的密码不一致")):r()},trigger:"blur"}]},S=async()=>{try{await p.value.validate(),c.value=!0;const n=V(a.newPassword),e=V(a.confirmPassword),r=await M.base.changePassword({newPassword:n,confirmPassword:e});r.code==="200"||r.code===200?(g.success("密码修改成功，请重新登录"),b(),y.value=new Date().toLocaleString(),setTimeout(()=>{v.dispatch("auth/logout"),window.location.href="/login"},2e3)):g.error(r.msg||"密码修改失败")}catch(n){console.error("修改密码失败:",n),g.error(n.message||"密码修改失败")}finally{c.value=!1}},b=()=>{a.newPassword="",a.confirmPassword="",p.value?.clearValidate()};return R(()=>{P.value=new Date().toLocaleString()}),(n,e)=>{const r=t("el-input"),m=t("el-form-item"),T=t("el-form"),w=t("el-card"),i=t("el-descriptions-item"),L=t("el-tag"),N=t("el-descriptions"),k=t("el-alert");return A(),z("div",q,[o(D,{title:"安全设置"}),s("div",U,[o(w,{class:"password-card",shadow:"never"},{header:l(()=>e[2]||(e[2]=[s("div",{class:"card-header"},[s("h3",null,"修改密码"),s("p",null,"为了您的账户安全，建议定期修改密码")],-1)])),default:l(()=>[o(T,{ref_key:"passwordFormRef",ref:p,model:a,rules:C,"label-width":"120px",class:"password-form"},{default:l(()=>[o(m,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(r,{modelValue:a.newPassword,"onUpdate:modelValue":e[0]||(e[0]=f=>a.newPassword=f),type:"password",placeholder:"请输入新密码","show-password":"",clearable:"",style:{width:"400px"}},null,8,["modelValue"]),e[3]||(e[3]=s("div",{class:"password-tips"},[s("p",null,"密码要求："),s("ul",null,[s("li",null,"长度6-20个字符"),s("li",null,"建议包含字母、数字和特殊字符"),s("li",null,"不要使用过于简单的密码")])],-1))]),_:1,__:[3]}),o(m,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(r,{modelValue:a.confirmPassword,"onUpdate:modelValue":e[1]||(e[1]=f=>a.confirmPassword=f),type:"password",placeholder:"请再次输入新密码","show-password":"",clearable:"",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),o(m,null,{default:l(()=>[o(h,{type:"primary",onClick:S,loading:c.value},{default:l(()=>e[4]||(e[4]=[d(" 修改密码 ")])),_:1,__:[4]},8,["loading"]),o(h,{onClick:b},{default:l(()=>e[5]||(e[5]=[d(" 重置 ")])),_:1,__:[5]})]),_:1})]),_:1},8,["model"])]),_:1}),o(w,{class:"security-info-card",shadow:"never"},{header:l(()=>e[6]||(e[6]=[s("div",{class:"card-header"},[s("h3",null,"安全信息")],-1)])),default:l(()=>[o(N,{column:1,border:""},{default:l(()=>[o(i,{label:"当前用户"},{default:l(()=>[d(_(x.value.username||"管理员"),1)]),_:1}),o(i,{label:"最后登录时间"},{default:l(()=>[d(_(P.value||"未知"),1)]),_:1}),o(i,{label:"最后修改密码时间"},{default:l(()=>[d(_(y.value||"未知"),1)]),_:1}),o(i,{label:"账户状态"},{default:l(()=>[o(L,{type:"success"},{default:l(()=>e[7]||(e[7]=[d("正常")])),_:1,__:[7]})]),_:1})]),_:1})]),_:1}),o(w,{class:"security-tips-card",shadow:"never"},{header:l(()=>e[8]||(e[8]=[s("div",{class:"card-header"},[s("h3",null,"安全建议")],-1)])),default:l(()=>[s("div",Z,[o(k,{title:"安全提醒",type:"info",closable:!1,"show-icon":""},{default:l(()=>e[9]||(e[9]=[s("ul",null,[s("li",null,"定期修改密码，建议每3个月更换一次"),s("li",null,"不要在多个平台使用相同密码"),s("li",null,"不要将密码告诉他人或写在容易被发现的地方"),s("li",null,"如发现账户异常，请立即修改密码并联系管理员")],-1)])),_:1,__:[9]})])]),_:1})])])}}},J=F($,[["__scopeId","data-v-01b86359"]]);export{J as default};
