/**
 * 轮播图API测试文件
 * 用于测试轮播图相关的6个API接口
 */

import { api } from '@/api-v2'

// 测试轮播图API的所有功能
export class BannerApiTest {
  constructor() {
    this.testResults = []
  }

  // 记录测试结果
  logResult(testName, success, message, data = null) {
    const result = {
      testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    }
    this.testResults.push(result)
    console.log(`${success ? '✅' : '❌'} ${testName}: ${message}`, data)
  }

  // 1. 测试获取轮播图列表
  async testBannerList() {
    try {
      const params = {
        status: 1,
        type: 1,
        pageNum: 1,
        pageSize: 5
      }
      
      const response = await api.service.bannerList(params)
      
      if (response && response.code === '200') {
        this.logResult('轮播图列表', true, '获取成功', response.data)
        return response.data
      } else {
        this.logResult('轮播图列表', false, response?.msg || '获取失败', response)
        return null
      }
    } catch (error) {
      this.logResult('轮播图列表', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 2. 测试获取轮播图详情
  async testBannerDetail(bannerId) {
    try {
      if (!bannerId) {
        this.logResult('轮播图详情', false, '缺少轮播图ID参数')
        return null
      }

      const response = await api.service.bannerDetail({ id: bannerId })
      
      if (response && response.code === '200') {
        this.logResult('轮播图详情', true, '获取成功', response.data)
        return response.data
      } else {
        this.logResult('轮播图详情', false, response?.msg || '获取失败', response)
        return null
      }
    } catch (error) {
      this.logResult('轮播图详情', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 3. 测试新增轮播图
  async testBannerAdd() {
    try {
      const bannerData = {
        img: 'https://example.com/test-banner.jpg',
        top: 999,
        link: 'https://example.com',
        status: 0,
        type: 1
      }
      
      const response = await api.service.bannerAdd(bannerData)
      
      if (response && response.code === '200') {
        this.logResult('新增轮播图', true, '新增成功', response.data)
        return response.data
      } else {
        this.logResult('新增轮播图', false, response?.msg || '新增失败', response)
        return null
      }
    } catch (error) {
      this.logResult('新增轮播图', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 4. 测试编辑轮播图
  async testBannerEdit(bannerId) {
    try {
      if (!bannerId) {
        this.logResult('编辑轮播图', false, '缺少轮播图ID参数')
        return null
      }

      const bannerData = {
        id: bannerId,
        img: 'https://example.com/test-banner-updated.jpg',
        top: 888,
        link: 'https://example.com/updated',
        status: 1,
        type: 1
      }
      
      const response = await api.service.bannerEdit(bannerData)
      
      if (response && response.code === '200') {
        this.logResult('编辑轮播图', true, '编辑成功', response.data)
        return response.data
      } else {
        this.logResult('编辑轮播图', false, response?.msg || '编辑失败', response)
        return null
      }
    } catch (error) {
      this.logResult('编辑轮播图', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 5. 测试修改轮播图状态
  async testBannerStatus(bannerId) {
    try {
      if (!bannerId) {
        this.logResult('修改轮播图状态', false, '缺少轮播图ID参数')
        return null
      }

      const response = await api.service.bannerStatus({ id: bannerId })
      
      if (response && response.code === '200') {
        this.logResult('修改轮播图状态', true, '状态修改成功', response.data)
        return response.data
      } else {
        this.logResult('修改轮播图状态', false, response?.msg || '状态修改失败', response)
        return null
      }
    } catch (error) {
      this.logResult('修改轮播图状态', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 6. 测试删除轮播图
  async testBannerDelete(bannerId) {
    try {
      if (!bannerId) {
        this.logResult('删除轮播图', false, '缺少轮播图ID参数')
        return null
      }

      const response = await api.service.bannerDelete({ id: bannerId })
      
      if (response && response.code === '200') {
        this.logResult('删除轮播图', true, '删除成功', response.data)
        return response.data
      } else {
        this.logResult('删除轮播图', false, response?.msg || '删除失败', response)
        return null
      }
    } catch (error) {
      this.logResult('删除轮播图', false, `请求异常: ${error.message}`, error)
      return null
    }
  }

  // 运行完整的API测试流程
  async runFullTest() {
    console.log('🚀 开始轮播图API完整测试...')
    
    // 1. 获取轮播图列表
    const listData = await this.testBannerList()
    
    // 2. 如果列表中有数据，测试获取详情
    let testBannerId = null
    if (listData && listData.list && listData.list.length > 0) {
      testBannerId = listData.list[0].id
      await this.testBannerDetail(testBannerId)
    }
    
    // 3. 测试新增轮播图
    const addResult = await this.testBannerAdd()
    if (addResult && addResult.id) {
      testBannerId = addResult.id
    }
    
    // 4. 如果有可用的ID，测试编辑
    if (testBannerId) {
      await this.testBannerEdit(testBannerId)
    }
    
    // 5. 测试修改状态
    if (testBannerId) {
      await this.testBannerStatus(testBannerId)
    }
    
    // 6. 最后测试删除（注意：这会真的删除数据）
    // 在生产环境中请谨慎使用
    // if (testBannerId) {
    //   await this.testBannerDelete(testBannerId)
    // }
    
    console.log('✅ 轮播图API测试完成')
    return this.getTestSummary()
  }

  // 获取测试摘要
  getTestSummary() {
    const total = this.testResults.length
    const passed = this.testResults.filter(r => r.success).length
    const failed = total - passed
    
    return {
      total,
      passed,
      failed,
      passRate: total > 0 ? ((passed / total) * 100).toFixed(2) + '%' : '0%',
      results: this.testResults
    }
  }

  // 打印测试报告
  printTestReport() {
    const summary = this.getTestSummary()
    
    console.log('\n📊 轮播图API测试报告')
    console.log('='.repeat(50))
    console.log(`总测试数: ${summary.total}`)
    console.log(`通过数: ${summary.passed}`)
    console.log(`失败数: ${summary.failed}`)
    console.log(`通过率: ${summary.passRate}`)
    console.log('='.repeat(50))
    
    summary.results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.testName}: ${result.success ? '✅' : '❌'} ${result.message}`)
    })
    
    return summary
  }
}

// 导出测试实例
export const bannerApiTest = new BannerApiTest()

// 使用示例：
// import { bannerApiTest } from '@/test/banner-api-test.js'
// 
// // 运行完整测试
// bannerApiTest.runFullTest().then(() => {
//   bannerApiTest.printTestReport()
// })
//
// // 或者单独测试某个接口
// bannerApiTest.testBannerList()
