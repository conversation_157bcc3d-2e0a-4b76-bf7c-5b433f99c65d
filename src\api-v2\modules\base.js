/**
 * 基础模块 - V2版本
 * 包含登录、配置等基础功能接口
 */

import { get, post } from '../index'
import { md5 } from '@/utils/crypto'

export default {
  /**
   * 用户登录 - 调用真实的登录接口，支持Cookie认证
   * @param {Object} querys 登录参数
   * @param {string} querys.username 用户名
   * @param {string} querys.password 密码（明文，会自动进行MD5加密）
   * @param {boolean} querys.rememberMe 记住我选项
   * @returns {Promise} 返回登录结果（包含菜单数据）
   */
  async login(querys) {
    console.log('🔐 用户登录API-V2请求:', {
      username: querys.username,
      rememberMe: querys.rememberMe
    })

    // 对密码进行MD5加密
    const encryptedPassword = md5(querys.password)

    const loginData = {
      username: querys.username,
      password: encryptedPassword
    }

    console.log('🔐 登录请求数据:', {
      username: loginData.username,
      password: '***已加密***'
    })

    try {
      // 调用真实的登录接口
      const response = await post('/api/admin/login/loginByPass', loginData)
      console.log('🔐 登录API响应:', response)

      // 处理响应数据
      if (response.code === '200' || response.code === 200) {
        console.log('🔐 登录成功，后端已返回成功响应')

        // 立即检查Cookie是否已设置
        const checkCookie = () => {
          const allCookies = document.cookie
          console.log('🍪 当前所有Cookies:', allCookies)

          const autographCookie = allCookies
            .split('; ')
            .find(row => row.startsWith('autograph='))

          if (autographCookie) {
            const cookieValue = autographCookie.split('=')[1]
            console.log('✅ autograph Cookie已成功设置!')
            console.log('🔑 Cookie值:', cookieValue)
            console.log('📝 Cookie完整信息:', autographCookie)
            return true
          } else {
            console.warn('⚠️ 未检测到autograph Cookie')
            console.log('🔍 可能的原因:')
            console.log('   1. 后端Set-Cookie头未正确设置')
            console.log('   2. Cookie域名/路径不匹配')
            console.log('   3. 浏览器安全策略阻止')
            return false
          }
        }

        // 立即检查一次
        const immediateCheck = checkCookie()

        // 如果立即检查失败，延迟再检查一次
        if (!immediateCheck) {
          setTimeout(() => {
            console.log('🔄 延迟检查Cookie状态...')
            checkCookie()
          }, 200)
        }

        // 检查登录响应中是否已包含菜单数据
        let userMenus = []
        if (response.data && response.data.menus) {
          // 如果登录接口直接返回了菜单数据，直接使用
          userMenus = response.data.menus
          console.log('🌲 登录接口直接返回菜单数据:', userMenus)
        } else if (response.data && Array.isArray(response.data)) {
          // 如果登录接口返回的data本身就是菜单数组
          userMenus = response.data
          console.log('🌲 登录接口返回菜单数组:', userMenus)
        } else {
          console.log('🌲 登录接口未返回菜单数据，使用空数组')
          userMenus = []
        }

        // 返回标准化的响应格式（包含菜单数据）
        return {
          code: '200',
          msg: response.msg || '登录成功',
          data: {
            // Cookie认证模式下，用户信息可以从响应中获取或使用默认值
            userInfo: response.data || {
              id: 1,
              username: querys.username,
              name: '管理员',
              avatar: '',
              roles: ['admin'],
              permissions: ['*:*:*']
            },
            // 重要：添加菜单数据到登录响应中
            menus: userMenus,
            // 重要：不返回token，让浏览器自动管理autograph cookie
            // token会通过Set-Cookie头自动设置，前端不应该手动处理
            cookieAuth: true // 标识这是cookie认证模式
          }
        }
      } else {
        throw new Error(response.msg || response.message || '登录失败')
      }
    } catch (error) {
      console.error('🔐 登录失败:', error)
      // 确保错误信息格式正确
      const errorMessage = error.response?.data?.msg ||
                         error.response?.data?.message ||
                         error.message ||
                         '登录失败，请检查用户名和密码'
      throw new Error(errorMessage)
    }
  },

  /**
   * 用户登出 - 调用真实的登出接口
   * @returns {Promise} 返回登出结果
   */
  logout() {
    console.log('🚪 用户登出API-V2请求')

    // 调用真实的登出接口 - 使用用户提供的接口路径
    return post('/api/admin/login/logout')
      .then(response => {
        console.log('🚪 登出API响应:', response)
        return response
      })
      .catch(error => {
        console.error('🚪 登出失败:', error)
        // 即使登出接口失败，也返回成功（本地清理更重要）
        return {
          code: '200',
          msg: '登出成功',
          data: null
        }
      })
  },

  /**
   * 获取用户信息 - 调用真实的用户信息接口
   * @returns {Promise} 返回用户信息
   */
  getUserInfo() {
    console.log('👤 获取用户信息API-V2请求')

    // 调用真实的用户信息接口
    return get('/api/admin/auth/userinfo')
      .then(response => {
        console.log('👤 获取用户信息API响应:', response)

        // 确保返回标准格式
        if (response.code === '200' || response.code === 200) {
          return {
            code: '200',
            msg: response.msg || '获取成功',
            data: response.data || {
              id: 1,
              username: 'admin',
              name: '管理员',
              avatar: '',
              roles: ['admin'],
              permissions: ['*:*:*'],
              email: '<EMAIL>',
              phone: '13800138000',
              status: 1,
              createTime: new Date().toISOString(),
              updateTime: new Date().toISOString()
            }
          }
        } else {
          throw new Error(response.msg || '获取用户信息失败')
        }
      })
      .catch(error => {
        console.error('👤 获取用户信息失败:', error)
        throw error
      })
  },

  /**
   * 修改密码 - 使用用户提供的接口路径
   * @param {Object} querys 修改密码参数
   * @param {string} querys.newPassword 新密码（需要MD5加密）
   * @param {string} querys.confirmPassword 确认密码（需要MD5加密）
   * @returns {Promise} 返回修改结果
   */
  changePassword(querys) {
    console.log('🔑 修改密码API-V2请求')

    // 确保密码已经MD5加密
    const requestData = {
      newPassword: querys.newPassword,
      confirmPassword: querys.confirmPassword
    }

    return post('/api/admin/login/updatePass', requestData)
  },

  /**
   * 获取系统配置
   * @returns {Promise} 返回系统配置
   */
  getConfig() {
    console.log('⚙️ 获取系统配置API-V2请求')
    return get('/api/admin/config/info')
  },

  /**
   * 更新系统配置
   * @param {Object} querys 配置参数
   * @returns {Promise} 返回更新结果
   */
  updateConfig(querys) {
    console.log('⚙️ 更新系统配置API-V2请求:', querys)
    return post('/api/admin/config/update', querys)
  }
}
