import{E as i,x as Y,q as Re}from"./element-fdzwdDuf.js";import{T as qe,L as I}from"./LbButton-BtU4V_Gr.js";import{L as Z}from"./LbImage-CnNh5Udj.js";import{L as Be}from"./LbPage-DnbiQ0Ct.js";import{_ as Ee,a as P,m as je}from"./index-C9Xz1oqp.js";import{r as m,X as A,c as Oe,h as He,y as N,Q as a,A as u,I as t,al as g,J as Ke,ar as Je,H as Qe,z as w,M as f,O as b,K as F,u as ee}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const We={class:"account-franchisee"},Xe={class:"content-container"},Ge={class:"search-form-container"},Ye={class:"table-container"},Ze={class:"table-operate"},el={class:"dialog-footer"},ll={key:0,class:"upload-progress"},al={class:"dialog-footer"},tl={class:"image-view-container"},ol={key:0,class:"image-item"},sl={key:1,class:"image-item"},nl={key:2,class:"image-item"},rl={__name:"AccountFranchisee",setup(dl){const $=m(!1),M=m(!1),R=m(!1),le=m([]),ae=m(0),te=m(),D=m(),oe=m(),V=m(!1),x=m(!1),q=m(!1),L=m([]),T=m([]),k=m([]),_=m(0),B=m(!1),E=m([]),de={value:"id",label:"trueName",children:"children",multiple:!1,emitPath:!0,checkStrictly:!0},d=A({pageNum:1,pageSize:10,username:"",legalPersonName:"",status:null,type:null}),o=A({id:null,userId:null,username:"",password:"",type:2,cityId:[],legalPersonName:"",legalPersonIdCard:"",legalPersonTel:"",legalPersonIdCardImg1:"",legalPersonIdCardImg2:"",legalPersonLicense:""}),p=A({id:null,legalPersonName:"",legalPersonTel:"",type:null,status:1,examinedText:""}),v=A({legalPersonIdCardImg1:"",legalPersonIdCardImg2:"",legalPersonLicense:""}),ue={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],type:[{required:!0,message:"请选择代理类型",trigger:"change"}],cityId:[{required:!0,message:"请选择代理城市",trigger:"change"}],legalPersonName:[{required:!0,message:"请输入法人姓名",trigger:"blur"}],legalPersonIdCard:[{required:!0,message:"请输入法人身份证号",trigger:"blur"}],legalPersonTel:[{required:!0,message:"请输入法人手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],legalPersonIdCardImg1:[{required:!0,message:"请上传身份证正面照片",trigger:"change"}],legalPersonIdCardImg2:[{required:!0,message:"请上传身份证反面照片",trigger:"change"}],legalPersonLicense:[{required:!0,message:"请上传营业执照照片",trigger:"change"}]},ie={status:[{required:!0,message:"请选择审核结果",trigger:"change"}],examinedText:[{required:!0,message:"请输入审核原因",trigger:"blur"}]},ge=Oe(()=>o.id?"编辑代理商":"新增代理商"),C=async e=>{try{$.value=!0,e&&(d.pageNum=1),console.log("📋 获取代理商列表:",d);const l={pageNum:d.pageNum,pageSize:d.pageSize};d.username&&(l.username=d.username),d.legalPersonName&&(l.legalPersonName=d.legalPersonName),d.status!==null&&d.status!==""&&(l.status=d.status),d.type!==null&&d.type!==""&&(l.type=d.type);const n=await P.account.agentList(l);if(n.code==="200"||n.code===200){const r=n.data;le.value=(r.list||[]).map(c=>({...c,statusLoading:!1})),ae.value=r.totalCount||0,console.log("✅ 代理商列表获取成功:",r)}else i.error(n.msg||"获取列表失败")}catch(l){console.error("获取代理商列表失败:",l),i.error("获取数据失败")}finally{$.value=!1}},pe=e=>({1:"danger",2:"warning",3:"success"})[e]||"info",j=e=>({1:"省级代理",2:"市级代理",3:"区县代理"})[e]||"未知",ce=e=>({0:"warning",1:"success",2:"danger","-1":"info"})[e]||"info",me=e=>({0:"未审核",1:"审核通过",2:"审核未通过","-1":"禁用"})[e]||"未知",fe=(e,l)=>{if(e){const n=e.split(",");return`城市ID: ${l||n[n.length-1]}`}return"未设置"},ye=()=>{C(1)},Ie=()=>{d.username="",d.legalPersonName="",d.status=null,d.type=null,te.value?.resetFields(),C(1)},_e=()=>{K(),V.value=!0},ve=e=>{if(K(),o.id=e.id,o.userId=e.userId,o.username=e.username,o.type=e.type,o.legalPersonName=e.legalPersonName,o.legalPersonIdCard=e.legalPersonIdcard,o.legalPersonTel=e.legalPersonTel,o.legalPersonIdCardImg1=e.legalPersonIdcardImg1||"",o.legalPersonIdCardImg2=e.legalPersonIdcardImg2||"",o.legalPersonLicense=e.legalPersonLicense||"",e.cityId){const l=e.cityId.split(",").map(n=>parseInt(n));e.type===1?o.cityId=[l[0]]:e.type===2?o.cityId=l.slice(0,2):e.type===3?o.cityId=l.slice(0,3):o.cityId=l,console.log("📍 编辑回填城市数据:",{原始cityId:e.cityId,代理类型:e.type,处理后cityId:o.cityId})}e.legalPersonIdcardImg1&&(L.value=[{name:"image1",url:e.legalPersonIdcardImg1}]),e.legalPersonIdcardImg2&&(T.value=[{name:"image2",url:e.legalPersonIdcardImg2}]),e.legalPersonLicense&&(k.value=[{name:"image3",url:e.legalPersonLicense}]),V.value=!0},Pe=e=>{Object.assign(p,{id:e.id,legalPersonName:e.legalPersonName,legalPersonTel:e.legalPersonTel,type:e.type,status:1,examinedText:""}),x.value=!0},be=e=>{v.legalPersonIdCardImg1=e.legalPersonIdcardImg1||"",v.legalPersonIdCardImg2=e.legalPersonIdcardImg2||"",v.legalPersonLicense=e.legalPersonLicense||"",q.value=!0},Ce=async()=>{try{await Re.confirm("确定要修改代理商默认密码吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.account.agentDefaultPassword();e.code==="200"||e.code===200?i.success("默认密码修改成功"):i.error(e.msg||"修改失败")}catch(e){e!=="cancel"&&(console.error("修改默认密码失败:",e),i.error("修改失败"))}},he=async e=>{if(e.statusLoading)return;const l=e.isAgent;try{e.statusLoading=!0,console.log(`🔄 切换代理商状态: ID=${e.id}, 当前状态=${e.isAgent}`);const n=await P.account.agentStatus({id:e.id});console.log("📊 状态切换API响应:",n),n.code==="200"||n.code===200?(i.success("状态修改成功"),console.log(`✅ 状态切换成功: ID=${e.id}, 新状态=${e.isAgent}`),await C()):(e.isAgent=l,console.error("❌ 状态切换失败:",n),i.error(n.msg||"状态修改失败"))}catch(n){e.isAgent=l,console.error("❌ 状态切换异常:",n),i.error("状态修改失败")}finally{e.statusLoading=!1}},Ve=async()=>{try{await oe.value.validate(),R.value=!0;const e={id:p.id,examinedText:p.examinedText,status:p.status},l=await P.account.agentExamine(e);l.code==="200"||l.code===200?(i.success("审核成功"),x.value=!1,C()):i.error(l.msg||"审核失败")}catch(e){console.error("审核失败:",e),i.error("审核失败")}finally{R.value=!1}},O=e=>(console.log("📋 图片上传前验证:",e),e.type.indexOf("image/")===0?(console.log("✅ 图片验证通过"),!0):(i.error("只能上传图片文件!"),!1)),H=async(e,l)=>{console.log("🖼️ 图片文件变更:",e,l),e.status==="ready"&&await we(e,l)},U=e=>{console.log("🗑️ 移除图片:",e),o[e]="",_.value=0,e==="legalPersonIdCardImg1"?L.value=[]:e==="legalPersonIdCardImg2"?T.value=[]:e==="legalPersonLicense"&&(k.value=[])},we=async(e,l)=>{console.log("📤 开始上传图片:",e,l);try{B.value=!0,_.value=0;const n=new FormData;n.append("multipartFile",e.raw),console.log("📦 FormData创建完成:",n);const r=await P.upload.uploadFile(n,c=>{_.value=Math.round(c.loaded*100/c.total),console.log("📊 上传进度:",_.value+"%")});if(console.log("✅ 图片上传成功:",r),r.code===200||r.code==="200"){o[l]=r.data.url||r.data.fileUrl||r.data,i.success("图片上传成功");const c={legalPersonIdCardImg1:L,legalPersonIdCardImg2:T,legalPersonLicense:k}[l];c&&(c.value=[{name:e.name,url:o[l],status:"success"}]),console.log("💾 图片URL已保存到表单:",o[l])}else throw new Error(r.message||r.msg||"上传失败")}catch(n){console.error("❌ 图片上传失败:",n),i.error("图片上传失败: "+(n.message||"未知错误")),U(l)}finally{B.value=!1,_.value=0}},xe=async()=>{try{await D.value.validate(),M.value=!0;const e={...o};if(e.password&&e.password.trim()?e.password=je(e.password):delete e.password,Array.isArray(e.cityId)&&e.cityId.length>0){const n=e.type;if(e.cityId.length!==n){i.error(`${j(e.type)}需要选择${n}级城市`);return}console.log("🏙️ 提交城市数据:",{代理类型:e.type,城市ID数组:e.cityId,数组长度:e.cityId.length})}else{i.error("请选择代理城市");return}let l;o.id?(l=await P.account.agentEdit(e),console.log("✏️ 编辑代理商提交:",e)):(l=await P.account.agentAdd(e),console.log("➕ 新增代理商提交:",e)),l.code==="200"||l.code===200?(i.success(o.id?"更新成功":"新增成功"),V.value=!1,C()):i.error(l.msg||"操作失败")}catch(e){console.error("提交失败:",e),i.error("操作失败")}finally{M.value=!1}},Le=()=>{K()},Te=()=>{p.id=null,p.legalPersonName="",p.legalPersonTel="",p.type=null,p.status=1,p.examinedText=""},K=()=>{o.id=null,o.userId=null,o.username="",o.password="",o.type=2,o.cityId=[],o.legalPersonName="",o.legalPersonIdCard="",o.legalPersonTel="",o.legalPersonIdCardImg1="",o.legalPersonIdCardImg2="",o.legalPersonLicense="",L.value=[],T.value=[],k.value=[],_.value=0,B.value=!1,D.value&&D.value.clearValidate()},ke=e=>{d.pageSize=e,se(1)},se=e=>{d.pageNum=e,C()},ze=e=>{console.log("🏙️ 城市选择变更:",e),e&&e.length>0?e.length===1?(o.type=1,o.cityId=[e[0]],console.log("📍 设置为省级代理:",o.cityId)):e.length===2?(o.type=2,o.cityId=[e[0],e[1]],console.log("📍 设置为市级代理:",o.cityId)):e.length===3&&(o.type=3,o.cityId=[e[0],e[1],e[2]],console.log("📍 设置为区县级代理:",o.cityId)):(o.cityId=[],o.type=2)},Ne=async()=>{try{console.log("🌳 开始获取城市树数据");const e=await P.account.agentCityTree();e.code==="200"||e.code===200?(E.value=e.data||[],console.log("✅ 城市树数据获取成功:",E.value)):(console.error("❌ 获取城市数据失败:",e.msg),i.error(e.msg||"获取城市数据失败"))}catch(e){console.error("❌ 获取城市数据异常:",e),i.error("获取城市数据失败")}};return He(()=>{C(1),Ne()}),(e,l)=>{const n=g("el-input"),r=g("el-form-item"),c=g("el-option"),J=g("el-select"),h=g("el-col"),S=g("el-row"),Q=g("el-form"),y=g("el-table-column"),ne=g("el-tag"),De=g("el-switch"),Ue=g("el-table"),re=g("el-radio"),Se=g("el-radio-group"),W=g("el-dialog"),Ae=g("el-input-number"),Fe=g("el-cascader"),X=g("el-icon"),G=g("el-upload"),$e=g("el-progress"),Me=Je("loading");return w(),N("div",We,[a(qe,{title:"代理商管理"}),u("div",Xe,[u("div",Ge,[a(Q,{ref_key:"searchFormRef",ref:te,model:d,inline:!0,class:"search-form"},{default:t(()=>[a(S,{gutter:20},{default:t(()=>[a(h,{span:24},{default:t(()=>[a(r,{label:"用户名",prop:"username"},{default:t(()=>[a(n,{size:"default",modelValue:d.username,"onUpdate:modelValue":l[0]||(l[0]=s=>d.username=s),placeholder:"请输入用户名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(r,{label:"法人姓名",prop:"legalPersonName"},{default:t(()=>[a(n,{size:"default",modelValue:d.legalPersonName,"onUpdate:modelValue":l[1]||(l[1]=s=>d.legalPersonName=s),placeholder:"请输入法人姓名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(r,{label:"审核状态",prop:"status"},{default:t(()=>[a(J,{size:"default",modelValue:d.status,"onUpdate:modelValue":l[2]||(l[2]=s=>d.status=s),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:t(()=>[a(c,{label:"未审核",value:0}),a(c,{label:"审核通过",value:1}),a(c,{label:"审核未通过",value:2})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"代理类型",prop:"type"},{default:t(()=>[a(J,{size:"default",modelValue:d.type,"onUpdate:modelValue":l[3]||(l[3]=s=>d.type=s),placeholder:"请选择类型",clearable:"",style:{width:"150px"}},{default:t(()=>[a(c,{label:"省级代理",value:1}),a(c,{label:"市级代理",value:2}),a(c,{label:"区县代理",value:3})]),_:1},8,["modelValue"])]),_:1}),a(r,null,{default:t(()=>[a(I,{size:"default",type:"primary",icon:"Search",onClick:ye},{default:t(()=>l[19]||(l[19]=[f(" 搜索 ")])),_:1,__:[19]}),a(I,{size:"default",icon:"RefreshLeft",onClick:Ie},{default:t(()=>l[20]||(l[20]=[f(" 重置 ")])),_:1,__:[20]}),a(I,{size:"default",type:"primary",icon:"Plus",onClick:_e},{default:t(()=>l[21]||(l[21]=[f(" 新增代理商 ")])),_:1,__:[21]}),a(I,{size:"default",type:"warning",icon:"Key",onClick:Ce},{default:t(()=>l[22]||(l[22]=[f(" 修改默认密码 ")])),_:1,__:[22]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u("div",Ye,[Ke((w(),Qe(Ue,{data:le.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:t(()=>[a(y,{prop:"id",label:"ID",width:"80",align:"center"}),a(y,{prop:"username",label:"用户名",width:"120"}),a(y,{prop:"userId",label:"用户ID",width:"100",align:"center"}),a(y,{prop:"legalPersonName",label:"法人姓名",width:"120"}),a(y,{prop:"legalPersonTel",label:"法人电话",width:"130"}),a(y,{prop:"legalPersonIdcard",label:"身份证号",width:"180"}),a(y,{prop:"type",label:"代理类型",width:"100",align:"center"},{default:t(s=>[a(ne,{type:pe(s.row.type),size:"default"},{default:t(()=>[f(b(j(s.row.type)),1)]),_:2},1032,["type"])]),_:1}),a(y,{label:"代理区域",width:"150"},{default:t(s=>[u("span",null,b(fe(s.row.cityId,s.row.selectCityId)),1)]),_:1}),a(y,{prop:"cash",label:"余额",width:"100",align:"center"},{default:t(s=>[u("span",null,"¥"+b(s.row.cash||0),1)]),_:1}),a(y,{label:"审核状态",width:"120",align:"center"},{default:t(s=>[a(ne,{type:ce(s.row.status),size:"default"},{default:t(()=>[f(b(me(s.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(y,{label:"状态",width:"100",align:"center"},{default:t(s=>[a(De,{modelValue:s.row.isAgent,"onUpdate:modelValue":z=>s.row.isAgent=z,"active-value":1,"inactive-value":0,loading:s.row.statusLoading,onChange:z=>he(s.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),a(y,{prop:"createTime",label:"创建时间",width:"170"}),a(y,{label:"操作",width:"280",fixed:"right"},{default:t(s=>[u("div",Ze,[a(I,{size:"default",type:"primary",onClick:z=>Pe(s.row)},{default:t(()=>l[23]||(l[23]=[f(" 审核 ")])),_:2,__:[23]},1032,["onClick"]),a(I,{size:"default",type:"success",onClick:z=>ve(s.row)},{default:t(()=>l[24]||(l[24]=[f(" 编辑 ")])),_:2,__:[24]},1032,["onClick"]),a(I,{size:"default",type:"info",onClick:z=>be(s.row)},{default:t(()=>l[25]||(l[25]=[f(" 查看证件 ")])),_:2,__:[25]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Me,$.value]])]),a(Be,{page:d.pageNum,"page-size":d.pageSize,total:ae.value,onHandleSizeChange:ke,onHandleCurrentChange:se},null,8,["page","page-size","total"])]),a(W,{modelValue:x.value,"onUpdate:modelValue":l[7]||(l[7]=s=>x.value=s),title:"代理商审核",width:"500px",onClose:Te},{footer:t(()=>[u("span",el,[a(I,{onClick:l[6]||(l[6]=s=>x.value=!1)},{default:t(()=>l[28]||(l[28]=[f("取消")])),_:1,__:[28]}),a(I,{type:"primary",onClick:Ve,loading:R.value},{default:t(()=>l[29]||(l[29]=[f("确定")])),_:1,__:[29]},8,["loading"])])]),default:t(()=>[a(Q,{model:p,rules:ie,ref_key:"auditFormRef",ref:oe,"label-width":"100px"},{default:t(()=>[a(r,{label:"法人姓名"},{default:t(()=>[u("span",null,b(p.legalPersonName),1)]),_:1}),a(r,{label:"联系方式"},{default:t(()=>[u("span",null,b(p.legalPersonTel),1)]),_:1}),a(r,{label:"代理类型"},{default:t(()=>[u("span",null,b(j(p.type)),1)]),_:1}),a(r,{label:"审核结果",prop:"status"},{default:t(()=>[a(Se,{modelValue:p.status,"onUpdate:modelValue":l[4]||(l[4]=s=>p.status=s)},{default:t(()=>[a(re,{value:1},{default:t(()=>l[26]||(l[26]=[f("审核通过")])),_:1,__:[26]}),a(re,{value:2},{default:t(()=>l[27]||(l[27]=[f("审核未通过")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"审核原因",prop:"examinedText"},{default:t(()=>[a(n,{modelValue:p.examinedText,"onUpdate:modelValue":l[5]||(l[5]=s=>p.examinedText=s),type:"textarea",rows:4,placeholder:"请输入审核原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(W,{modelValue:V.value,"onUpdate:modelValue":l[17]||(l[17]=s=>V.value=s),title:ge.value,width:"800px","close-on-click-modal":!1,onClose:Le},{footer:t(()=>[u("span",al,[a(I,{onClick:l[16]||(l[16]=s=>V.value=!1)},{default:t(()=>l[34]||(l[34]=[f("取消")])),_:1,__:[34]}),a(I,{type:"primary",onClick:xe,loading:M.value},{default:t(()=>l[35]||(l[35]=[f("确定")])),_:1,__:[35]},8,["loading"])])]),default:t(()=>[a(Q,{model:o,rules:ue,ref_key:"formRef",ref:D,"label-width":"120px"},{default:t(()=>[a(S,{gutter:20},{default:t(()=>[a(h,{span:12},{default:t(()=>[a(r,{label:"用户ID",prop:"userId"},{default:t(()=>[a(Ae,{modelValue:o.userId,"onUpdate:modelValue":l[8]||(l[8]=s=>o.userId=s),placeholder:"请输入用户ID",style:{width:"100%"},min:1},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:t(()=>[a(r,{label:"用户名",prop:"username"},{default:t(()=>[a(n,{modelValue:o.username,"onUpdate:modelValue":l[9]||(l[9]=s=>o.username=s),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,{gutter:20},{default:t(()=>[a(h,{span:12},{default:t(()=>[a(r,{label:"密码",prop:"password"},{default:t(()=>[a(n,{modelValue:o.password,"onUpdate:modelValue":l[10]||(l[10]=s=>o.password=s),type:"password",placeholder:"不填则使用默认密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:t(()=>[a(r,{label:"代理类型",prop:"type"},{default:t(()=>[a(J,{modelValue:o.type,"onUpdate:modelValue":l[11]||(l[11]=s=>o.type=s),placeholder:"请选择代理类型",style:{width:"100%"}},{default:t(()=>[a(c,{label:"省级代理",value:1}),a(c,{label:"市级代理",value:2}),a(c,{label:"区县代理",value:3})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(r,{label:"代理城市",prop:"cityId"},{default:t(()=>[a(Fe,{modelValue:o.cityId,"onUpdate:modelValue":l[12]||(l[12]=s=>o.cityId=s),options:E.value,props:de,placeholder:"请选择代理城市",style:{width:"100%"},clearable:"",onChange:ze},null,8,["modelValue","options"]),l[30]||(l[30]=u("div",{class:"city-tips"},[u("p",null,"选择说明："),u("ul",null,[u("li",null,"省级代理：只选择省份（如：北京）"),u("li",null,"市级代理：选择到市级（如：北京 > 北京市）"),u("li",null,"区县代理：选择到区县（如：北京 > 北京市 > 东城区）")])],-1))]),_:1,__:[30]}),a(S,{gutter:20},{default:t(()=>[a(h,{span:12},{default:t(()=>[a(r,{label:"法人姓名",prop:"legalPersonName"},{default:t(()=>[a(n,{modelValue:o.legalPersonName,"onUpdate:modelValue":l[13]||(l[13]=s=>o.legalPersonName=s),placeholder:"请输入法人姓名"},null,8,["modelValue"])]),_:1})]),_:1}),a(h,{span:12},{default:t(()=>[a(r,{label:"法人身份证",prop:"legalPersonIdCard"},{default:t(()=>[a(n,{modelValue:o.legalPersonIdCard,"onUpdate:modelValue":l[14]||(l[14]=s=>o.legalPersonIdCard=s),placeholder:"请输入法人身份证号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(r,{label:"法人手机号",prop:"legalPersonTel"},{default:t(()=>[a(n,{modelValue:o.legalPersonTel,"onUpdate:modelValue":l[15]||(l[15]=s=>o.legalPersonTel=s),placeholder:"请输入法人手机号",style:{width:"300px"}},null,8,["modelValue"])]),_:1}),a(r,{label:"身份证正面",prop:"legalPersonIdCardImg1"},{default:t(()=>[a(G,{class:"image-upload",action:"#","auto-upload":!1,"on-change":s=>H(s,"legalPersonIdCardImg1"),"on-remove":()=>U("legalPersonIdCardImg1"),"before-upload":O,"file-list":L.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:t(()=>l[31]||(l[31]=[u("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:t(()=>[a(X,null,{default:t(()=>[a(ee(Y))]),_:1})]),_:1},8,["on-change","on-remove","file-list"])]),_:1}),a(r,{label:"身份证反面",prop:"legalPersonIdCardImg2"},{default:t(()=>[a(G,{class:"image-upload",action:"#","auto-upload":!1,"on-change":s=>H(s,"legalPersonIdCardImg2"),"on-remove":()=>U("legalPersonIdCardImg2"),"before-upload":O,"file-list":T.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:t(()=>l[32]||(l[32]=[u("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:t(()=>[a(X,null,{default:t(()=>[a(ee(Y))]),_:1})]),_:1},8,["on-change","on-remove","file-list"])]),_:1}),a(r,{label:"营业执照",prop:"legalPersonLicense"},{default:t(()=>[a(G,{class:"image-upload",action:"#","auto-upload":!1,"on-change":s=>H(s,"legalPersonLicense"),"on-remove":()=>U("legalPersonLicense"),"before-upload":O,"file-list":k.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:t(()=>l[33]||(l[33]=[u("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:t(()=>[a(X,null,{default:t(()=>[a(ee(Y))]),_:1})]),_:1},8,["on-change","on-remove","file-list"])]),_:1}),_.value>0&&_.value<100?(w(),N("div",ll,[a($e,{percentage:_.value,"show-text":!0},null,8,["percentage"]),u("p",null,"上传中... "+b(_.value)+"%",1)])):F("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(W,{modelValue:q.value,"onUpdate:modelValue":l[18]||(l[18]=s=>q.value=s),title:"查看证件",width:"600px"},{default:t(()=>[u("div",tl,[v.legalPersonIdCardImg1?(w(),N("div",ol,[l[36]||(l[36]=u("h4",null,"身份证正面",-1)),a(Z,{src:v.legalPersonIdCardImg1,width:"200",height:"120"},null,8,["src"])])):F("",!0),v.legalPersonIdCardImg2?(w(),N("div",sl,[l[37]||(l[37]=u("h4",null,"身份证反面",-1)),a(Z,{src:v.legalPersonIdCardImg2,width:"200",height:"120"},null,8,["src"])])):F("",!0),v.legalPersonLicense?(w(),N("div",nl,[l[38]||(l[38]=u("h4",null,"营业执照",-1)),a(Z,{src:v.legalPersonLicense,width:"200",height:"120"},null,8,["src"])])):F("",!0)])]),_:1},8,["modelValue"])])}}},yl=Ee(rl,[["__scopeId","data-v-bacb0736"]]);export{yl as default};
