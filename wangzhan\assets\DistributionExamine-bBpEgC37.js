import{r as y,X as V,h as ie,y as $,Q as t,A as u,J as ne,I as a,al as r,ar as re,H as N,z as x,M as s,O as i,V as de,K as T,P as ue,a6 as pe}from"./vendor-DmFBDimT.js";import{T as me,L as w}from"./LbButton-BtU4V_Gr.js";import{_ as _e}from"./index-C9Xz1oqp.js";import{E as k}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const fe={class:"lb-goods-list"},ce={class:"page-main"},ge=["src"],be={style:{color:"#e6a23c","font-weight":"600"}},ve={class:"table-operate"},ye={class:"pagination-section"},we={key:0,style:{"margin-top":"20px"}},xe={class:"id-card-images"},ke=["src"],Ce={__name:"DistributionExamine",setup(he){const U=y(!1),F=y([]),P=y(),j=y([]),z=y(!1),C=y(!1),L=y(),M=y(!1),h=V({all:0,nopass:0,ing:0,pass:0}),n=V({name:"",status:0,start_time:"",end_time:""}),f=V({page:1,pageSize:10,total:0}),p=V({user_id:"",nickName:"",user_name:"",mobile:"",id_code:"",create_time:"",commission_total:0,invite_count:0,id_card_images:[]}),d=V({id:"",user_name:"",mobile:"",id_code:"",create_time:"",status:2,sh_text:""}),Y={status:[{required:!0,message:"请选择审核结果",trigger:"change"}],sh_text:[{required:!0,message:"请输入审核说明",trigger:"blur"}]},g=async(o=1)=>{U.value=!0,f.page=o;try{const e=new URLSearchParams({page:f.page,pageSize:f.pageSize,name:n.name,status:n.status,start_time:n.start_time,end_time:n.end_time}),b=await(await fetch(`/api/distribution/examine/list?${e}`)).json();b.code===200?(F.value=b.data.list||[],f.total=b.data.total||0,Object.assign(h,b.data.count||{})):k.error(b.message||"获取数据失败")}catch(e){console.error("获取分销审核列表失败:",e),k.error("获取数据失败")}finally{U.value=!1}},D=o=>{n.status=o,g(1)},q=()=>{n.name="",n.status=0,n.start_time="",n.end_time="",j.value=[],g(1)},A=o=>({1:"warning",2:"success",3:"danger",4:"info"})[o]||"info",J=o=>({1:"申请中",2:"已授权",3:"已拒绝",4:"未授权"})[o]||"未知",O=(o,e)=>{if(!o)return"";const m=new Date(o);return e===1?m.toLocaleDateString():m.toLocaleTimeString()},H=async o=>{try{const m=await(await fetch(`/api/distribution/examine/detail/${o.id}`)).json();m.code===200?(Object.assign(p,m.data),z.value=!0):k.error(m.message||"获取详情失败")}catch(e){console.error("获取分销商详情失败:",e),k.error("获取详情失败")}},K=o=>{Object.assign(d,{id:o.id,user_name:o.user_name,mobile:o.mobile,id_code:o.id_code,create_time:o.create_time,status:2,sh_text:""}),C.value=!0},Q=o=>{Object.assign(d,{id:o.id,user_name:o.user_name,mobile:o.mobile,id_code:o.id_code,create_time:o.create_time,status:4,sh_text:""}),C.value=!0},X=async()=>{try{await L.value.validate(),M.value=!0;const e=await(await fetch(`/api/distribution/examine/audit/${d.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:d.status,sh_text:d.sh_text})})).json();e.code===200?(k.success("审核成功"),C.value=!1,g()):k.error(e.message||"审核失败")}catch(o){console.error("审核失败:",o),k.error("审核失败")}finally{M.value=!1}},G=o=>{f.pageSize=o,g(1)},W=o=>{g(o)};return ie(()=>{g()}),(o,e)=>{const m=r("el-button"),b=r("el-row"),R=r("el-input"),c=r("el-form-item"),Z=r("el-date-picker"),B=r("el-form"),_=r("el-table-column"),ee=r("el-tag"),te=r("el-table"),ae=r("el-pagination"),v=r("el-descriptions-item"),le=r("el-descriptions"),E=r("el-dialog"),I=r("el-radio"),oe=r("el-radio-group"),se=re("loading");return x(),$("div",fe,[t(me),u("div",ce,[t(b,{class:"page-top-operate"},{default:a(()=>[t(m,{onClick:e[0]||(e[0]=l=>D(0)),type:n.status===0?"primary":"",size:"default"},{default:a(()=>[s(" 全部（"+i(h.all||0)+"） ",1)]),_:1},8,["type"]),t(m,{onClick:e[1]||(e[1]=l=>D(4)),type:n.status===4?"primary":"",size:"default"},{default:a(()=>[s(" 未授权（"+i(h.nopass||0)+"） ",1)]),_:1},8,["type"]),t(m,{onClick:e[2]||(e[2]=l=>D(1)),type:n.status===1?"primary":"",size:"default"},{default:a(()=>[s(" 申请中（"+i(h.ing||0)+"） ",1)]),_:1},8,["type"]),t(m,{onClick:e[3]||(e[3]=l=>D(2)),type:n.status===2?"primary":"",size:"default"},{default:a(()=>[s(" 已授权（"+i(h.pass||0)+"） ",1)]),_:1},8,["type"])]),_:1}),t(b,{class:"page-search-form"},{default:a(()=>[t(B,{onSubmit:e[8]||(e[8]=de(()=>{},["prevent"])),inline:!0,model:n,ref_key:"searchFormRef",ref:P},{default:a(()=>[t(c,{label:"输入查询",prop:"name"},{default:a(()=>[t(R,{modelValue:n.name,"onUpdate:modelValue":e[4]||(e[4]=l=>n.name=l),placeholder:"请输入姓名/手机号",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(c,{label:"申请时间",prop:"start_time"},{default:a(()=>[t(Z,{onChange:e[5]||(e[5]=l=>g(1)),modelValue:j.value,"onUpdate:modelValue":e[6]||(e[6]=l=>j.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(c,null,{default:a(()=>[t(w,{size:"default",type:"primary",style:{"margin-right":"5px"},onClick:e[7]||(e[7]=l=>g(1))},{default:a(()=>e[17]||(e[17]=[s(" 搜索 ")])),_:1,__:[17]}),t(w,{size:"default",style:{"margin-right":"5px"},onClick:q},{default:a(()=>e[18]||(e[18]=[s(" 重置 ")])),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),_:1}),ne((x(),N(te,{data:F.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:a(()=>[t(_,{prop:"id",label:"ID",width:"80",fixed:""}),t(_,{prop:"user_id",label:"用户ID",width:"100"}),t(_,{prop:"avatarUrl",label:"头像",width:"80"},{default:a(l=>[u("img",{src:l.row.avatarUrl,alt:"头像",style:{width:"40px",height:"40px","border-radius":"50%"}},null,8,ge)]),_:1}),t(_,{prop:"nickName",label:"昵称","min-width":"120"}),t(_,{prop:"user_name",label:"姓名","min-width":"120"}),t(_,{prop:"mobile",label:"手机号",width:"130"}),t(_,{prop:"id_code",label:"身份证号",width:"180"}),t(_,{prop:"create_time",label:"申请时间",width:"170"},{default:a(l=>[u("div",null,i(O(l.row.create_time,1)),1),u("div",null,i(O(l.row.create_time,2)),1)]),_:1}),t(_,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(ee,{type:A(l.row.status)},{default:a(()=>[s(i(J(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"commission_total",label:"累计佣金",width:"120"},{default:a(l=>[u("span",be,"¥"+i(l.row.commission_total||0),1)]),_:1}),t(_,{prop:"invite_count",label:"邀请人数",width:"100"}),t(_,{label:"操作",width:"200",fixed:"right"},{default:a(l=>[u("div",ve,[t(w,{size:"mini",type:"primary",onClick:S=>H(l.row)},{default:a(()=>e[19]||(e[19]=[s(" 查看详情 ")])),_:2,__:[19]},1032,["onClick"]),l.row.status===1?(x(),N(w,{key:0,size:"mini",type:"success",onClick:S=>K(l.row)},{default:a(()=>e[20]||(e[20]=[s(" 通过审核 ")])),_:2,__:[20]},1032,["onClick"])):T("",!0),l.row.status===1?(x(),N(w,{key:1,size:"mini",type:"danger",onClick:S=>Q(l.row)},{default:a(()=>e[21]||(e[21]=[s(" 拒绝审核 ")])),_:2,__:[21]},1032,["onClick"])):T("",!0)])]),_:1})]),_:1},8,["data"])),[[se,U.value]]),u("div",ye,[t(ae,{"current-page":f.page,"onUpdate:currentPage":e[9]||(e[9]=l=>f.page=l),"page-size":f.pageSize,"onUpdate:pageSize":e[10]||(e[10]=l=>f.pageSize=l),"page-sizes":[10,20,50,100],total:f.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:W},null,8,["current-page","page-size","total"])])]),t(E,{modelValue:z.value,"onUpdate:modelValue":e[12]||(e[12]=l=>z.value=l),title:"分销商详情",width:"60%"},{footer:a(()=>[t(w,{onClick:e[11]||(e[11]=l=>z.value=!1)},{default:a(()=>e[23]||(e[23]=[s("关闭")])),_:1,__:[23]})]),default:a(()=>[t(le,{column:2,border:""},{default:a(()=>[t(v,{label:"用户ID"},{default:a(()=>[s(i(p.user_id),1)]),_:1}),t(v,{label:"昵称"},{default:a(()=>[s(i(p.nickName),1)]),_:1}),t(v,{label:"姓名"},{default:a(()=>[s(i(p.user_name),1)]),_:1}),t(v,{label:"手机号"},{default:a(()=>[s(i(p.mobile),1)]),_:1}),t(v,{label:"身份证号"},{default:a(()=>[s(i(p.id_code),1)]),_:1}),t(v,{label:"申请时间"},{default:a(()=>[s(i(p.create_time),1)]),_:1}),t(v,{label:"累计佣金"},{default:a(()=>[s("¥"+i(p.commission_total||0),1)]),_:1}),t(v,{label:"邀请人数"},{default:a(()=>[s(i(p.invite_count||0),1)]),_:1})]),_:1}),p.id_card_images&&p.id_card_images.length>0?(x(),$("div",we,[e[22]||(e[22]=u("h4",null,"身份证照片",-1)),u("div",xe,[(x(!0),$(ue,null,pe(p.id_card_images,(l,S)=>(x(),$("img",{key:S,src:l,alt:"身份证照片",style:{width:"150px",height:"100px","margin-right":"8px","border-radius":"4px"}},null,8,ke))),128))])])):T("",!0)]),_:1},8,["modelValue"]),t(E,{modelValue:C.value,"onUpdate:modelValue":e[16]||(e[16]=l=>C.value=l),title:"分销商审核",width:"50%","close-on-click-modal":!1},{footer:a(()=>[t(w,{onClick:e[15]||(e[15]=l=>C.value=!1)},{default:a(()=>e[26]||(e[26]=[s("取消")])),_:1,__:[26]}),t(w,{type:"primary",onClick:X,loading:M.value},{default:a(()=>e[27]||(e[27]=[s("确定审核")])),_:1,__:[27]},8,["loading"])]),default:a(()=>[t(B,{model:d,rules:Y,ref_key:"auditFormRef",ref:L,"label-width":"100px"},{default:a(()=>[t(c,{label:"申请人"},{default:a(()=>[u("span",null,i(d.user_name),1)]),_:1}),t(c,{label:"手机号"},{default:a(()=>[u("span",null,i(d.mobile),1)]),_:1}),t(c,{label:"身份证号"},{default:a(()=>[u("span",null,i(d.id_code),1)]),_:1}),t(c,{label:"申请时间"},{default:a(()=>[u("span",null,i(d.create_time),1)]),_:1}),t(c,{label:"审核结果",prop:"status"},{default:a(()=>[t(oe,{modelValue:d.status,"onUpdate:modelValue":e[13]||(e[13]=l=>d.status=l)},{default:a(()=>[t(I,{value:2},{default:a(()=>e[24]||(e[24]=[s("通过审核")])),_:1,__:[24]}),t(I,{value:4},{default:a(()=>e[25]||(e[25]=[s("拒绝申请")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"审核说明",prop:"sh_text"},{default:a(()=>[t(R,{modelValue:d.sh_text,"onUpdate:modelValue":e[14]||(e[14]=l=>d.sh_text=l),type:"textarea",rows:4,placeholder:"请输入审核说明",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ue=_e(Ce,[["__scopeId","data-v-efa6633e"]]);export{Ue as default};
