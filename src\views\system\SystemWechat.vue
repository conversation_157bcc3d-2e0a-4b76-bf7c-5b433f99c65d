<!--
  小程序设置页面
 系统设置页面布局重构
-->

<template>
  <div class="lb-system-wechat">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>小程序设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          :rules="configRules" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="小程序名称" prop="app_name">
            <el-input
              v-model="configForm.app_name"
              placeholder="请输入小程序名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="AppID" prop="app_id">
            <el-input
              v-model="configForm.app_id"
              placeholder="请输入小程序AppID"
              maxlength="100"
            />
          </el-form-item>
          
          <el-form-item label="AppSecret" prop="app_secret">
            <el-input
              v-model="configForm.app_secret"
              placeholder="请输入小程序AppSecret"
              type="password"
              show-password
              maxlength="100"
            />
          </el-form-item>
          
          <el-form-item label="商户号" prop="mch_id">
            <el-input
              v-model="configForm.mch_id"
              placeholder="请输入微信支付商户号"
              maxlength="50"
            />
          </el-form-item>
          
          <el-form-item label="商户密钥" prop="mch_key">
            <el-input
              v-model="configForm.mch_key"
              placeholder="请输入微信支付商户密钥"
              type="password"
              show-password
              maxlength="100"
            />
          </el-form-item>
          
          <el-form-item label="小程序版本" prop="version">
            <el-input
              v-model="configForm.version"
              placeholder="请输入小程序版本号"
              maxlength="20"
            />
          </el-form-item>
          
          <el-form-item label="启用状态" prop="status">
            <el-radio-group v-model="configForm.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="调试模式" prop="debug">
            <el-radio-group v-model="configForm.debug">
              <el-radio :value="1">开启</el-radio>
              <el-radio :value="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="备注说明" prop="remark">
            <el-input
              v-model="configForm.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注说明"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">
              保存配置
            </LbButton>
            <LbButton @click="resetConfig" style="margin-left: 10px;">
              重置
            </LbButton>
            <LbButton type="success" @click="testConfig" style="margin-left: 10px;">
              测试连接
            </LbButton>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 配置说明 -->
      <el-card class="help-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>配置说明</span>
          </div>
        </template>
        
        <div class="help-content">
          <h4>小程序配置步骤：</h4>
          <ol>
            <li>登录微信公众平台，进入小程序管理后台</li>
            <li>在"开发"-"开发设置"中获取AppID和AppSecret</li>
            <li>在"微信支付"中获取商户号和商户密钥</li>
            <li>配置服务器域名，将本站域名添加到request合法域名中</li>
            <li>上传小程序代码并提交审核</li>
          </ol>
          
          <h4>注意事项：</h4>
          <ul>
            <li>AppSecret请妥善保管，不要泄露给他人</li>
            <li>商户密钥用于微信支付，请确保安全</li>
            <li>调试模式仅在开发环境使用，生产环境请关闭</li>
            <li>修改配置后需要重新发布小程序</li>
          </ul>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const saveLoading = ref(false)
const configFormRef = ref()

// 配置表单
const configForm = reactive({
  app_name: '',
  app_id: '',
  app_secret: '',
  mch_id: '',
  mch_key: '',
  version: '',
  status: 1,
  debug: 0,
  remark: ''
})

// 表单验证规则
const configRules = {
  app_name: [
    { required: true, message: '请输入小程序名称', trigger: 'blur' }
  ],
  app_id: [
    { required: true, message: '请输入小程序AppID', trigger: 'blur' }
  ],
  app_secret: [
    { required: true, message: '请输入小程序AppSecret', trigger: 'blur' }
  ]
}

// 方法
const getConfig = async () => {
  try {
    const response = await fetch('/api/system/wechat/config')
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    } else {
      ElMessage.error(result.message || '获取配置失败')
    }
  } catch (error) {
    console.error('获取小程序配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    
    saveLoading.value = true
    
    const response = await fetch('/api/system/wechat/config', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const resetConfig = () => {
  Object.assign(configForm, {
    app_name: '',
    app_id: '',
    app_secret: '',
    mch_id: '',
    mch_key: '',
    version: '',
    status: 1,
    debug: 0,
    remark: ''
  })
  if (configFormRef.value) {
    configFormRef.value.clearValidate()
  }
}

const testConfig = async () => {
  try {
    await configFormRef.value.validate()
    
    const response = await fetch('/api/system/wechat/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(result.message || '连接测试失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败')
  }
}

// 生命周期
onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-wechat {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}

.help-content {
  color: #606266;
  line-height: 1.6;
}

.help-content h4 {
  color: #303133;
  margin: 20px 0 10px 0;
}

.help-content ol,
.help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 8px 0;
}

@media (max-width: 768px) {
  .lb-system-wechat {
    padding: 10px;
  }
  
  .config-form {
    max-width: 100%;
  }
}
</style>
