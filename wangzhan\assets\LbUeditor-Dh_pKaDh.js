import{c as se,a as Jr,H as rr,I as Me,J as re,E as ge}from"./element-fdzwdDuf.js";import{_ as _i,a as Ii}from"./index-C9Xz1oqp.js";import{r as Ri,j as vr,h as Bi,n as Er,W as Mi,y as Di,z as Ui,A as Pi}from"./vendor-DmFBDimT.js";var M=(i=>(i[i.TYPE=3]="TYPE",i[i.LEVEL=12]="LEVEL",i[i.ATTRIBUTE=13]="ATTRIBUTE",i[i.BLOT=14]="BLOT",i[i.INLINE=7]="INLINE",i[i.BLOCK=11]="BLOCK",i[i.BLOCK_BLOT=10]="BLOCK_BLOT",i[i.INLINE_BLOT=6]="INLINE_BLOT",i[i.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",i[i.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",i[i.ANY=15]="ANY",i))(M||{});class zt{constructor(t,e,s={}){this.attrName=t,this.keyName=e;const n=M.TYPE&M.ATTRIBUTE;this.scope=s.scope!=null?s.scope&M.LEVEL|n:M.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class De extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}}const ti=class Xn{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let s=null;try{s=t.parentNode}catch{return null}return this.find(s,e)}return null}create(t,e,s){const n=this.query(e);if(n==null)throw new De(`Unable to create ${e} blot`);const l=n,o=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:l.create(s),c=new l(t,o,s);return Xn.blots.set(c.domNode,c),c}find(t,e=!1){return Xn.find(t,e)}query(t,e=M.ANY){let s;return typeof t=="string"?s=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?s=this.types.text:typeof t=="number"?t&M.LEVEL&M.BLOCK?s=this.types.block:t&M.LEVEL&M.INLINE&&(s=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(n=>(s=this.classes[n],!!s)),s=s||this.tags[t.tagName]),s==null?null:"scope"in s&&e&M.LEVEL&s.scope&&e&M.TYPE&s.scope?s:null}register(...t){return t.map(e=>{const s="blotName"in e,n="attrName"in e;if(!s&&!n)throw new De("Invalid definition");if(s&&e.blotName==="abstract")throw new De("Cannot register abstract class");const l=s?e.blotName:n?e.attrName:void 0;return this.types[l]=e,n?typeof e.keyName=="string"&&(this.attributes[e.keyName]=e):s&&(e.className&&(this.classes[e.className]=e),e.tagName&&(Array.isArray(e.tagName)?e.tagName=e.tagName.map(o=>o.toUpperCase()):e.tagName=e.tagName.toUpperCase(),(Array.isArray(e.tagName)?e.tagName:[e.tagName]).forEach(o=>{(this.tags[o]==null||e.className==null)&&(this.tags[o]=e)}))),e})}};ti.blots=new WeakMap;let Pe=ti;function Nr(i,t){return(i.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}class ji extends zt{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){Nr(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){const e=(Nr(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}const Ut=ji;function Pn(i){const t=i.split("-"),e=t.slice(1).map(s=>s[0].toUpperCase()+s.slice(1)).join("");return t[0]+e}class Hi extends zt{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[Pn(this.keyName)]=e,!0):!1}remove(t){t.style[Pn(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[Pn(this.keyName)];return this.canAdd(t,e)?e:""}}const le=Hi;class Fi{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=Pe.find(this.domNode);if(t==null)return;const e=zt.keys(this.domNode),s=Ut.keys(this.domNode),n=le.keys(this.domNode);e.concat(s).concat(n).forEach(l=>{const o=t.scroll.query(l,M.ATTRIBUTE);o instanceof zt&&(this.attributes[o.attrName]=o)})}copy(t){Object.keys(this.attributes).forEach(e=>{const s=this.attributes[e].value(this.domNode);t.format(e,s)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}const Ks=Fi,ei=class{constructor(t,e){this.scroll=t,this.domNode=e,Pe.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new De("Blot definition missing tagName");let e,s;return Array.isArray(this.tagName)?(typeof t=="string"?(s=t.toUpperCase(),parseInt(s,10).toString()===s&&(s=parseInt(s,10))):typeof t=="number"&&(s=t),typeof s=="number"?e=document.createElement(this.tagName[s-1]):s&&this.tagName.indexOf(s)>-1?e=document.createElement(s):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),Pe.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,s,n){const l=this.isolate(t,e);if(this.scroll.query(s,M.BLOT)!=null&&n)l.wrap(s,n);else if(this.scroll.query(s,M.ATTRIBUTE)!=null){const o=this.scroll.create(this.statics.scope);l.wrap(o),o.format(s,n)}}insertAt(t,e,s){const n=s==null?this.scroll.create("text",e):this.scroll.create(e,s),l=this.split(t);this.parent.insertBefore(n,l||void 0)}isolate(t,e){const s=this.split(t);if(s==null)throw new Error("Attempt to isolate at end");return s.split(e),s}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(s,this.next||void 0),this.remove()),s}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(s,this.next||void 0),typeof s.appendChild!="function")throw new De(`Cannot wrap ${t}`);return s.appendChild(this),s}};ei.blotName="abstract";let si=ei;const ni=class extends si{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let s=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(s+=1),[this.parent.domNode,s]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};ni.scope=M.INLINE_BLOT;let $i=ni;const bt=$i;class zi{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let s=e();for(;s&&t>0;)t-=1,s=e();return s}contains(t){const e=this.iterator();let s=e();for(;s;){if(s===t)return!0;s=e()}return!1}indexOf(t){const e=this.iterator();let s=e(),n=0;for(;s;){if(s===t)return n;n+=1,s=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,s=this.head;for(;s!=null;){if(s===t)return e;e+=s.length(),s=s.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){const s=this.iterator();let n=s();for(;n;){const l=n.length();if(t<l||e&&t===l&&(n.next==null||n.next.length()!==0))return[n,t];t-=l,n=s()}return[null,0]}forEach(t){const e=this.iterator();let s=e();for(;s;)t(s),s=e()}forEachAt(t,e,s){if(e<=0)return;const[n,l]=this.find(t);let o=t-l;const c=this.iterator(n);let u=c();for(;u&&o<t+e;){const d=u.length();t>o?s(u,t-o,Math.min(e,o+d-t)):s(u,0,Math.min(d,t+e-o)),o+=d,u=c()}}map(t){return this.reduce((e,s)=>(e.push(t(s)),e),[])}reduce(t,e){const s=this.iterator();let n=s();for(;n;)e=t(e,n),n=s();return e}}function Ar(i,t){const e=t.find(i);if(e)return e;try{return t.create(i)}catch{const s=t.create(M.INLINE);return Array.from(i.childNodes).forEach(n=>{s.domNode.appendChild(n)}),i.parentNode&&i.parentNode.replaceChild(s.domNode,i),s.attach(),s}}const ri=class ee extends si{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,ee.uiClass&&this.uiNode.classList.add(ee.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new zi,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const e=Ar(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(e){if(e instanceof De)return;throw e}})}deleteAt(t,e){if(t===0&&e===this.length())return this.remove();this.children.forEachAt(t,e,(s,n,l)=>{s.deleteAt(n,l)})}descendant(t,e=0){const[s,n]=this.children.find(e);return t.blotName==null&&t(s)||t.blotName!=null&&s instanceof t?[s,n]:s instanceof ee?s.descendant(t,n):[null,-1]}descendants(t,e=0,s=Number.MAX_VALUE){let n=[],l=s;return this.children.forEachAt(e,s,(o,c,u)=>{(t.blotName==null&&t(o)||t.blotName!=null&&o instanceof t)&&n.push(o),o instanceof ee&&(n=n.concat(o.descendants(t,c,l))),l-=u}),n}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(e=>{t||this.statics.allowedChildren.some(s=>e instanceof s)||(e.statics.scope===M.BLOCK_BLOT?(e.next!=null&&this.splitAfter(e),e.prev!=null&&this.splitAfter(e.prev),e.parent.unwrap(),t=!0):e instanceof ee?e.unwrap():e.remove())})}formatAt(t,e,s,n){this.children.forEachAt(t,e,(l,o,c)=>{l.formatAt(o,c,s,n)})}insertAt(t,e,s){const[n,l]=this.children.find(t);if(n)n.insertAt(l,e,s);else{const o=s==null?this.scroll.create("text",e):this.scroll.create(e,s);this.appendChild(o)}}insertBefore(t,e){t.parent!=null&&t.parent.children.remove(t);let s=null;this.children.insertBefore(t,e||null),t.parent=this,e!=null&&(s=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==s)&&this.domNode.insertBefore(t.domNode,s),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(s=>{t.insertBefore(s,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){const e=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(e)}else this.remove()}path(t,e=!1){const[s,n]=this.children.find(t,e),l=[[this,t]];return s instanceof ee?l.concat(s.path(n,e)):(s!=null&&l.push([s,n]),l)}removeChild(t){this.children.remove(t)}replaceWith(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;return s instanceof ee&&this.moveChildren(s),super.replaceWith(s)}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const s=this.clone();return this.parent&&this.parent.insertBefore(s,this.next||void 0),this.children.forEachAt(t,this.length(),(n,l,o)=>{const c=n.split(l,e);c!=null&&s.appendChild(c)}),s}splitAfter(t){const e=this.clone();for(;t.next!=null;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const s=[],n=[];t.forEach(l=>{l.target===this.domNode&&l.type==="childList"&&(s.push(...l.addedNodes),n.push(...l.removedNodes))}),n.forEach(l=>{if(l.parentNode!=null&&l.tagName!=="IFRAME"&&document.body.compareDocumentPosition(l)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const o=this.scroll.find(l);o!=null&&(o.domNode.parentNode==null||o.domNode.parentNode===this.domNode)&&o.detach()}),s.filter(l=>l.parentNode===this.domNode&&l!==this.uiNode).sort((l,o)=>l===o?0:l.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(l=>{let o=null;l.nextSibling!=null&&(o=this.scroll.find(l.nextSibling));const c=Ar(l,this.scroll);(c.next!==o||c.next==null)&&(c.parent!=null&&c.parent.removeChild(this),this.insertBefore(c,o||void 0))}),this.enforceAllowedChildren()}};ri.uiClass="";let Vi=ri;const Mt=Vi;function Ki(i,t){if(Object.keys(i).length!==Object.keys(t).length)return!1;for(const e in i)if(i[e]!==t[e])return!1;return!0}const Oe=class _e extends Mt{static create(t){return super.create(t)}static formats(t,e){const s=e.query(_e.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Ks(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(s=>{s instanceof _e||(s=s.wrap(_e.blotName,!0)),this.attributes.copy(s)}),this.unwrap();else{const s=this.scroll.query(t,M.INLINE);if(s==null)return;s instanceof zt?this.attributes.attribute(s,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,n){this.formats()[s]!=null||this.scroll.query(s,M.ATTRIBUTE)?this.isolate(t,e).format(s,n):super.formatAt(t,e,s,n)}optimize(t){super.optimize(t);const e=this.formats();if(Object.keys(e).length===0)return this.unwrap();const s=this.next;s instanceof _e&&s.prev===this&&Ki(e,s.formats())&&(s.moveChildren(this),s.remove())}replaceWith(t,e){const s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}wrap(t,e){const s=super.wrap(t,e);return s instanceof _e&&this.attributes.move(s),s}};Oe.allowedChildren=[Oe,bt],Oe.blotName="inline",Oe.scope=M.INLINE_BLOT,Oe.tagName="SPAN";let Gi=Oe;const ir=Gi,Ie=class Yn extends Mt{static create(t){return super.create(t)}static formats(t,e){const s=e.query(Yn.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Ks(this.domNode)}format(t,e){const s=this.scroll.query(t,M.BLOCK);s!=null&&(s instanceof zt?this.attributes.attribute(s,e):t===this.statics.blotName&&!e?this.replaceWith(Yn.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,n){this.scroll.query(s,M.BLOCK)!=null?this.format(s,n):super.formatAt(t,e,s,n)}insertAt(t,e,s){if(s==null||this.scroll.query(e,M.INLINE)!=null)super.insertAt(t,e,s);else{const n=this.split(t);if(n!=null){const l=this.scroll.create(e,s);n.parent.insertBefore(l,n)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){const s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}};Ie.blotName="block",Ie.scope=M.BLOCK_BLOT,Ie.tagName="P",Ie.allowedChildren=[ir,Ie,bt];let Wi=Ie;const as=Wi,Qn=class extends Mt{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,s,n){super.formatAt(t,e,s,n),this.enforceAllowedChildren()}insertAt(t,e,s){super.insertAt(t,e,s),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Qn.blotName="container",Qn.scope=M.BLOCK_BLOT;let Zi=Qn;const Gs=Zi;class Xi extends bt{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,s,n){t===0&&e===this.length()?this.format(s,n):super.formatAt(t,e,s,n)}formats(){return this.statics.formats(this.domNode,this.scroll)}}const wt=Xi,Yi={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Qi=100,Re=class extends Mt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(s=>{this.update(s)}),this.observer.observe(this.domNode,Yi),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const s=this.registry.find(t,e);return s?s.scroll===this?s:e?this.find(s.scroll.domNode.parentNode,!0):null:null}query(t,e=M.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(s=>{s.remove()}):super.deleteAt(t,e)}formatAt(t,e,s,n){this.update(),super.formatAt(t,e,s,n)}insertAt(t,e,s){this.update(),super.insertAt(t,e,s)}optimize(t=[],e={}){super.optimize(e);const s=e.mutationsMap||new WeakMap;let n=Array.from(this.observer.takeRecords());for(;n.length>0;)t.push(n.pop());const l=(u,d=!0)=>{u==null||u===this||u.domNode.parentNode!=null&&(s.has(u.domNode)||s.set(u.domNode,[]),d&&l(u.parent))},o=u=>{s.has(u.domNode)&&(u instanceof Mt&&u.children.forEach(o),s.delete(u.domNode),u.optimize(e))};let c=t;for(let u=0;c.length>0;u+=1){if(u>=Qi)throw new Error("[Parchment] Maximum optimize iterations reached");for(c.forEach(d=>{const b=this.find(d.target,!0);b!=null&&(b.domNode===d.target&&(d.type==="childList"?(l(this.find(d.previousSibling,!1)),Array.from(d.addedNodes).forEach(E=>{const g=this.find(E,!1);l(g,!1),g instanceof Mt&&g.children.forEach(m=>{l(m,!1)})})):d.type==="attributes"&&l(b.prev)),l(b))}),this.children.forEach(o),c=Array.from(this.observer.takeRecords()),n=c.slice();n.length>0;)t.push(n.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const s=new WeakMap;t.map(n=>{const l=this.find(n.target,!0);return l==null?null:s.has(l.domNode)?(s.get(l.domNode).push(n),null):(s.set(l.domNode,[n]),l)}).forEach(n=>{n!=null&&n!==this&&s.has(n.domNode)&&n.update(s.get(n.domNode)||[],e)}),e.mutationsMap=s,s.has(this.domNode)&&super.update(s.get(this.domNode),e),this.optimize(t,e)}};Re.blotName="scroll",Re.defaultChild=as,Re.allowedChildren=[as,Gs],Re.scope=M.BLOCK_BLOT,Re.tagName="DIV";let Ji=Re;const lr=Ji,Jn=class ii extends bt{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,s){s==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,s)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof ii&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const s=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(s,this.next||void 0),this.text=this.statics.value(this.domNode),s}update(t,e){t.some(s=>s.type==="characterData"&&s.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};Jn.blotName="text",Jn.scope=M.INLINE_BLOT;let tl=Jn;const $s=tl,el=Object.freeze(Object.defineProperty({__proto__:null,Attributor:zt,AttributorStore:Ks,BlockBlot:as,ClassAttributor:Ut,ContainerBlot:Gs,EmbedBlot:wt,InlineBlot:ir,LeafBlot:bt,ParentBlot:Mt,Registry:Pe,Scope:M,ScrollBlot:lr,StyleAttributor:le,TextBlot:$s},Symbol.toStringTag,{value:"Module"}));var Rs={exports:{}},jn,xr;function sl(){if(xr)return jn;xr=1;var i=-1,t=1,e=0;function s(f,L,p,C,q){if(f===L)return f?[[e,f]]:[];if(p!=null){var x=Ct(f,L,p);if(x)return x}var S=c(f,L),R=f.substring(0,S);f=f.substring(S),L=L.substring(S),S=d(f,L);var D=f.substring(f.length-S);f=f.substring(0,f.length-S),L=L.substring(0,L.length-S);var _=n(f,L);return R&&_.unshift([e,R]),D&&_.push([e,D]),O(_,q),C&&E(_),_}function n(f,L){var p;if(!f)return[[t,L]];if(!L)return[[i,f]];var C=f.length>L.length?f:L,q=f.length>L.length?L:f,x=C.indexOf(q);if(x!==-1)return p=[[t,C.substring(0,x)],[e,q],[t,C.substring(x+q.length)]],f.length>L.length&&(p[0][0]=p[2][0]=i),p;if(q.length===1)return[[i,f],[t,L]];var S=b(f,L);if(S){var R=S[0],D=S[1],_=S[2],z=S[3],H=S[4],K=s(R,_),G=s(D,z);return K.concat([[e,H]],G)}return l(f,L)}function l(f,L){for(var p=f.length,C=L.length,q=Math.ceil((p+C)/2),x=q,S=2*q,R=new Array(S),D=new Array(S),_=0;_<S;_++)R[_]=-1,D[_]=-1;R[x+1]=0,D[x+1]=0;for(var z=p-C,H=z%2!==0,K=0,G=0,P=0,nt=0,rt=0;rt<q;rt++){for(var V=-rt+K;V<=rt-G;V+=2){var Q=x+V,X;V===-rt||V!==rt&&R[Q-1]<R[Q+1]?X=R[Q+1]:X=R[Q-1]+1;for(var J=X-V;X<p&&J<C&&f.charAt(X)===L.charAt(J);)X++,J++;if(R[Q]=X,X>p)G+=2;else if(J>C)K+=2;else if(H){var et=x+z-V;if(et>=0&&et<S&&D[et]!==-1){var st=p-D[et];if(X>=st)return o(f,L,X,J)}}}for(var lt=-rt+P;lt<=rt-nt;lt+=2){var et=x+lt,st;lt===-rt||lt!==rt&&D[et-1]<D[et+1]?st=D[et+1]:st=D[et-1]+1;for(var dt=st-lt;st<p&&dt<C&&f.charAt(p-st-1)===L.charAt(C-dt-1);)st++,dt++;if(D[et]=st,st>p)nt+=2;else if(dt>C)P+=2;else if(!H){var Q=x+z-lt;if(Q>=0&&Q<S&&R[Q]!==-1){var X=R[Q],J=x+X-Q;if(st=p-st,X>=st)return o(f,L,X,J)}}}}return[[i,f],[t,L]]}function o(f,L,p,C){var q=f.substring(0,p),x=L.substring(0,C),S=f.substring(p),R=L.substring(C),D=s(q,x),_=s(S,R);return D.concat(_)}function c(f,L){if(!f||!L||f.charAt(0)!==L.charAt(0))return 0;for(var p=0,C=Math.min(f.length,L.length),q=C,x=0;p<q;)f.substring(x,q)==L.substring(x,q)?(p=q,x=p):C=q,q=Math.floor((C-p)/2+p);return k(f.charCodeAt(q-1))&&q--,q}function u(f,L){var p=f.length,C=L.length;if(p==0||C==0)return 0;p>C?f=f.substring(p-C):p<C&&(L=L.substring(0,p));var q=Math.min(p,C);if(f==L)return q;for(var x=0,S=1;;){var R=f.substring(q-S),D=L.indexOf(R);if(D==-1)return x;S+=D,(D==0||f.substring(q-S)==L.substring(0,S))&&(x=S,S++)}}function d(f,L){if(!f||!L||f.slice(-1)!==L.slice(-1))return 0;for(var p=0,C=Math.min(f.length,L.length),q=C,x=0;p<q;)f.substring(f.length-q,f.length-x)==L.substring(L.length-q,L.length-x)?(p=q,x=p):C=q,q=Math.floor((C-p)/2+p);return F(f.charCodeAt(f.length-q))&&q--,q}function b(f,L){var p=f.length>L.length?f:L,C=f.length>L.length?L:f;if(p.length<4||C.length*2<p.length)return null;function q(G,P,nt){for(var rt=G.substring(nt,nt+Math.floor(G.length/4)),V=-1,Q="",X,J,et,st;(V=P.indexOf(rt,V+1))!==-1;){var lt=c(G.substring(nt),P.substring(V)),dt=d(G.substring(0,nt),P.substring(0,V));Q.length<dt+lt&&(Q=P.substring(V-dt,V)+P.substring(V,V+lt),X=G.substring(0,nt-dt),J=G.substring(nt+lt),et=P.substring(0,V-dt),st=P.substring(V+lt))}return Q.length*2>=G.length?[X,J,et,st,Q]:null}var x=q(p,C,Math.ceil(p.length/4)),S=q(p,C,Math.ceil(p.length/2)),R;if(!x&&!S)return null;S?x?R=x[4].length>S[4].length?x:S:R=S:R=x;var D,_,z,H;f.length>L.length?(D=R[0],_=R[1],z=R[2],H=R[3]):(z=R[0],H=R[1],D=R[2],_=R[3]);var K=R[4];return[D,_,z,H,K]}function E(f){for(var L=!1,p=[],C=0,q=null,x=0,S=0,R=0,D=0,_=0;x<f.length;)f[x][0]==e?(p[C++]=x,S=D,R=_,D=0,_=0,q=f[x][1]):(f[x][0]==t?D+=f[x][1].length:_+=f[x][1].length,q&&q.length<=Math.max(S,R)&&q.length<=Math.max(D,_)&&(f.splice(p[C-1],0,[i,q]),f[p[C-1]+1][0]=t,C--,C--,x=C>0?p[C-1]:-1,S=0,R=0,D=0,_=0,q=null,L=!0)),x++;for(L&&O(f),T(f),x=1;x<f.length;){if(f[x-1][0]==i&&f[x][0]==t){var z=f[x-1][1],H=f[x][1],K=u(z,H),G=u(H,z);K>=G?(K>=z.length/2||K>=H.length/2)&&(f.splice(x,0,[e,H.substring(0,K)]),f[x-1][1]=z.substring(0,z.length-K),f[x+1][1]=H.substring(K),x++):(G>=z.length/2||G>=H.length/2)&&(f.splice(x,0,[e,z.substring(0,G)]),f[x-1][0]=t,f[x-1][1]=H.substring(0,H.length-G),f[x+1][0]=i,f[x+1][1]=z.substring(G),x++),x++}x++}}var g=/[^a-zA-Z0-9]/,m=/\s/,y=/[\r\n]/,A=/\n\r?\n$/,w=/^\r?\n\r?\n/;function T(f){function L(G,P){if(!G||!P)return 6;var nt=G.charAt(G.length-1),rt=P.charAt(0),V=nt.match(g),Q=rt.match(g),X=V&&nt.match(m),J=Q&&rt.match(m),et=X&&nt.match(y),st=J&&rt.match(y),lt=et&&G.match(A),dt=st&&P.match(w);return lt||dt?5:et||st?4:V&&!X&&J?3:X||J?2:V||Q?1:0}for(var p=1;p<f.length-1;){if(f[p-1][0]==e&&f[p+1][0]==e){var C=f[p-1][1],q=f[p][1],x=f[p+1][1],S=d(C,q);if(S){var R=q.substring(q.length-S);C=C.substring(0,C.length-S),q=R+q.substring(0,q.length-S),x=R+x}for(var D=C,_=q,z=x,H=L(C,q)+L(q,x);q.charAt(0)===x.charAt(0);){C+=q.charAt(0),q=q.substring(1)+x.charAt(0),x=x.substring(1);var K=L(C,q)+L(q,x);K>=H&&(H=K,D=C,_=q,z=x)}f[p-1][1]!=D&&(D?f[p-1][1]=D:(f.splice(p-1,1),p--),f[p][1]=_,z?f[p+1][1]=z:(f.splice(p+1,1),p--))}p++}}function O(f,L){f.push([e,""]);for(var p=0,C=0,q=0,x="",S="",R;p<f.length;){if(p<f.length-1&&!f[p][1]){f.splice(p,1);continue}switch(f[p][0]){case t:q++,S+=f[p][1],p++;break;case i:C++,x+=f[p][1],p++;break;case e:var D=p-q-C-1;if(L){if(D>=0&&Y(f[D][1])){var _=f[D][1].slice(-1);if(f[D][1]=f[D][1].slice(0,-1),x=_+x,S=_+S,!f[D][1]){f.splice(D,1),p--;var z=D-1;f[z]&&f[z][0]===t&&(q++,S=f[z][1]+S,z--),f[z]&&f[z][0]===i&&(C++,x=f[z][1]+x,z--),D=z}}if(j(f[p][1])){var _=f[p][1].charAt(0);f[p][1]=f[p][1].slice(1),x+=_,S+=_}}if(p<f.length-1&&!f[p][1]){f.splice(p,1);break}if(x.length>0||S.length>0){x.length>0&&S.length>0&&(R=c(S,x),R!==0&&(D>=0?f[D][1]+=S.substring(0,R):(f.splice(0,0,[e,S.substring(0,R)]),p++),S=S.substring(R),x=x.substring(R)),R=d(S,x),R!==0&&(f[p][1]=S.substring(S.length-R)+f[p][1],S=S.substring(0,S.length-R),x=x.substring(0,x.length-R)));var H=q+C;x.length===0&&S.length===0?(f.splice(p-H,H),p=p-H):x.length===0?(f.splice(p-H,H,[t,S]),p=p-H+1):S.length===0?(f.splice(p-H,H,[i,x]),p=p-H+1):(f.splice(p-H,H,[i,x],[t,S]),p=p-H+2)}p!==0&&f[p-1][0]===e?(f[p-1][1]+=f[p][1],f.splice(p,1)):p++,q=0,C=0,x="",S="";break}}f[f.length-1][1]===""&&f.pop();var K=!1;for(p=1;p<f.length-1;)f[p-1][0]===e&&f[p+1][0]===e&&(f[p][1].substring(f[p][1].length-f[p-1][1].length)===f[p-1][1]?(f[p][1]=f[p-1][1]+f[p][1].substring(0,f[p][1].length-f[p-1][1].length),f[p+1][1]=f[p-1][1]+f[p+1][1],f.splice(p-1,1),K=!0):f[p][1].substring(0,f[p+1][1].length)==f[p+1][1]&&(f[p-1][1]+=f[p+1][1],f[p][1]=f[p][1].substring(f[p+1][1].length)+f[p+1][1],f.splice(p+1,1),K=!0)),p++;K&&O(f,L)}function k(f){return f>=55296&&f<=56319}function F(f){return f>=56320&&f<=57343}function j(f){return F(f.charCodeAt(0))}function Y(f){return k(f.charCodeAt(f.length-1))}function it(f){for(var L=[],p=0;p<f.length;p++)f[p][1].length>0&&L.push(f[p]);return L}function ct(f,L,p,C){return Y(f)||j(C)?null:it([[e,f],[i,L],[t,p],[e,C]])}function Ct(f,L,p){var C=typeof p=="number"?{index:p,length:0}:p.oldRange,q=typeof p=="number"?null:p.newRange,x=f.length,S=L.length;if(C.length===0&&(q===null||q.length===0)){var R=C.index,D=f.slice(0,R),_=f.slice(R),z=q?q.index:null;t:{var H=R+S-x;if(z!==null&&z!==H||H<0||H>S)break t;var K=L.slice(0,H),G=L.slice(H);if(G!==_)break t;var P=Math.min(R,H),nt=D.slice(0,P),rt=K.slice(0,P);if(nt!==rt)break t;var V=D.slice(P),Q=K.slice(P);return ct(nt,V,Q,_)}t:{if(z!==null&&z!==R)break t;var X=R,K=L.slice(0,X),G=L.slice(X);if(K!==D)break t;var J=Math.min(x-X,S-X),et=_.slice(_.length-J),st=G.slice(G.length-J);if(et!==st)break t;var V=_.slice(0,_.length-J),Q=G.slice(0,G.length-J);return ct(D,V,Q,et)}}if(C.length>0&&q&&q.length===0)t:{var nt=f.slice(0,C.index),et=f.slice(C.index+C.length),P=nt.length,J=et.length;if(S<P+J)break t;var rt=L.slice(0,P),st=L.slice(S-J);if(nt!==rt||et!==st)break t;var V=f.slice(P,x-J),Q=L.slice(P,S-J);return ct(nt,V,Q,et)}return null}function xt(f,L,p,C){return s(f,L,p,C,!0)}return xt.INSERT=t,xt.DELETE=i,xt.EQUAL=e,jn=xt,jn}var rs={exports:{}};rs.exports;var wr;function li(){return wr||(wr=1,function(i,t){var e=200,s="__lodash_hash_undefined__",n=9007199254740991,l="[object Arguments]",o="[object Array]",c="[object Boolean]",u="[object Date]",d="[object Error]",b="[object Function]",E="[object GeneratorFunction]",g="[object Map]",m="[object Number]",y="[object Object]",A="[object Promise]",w="[object RegExp]",T="[object Set]",O="[object String]",k="[object Symbol]",F="[object WeakMap]",j="[object ArrayBuffer]",Y="[object DataView]",it="[object Float32Array]",ct="[object Float64Array]",Ct="[object Int8Array]",xt="[object Int16Array]",f="[object Int32Array]",L="[object Uint8Array]",p="[object Uint8ClampedArray]",C="[object Uint16Array]",q="[object Uint32Array]",x=/[\\^$.*+?()[\]{}|]/g,S=/\w*$/,R=/^\[object .+?Constructor\]$/,D=/^(?:0|[1-9]\d*)$/,_={};_[l]=_[o]=_[j]=_[Y]=_[c]=_[u]=_[it]=_[ct]=_[Ct]=_[xt]=_[f]=_[g]=_[m]=_[y]=_[w]=_[T]=_[O]=_[k]=_[L]=_[p]=_[C]=_[q]=!0,_[d]=_[b]=_[F]=!1;var z=typeof se=="object"&&se&&se.Object===Object&&se,H=typeof self=="object"&&self&&self.Object===Object&&self,K=z||H||Function("return this")(),G=t&&!t.nodeType&&t,P=G&&!0&&i&&!i.nodeType&&i,nt=P&&P.exports===G;function rt(r,a){return r.set(a[0],a[1]),r}function V(r,a){return r.add(a),r}function Q(r,a){for(var h=-1,v=r?r.length:0;++h<v&&a(r[h],h,r)!==!1;);return r}function X(r,a){for(var h=-1,v=a.length,$=r.length;++h<v;)r[$+h]=a[h];return r}function J(r,a,h,v){for(var $=-1,U=r?r.length:0;++$<U;)h=a(h,r[$],$,r);return h}function et(r,a){for(var h=-1,v=Array(r);++h<r;)v[h]=a(h);return v}function st(r,a){return r?.[a]}function lt(r){var a=!1;if(r!=null&&typeof r.toString!="function")try{a=!!(r+"")}catch{}return a}function dt(r){var a=-1,h=Array(r.size);return r.forEach(function(v,$){h[++a]=[$,v]}),h}function He(r,a){return function(h){return r(a(h))}}function ps(r){var a=-1,h=Array(r.size);return r.forEach(function(v){h[++a]=v}),h}var Ys=Array.prototype,Qs=Function.prototype,Ee=Object.prototype,Fe=K["__core-js_shared__"],ms=function(){var r=/[^.]+$/.exec(Fe&&Fe.keys&&Fe.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),bs=Qs.toString,jt=Ee.hasOwnProperty,Ne=Ee.toString,Js=RegExp("^"+bs.call(jt).replace(x,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),oe=nt?K.Buffer:void 0,Ae=K.Symbol,$e=K.Uint8Array,Tt=He(Object.getPrototypeOf,Object),ys=Object.create,vs=Ee.propertyIsEnumerable,tn=Ys.splice,ze=Object.getOwnPropertySymbols,xe=oe?oe.isBuffer:void 0,Es=He(Object.keys,Object),we=_t(K,"DataView"),ae=_t(K,"Map"),Ot=_t(K,"Promise"),Te=_t(K,"Set"),Ve=_t(K,"WeakMap"),ce=_t(Object,"create"),Ke=yt(we),ue=yt(ae),Ge=yt(Ot),We=yt(Te),Ze=yt(Ve),Jt=Ae?Ae.prototype:void 0,Ns=Jt?Jt.valueOf:void 0;function Kt(r){var a=-1,h=r?r.length:0;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function en(){this.__data__=ce?ce(null):{}}function sn(r){return this.has(r)&&delete this.__data__[r]}function nn(r){var a=this.__data__;if(ce){var h=a[r];return h===s?void 0:h}return jt.call(a,r)?a[r]:void 0}function As(r){var a=this.__data__;return ce?a[r]!==void 0:jt.call(a,r)}function Xe(r,a){var h=this.__data__;return h[r]=ce&&a===void 0?s:a,this}Kt.prototype.clear=en,Kt.prototype.delete=sn,Kt.prototype.get=nn,Kt.prototype.has=As,Kt.prototype.set=Xe;function ut(r){var a=-1,h=r?r.length:0;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function rn(){this.__data__=[]}function ln(r){var a=this.__data__,h=qe(a,r);if(h<0)return!1;var v=a.length-1;return h==v?a.pop():tn.call(a,h,1),!0}function on(r){var a=this.__data__,h=qe(a,r);return h<0?void 0:a[h][1]}function an(r){return qe(this.__data__,r)>-1}function cn(r,a){var h=this.__data__,v=qe(h,r);return v<0?h.push([r,a]):h[v][1]=a,this}ut.prototype.clear=rn,ut.prototype.delete=ln,ut.prototype.get=on,ut.prototype.has=an,ut.prototype.set=cn;function gt(r){var a=-1,h=r?r.length:0;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function un(){this.__data__={hash:new Kt,map:new(ae||ut),string:new Kt}}function hn(r){return fe(this,r).delete(r)}function fn(r){return fe(this,r).get(r)}function dn(r){return fe(this,r).has(r)}function gn(r,a){return fe(this,r).set(r,a),this}gt.prototype.clear=un,gt.prototype.delete=hn,gt.prototype.get=fn,gt.prototype.has=dn,gt.prototype.set=gn;function Nt(r){this.__data__=new ut(r)}function pn(){this.__data__=new ut}function mn(r){return this.__data__.delete(r)}function bn(r){return this.__data__.get(r)}function yn(r){return this.__data__.has(r)}function vn(r,a){var h=this.__data__;if(h instanceof ut){var v=h.__data__;if(!ae||v.length<e-1)return v.push([r,a]),this;h=this.__data__=new gt(v)}return h.set(r,a),this}Nt.prototype.clear=pn,Nt.prototype.delete=mn,Nt.prototype.get=bn,Nt.prototype.has=yn,Nt.prototype.set=vn;function Le(r,a){var h=ts(r)||ke(r)?et(r.length,String):[],v=h.length,$=!!v;for(var U in r)jt.call(r,U)&&!($&&(U=="length"||In(U,v)))&&h.push(U);return h}function xs(r,a,h){var v=r[a];(!(jt.call(r,a)&&Ss(v,h))||h===void 0&&!(a in r))&&(r[a]=h)}function qe(r,a){for(var h=r.length;h--;)if(Ss(r[h][0],a))return h;return-1}function Ht(r,a){return r&&Je(a,ss(a),r)}function Ye(r,a,h,v,$,U,Z){var W;if(v&&(W=U?v(r,$,U,Z):v(r)),W!==void 0)return W;if(!$t(r))return r;var ot=ts(r);if(ot){if(W=On(r),!a)return Sn(r,W)}else{var tt=Wt(r),pt=tt==b||tt==E;if(ks(r))return Se(r,a);if(tt==y||tt==l||pt&&!U){if(lt(r))return U?r:{};if(W=Ft(pt?{}:r),!a)return kn(r,Ht(W,r))}else{if(!_[tt])return U?r:{};W=_n(r,tt,Ye,a)}}Z||(Z=new Nt);var At=Z.get(r);if(At)return At;if(Z.set(r,W),!ot)var at=h?Cn(r):ss(r);return Q(at||r,function(mt,ht){at&&(ht=mt,mt=r[ht]),xs(W,ht,Ye(mt,a,h,v,ht,r,Z))}),W}function En(r){return $t(r)?ys(r):{}}function Nn(r,a,h){var v=a(r);return ts(r)?v:X(v,h(r))}function An(r){return Ne.call(r)}function xn(r){if(!$t(r)||Bn(r))return!1;var a=es(r)||lt(r)?Js:R;return a.test(yt(r))}function wn(r){if(!Ls(r))return Es(r);var a=[];for(var h in Object(r))jt.call(r,h)&&h!="constructor"&&a.push(h);return a}function Se(r,a){if(a)return r.slice();var h=new r.constructor(r.length);return r.copy(h),h}function Qe(r){var a=new r.constructor(r.byteLength);return new $e(a).set(new $e(r)),a}function he(r,a){var h=a?Qe(r.buffer):r.buffer;return new r.constructor(h,r.byteOffset,r.byteLength)}function ws(r,a,h){var v=a?h(dt(r),!0):dt(r);return J(v,rt,new r.constructor)}function Ts(r){var a=new r.constructor(r.source,S.exec(r));return a.lastIndex=r.lastIndex,a}function Tn(r,a,h){var v=a?h(ps(r),!0):ps(r);return J(v,V,new r.constructor)}function Ln(r){return Ns?Object(Ns.call(r)):{}}function qn(r,a){var h=a?Qe(r.buffer):r.buffer;return new r.constructor(h,r.byteOffset,r.length)}function Sn(r,a){var h=-1,v=r.length;for(a||(a=Array(v));++h<v;)a[h]=r[h];return a}function Je(r,a,h,v){h||(h={});for(var $=-1,U=a.length;++$<U;){var Z=a[$],W=void 0;xs(h,Z,W===void 0?r[Z]:W)}return h}function kn(r,a){return Je(r,Gt(r),a)}function Cn(r){return Nn(r,ss,Gt)}function fe(r,a){var h=r.__data__;return Rn(a)?h[typeof a=="string"?"string":"hash"]:h.map}function _t(r,a){var h=st(r,a);return xn(h)?h:void 0}var Gt=ze?He(ze,Object):Dn,Wt=An;(we&&Wt(new we(new ArrayBuffer(1)))!=Y||ae&&Wt(new ae)!=g||Ot&&Wt(Ot.resolve())!=A||Te&&Wt(new Te)!=T||Ve&&Wt(new Ve)!=F)&&(Wt=function(r){var a=Ne.call(r),h=a==y?r.constructor:void 0,v=h?yt(h):void 0;if(v)switch(v){case Ke:return Y;case ue:return g;case Ge:return A;case We:return T;case Ze:return F}return a});function On(r){var a=r.length,h=r.constructor(a);return a&&typeof r[0]=="string"&&jt.call(r,"index")&&(h.index=r.index,h.input=r.input),h}function Ft(r){return typeof r.constructor=="function"&&!Ls(r)?En(Tt(r)):{}}function _n(r,a,h,v){var $=r.constructor;switch(a){case j:return Qe(r);case c:case u:return new $(+r);case Y:return he(r,v);case it:case ct:case Ct:case xt:case f:case L:case p:case C:case q:return qn(r,v);case g:return ws(r,v,h);case m:case O:return new $(r);case w:return Ts(r);case T:return Tn(r,v,h);case k:return Ln(r)}}function In(r,a){return a=a??n,!!a&&(typeof r=="number"||D.test(r))&&r>-1&&r%1==0&&r<a}function Rn(r){var a=typeof r;return a=="string"||a=="number"||a=="symbol"||a=="boolean"?r!=="__proto__":r===null}function Bn(r){return!!ms&&ms in r}function Ls(r){var a=r&&r.constructor,h=typeof a=="function"&&a.prototype||Ee;return r===h}function yt(r){if(r!=null){try{return bs.call(r)}catch{}try{return r+""}catch{}}return""}function qs(r){return Ye(r,!0,!0)}function Ss(r,a){return r===a||r!==r&&a!==a}function ke(r){return Mn(r)&&jt.call(r,"callee")&&(!vs.call(r,"callee")||Ne.call(r)==l)}var ts=Array.isArray;function Ce(r){return r!=null&&Cs(r.length)&&!es(r)}function Mn(r){return Os(r)&&Ce(r)}var ks=xe||Un;function es(r){var a=$t(r)?Ne.call(r):"";return a==b||a==E}function Cs(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=n}function $t(r){var a=typeof r;return!!r&&(a=="object"||a=="function")}function Os(r){return!!r&&typeof r=="object"}function ss(r){return Ce(r)?Le(r):wn(r)}function Dn(){return[]}function Un(){return!1}i.exports=qs}(rs,rs.exports)),rs.exports}var is={exports:{}};is.exports;var Tr;function oi(){return Tr||(Tr=1,function(i,t){var e=200,s="__lodash_hash_undefined__",n=1,l=2,o=9007199254740991,c="[object Arguments]",u="[object Array]",d="[object AsyncFunction]",b="[object Boolean]",E="[object Date]",g="[object Error]",m="[object Function]",y="[object GeneratorFunction]",A="[object Map]",w="[object Number]",T="[object Null]",O="[object Object]",k="[object Promise]",F="[object Proxy]",j="[object RegExp]",Y="[object Set]",it="[object String]",ct="[object Symbol]",Ct="[object Undefined]",xt="[object WeakMap]",f="[object ArrayBuffer]",L="[object DataView]",p="[object Float32Array]",C="[object Float64Array]",q="[object Int8Array]",x="[object Int16Array]",S="[object Int32Array]",R="[object Uint8Array]",D="[object Uint8ClampedArray]",_="[object Uint16Array]",z="[object Uint32Array]",H=/[\\^$.*+?()[\]{}|]/g,K=/^\[object .+?Constructor\]$/,G=/^(?:0|[1-9]\d*)$/,P={};P[p]=P[C]=P[q]=P[x]=P[S]=P[R]=P[D]=P[_]=P[z]=!0,P[c]=P[u]=P[f]=P[b]=P[L]=P[E]=P[g]=P[m]=P[A]=P[w]=P[O]=P[j]=P[Y]=P[it]=P[xt]=!1;var nt=typeof se=="object"&&se&&se.Object===Object&&se,rt=typeof self=="object"&&self&&self.Object===Object&&self,V=nt||rt||Function("return this")(),Q=t&&!t.nodeType&&t,X=Q&&!0&&i&&!i.nodeType&&i,J=X&&X.exports===Q,et=J&&nt.process,st=function(){try{return et&&et.binding&&et.binding("util")}catch{}}(),lt=st&&st.isTypedArray;function dt(r,a){for(var h=-1,v=r==null?0:r.length,$=0,U=[];++h<v;){var Z=r[h];a(Z,h,r)&&(U[$++]=Z)}return U}function He(r,a){for(var h=-1,v=a.length,$=r.length;++h<v;)r[$+h]=a[h];return r}function ps(r,a){for(var h=-1,v=r==null?0:r.length;++h<v;)if(a(r[h],h,r))return!0;return!1}function Ys(r,a){for(var h=-1,v=Array(r);++h<r;)v[h]=a(h);return v}function Qs(r){return function(a){return r(a)}}function Ee(r,a){return r.has(a)}function Fe(r,a){return r?.[a]}function ms(r){var a=-1,h=Array(r.size);return r.forEach(function(v,$){h[++a]=[$,v]}),h}function bs(r,a){return function(h){return r(a(h))}}function jt(r){var a=-1,h=Array(r.size);return r.forEach(function(v){h[++a]=v}),h}var Ne=Array.prototype,Js=Function.prototype,oe=Object.prototype,Ae=V["__core-js_shared__"],$e=Js.toString,Tt=oe.hasOwnProperty,ys=function(){var r=/[^.]+$/.exec(Ae&&Ae.keys&&Ae.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),vs=oe.toString,tn=RegExp("^"+$e.call(Tt).replace(H,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ze=J?V.Buffer:void 0,xe=V.Symbol,Es=V.Uint8Array,we=oe.propertyIsEnumerable,ae=Ne.splice,Ot=xe?xe.toStringTag:void 0,Te=Object.getOwnPropertySymbols,Ve=ze?ze.isBuffer:void 0,ce=bs(Object.keys,Object),Ke=Gt(V,"DataView"),ue=Gt(V,"Map"),Ge=Gt(V,"Promise"),We=Gt(V,"Set"),Ze=Gt(V,"WeakMap"),Jt=Gt(Object,"create"),Ns=yt(Ke),Kt=yt(ue),en=yt(Ge),sn=yt(We),nn=yt(Ze),As=xe?xe.prototype:void 0,Xe=As?As.valueOf:void 0;function ut(r){var a=-1,h=r==null?0:r.length;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function rn(){this.__data__=Jt?Jt(null):{},this.size=0}function ln(r){var a=this.has(r)&&delete this.__data__[r];return this.size-=a?1:0,a}function on(r){var a=this.__data__;if(Jt){var h=a[r];return h===s?void 0:h}return Tt.call(a,r)?a[r]:void 0}function an(r){var a=this.__data__;return Jt?a[r]!==void 0:Tt.call(a,r)}function cn(r,a){var h=this.__data__;return this.size+=this.has(r)?0:1,h[r]=Jt&&a===void 0?s:a,this}ut.prototype.clear=rn,ut.prototype.delete=ln,ut.prototype.get=on,ut.prototype.has=an,ut.prototype.set=cn;function gt(r){var a=-1,h=r==null?0:r.length;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function un(){this.__data__=[],this.size=0}function hn(r){var a=this.__data__,h=Se(a,r);if(h<0)return!1;var v=a.length-1;return h==v?a.pop():ae.call(a,h,1),--this.size,!0}function fn(r){var a=this.__data__,h=Se(a,r);return h<0?void 0:a[h][1]}function dn(r){return Se(this.__data__,r)>-1}function gn(r,a){var h=this.__data__,v=Se(h,r);return v<0?(++this.size,h.push([r,a])):h[v][1]=a,this}gt.prototype.clear=un,gt.prototype.delete=hn,gt.prototype.get=fn,gt.prototype.has=dn,gt.prototype.set=gn;function Nt(r){var a=-1,h=r==null?0:r.length;for(this.clear();++a<h;){var v=r[a];this.set(v[0],v[1])}}function pn(){this.size=0,this.__data__={hash:new ut,map:new(ue||gt),string:new ut}}function mn(r){var a=_t(this,r).delete(r);return this.size-=a?1:0,a}function bn(r){return _t(this,r).get(r)}function yn(r){return _t(this,r).has(r)}function vn(r,a){var h=_t(this,r),v=h.size;return h.set(r,a),this.size+=h.size==v?0:1,this}Nt.prototype.clear=pn,Nt.prototype.delete=mn,Nt.prototype.get=bn,Nt.prototype.has=yn,Nt.prototype.set=vn;function Le(r){var a=-1,h=r==null?0:r.length;for(this.__data__=new Nt;++a<h;)this.add(r[a])}function xs(r){return this.__data__.set(r,s),this}function qe(r){return this.__data__.has(r)}Le.prototype.add=Le.prototype.push=xs,Le.prototype.has=qe;function Ht(r){var a=this.__data__=new gt(r);this.size=a.size}function Ye(){this.__data__=new gt,this.size=0}function En(r){var a=this.__data__,h=a.delete(r);return this.size=a.size,h}function Nn(r){return this.__data__.get(r)}function An(r){return this.__data__.has(r)}function xn(r,a){var h=this.__data__;if(h instanceof gt){var v=h.__data__;if(!ue||v.length<e-1)return v.push([r,a]),this.size=++h.size,this;h=this.__data__=new Nt(v)}return h.set(r,a),this.size=h.size,this}Ht.prototype.clear=Ye,Ht.prototype.delete=En,Ht.prototype.get=Nn,Ht.prototype.has=An,Ht.prototype.set=xn;function wn(r,a){var h=ke(r),v=!h&&Ss(r),$=!h&&!v&&Ce(r),U=!h&&!v&&!$&&Os(r),Z=h||v||$||U,W=Z?Ys(r.length,String):[],ot=W.length;for(var tt in r)Tt.call(r,tt)&&!(Z&&(tt=="length"||$&&(tt=="offset"||tt=="parent")||U&&(tt=="buffer"||tt=="byteLength"||tt=="byteOffset")||_n(tt,ot)))&&W.push(tt);return W}function Se(r,a){for(var h=r.length;h--;)if(qs(r[h][0],a))return h;return-1}function Qe(r,a,h){var v=a(r);return ke(r)?v:He(v,h(r))}function he(r){return r==null?r===void 0?Ct:T:Ot&&Ot in Object(r)?Wt(r):Ls(r)}function ws(r){return $t(r)&&he(r)==c}function Ts(r,a,h,v,$){return r===a?!0:r==null||a==null||!$t(r)&&!$t(a)?r!==r&&a!==a:Tn(r,a,h,v,Ts,$)}function Tn(r,a,h,v,$,U){var Z=ke(r),W=ke(a),ot=Z?u:Ft(r),tt=W?u:Ft(a);ot=ot==c?O:ot,tt=tt==c?O:tt;var pt=ot==O,At=tt==O,at=ot==tt;if(at&&Ce(r)){if(!Ce(a))return!1;Z=!0,pt=!1}if(at&&!pt)return U||(U=new Ht),Z||Os(r)?Je(r,a,h,v,$,U):kn(r,a,ot,h,v,$,U);if(!(h&n)){var mt=pt&&Tt.call(r,"__wrapped__"),ht=At&&Tt.call(a,"__wrapped__");if(mt||ht){var te=mt?r.value():r,Zt=ht?a.value():a;return U||(U=new Ht),$(te,Zt,h,v,U)}}return at?(U||(U=new Ht),Cn(r,a,h,v,$,U)):!1}function Ln(r){if(!Cs(r)||Rn(r))return!1;var a=ks(r)?tn:K;return a.test(yt(r))}function qn(r){return $t(r)&&es(r.length)&&!!P[he(r)]}function Sn(r){if(!Bn(r))return ce(r);var a=[];for(var h in Object(r))Tt.call(r,h)&&h!="constructor"&&a.push(h);return a}function Je(r,a,h,v,$,U){var Z=h&n,W=r.length,ot=a.length;if(W!=ot&&!(Z&&ot>W))return!1;var tt=U.get(r);if(tt&&U.get(a))return tt==a;var pt=-1,At=!0,at=h&l?new Le:void 0;for(U.set(r,a),U.set(a,r);++pt<W;){var mt=r[pt],ht=a[pt];if(v)var te=Z?v(ht,mt,pt,a,r,U):v(mt,ht,pt,r,a,U);if(te!==void 0){if(te)continue;At=!1;break}if(at){if(!ps(a,function(Zt,de){if(!Ee(at,de)&&(mt===Zt||$(mt,Zt,h,v,U)))return at.push(de)})){At=!1;break}}else if(!(mt===ht||$(mt,ht,h,v,U))){At=!1;break}}return U.delete(r),U.delete(a),At}function kn(r,a,h,v,$,U,Z){switch(h){case L:if(r.byteLength!=a.byteLength||r.byteOffset!=a.byteOffset)return!1;r=r.buffer,a=a.buffer;case f:return!(r.byteLength!=a.byteLength||!U(new Es(r),new Es(a)));case b:case E:case w:return qs(+r,+a);case g:return r.name==a.name&&r.message==a.message;case j:case it:return r==a+"";case A:var W=ms;case Y:var ot=v&n;if(W||(W=jt),r.size!=a.size&&!ot)return!1;var tt=Z.get(r);if(tt)return tt==a;v|=l,Z.set(r,a);var pt=Je(W(r),W(a),v,$,U,Z);return Z.delete(r),pt;case ct:if(Xe)return Xe.call(r)==Xe.call(a)}return!1}function Cn(r,a,h,v,$,U){var Z=h&n,W=fe(r),ot=W.length,tt=fe(a),pt=tt.length;if(ot!=pt&&!Z)return!1;for(var At=ot;At--;){var at=W[At];if(!(Z?at in a:Tt.call(a,at)))return!1}var mt=U.get(r);if(mt&&U.get(a))return mt==a;var ht=!0;U.set(r,a),U.set(a,r);for(var te=Z;++At<ot;){at=W[At];var Zt=r[at],de=a[at];if(v)var yr=Z?v(de,Zt,at,a,r,U):v(Zt,de,at,r,a,U);if(!(yr===void 0?Zt===de||$(Zt,de,h,v,U):yr)){ht=!1;break}te||(te=at=="constructor")}if(ht&&!te){var _s=r.constructor,Is=a.constructor;_s!=Is&&"constructor"in r&&"constructor"in a&&!(typeof _s=="function"&&_s instanceof _s&&typeof Is=="function"&&Is instanceof Is)&&(ht=!1)}return U.delete(r),U.delete(a),ht}function fe(r){return Qe(r,ss,On)}function _t(r,a){var h=r.__data__;return In(a)?h[typeof a=="string"?"string":"hash"]:h.map}function Gt(r,a){var h=Fe(r,a);return Ln(h)?h:void 0}function Wt(r){var a=Tt.call(r,Ot),h=r[Ot];try{r[Ot]=void 0;var v=!0}catch{}var $=vs.call(r);return v&&(a?r[Ot]=h:delete r[Ot]),$}var On=Te?function(r){return r==null?[]:(r=Object(r),dt(Te(r),function(a){return we.call(r,a)}))}:Dn,Ft=he;(Ke&&Ft(new Ke(new ArrayBuffer(1)))!=L||ue&&Ft(new ue)!=A||Ge&&Ft(Ge.resolve())!=k||We&&Ft(new We)!=Y||Ze&&Ft(new Ze)!=xt)&&(Ft=function(r){var a=he(r),h=a==O?r.constructor:void 0,v=h?yt(h):"";if(v)switch(v){case Ns:return L;case Kt:return A;case en:return k;case sn:return Y;case nn:return xt}return a});function _n(r,a){return a=a??o,!!a&&(typeof r=="number"||G.test(r))&&r>-1&&r%1==0&&r<a}function In(r){var a=typeof r;return a=="string"||a=="number"||a=="symbol"||a=="boolean"?r!=="__proto__":r===null}function Rn(r){return!!ys&&ys in r}function Bn(r){var a=r&&r.constructor,h=typeof a=="function"&&a.prototype||oe;return r===h}function Ls(r){return vs.call(r)}function yt(r){if(r!=null){try{return $e.call(r)}catch{}try{return r+""}catch{}}return""}function qs(r,a){return r===a||r!==r&&a!==a}var Ss=ws(function(){return arguments}())?ws:function(r){return $t(r)&&Tt.call(r,"callee")&&!we.call(r,"callee")},ke=Array.isArray;function ts(r){return r!=null&&es(r.length)&&!ks(r)}var Ce=Ve||Un;function Mn(r,a){return Ts(r,a)}function ks(r){if(!Cs(r))return!1;var a=he(r);return a==m||a==y||a==d||a==F}function es(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=o}function Cs(r){var a=typeof r;return r!=null&&(a=="object"||a=="function")}function $t(r){return r!=null&&typeof r=="object"}var Os=lt?Qs(lt):qn;function ss(r){return ts(r)?wn(r):Sn(r)}function Dn(){return[]}function Un(){return!1}i.exports=Mn}(is,is.exports)),is.exports}var Bs={},Lr;function nl(){if(Lr)return Bs;Lr=1,Object.defineProperty(Bs,"__esModule",{value:!0});const i=li(),t=oi();var e;return function(s){function n(u={},d={},b=!1){typeof u!="object"&&(u={}),typeof d!="object"&&(d={});let E=i(d);b||(E=Object.keys(E).reduce((g,m)=>(E[m]!=null&&(g[m]=E[m]),g),{}));for(const g in u)u[g]!==void 0&&d[g]===void 0&&(E[g]=u[g]);return Object.keys(E).length>0?E:void 0}s.compose=n;function l(u={},d={}){typeof u!="object"&&(u={}),typeof d!="object"&&(d={});const b=Object.keys(u).concat(Object.keys(d)).reduce((E,g)=>(t(u[g],d[g])||(E[g]=d[g]===void 0?null:d[g]),E),{});return Object.keys(b).length>0?b:void 0}s.diff=l;function o(u={},d={}){u=u||{};const b=Object.keys(d).reduce((E,g)=>(d[g]!==u[g]&&u[g]!==void 0&&(E[g]=d[g]),E),{});return Object.keys(u).reduce((E,g)=>(u[g]!==d[g]&&d[g]===void 0&&(E[g]=null),E),b)}s.invert=o;function c(u,d,b=!1){if(typeof u!="object")return d;if(typeof d!="object")return;if(!b)return d;const E=Object.keys(d).reduce((g,m)=>(u[m]===void 0&&(g[m]=d[m]),g),{});return Object.keys(E).length>0?E:void 0}s.transform=c}(e||(e={})),Bs.default=e,Bs}var Ms={},qr;function ai(){if(qr)return Ms;qr=1,Object.defineProperty(Ms,"__esModule",{value:!0});var i;return function(t){function e(s){return typeof s.delete=="number"?s.delete:typeof s.retain=="number"?s.retain:typeof s.retain=="object"&&s.retain!==null?1:typeof s.insert=="string"?s.insert.length:1}t.length=e}(i||(i={})),Ms.default=i,Ms}var Ds={},Sr;function rl(){if(Sr)return Ds;Sr=1,Object.defineProperty(Ds,"__esModule",{value:!0});const i=ai();class t{constructor(s){this.ops=s,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(s){s||(s=1/0);const n=this.ops[this.index];if(n){const l=this.offset,o=i.default.length(n);if(s>=o-l?(s=o-l,this.index+=1,this.offset=0):this.offset+=s,typeof n.delete=="number")return{delete:s};{const c={};return n.attributes&&(c.attributes=n.attributes),typeof n.retain=="number"?c.retain=s:typeof n.retain=="object"&&n.retain!==null?c.retain=n.retain:typeof n.insert=="string"?c.insert=n.insert.substr(l,s):c.insert=n.insert,c}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?i.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const s=this.ops[this.index];return s?typeof s.delete=="number"?"delete":typeof s.retain=="number"||typeof s.retain=="object"&&s.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const s=this.offset,n=this.index,l=this.next(),o=this.ops.slice(this.index);return this.offset=s,this.index=n,[l].concat(o)}}else return[]}}return Ds.default=t,Ds}var kr;function il(){return kr||(kr=1,function(i,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=sl(),s=li(),n=oi(),l=nl();t.AttributeMap=l.default;const o=ai();t.Op=o.default;const c=rl();t.OpIterator=c.default;const u="\0",d=(E,g)=>{if(typeof E!="object"||E===null)throw new Error(`cannot retain a ${typeof E}`);if(typeof g!="object"||g===null)throw new Error(`cannot retain a ${typeof g}`);const m=Object.keys(E)[0];if(!m||m!==Object.keys(g)[0])throw new Error(`embed types not matched: ${m} != ${Object.keys(g)[0]}`);return[m,E[m],g[m]]};class b{constructor(g){Array.isArray(g)?this.ops=g:g!=null&&Array.isArray(g.ops)?this.ops=g.ops:this.ops=[]}static registerEmbed(g,m){this.handlers[g]=m}static unregisterEmbed(g){delete this.handlers[g]}static getHandler(g){const m=this.handlers[g];if(!m)throw new Error(`no handlers for embed type "${g}"`);return m}insert(g,m){const y={};return typeof g=="string"&&g.length===0?this:(y.insert=g,m!=null&&typeof m=="object"&&Object.keys(m).length>0&&(y.attributes=m),this.push(y))}delete(g){return g<=0?this:this.push({delete:g})}retain(g,m){if(typeof g=="number"&&g<=0)return this;const y={retain:g};return m!=null&&typeof m=="object"&&Object.keys(m).length>0&&(y.attributes=m),this.push(y)}push(g){let m=this.ops.length,y=this.ops[m-1];if(g=s(g),typeof y=="object"){if(typeof g.delete=="number"&&typeof y.delete=="number")return this.ops[m-1]={delete:y.delete+g.delete},this;if(typeof y.delete=="number"&&g.insert!=null&&(m-=1,y=this.ops[m-1],typeof y!="object"))return this.ops.unshift(g),this;if(n(g.attributes,y.attributes)){if(typeof g.insert=="string"&&typeof y.insert=="string")return this.ops[m-1]={insert:y.insert+g.insert},typeof g.attributes=="object"&&(this.ops[m-1].attributes=g.attributes),this;if(typeof g.retain=="number"&&typeof y.retain=="number")return this.ops[m-1]={retain:y.retain+g.retain},typeof g.attributes=="object"&&(this.ops[m-1].attributes=g.attributes),this}}return m===this.ops.length?this.ops.push(g):this.ops.splice(m,0,g),this}chop(){const g=this.ops[this.ops.length-1];return g&&typeof g.retain=="number"&&!g.attributes&&this.ops.pop(),this}filter(g){return this.ops.filter(g)}forEach(g){this.ops.forEach(g)}map(g){return this.ops.map(g)}partition(g){const m=[],y=[];return this.forEach(A=>{(g(A)?m:y).push(A)}),[m,y]}reduce(g,m){return this.ops.reduce(g,m)}changeLength(){return this.reduce((g,m)=>m.insert?g+o.default.length(m):m.delete?g-m.delete:g,0)}length(){return this.reduce((g,m)=>g+o.default.length(m),0)}slice(g=0,m=1/0){const y=[],A=new c.default(this.ops);let w=0;for(;w<m&&A.hasNext();){let T;w<g?T=A.next(g-w):(T=A.next(m-w),y.push(T)),w+=o.default.length(T)}return new b(y)}compose(g){const m=new c.default(this.ops),y=new c.default(g.ops),A=[],w=y.peek();if(w!=null&&typeof w.retain=="number"&&w.attributes==null){let O=w.retain;for(;m.peekType()==="insert"&&m.peekLength()<=O;)O-=m.peekLength(),A.push(m.next());w.retain-O>0&&y.next(w.retain-O)}const T=new b(A);for(;m.hasNext()||y.hasNext();)if(y.peekType()==="insert")T.push(y.next());else if(m.peekType()==="delete")T.push(m.next());else{const O=Math.min(m.peekLength(),y.peekLength()),k=m.next(O),F=y.next(O);if(F.retain){const j={};if(typeof k.retain=="number")j.retain=typeof F.retain=="number"?O:F.retain;else if(typeof F.retain=="number")k.retain==null?j.insert=k.insert:j.retain=k.retain;else{const it=k.retain==null?"insert":"retain",[ct,Ct,xt]=d(k[it],F.retain),f=b.getHandler(ct);j[it]={[ct]:f.compose(Ct,xt,it==="retain")}}const Y=l.default.compose(k.attributes,F.attributes,typeof k.retain=="number");if(Y&&(j.attributes=Y),T.push(j),!y.hasNext()&&n(T.ops[T.ops.length-1],j)){const it=new b(m.rest());return T.concat(it).chop()}}else typeof F.delete=="number"&&(typeof k.retain=="number"||typeof k.retain=="object"&&k.retain!==null)&&T.push(F)}return T.chop()}concat(g){const m=new b(this.ops.slice());return g.ops.length>0&&(m.push(g.ops[0]),m.ops=m.ops.concat(g.ops.slice(1))),m}diff(g,m){if(this.ops===g.ops)return new b;const y=[this,g].map(k=>k.map(F=>{if(F.insert!=null)return typeof F.insert=="string"?F.insert:u;const j=k===g?"on":"with";throw new Error("diff() called "+j+" non-document")}).join("")),A=new b,w=e(y[0],y[1],m,!0),T=new c.default(this.ops),O=new c.default(g.ops);return w.forEach(k=>{let F=k[1].length;for(;F>0;){let j=0;switch(k[0]){case e.INSERT:j=Math.min(O.peekLength(),F),A.push(O.next(j));break;case e.DELETE:j=Math.min(F,T.peekLength()),T.next(j),A.delete(j);break;case e.EQUAL:j=Math.min(T.peekLength(),O.peekLength(),F);const Y=T.next(j),it=O.next(j);n(Y.insert,it.insert)?A.retain(j,l.default.diff(Y.attributes,it.attributes)):A.push(it).delete(j);break}F-=j}}),A.chop()}eachLine(g,m=`
`){const y=new c.default(this.ops);let A=new b,w=0;for(;y.hasNext();){if(y.peekType()!=="insert")return;const T=y.peek(),O=o.default.length(T)-y.peekLength(),k=typeof T.insert=="string"?T.insert.indexOf(m,O)-O:-1;if(k<0)A.push(y.next());else if(k>0)A.push(y.next(k));else{if(g(A,y.next(1).attributes||{},w)===!1)return;w+=1,A=new b}}A.length()>0&&g(A,{},w)}invert(g){const m=new b;return this.reduce((y,A)=>{if(A.insert)m.delete(o.default.length(A));else{if(typeof A.retain=="number"&&A.attributes==null)return m.retain(A.retain),y+A.retain;if(A.delete||typeof A.retain=="number"){const w=A.delete||A.retain;return g.slice(y,y+w).forEach(O=>{A.delete?m.push(O):A.retain&&A.attributes&&m.retain(o.default.length(O),l.default.invert(A.attributes,O.attributes))}),y+w}else if(typeof A.retain=="object"&&A.retain!==null){const w=g.slice(y,y+1),T=new c.default(w.ops).next(),[O,k,F]=d(A.retain,T.insert),j=b.getHandler(O);return m.retain({[O]:j.invert(k,F)},l.default.invert(A.attributes,T.attributes)),y+1}}return y},0),m.chop()}transform(g,m=!1){if(m=!!m,typeof g=="number")return this.transformPosition(g,m);const y=g,A=new c.default(this.ops),w=new c.default(y.ops),T=new b;for(;A.hasNext()||w.hasNext();)if(A.peekType()==="insert"&&(m||w.peekType()!=="insert"))T.retain(o.default.length(A.next()));else if(w.peekType()==="insert")T.push(w.next());else{const O=Math.min(A.peekLength(),w.peekLength()),k=A.next(O),F=w.next(O);if(k.delete)continue;if(F.delete)T.push(F);else{const j=k.retain,Y=F.retain;let it=typeof Y=="object"&&Y!==null?Y:O;if(typeof j=="object"&&j!==null&&typeof Y=="object"&&Y!==null){const ct=Object.keys(j)[0];if(ct===Object.keys(Y)[0]){const Ct=b.getHandler(ct);Ct&&(it={[ct]:Ct.transform(j[ct],Y[ct],m)})}}T.retain(it,l.default.transform(k.attributes,F.attributes,m))}}return T.chop()}transformPosition(g,m=!1){m=!!m;const y=new c.default(this.ops);let A=0;for(;y.hasNext()&&A<=g;){const w=y.peekLength(),T=y.peekType();if(y.next(),T==="delete"){g-=Math.min(w,g-A);continue}else T==="insert"&&(A<g||!m)&&(g+=w);A+=w}return g}}b.Op=o.default,b.OpIterator=c.default,b.AttributeMap=l.default,b.handlers={},t.default=b,i.exports=b,i.exports.default=b}(Rs,Rs.exports)),Rs.exports}var kt=il();const B=Jr(kt);class Pt extends wt{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}Pt.blotName="break";Pt.tagName="BR";let Dt=class extends $s{};const ll={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Ws(i){return i.replace(/[&<>"']/g,t=>ll[t])}class vt extends ir{static allowedChildren=[vt,Pt,wt,Dt];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,e){const s=vt.order.indexOf(t),n=vt.order.indexOf(e);return s>=0||n>=0?s-n:t===e?0:t<e?-1:1}formatAt(t,e,s,n){if(vt.compare(this.statics.blotName,s)<0&&this.scroll.query(s,M.BLOT)){const l=this.isolate(t,e);n&&l.wrap(s,n)}else super.formatAt(t,e,s,n)}optimize(t){if(super.optimize(t),this.parent instanceof vt&&vt.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}}const Cr=1;class ft extends as{cache={};delta(){return this.cache.delta==null&&(this.cache.delta=ci(this)),this.cache.delta}deleteAt(t,e){super.deleteAt(t,e),this.cache={}}formatAt(t,e,s,n){e<=0||(this.scroll.query(s,M.BLOCK)?t+e===this.length()&&this.format(s,n):super.formatAt(t,Math.min(e,this.length()-t-1),s,n),this.cache={})}insertAt(t,e,s){if(s!=null){super.insertAt(t,e,s),this.cache={};return}if(e.length===0)return;const n=e.split(`
`),l=n.shift();l.length>0&&(t<this.length()-1||this.children.tail==null?super.insertAt(Math.min(t,this.length()-1),l):this.children.tail.insertAt(this.children.tail.length(),l),this.cache={});let o=this;n.reduce((c,u)=>(o=o.split(c,!0),o.insertAt(0,u),u.length),t+l.length)}insertBefore(t,e){const{head:s}=this.children;super.insertBefore(t,e),s instanceof Pt&&s.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+Cr),this.cache.length}moveChildren(t,e){super.moveChildren(t,e),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(e&&(t===0||t>=this.length()-Cr)){const n=this.clone();return t===0?(this.parent.insertBefore(n,this),this):(this.parent.insertBefore(n,this.next),n)}const s=super.split(t,e);return this.cache={},s}}ft.blotName="block";ft.tagName="P";ft.defaultChild=Pt;ft.allowedChildren=[Pt,vt,wt,Dt];class St extends wt{attach(){super.attach(),this.attributes=new Ks(this.domNode)}delta(){return new B().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const s=this.scroll.query(t,M.BLOCK_ATTRIBUTE);s!=null&&this.attributes.attribute(s,e)}formatAt(t,e,s,n){this.format(s,n)}insertAt(t,e,s){if(s!=null){super.insertAt(t,e,s);return}const n=e.split(`
`),l=n.pop(),o=n.map(u=>{const d=this.scroll.create(ft.blotName);return d.insertAt(0,u),d}),c=this.split(t);o.forEach(u=>{this.parent.insertBefore(u,c)}),l&&this.parent.insertBefore(this.scroll.create("text",l),c)}}St.scope=M.BLOCK_BLOT;function ci(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return i.descendants(bt).reduce((e,s)=>s.length()===0?e:e.insert(s.value(),Lt(s,{},t)),new B).insert(`
`,Lt(i))}function Lt(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return i==null||("formats"in i&&typeof i.formats=="function"&&(t={...t,...i.formats()},e&&delete t["code-token"]),i.parent==null||i.parent.statics.blotName==="scroll"||i.parent.statics.scope!==i.statics.scope)?t:Lt(i.parent,t,e)}class Rt extends wt{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\uFEFF";static value(){}constructor(t,e,s){super(t,e),this.selection=s,this.textNode=document.createTextNode(Rt.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let s=this,n=0;for(;s!=null&&s.statics.scope!==M.BLOCK_BLOT;)n+=s.offset(s.parent),s=s.parent;s!=null&&(this.savedLength=Rt.CONTENTS.length,s.optimize(),s.formatAt(n,Rt.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof Dt?this.prev:null,s=e?e.length():0,n=this.next instanceof Dt?this.next:null,l=n?n.text:"",{textNode:o}=this,c=o.data.split(Rt.CONTENTS).join("");o.data=Rt.CONTENTS;let u;if(e)u=e,(c||n)&&(e.insertAt(e.length(),c+l),n&&n.remove());else if(n)u=n,n.insertAt(0,c);else{const d=document.createTextNode(c);u=this.scroll.create(d),this.parent.insertBefore(u,this)}if(this.remove(),t){const d=(g,m)=>e&&g===e.domNode?m:g===o?s+m-1:n&&g===n.domNode?s+c.length+m:null,b=d(t.start.node,t.start.offset),E=d(t.end.node,t.end.offset);if(b!==null&&E!==null)return{startNode:u.domNode,startOffset:b,endNode:u.domNode,endOffset:E}}return null}update(t,e){if(t.some(s=>s.type==="characterData"&&s.target===this.textNode)){const s=this.restore();s&&(e.range=s)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=Rt.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}}var Hn={exports:{}},Or;function ol(){return Or||(Or=1,function(i){var t=Object.prototype.hasOwnProperty,e="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(e=!1));function n(u,d,b){this.fn=u,this.context=d,this.once=b||!1}function l(u,d,b,E,g){if(typeof b!="function")throw new TypeError("The listener must be a function");var m=new n(b,E||u,g),y=e?e+d:d;return u._events[y]?u._events[y].fn?u._events[y]=[u._events[y],m]:u._events[y].push(m):(u._events[y]=m,u._eventsCount++),u}function o(u,d){--u._eventsCount===0?u._events=new s:delete u._events[d]}function c(){this._events=new s,this._eventsCount=0}c.prototype.eventNames=function(){var d=[],b,E;if(this._eventsCount===0)return d;for(E in b=this._events)t.call(b,E)&&d.push(e?E.slice(1):E);return Object.getOwnPropertySymbols?d.concat(Object.getOwnPropertySymbols(b)):d},c.prototype.listeners=function(d){var b=e?e+d:d,E=this._events[b];if(!E)return[];if(E.fn)return[E.fn];for(var g=0,m=E.length,y=new Array(m);g<m;g++)y[g]=E[g].fn;return y},c.prototype.listenerCount=function(d){var b=e?e+d:d,E=this._events[b];return E?E.fn?1:E.length:0},c.prototype.emit=function(d,b,E,g,m,y){var A=e?e+d:d;if(!this._events[A])return!1;var w=this._events[A],T=arguments.length,O,k;if(w.fn){switch(w.once&&this.removeListener(d,w.fn,void 0,!0),T){case 1:return w.fn.call(w.context),!0;case 2:return w.fn.call(w.context,b),!0;case 3:return w.fn.call(w.context,b,E),!0;case 4:return w.fn.call(w.context,b,E,g),!0;case 5:return w.fn.call(w.context,b,E,g,m),!0;case 6:return w.fn.call(w.context,b,E,g,m,y),!0}for(k=1,O=new Array(T-1);k<T;k++)O[k-1]=arguments[k];w.fn.apply(w.context,O)}else{var F=w.length,j;for(k=0;k<F;k++)switch(w[k].once&&this.removeListener(d,w[k].fn,void 0,!0),T){case 1:w[k].fn.call(w[k].context);break;case 2:w[k].fn.call(w[k].context,b);break;case 3:w[k].fn.call(w[k].context,b,E);break;case 4:w[k].fn.call(w[k].context,b,E,g);break;default:if(!O)for(j=1,O=new Array(T-1);j<T;j++)O[j-1]=arguments[j];w[k].fn.apply(w[k].context,O)}}return!0},c.prototype.on=function(d,b,E){return l(this,d,b,E,!1)},c.prototype.once=function(d,b,E){return l(this,d,b,E,!0)},c.prototype.removeListener=function(d,b,E,g){var m=e?e+d:d;if(!this._events[m])return this;if(!b)return o(this,m),this;var y=this._events[m];if(y.fn)y.fn===b&&(!g||y.once)&&(!E||y.context===E)&&o(this,m);else{for(var A=0,w=[],T=y.length;A<T;A++)(y[A].fn!==b||g&&!y[A].once||E&&y[A].context!==E)&&w.push(y[A]);w.length?this._events[m]=w.length===1?w[0]:w:o(this,m)}return this},c.prototype.removeAllListeners=function(d){var b;return d?(b=e?e+d:d,this._events[b]&&o(this,b)):(this._events=new s,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=e,c.EventEmitter=c,i.exports=c}(Hn)),Hn.exports}var al=ol();const cl=Jr(al),tr=new WeakMap,er=["error","warn","log","info"];let sr="warn";function ui(i){if(sr&&er.indexOf(i)<=er.indexOf(sr)){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];console[i](...e)}}function Qt(i){return er.reduce((t,e)=>(t[e]=ui.bind(console,e,i),t),{})}Qt.level=i=>{sr=i};ui.level=Qt.level;const Fn=Qt("quill:events"),ul=["selectionchange","mousedown","mouseup","click"];ul.forEach(i=>{document.addEventListener(i,function(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];Array.from(document.querySelectorAll(".ql-container")).forEach(n=>{const l=tr.get(n);l&&l.emitter&&l.emitter.handleDOM(...e)})})});class I extends cl{static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",Fn.error)}emit(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return Fn.log.call(Fn,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),n=1;n<e;n++)s[n-1]=arguments[n];(this.domListeners[t.type]||[]).forEach(l=>{let{node:o,handler:c}=l;(t.target===o||o.contains(t.target))&&c(t,...s)})}listenDOM(t,e,s){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:s})}}const $n=Qt("quill:selection");class pe{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class hl{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new pe(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,I.sources.USER),1)}),this.emitter.on(I.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const s=this.getNativeRange();s!=null&&s.start.node!==this.cursor.textNode&&this.emitter.once(I.events.SCROLL_UPDATE,(n,l)=>{try{this.root.contains(s.start.node)&&this.root.contains(s.end.node)&&this.setNativeRange(s.start.node,s.start.offset,s.end.node,s.end.offset);const o=l.some(c=>c.type==="characterData"||c.type==="childList"||c.type==="attributes"&&c.target===this.root);this.update(o?I.sources.SILENT:n)}catch{}})}),this.emitter.on(I.events.SCROLL_OPTIMIZE,(s,n)=>{if(n.range){const{startNode:l,startOffset:o,endNode:c,endOffset:u}=n.range;this.setNativeRange(l,o,c,u),this.update(I.sources.SILENT)}}),this.update(I.sources.SILENT)}handleComposition(){this.emitter.on(I.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(I.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(I.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const s=this.getNativeRange();if(!(s==null||!s.native.collapsed||this.scroll.query(t,M.BLOCK))){if(s.start.node!==this.cursor.textNode){const n=this.scroll.find(s.start.node,!1);if(n==null)return;if(n instanceof bt){const l=n.split(s.start.offset);n.parent.insertBefore(this.cursor,l)}else n.insertBefore(this.cursor,s.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const s=this.scroll.length();t=Math.min(t,s-1),e=Math.min(t+e,s-1)-t;let n,[l,o]=this.scroll.leaf(t);if(l==null)return null;if(e>0&&o===l.length()){const[b]=this.scroll.leaf(t+1);if(b){const[E]=this.scroll.line(t),[g]=this.scroll.line(t+1);E===g&&(l=b,o=0)}}[n,o]=l.position(o,!0);const c=document.createRange();if(e>0)return c.setStart(n,o),[l,o]=this.scroll.leaf(t+e),l==null?null:([n,o]=l.position(o,!0),c.setEnd(n,o),c.getBoundingClientRect());let u="left",d;if(n instanceof Text){if(!n.data.length)return null;o<n.data.length?(c.setStart(n,o),c.setEnd(n,o+1)):(c.setStart(n,o-1),c.setEnd(n,o),u="right"),d=c.getBoundingClientRect()}else{if(!(l.domNode instanceof Element))return null;d=l.domNode.getBoundingClientRect(),o>0&&(u="right")}return{bottom:d.top+d.height,height:d.height,left:d[u],right:d[u],top:d.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const s=this.normalizeNative(e);return $n.info("getNativeRange",s),s}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&zn(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const s=e.map(o=>{const[c,u]=o,d=this.scroll.find(c,!0),b=d.offset(this.scroll);return u===0?b:d instanceof bt?b+d.index(c,u):b+d.length()}),n=Math.min(Math.max(...s),this.scroll.length()-1),l=Math.min(n,...s);return new pe(l,n-l)}normalizeNative(t){if(!zn(this.root,t.startContainer)||!t.collapsed&&!zn(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(s=>{let{node:n,offset:l}=s;for(;!(n instanceof Text)&&n.childNodes.length>0;)if(n.childNodes.length>l)n=n.childNodes[l],l=0;else if(n.childNodes.length===l)n=n.lastChild,n instanceof Text?l=n.data.length:n.childNodes.length>0?l=n.childNodes.length:l=n.childNodes.length+1;else break;s.node=n,s.offset=l}),e}rangeToNative(t){const e=this.scroll.length(),s=(n,l)=>{n=Math.min(e-1,n);const[o,c]=this.scroll.leaf(n);return o?o.position(c,l):[null,-1]};return[...s(t.index,!1),...s(t.index+t.length,!0)]}setNativeRange(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,l=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if($n.info("setNativeRange",t,e,s,n),t!=null&&(this.root.parentNode==null||t.parentNode==null||s.parentNode==null))return;const o=document.getSelection();if(o!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:c}=this.getNativeRange()||{};if(c==null||l||t!==c.startContainer||e!==c.startOffset||s!==c.endContainer||n!==c.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),s instanceof Element&&s.tagName==="BR"&&(n=Array.from(s.parentNode.childNodes).indexOf(s),s=s.parentNode);const u=document.createRange();u.setStart(t,e),u.setEnd(s,n),o.removeAllRanges(),o.addRange(u)}}else o.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:I.sources.API;if(typeof e=="string"&&(s=e,e=!1),$n.info("setRange",t),t!=null){const n=this.rangeToNative(t);this.setNativeRange(...n,e)}else this.setNativeRange(null);this.update(s)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:I.sources.USER;const e=this.lastRange,[s,n]=this.getRange();if(this.lastRange=s,this.lastNative=n,this.lastRange!=null&&(this.savedRange=this.lastRange),!rr(e,this.lastRange)){if(!this.composing&&n!=null&&n.native.collapsed&&n.start.node!==this.cursor.textNode){const o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}const l=[I.events.SELECTION_CHANGE,Me(this.lastRange),Me(e),t];this.emitter.emit(I.events.EDITOR_CHANGE,...l),t!==I.sources.SILENT&&this.emitter.emit(...l)}}}function zn(i,t){try{t.parentNode}catch{return!1}return i.contains(t)}const fl=/^[ -~]*$/;class dl{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const s=_r(t),n=new B;return pl(s.ops.slice()).reduce((o,c)=>{const u=kt.Op.length(c);let d=c.attributes||{},b=!1,E=!1;if(c.insert!=null){if(n.retain(u),typeof c.insert=="string"){const y=c.insert;E=!y.endsWith(`
`)&&(e<=o||!!this.scroll.descendant(St,o)[0]),this.scroll.insertAt(o,y);const[A,w]=this.scroll.line(o);let T=re({},Lt(A));if(A instanceof ft){const[O]=A.descendant(bt,w);O&&(T=re(T,Lt(O)))}d=kt.AttributeMap.diff(T,d)||{}}else if(typeof c.insert=="object"){const y=Object.keys(c.insert)[0];if(y==null)return o;const A=this.scroll.query(y,M.INLINE)!=null;if(A)(e<=o||this.scroll.descendant(St,o)[0])&&(E=!0);else if(o>0){const[w,T]=this.scroll.descendant(bt,o-1);w instanceof Dt?w.value()[T]!==`
`&&(b=!0):w instanceof wt&&w.statics.scope===M.INLINE_BLOT&&(b=!0)}if(this.scroll.insertAt(o,y,c.insert[y]),A){const[w]=this.scroll.descendant(bt,o);if(w){const T=re({},Lt(w));d=kt.AttributeMap.diff(T,d)||{}}}}e+=u}else if(n.push(c),c.retain!==null&&typeof c.retain=="object"){const y=Object.keys(c.retain)[0];if(y==null)return o;this.scroll.updateEmbedAt(o,y,c.retain[y])}Object.keys(d).forEach(y=>{this.scroll.formatAt(o,u,y,d[y])});const g=b?1:0,m=E?1:0;return e+=g+m,n.retain(g),n.delete(m),o+u+g+m},0),n.reduce((o,c)=>typeof c.delete=="number"?(this.scroll.deleteAt(o,c.delete),o):o+kt.Op.length(c),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(s)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new B().retain(t).delete(e))}formatLine(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(s).forEach(l=>{this.scroll.lines(t,Math.max(e,1)).forEach(o=>{o.format(l,s[l])})}),this.scroll.optimize();const n=new B().retain(t).retain(e,Me(s));return this.update(n)}formatText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(s).forEach(l=>{this.scroll.formatAt(t,e,l,s[l])});const n=new B().retain(t).retain(e,Me(s));return this.update(n)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new B)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=[],n=[];e===0?this.scroll.path(t).forEach(c=>{const[u]=c;u instanceof ft?s.push(u):u instanceof bt&&n.push(u)}):(s=this.scroll.lines(t,e),n=this.scroll.descendants(bt,t,e));const[l,o]=[s,n].map(c=>{const u=c.shift();if(u==null)return{};let d=Lt(u);for(;Object.keys(d).length>0;){const b=c.shift();if(b==null)return d;d=gl(Lt(b),d)}return d});return{...l,...o}}getHTML(t,e){const[s,n]=this.scroll.line(t);if(s){const l=s.length();return s.length()>=n+e&&!(n===0&&e===l)?cs(s,n,e,!0):cs(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(s=>typeof s.insert=="string").map(s=>s.insert).join("")}insertContents(t,e){const s=_r(e),n=new B().retain(t).concat(s);return this.scroll.insertContents(t,s),this.update(n)}insertEmbed(t,e,s){return this.scroll.insertAt(t,e,s),this.update(new B().retain(t).insert({[e]:s}))}insertText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(s).forEach(n=>{this.scroll.formatAt(t,e.length,n,s[n])}),this.update(new B().retain(t).insert(e,Me(s)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if(t?.statics.blotName!==ft.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof Pt}removeFormat(t,e){const s=this.getText(t,e),[n,l]=this.scroll.line(t+e);let o=0,c=new B;n!=null&&(o=n.length()-l,c=n.delta().slice(l,l+o-1).insert(`
`));const d=this.getContents(t,e+o).diff(new B().insert(s).concat(c)),b=new B().retain(t).concat(d);return this.applyDelta(b)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const n=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(fl)&&this.scroll.find(e[0].target)){const l=this.scroll.find(e[0].target),o=Lt(l),c=l.offset(this.scroll),u=e[0].oldValue.replace(Rt.CONTENTS,""),d=new B().insert(u),b=new B().insert(l.value()),E=s&&{oldRange:Ir(s.oldRange,-c),newRange:Ir(s.newRange,-c)};t=new B().retain(c).concat(d.diff(b,E)).reduce((m,y)=>y.insert?m.insert(y.insert,o):m.push(y),new B),this.delta=n.compose(t)}else this.delta=this.getDelta(),(!t||!rr(n.compose(t),this.delta))&&(t=n.diff(this.delta,s));return t}}function Be(i,t,e){if(i.length===0){const[m]=Vn(e.pop());return t<=0?`</li></${m}>`:`</li></${m}>${Be([],t-1,e)}`}const[{child:s,offset:n,length:l,indent:o,type:c},...u]=i,[d,b]=Vn(c);if(o>t)return e.push(c),o===t+1?`<${d}><li${b}>${cs(s,n,l)}${Be(u,o,e)}`:`<${d}><li>${Be(i,t+1,e)}`;const E=e[e.length-1];if(o===t&&c===E)return`</li><li${b}>${cs(s,n,l)}${Be(u,o,e)}`;const[g]=Vn(e.pop());return`</li></${g}>${Be(i,t-1,e)}`}function cs(i,t,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in i&&typeof i.html=="function")return i.html(t,e);if(i instanceof Dt)return Ws(i.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(i instanceof Mt){if(i.statics.blotName==="list-container"){const d=[];return i.children.forEachAt(t,e,(b,E,g)=>{const m="formats"in b&&typeof b.formats=="function"?b.formats():{};d.push({child:b,offset:E,length:g,indent:m.indent||0,type:m.list})}),Be(d,-1,[])}const n=[];if(i.children.forEachAt(t,e,(d,b,E)=>{n.push(cs(d,b,E))}),s||i.statics.blotName==="list")return n.join("");const{outerHTML:l,innerHTML:o}=i.domNode,[c,u]=l.split(`>${o}<`);return c==="<table"?`<table style="border: 1px solid #000;">${n.join("")}<${u}`:`${c}>${n.join("")}<${u}`}return i.domNode instanceof Element?i.domNode.outerHTML:""}function gl(i,t){return Object.keys(t).reduce((e,s)=>{if(i[s]==null)return e;const n=t[s];return n===i[s]?e[s]=n:Array.isArray(n)?n.indexOf(i[s])<0?e[s]=n.concat([i[s]]):e[s]=n:e[s]=[n,i[s]],e},{})}function Vn(i){const t=i==="ordered"?"ol":"ul";switch(i){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function _r(i){return i.reduce((t,e)=>{if(typeof e.insert=="string"){const s=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(s,e.attributes)}return t.push(e)},new B)}function Ir(i,t){let{index:e,length:s}=i;return new pe(e+t,s)}function pl(i){const t=[];return i.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((n,l)=>{l&&t.push({insert:`
`,attributes:e.attributes}),n&&t.push({insert:n,attributes:e.attributes})}):t.push(e)}),t}class Vt{static DEFAULTS={};constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}const Us="\uFEFF";class or extends wt{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(s=>{this.contentNode.appendChild(s)}),this.leftGuard=document.createTextNode(Us),this.rightGuard=document.createTextNode(Us),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,s;const n=t.data.split(Us).join("");if(t===this.leftGuard)if(this.prev instanceof Dt){const l=this.prev.length();this.prev.insertAt(l,n),e={startNode:this.prev.domNode,startOffset:l+n.length}}else s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this),e={startNode:s,startOffset:n.length};else t===this.rightGuard&&(this.next instanceof Dt?(this.next.insertAt(0,n),e={startNode:this.next.domNode,startOffset:n.length}):(s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this.next),e={startNode:s,startOffset:n.length}));return t.data=Us,e}update(t,e){t.forEach(s=>{if(s.type==="characterData"&&(s.target===this.leftGuard||s.target===this.rightGuard)){const n=this.restore(s.target);n&&(e.range=n)}})}}class ml{isComposing=!1;constructor(t,e){this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof or)&&(this.emitter.emit(I.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(I.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(I.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(I.events.COMPOSITION_END,t),this.isComposing=!1}}class je{static DEFAULTS={modules:{}};static themes={default:je};modules={};constructor(t,e){this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}const bl=i=>i.parentElement||i.getRootNode().host||null,yl=i=>{const t=i.getBoundingClientRect(),e="offsetWidth"in i&&Math.abs(t.width)/i.offsetWidth||1,s="offsetHeight"in i&&Math.abs(t.height)/i.offsetHeight||1;return{top:t.top,right:t.left+i.clientWidth*e,bottom:t.top+i.clientHeight*s,left:t.left}},Ps=i=>{const t=parseInt(i,10);return Number.isNaN(t)?0:t},Rr=(i,t,e,s,n,l)=>i<e&&t>s?0:i<e?-(e-i+n):t>s?t-i>s-e?i+n-e:t-s+l:0,vl=(i,t)=>{const e=i.ownerDocument;let s=t,n=i;for(;n;){const l=n===e.body,o=l?{top:0,right:window.visualViewport?.width??e.documentElement.clientWidth,bottom:window.visualViewport?.height??e.documentElement.clientHeight,left:0}:yl(n),c=getComputedStyle(n),u=Rr(s.left,s.right,o.left,o.right,Ps(c.scrollPaddingLeft),Ps(c.scrollPaddingRight)),d=Rr(s.top,s.bottom,o.top,o.bottom,Ps(c.scrollPaddingTop),Ps(c.scrollPaddingBottom));if(u||d)if(l)e.defaultView?.scrollBy(u,d);else{const{scrollLeft:b,scrollTop:E}=n;d&&(n.scrollTop+=d),u&&(n.scrollLeft+=u);const g=n.scrollLeft-b,m=n.scrollTop-E;s={left:s.left-g,top:s.top-m,right:s.right-g,bottom:s.bottom-m}}n=l||c.position==="fixed"?null:bl(n)}},El=100,Nl=["block","break","cursor","inline","scroll","text"],Al=(i,t,e)=>{const s=new Pe;return Nl.forEach(n=>{const l=t.query(n);l&&s.register(l)}),i.forEach(n=>{let l=t.query(n);l||e.error(`Cannot register "${n}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;l;)if(s.register(l),l="blotName"in l?l.requiredContainer??null:null,o+=1,o>El){e.error(`Cycle detected in registering blot requiredContainer: "${n}"`);break}}),s},Ue=Qt("quill"),js=new Pe;Mt.uiClass="ql-ui";class N{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:js,theme:"default"};static events=I.events;static sources=I.sources;static version="2.0.3";static imports={delta:B,parchment:el,"core/module":Vt,"core/theme":je};static debug(t){t===!0&&(t="log"),Qt.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return tr.get(t)||js.find(t,e)}static import(t){return this.imports[t]==null&&Ue.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),s="attrName"in t?t.attrName:t.blotName;typeof s=="string"?this.register(`formats/${s}`,t,e):Object.keys(t).forEach(n=>{this.register(n,t[n],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],s=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!s&&Ue.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&js.register(e),typeof e.register=="function"&&e.register(js)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=xl(t,e),this.container=this.options.container,this.container==null){Ue.error("Invalid Quill container",t);return}this.options.debug&&N.debug(this.options.debug);const s=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",tr.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new I;const n=lr.blotName,l=this.options.registry.query(n);if(!l||!("blotName"in l))throw new Error(`Cannot initialize Quill without "${n}" blot`);if(this.scroll=new l(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new dl(this.scroll),this.selection=new hl(this.scroll,this.emitter),this.composition=new ml(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(I.events.EDITOR_CHANGE,o=>{o===I.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(I.events.SCROLL_UPDATE,(o,c)=>{const u=this.selection.lastRange,[d]=this.selection.getRange(),b=u&&d?{oldRange:u,newRange:d}:void 0;It.call(this,()=>this.editor.update(null,c,b),o)}),this.emitter.on(I.events.SCROLL_EMBED_UPDATE,(o,c)=>{const u=this.selection.lastRange,[d]=this.selection.getRange(),b=u&&d?{oldRange:u,newRange:d}:void 0;It.call(this,()=>{const E=new B().retain(o.offset(this)).retain({[o.statics.blotName]:c});return this.editor.update(E,[],b)},N.sources.USER)}),s){const o=this.clipboard.convert({html:`${s}<p><br></p>`,text:`
`});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const s=t;t=document.createElement("div"),t.classList.add(s)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,s){return[t,e,,s]=Xt(t,e,s),It.call(this,()=>this.editor.deleteText(t,e),s,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:I.sources.API;return It.call(this,()=>{const n=this.getSelection(!0);let l=new B;if(n==null)return l;if(this.scroll.query(t,M.BLOCK))l=this.editor.formatLine(n.index,n.length,{[t]:e});else{if(n.length===0)return this.selection.format(t,e),l;l=this.editor.formatText(n.index,n.length,{[t]:e})}return this.setSelection(n,I.sources.SILENT),l},s)}formatLine(t,e,s,n,l){let o;return[t,e,o,l]=Xt(t,e,s,n,l),It.call(this,()=>this.editor.formatLine(t,e,o),l,t,0)}formatText(t,e,s,n,l){let o;return[t,e,o,l]=Xt(t,e,s,n,l),It.call(this,()=>this.editor.formatText(t,e,o),l,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=null;if(typeof t=="number"?s=this.selection.getBounds(t,e):s=this.selection.getBounds(t.index,t.length),!s)return null;const n=this.container.getBoundingClientRect();return{bottom:s.bottom-n.top,height:s.height,left:s.left-n.left,right:s.right-n.left,top:s.top-n.top,width:s.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=Xt(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Xt(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Xt(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:N.sources.API;return It.call(this,()=>this.editor.insertEmbed(t,e,s),n,t)}insertText(t,e,s,n,l){let o;return[t,,o,l]=Xt(t,0,s,n,l),It.call(this,()=>this.editor.insertText(t,e,o),l,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,s){return[t,e,,s]=Xt(t,e,s),It.call(this,()=>this.editor.removeFormat(t,e),s,t)}scrollRectIntoView(t){vl(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:I.sources.API;return It.call(this,()=>{t=new B(t);const s=this.getLength(),n=this.editor.deleteText(0,s),l=this.editor.insertContents(0,t),o=this.editor.deleteText(this.getLength()-1,1);return n.compose(l).compose(o)},e)}setSelection(t,e,s){t==null?this.selection.setRange(null,e||N.sources.API):([t,e,,s]=Xt(t,e,s),this.selection.setRange(new pe(Math.max(0,t),e),s),s!==I.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:I.sources.API;const s=new B().insert(t);return this.setContents(s,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:I.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:I.sources.API;return It.call(this,()=>(t=new B(t),this.editor.applyDelta(t)),e,!0)}}function Br(i){return typeof i=="string"?document.querySelector(i):i}function Kn(i){return Object.entries(i??{}).reduce((t,e)=>{let[s,n]=e;return{...t,[s]:n===!0?{}:n}},{})}function Mr(i){return Object.fromEntries(Object.entries(i).filter(t=>t[1]!==void 0))}function xl(i,t){const e=Br(i);if(!e)throw new Error("Invalid Quill container");const n=!t.theme||t.theme===N.DEFAULTS.theme?je:N.import(`themes/${t.theme}`);if(!n)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:l,...o}=N.DEFAULTS,{modules:c,...u}=n.DEFAULTS;let d=Kn(t.modules);d!=null&&d.toolbar&&d.toolbar.constructor!==Object&&(d={...d,toolbar:{container:d.toolbar}});const b=re({},Kn(l),Kn(c),d),E={...o,...Mr(u),...Mr(t)};let g=t.registry;return g?t.formats&&Ue.warn('Ignoring "formats" option because "registry" is specified'):g=t.formats?Al(t.formats,E.registry,Ue):E.registry,{...E,registry:g,container:e,theme:n,modules:Object.entries(b).reduce((m,y)=>{let[A,w]=y;if(!w)return m;const T=N.import(`modules/${A}`);return T==null?(Ue.error(`Cannot load ${A} module. Are you sure you registered it?`),m):{...m,[A]:re({},T.DEFAULTS||{},w)}},{}),bounds:Br(E.bounds)}}function It(i,t,e,s){if(!this.isEnabled()&&t===I.sources.USER&&!this.allowReadOnlyEdits)return new B;let n=e==null?null:this.getSelection();const l=this.editor.delta,o=i();if(n!=null&&(e===!0&&(e=n.index),s==null?n=Dr(n,o,t):s!==0&&(n=Dr(n,e,s,t)),this.setSelection(n,I.sources.SILENT)),o.length()>0){const c=[I.events.TEXT_CHANGE,o,l,t];this.emitter.emit(I.events.EDITOR_CHANGE,...c),t!==I.sources.SILENT&&this.emitter.emit(...c)}return o}function Xt(i,t,e,s,n){let l={};return typeof i.index=="number"&&typeof i.length=="number"?typeof t!="number"?(n=s,s=e,e=t,t=i.length,i=i.index):(t=i.length,i=i.index):typeof t!="number"&&(n=s,s=e,e=t,t=0),typeof e=="object"?(l=e,n=s):typeof e=="string"&&(s!=null?l[e]=s:n=e),n=n||I.sources.API,[i,t,l,n]}function Dr(i,t,e,s){const n=typeof e=="number"?e:0;if(i==null)return null;let l,o;return t&&typeof t.transformPosition=="function"?[l,o]=[i.index,i.index+i.length].map(c=>t.transformPosition(c,s!==I.sources.USER)):[l,o]=[i.index,i.index+i.length].map(c=>c<t||c===t&&s===I.sources.USER?c:n>=0?c+n:Math.max(t,c+n)),new pe(l,o-l)}class be extends Gs{}function Ur(i){return i instanceof ft||i instanceof St}function Pr(i){return typeof i.updateContent=="function"}class wl extends lr{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=ft;static allowedChildren=[ft,St,be];constructor(t,e,s){let{emitter:n}=s;super(t,e),this.emitter=n,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",l=>this.handleDragStart(l))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(I.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(I.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(I.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[s,n]=this.line(t),[l]=this.line(t+e);if(super.deleteAt(t,e),l!=null&&s!==l&&n>0){if(s instanceof St||l instanceof St){this.optimize();return}const o=l.children.head instanceof Pt?null:l.children.head;s.moveChildren(l,o),s.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,s,n){super.formatAt(t,e,s,n),this.optimize()}insertAt(t,e,s){if(t>=this.length())if(s==null||this.scroll.query(e,M.BLOCK)==null){const n=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(n),s==null&&e.endsWith(`
`)?n.insertAt(0,e.slice(0,-1),s):n.insertAt(0,e,s)}else{const n=this.scroll.create(e,s);this.appendChild(n)}else super.insertAt(t,e,s);this.optimize()}insertBefore(t,e){if(t.statics.scope===M.INLINE_BLOT){const s=this.scroll.create(this.statics.defaultChild.blotName);s.appendChild(t),super.insertBefore(s,e)}else super.insertBefore(t,e)}insertContents(t,e){const s=this.deltaToRenderBlocks(e.concat(new B().insert(`
`))),n=s.pop();if(n==null)return;this.batchStart();const l=s.shift();if(l){const u=l.type==="block"&&(l.delta.length()===0||!this.descendant(St,t)[0]&&t<this.length()),d=l.type==="block"?l.delta:new B().insert({[l.key]:l.value});Gn(this,t,d);const b=l.type==="block"?1:0,E=t+d.length()+b;u&&this.insertAt(E-1,`
`);const g=Lt(this.line(t)[0]),m=kt.AttributeMap.diff(g,l.attributes)||{};Object.keys(m).forEach(y=>{this.formatAt(E-1,1,y,m[y])}),t=E}let[o,c]=this.children.find(t);if(s.length&&(o&&(o=o.split(c),c=0),s.forEach(u=>{if(u.type==="block"){const d=this.createBlock(u.attributes,o||void 0);Gn(d,0,u.delta)}else{const d=this.create(u.key,u.value);this.insertBefore(d,o||void 0),Object.keys(u.attributes).forEach(b=>{d.format(b,u.attributes[b])})}})),n.type==="block"&&n.delta.length()){const u=o?o.offset(o.scroll)+c:this.length();Gn(this,u,n.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[s,n]=e;return s instanceof bt?[s,n]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(Ur,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const s=(n,l,o)=>{let c=[],u=o;return n.children.forEachAt(l,o,(d,b,E)=>{Ur(d)?c.push(d):d instanceof Gs&&(c=c.concat(s(d,b,u))),u-=E}),c};return s(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(I.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=I.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(s=>{let{target:n}=s;const l=this.find(n,!0);return l&&!Pr(l)}),t.length>0&&this.emitter.emit(I.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(I.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,s){const[n]=this.descendant(l=>l instanceof St,t);n&&n.statics.blotName===e&&Pr(n)&&n.updateContent(s)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let s=new B;return t.forEach(n=>{const l=n?.insert;if(l)if(typeof l=="string"){const o=l.split(`
`);o.slice(0,-1).forEach(u=>{s.insert(u,n.attributes),e.push({type:"block",delta:s,attributes:n.attributes??{}}),s=new B});const c=o[o.length-1];c&&s.insert(c,n.attributes)}else{const o=Object.keys(l)[0];if(!o)return;this.query(o,M.INLINE)?s.push(n):(s.length()&&e.push({type:"block",delta:s,attributes:{}}),s=new B,e.push({type:"blockEmbed",key:o,value:l[o],attributes:n.attributes??{}}))}}),s.length()&&e.push({type:"block",delta:s,attributes:{}}),e}createBlock(t,e){let s;const n={};Object.entries(t).forEach(c=>{let[u,d]=c;this.query(u,M.BLOCK&M.BLOT)!=null?s=u:n[u]=d});const l=this.create(s||this.statics.defaultChild.blotName,s?t[s]:void 0);this.insertBefore(l,e||void 0);const o=l.length();return Object.entries(n).forEach(c=>{let[u,d]=c;l.formatAt(0,o,u,d)}),l}}function Gn(i,t,e){e.reduce((s,n)=>{const l=kt.Op.length(n);let o=n.attributes||{};if(n.insert!=null){if(typeof n.insert=="string"){const c=n.insert;i.insertAt(s,c);const[u]=i.descendant(bt,s),d=Lt(u);o=kt.AttributeMap.diff(d,o)||{}}else if(typeof n.insert=="object"){const c=Object.keys(n.insert)[0];if(c==null)return s;if(i.insertAt(s,c,n.insert[c]),i.scroll.query(c,M.INLINE)!=null){const[d]=i.descendant(bt,s),b=Lt(d);o=kt.AttributeMap.diff(b,o)||{}}}}return Object.keys(o).forEach(c=>{i.formatAt(s,l,c,o[c])}),s+l},t)}const ar={scope:M.BLOCK,whitelist:["right","center","justify"]},Tl=new zt("align","align",ar),hi=new Ut("align","ql-align",ar),fi=new le("align","text-align",ar);class di extends le{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(n=>`00${parseInt(n,10).toString(16)}`.slice(-2)).join("")}`):e}}const Ll=new Ut("color","ql-color",{scope:M.INLINE}),cr=new di("color","color",{scope:M.INLINE}),ql=new Ut("background","ql-bg",{scope:M.INLINE}),ur=new di("background","background-color",{scope:M.INLINE});class ye extends be{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(s=>s.length()<=1?"":s.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${Ws(this.code(t,e))}
</pre>`}}class Et extends ft{static TAB="  ";static register(){N.register(ye)}}class hr extends vt{}hr.blotName="code";hr.tagName="CODE";Et.blotName="code-block";Et.className="ql-code-block";Et.tagName="DIV";ye.blotName="code-block-container";ye.className="ql-code-block-container";ye.tagName="DIV";ye.allowedChildren=[Et];Et.allowedChildren=[Dt,Pt,Rt];Et.requiredContainer=ye;const fr={scope:M.BLOCK,whitelist:["rtl"]},gi=new zt("direction","dir",fr),pi=new Ut("direction","ql-direction",fr),mi=new le("direction","direction",fr),bi={scope:M.INLINE,whitelist:["serif","monospace"]},yi=new Ut("font","ql-font",bi);class Sl extends le{value(t){return super.value(t).replace(/["']/g,"")}}const vi=new Sl("font","font-family",bi),Ei=new Ut("size","ql-size",{scope:M.INLINE,whitelist:["small","large","huge"]}),Ni=new le("size","font-size",{scope:M.INLINE,whitelist:["10px","18px","32px"]}),kl=Qt("quill:keyboard"),Cl=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class Zs extends Vt{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(s=>!!e[s]!==t[s]&&e[s]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(s=>{this.options.bindings[s]&&this.addBinding(this.options.bindings[s])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const n=_l(t);if(n==null){kl.warn("Attempted to add invalid keyboard binding",n);return}typeof e=="function"&&(e={handler:e}),typeof s=="function"&&(s={handler:s}),(Array.isArray(n.key)?n.key:[n.key]).forEach(o=>{const c={...n,key:o,...e,...s};this.bindings[c.key]=this.bindings[c.key]||[],this.bindings[c.key].push(c)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const n=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(T=>Zs.match(t,T));if(n.length===0)return;const l=N.find(t.target,!0);if(l&&l.scroll!==this.quill.scroll)return;const o=this.quill.getSelection();if(o==null||!this.quill.hasFocus())return;const[c,u]=this.quill.getLine(o.index),[d,b]=this.quill.getLeaf(o.index),[E,g]=o.length===0?[d,b]:this.quill.getLeaf(o.index+o.length),m=d instanceof $s?d.value().slice(0,b):"",y=E instanceof $s?E.value().slice(g):"",A={collapsed:o.length===0,empty:o.length===0&&c.length()<=1,format:this.quill.getFormat(o),line:c,offset:u,prefix:m,suffix:y,event:t};n.some(T=>{if(T.collapsed!=null&&T.collapsed!==A.collapsed||T.empty!=null&&T.empty!==A.empty||T.offset!=null&&T.offset!==A.offset)return!1;if(Array.isArray(T.format)){if(T.format.every(O=>A.format[O]==null))return!1}else if(typeof T.format=="object"&&!Object.keys(T.format).every(O=>T.format[O]===!0?A.format[O]!=null:T.format[O]===!1?A.format[O]==null:rr(T.format[O],A.format[O])))return!1;return T.prefix!=null&&!T.prefix.test(A.prefix)||T.suffix!=null&&!T.suffix.test(A.suffix)?!1:T.handler.call(this,o,A,T)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const s=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let n={};const[l]=this.quill.getLine(t.index);let o=new B().retain(t.index-s).delete(s);if(e.offset===0){const[c]=this.quill.getLine(t.index-1);if(c&&!(c.statics.blotName==="block"&&c.length()<=1)){const d=l.formats(),b=this.quill.getFormat(t.index-1,1);if(n=kt.AttributeMap.diff(d,b)||{},Object.keys(n).length>0){const E=new B().retain(t.index+l.length()-2).retain(1,n);o=o.compose(E)}}}this.quill.updateContents(o,N.sources.USER),this.quill.focus()}handleDelete(t,e){const s=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-s)return;let n={};const[l]=this.quill.getLine(t.index);let o=new B().retain(t.index).delete(s);if(e.offset>=l.length()-1){const[c]=this.quill.getLine(t.index+1);if(c){const u=l.formats(),d=this.quill.getFormat(t.index,1);n=kt.AttributeMap.diff(u,d)||{},Object.keys(n).length>0&&(o=o.retain(c.length()-1).retain(1,n))}}this.quill.updateContents(o,N.sources.USER),this.quill.focus()}handleDeleteRange(t){dr({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const s=Object.keys(e.format).reduce((l,o)=>(this.quill.scroll.query(o,M.BLOCK)&&!Array.isArray(e.format[o])&&(l[o]=e.format[o]),l),{}),n=new B().retain(t.index).delete(t.length).insert(`
`,s);this.quill.updateContents(n,N.sources.USER),this.quill.setSelection(t.index+1,N.sources.SILENT),this.quill.focus()}}const Ol={bindings:{bold:Wn("bold"),italic:Wn("italic"),underline:Wn("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(i,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",N.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(i,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",N.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(i,t){t.format.indent!=null?this.quill.format("indent","-1",N.sources.USER):t.format.list!=null&&this.quill.format("list",!1,N.sources.USER)}},"indent code-block":jr(!0),"outdent code-block":jr(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(i){this.quill.deleteText(i.index-1,1,N.sources.USER)}},tab:{key:"Tab",handler(i,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new B().retain(i.index).delete(i.length).insert("	");return this.quill.updateContents(e,N.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(i.index+1,N.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,N.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(i,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(i.index,i.length,e,N.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(i){const[t,e]=this.quill.getLine(i.index),s={...t.formats(),list:"checked"},n=new B().retain(i.index).insert(`
`,s).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,N.sources.USER),this.quill.setSelection(i.index+1,N.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(i,t){const[e,s]=this.quill.getLine(i.index),n=new B().retain(i.index).insert(`
`,t.format).retain(e.length()-s-1).retain(1,{header:null});this.quill.updateContents(n,N.sources.USER),this.quill.setSelection(i.index+1,N.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(i){const t=this.quill.getModule("table");if(t){const[e,s,n,l]=t.getTable(i),o=Il(e,s,n,l);if(o==null)return;let c=e.offset();if(o<0){const u=new B().retain(c).insert(`
`);this.quill.updateContents(u,N.sources.USER),this.quill.setSelection(i.index+1,i.length,N.sources.SILENT)}else if(o>0){c+=e.length();const u=new B().retain(c).insert(`
`);this.quill.updateContents(u,N.sources.USER),this.quill.setSelection(c,N.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(i,t){const{event:e,line:s}=t,n=s.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(n-1,N.sources.USER):this.quill.setSelection(n+s.length(),N.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(i,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[s,n]=this.quill.getLine(i.index);if(n>e)return!0;let l;switch(t.prefix.trim()){case"[]":case"[ ]":l="unchecked";break;case"[x]":l="checked";break;case"-":case"*":l="bullet";break;default:l="ordered"}this.quill.insertText(i.index," ",N.sources.USER),this.quill.history.cutoff();const o=new B().retain(i.index-n).delete(e+1).retain(s.length()-2-n).retain(1,{list:l});return this.quill.updateContents(o,N.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(i.index-e,N.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(i){const[t,e]=this.quill.getLine(i.index);let s=2,n=t;for(;n!=null&&n.length()<=1&&n.formats()["code-block"];)if(n=n.prev,s-=1,s<=0){const l=new B().retain(i.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(l,N.sources.USER),this.quill.setSelection(i.index-1,N.sources.SILENT),!1}return!0}},"embed left":Hs("ArrowLeft",!1),"embed left shift":Hs("ArrowLeft",!0),"embed right":Hs("ArrowRight",!1),"embed right shift":Hs("ArrowRight",!0),"table down":Hr(!1),"table up":Hr(!0)}};Zs.DEFAULTS=Ol;function jr(i){return{key:"Tab",shiftKey:!i,format:{"code-block":!0},handler(t,e){let{event:s}=e;const n=this.quill.scroll.query("code-block"),{TAB:l}=n;if(t.length===0&&!s.shiftKey){this.quill.insertText(t.index,l,N.sources.USER),this.quill.setSelection(t.index+l.length,N.sources.SILENT);return}const o=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:c,length:u}=t;o.forEach((d,b)=>{i?(d.insertAt(0,l),b===0?c+=l.length:u+=l.length):d.domNode.textContent.startsWith(l)&&(d.deleteAt(0,l.length),b===0?c-=l.length:u-=l.length)}),this.quill.update(N.sources.USER),this.quill.setSelection(c,u,N.sources.SILENT)}}}function Hs(i,t){return{key:i,shiftKey:t,altKey:null,[i==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(s){let{index:n}=s;i==="ArrowRight"&&(n+=s.length+1);const[l]=this.quill.getLeaf(n);return l instanceof wt?(i==="ArrowLeft"?t?this.quill.setSelection(s.index-1,s.length+1,N.sources.USER):this.quill.setSelection(s.index-1,N.sources.USER):t?this.quill.setSelection(s.index,s.length+1,N.sources.USER):this.quill.setSelection(s.index+s.length+1,N.sources.USER),!1):!0}}}function Wn(i){return{key:i[0],shortKey:!0,handler(t,e){this.quill.format(i,!e.format[i],N.sources.USER)}}}function Hr(i){return{key:i?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const s=i?"prev":"next",n=e.line,l=n.parent[s];if(l!=null){if(l.statics.blotName==="table-row"){let o=l.children.head,c=n;for(;c.prev!=null;)c=c.prev,o=o.next;const u=o.offset(this.quill.scroll)+Math.min(e.offset,o.length()-1);this.quill.setSelection(u,0,N.sources.USER)}}else{const o=n.table()[s];o!=null&&(i?this.quill.setSelection(o.offset(this.quill.scroll)+o.length()-1,0,N.sources.USER):this.quill.setSelection(o.offset(this.quill.scroll),0,N.sources.USER))}return!1}}}function _l(i){if(typeof i=="string"||typeof i=="number")i={key:i};else if(typeof i=="object")i=Me(i);else return null;return i.shortKey&&(i[Cl]=i.shortKey,delete i.shortKey),i}function dr(i){let{quill:t,range:e}=i;const s=t.getLines(e);let n={};if(s.length>1){const l=s[0].formats(),o=s[s.length-1].formats();n=kt.AttributeMap.diff(o,l)||{}}t.deleteText(e,N.sources.USER),Object.keys(n).length>0&&t.formatLine(e.index,1,n,N.sources.USER),t.setSelection(e.index,N.sources.SILENT)}function Il(i,t,e,s){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?s===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const Rl=/font-weight:\s*normal/,Bl=["P","OL","UL"],Fr=i=>i&&Bl.includes(i.tagName),Ml=i=>{Array.from(i.querySelectorAll("br")).filter(t=>Fr(t.previousElementSibling)&&Fr(t.nextElementSibling)).forEach(t=>{t.parentNode?.removeChild(t)})},Dl=i=>{Array.from(i.querySelectorAll('b[style*="font-weight"]')).filter(t=>t.getAttribute("style")?.match(Rl)).forEach(t=>{const e=i.createDocumentFragment();e.append(...t.childNodes),t.parentNode?.replaceChild(e,t)})};function Ul(i){i.querySelector('[id^="docs-internal-guid-"]')&&(Dl(i),Ml(i))}const Pl=/\bmso-list:[^;]*ignore/i,jl=/\bmso-list:[^;]*\bl(\d+)/i,Hl=/\bmso-list:[^;]*\blevel(\d+)/i,Fl=(i,t)=>{const e=i.getAttribute("style"),s=e?.match(jl);if(!s)return null;const n=Number(s[1]),l=e?.match(Hl),o=l?Number(l[1]):1,c=new RegExp(`@list l${n}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),u=t.match(c),d=u&&u[1]==="bullet"?"bullet":"ordered";return{id:n,indent:o,type:d,element:i}},$l=i=>{const t=Array.from(i.querySelectorAll("[style*=mso-list]")),e=[],s=[];t.forEach(o=>{(o.getAttribute("style")||"").match(Pl)?e.push(o):s.push(o)}),e.forEach(o=>o.parentNode?.removeChild(o));const n=i.documentElement.innerHTML,l=s.map(o=>Fl(o,n)).filter(o=>o);for(;l.length;){const o=[];let c=l.shift();for(;c;)o.push(c),c=l.length&&l[0]?.element===c.element.nextElementSibling&&l[0].id===c.id?l.shift():null;const u=document.createElement("ul");o.forEach(E=>{const g=document.createElement("li");g.setAttribute("data-list",E.type),E.indent>1&&g.setAttribute("class",`ql-indent-${E.indent-1}`),g.innerHTML=E.element.innerHTML,u.appendChild(g)});const d=o[0]?.element,{parentNode:b}=d??{};d&&b?.replaceChild(u,d),o.slice(1).forEach(E=>{let{element:g}=E;b?.removeChild(g)})}};function zl(i){i.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&$l(i)}const Vl=[zl,Ul],Kl=i=>{i.documentElement&&Vl.forEach(t=>{t(i)})},Gl=Qt("quill:clipboard"),Wl=[[Node.TEXT_NODE,oo],[Node.TEXT_NODE,zr],["br",to],[Node.ELEMENT_NODE,zr],[Node.ELEMENT_NODE,Jl],[Node.ELEMENT_NODE,Ql],[Node.ELEMENT_NODE,io],["li",no],["ol, ul",ro],["pre",eo],["tr",lo],["b",Zn("bold")],["i",Zn("italic")],["strike",Zn("strike")],["style",so]],Zl=[Tl,gi].reduce((i,t)=>(i[t.keyName]=t,i),{}),$r=[fi,ur,cr,mi,vi,Ni].reduce((i,t)=>(i[t.keyName]=t,i),{});class Xl extends Vt{static DEFAULTS={matchers:[]};constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",s=>this.onCaptureCopy(s,!1)),this.quill.root.addEventListener("cut",s=>this.onCaptureCopy(s,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Wl.concat(this.options.matchers??[]).forEach(s=>{let[n,l]=s;this.addMatcher(n,l)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:s}=t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(n[Et.blotName])return new B().insert(s||"",{[Et.blotName]:n[Et.blotName]});if(!e)return new B().insert(s||"",n);const l=this.convertHTML(e);return hs(l,`
`)&&(l.ops[l.ops.length-1].attributes==null||n.table)?l.compose(new B().retain(l.length()-1).delete(1)):l}normalizeHTML(t){Kl(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const s=e.body,n=new WeakMap,[l,o]=this.prepareMatching(s,n);return gr(this.quill.scroll,s,l,o,n)}dangerouslyPasteHTML(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:N.sources.API;if(typeof t=="string"){const n=this.convert({html:t,text:""});this.quill.setContents(n,e),this.quill.setSelection(0,N.sources.SILENT)}else{const n=this.convert({html:e,text:""});this.quill.updateContents(new B().retain(t).concat(n),s),this.quill.setSelection(t+n.length(),N.sources.SILENT)}}onCaptureCopy(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[s]=this.quill.selection.getRange();if(s==null)return;const{html:n,text:l}=this.onCopy(s,e);t.clipboardData?.setData("text/plain",l),t.clipboardData?.setData("text/html",n),e&&dr({range:s,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const s=t.clipboardData?.getData("text/html");let n=t.clipboardData?.getData("text/plain");if(!s&&!n){const o=t.clipboardData?.getData("text/uri-list");o&&(n=this.normalizeURIList(o))}const l=Array.from(t.clipboardData?.files||[]);if(!s&&l.length>0){this.quill.uploader.upload(e,l);return}if(s&&l.length>0){const o=new DOMParser().parseFromString(s,"text/html");if(o.body.childElementCount===1&&o.body.firstElementChild?.tagName==="IMG"){this.quill.uploader.upload(e,l);return}}this.onPaste(e,{html:s,text:n})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:s,html:n}=e;const l=this.quill.getFormat(t.index),o=this.convert({text:s,html:n},l);Gl.log("onPaste",o,{text:s,html:n});const c=new B().retain(t.index).delete(t.length).concat(o);this.quill.updateContents(c,N.sources.USER),this.quill.setSelection(c.length()-t.length,N.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const s=[],n=[];return this.matchers.forEach(l=>{const[o,c]=l;switch(o){case Node.TEXT_NODE:n.push(c);break;case Node.ELEMENT_NODE:s.push(c);break;default:Array.from(t.querySelectorAll(o)).forEach(u=>{e.has(u)?e.get(u)?.push(c):e.set(u,[c])});break}}),[s,n]}}function ve(i,t,e,s){return s.query(t)?i.reduce((n,l)=>{if(!l.insert)return n;if(l.attributes&&l.attributes[t])return n.push(l);const o=e?{[t]:e}:{};return n.insert(l.insert,{...o,...l.attributes})},new B):i}function hs(i,t){let e="";for(let s=i.ops.length-1;s>=0&&e.length<t.length;--s){const n=i.ops[s];if(typeof n.insert!="string")break;e=n.insert+e}return e.slice(-1*t.length)===t}function ne(i,t){if(!(i instanceof Element))return!1;const e=t.query(i);return e&&e.prototype instanceof wt?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(i.tagName.toLowerCase())}function Yl(i,t){return i.previousElementSibling&&i.nextElementSibling&&!ne(i.previousElementSibling,t)&&!ne(i.nextElementSibling,t)}const Fs=new WeakMap;function Ai(i){return i==null?!1:(Fs.has(i)||(i.tagName==="PRE"?Fs.set(i,!0):Fs.set(i,Ai(i.parentNode))),Fs.get(i))}function gr(i,t,e,s,n){return t.nodeType===t.TEXT_NODE?s.reduce((l,o)=>o(t,l,i),new B):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((l,o)=>{let c=gr(i,o,e,s,n);return o.nodeType===t.ELEMENT_NODE&&(c=e.reduce((u,d)=>d(o,u,i),c),c=(n.get(o)||[]).reduce((u,d)=>d(o,u,i),c)),l.concat(c)},new B):new B}function Zn(i){return(t,e,s)=>ve(e,i,!0,s)}function Ql(i,t,e){const s=zt.keys(i),n=Ut.keys(i),l=le.keys(i),o={};return s.concat(n).concat(l).forEach(c=>{let u=e.query(c,M.ATTRIBUTE);u!=null&&(o[u.attrName]=u.value(i),o[u.attrName])||(u=Zl[c],u!=null&&(u.attrName===c||u.keyName===c)&&(o[u.attrName]=u.value(i)||void 0),u=$r[c],u!=null&&(u.attrName===c||u.keyName===c)&&(u=$r[c],o[u.attrName]=u.value(i)||void 0))}),Object.entries(o).reduce((c,u)=>{let[d,b]=u;return ve(c,d,b,e)},t)}function Jl(i,t,e){const s=e.query(i);if(s==null)return t;if(s.prototype instanceof wt){const n={},l=s.value(i);if(l!=null)return n[s.blotName]=l,new B().insert(n,s.formats(i,e))}else if(s.prototype instanceof as&&!hs(t,`
`)&&t.insert(`
`),"blotName"in s&&"formats"in s&&typeof s.formats=="function")return ve(t,s.blotName,s.formats(i,e),e);return t}function to(i,t){return hs(t,`
`)||t.insert(`
`),t}function eo(i,t,e){const s=e.query("code-block"),n=s&&"formats"in s&&typeof s.formats=="function"?s.formats(i,e):!0;return ve(t,"code-block",n,e)}function so(){return new B}function no(i,t,e){const s=e.query(i);if(s==null||s.blotName!=="list"||!hs(t,`
`))return t;let n=-1,l=i.parentNode;for(;l!=null;)["OL","UL"].includes(l.tagName)&&(n+=1),l=l.parentNode;return n<=0?t:t.reduce((o,c)=>c.insert?c.attributes&&typeof c.attributes.indent=="number"?o.push(c):o.insert(c.insert,{indent:n,...c.attributes||{}}):o,new B)}function ro(i,t,e){const s=i;let n=s.tagName==="OL"?"ordered":"bullet";const l=s.getAttribute("data-checked");return l&&(n=l==="true"?"checked":"unchecked"),ve(t,"list",n,e)}function zr(i,t,e){if(!hs(t,`
`)){if(ne(i,e)&&(i.childNodes.length>0||i instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&i.nextSibling){let s=i.nextSibling;for(;s!=null;){if(ne(s,e))return t.insert(`
`);const n=e.query(s);if(n&&n.prototype instanceof St)return t.insert(`
`);s=s.firstChild}}}return t}function io(i,t,e){const s={},n=i.style||{};return n.fontStyle==="italic"&&(s.italic=!0),n.textDecoration==="underline"&&(s.underline=!0),n.textDecoration==="line-through"&&(s.strike=!0),(n.fontWeight?.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(s.bold=!0),t=Object.entries(s).reduce((l,o)=>{let[c,u]=o;return ve(l,c,u,e)},t),parseFloat(n.textIndent||0)>0?new B().insert("	").concat(t):t}function lo(i,t,e){const s=i.parentElement?.tagName==="TABLE"?i.parentElement:i.parentElement?.parentElement;if(s!=null){const l=Array.from(s.querySelectorAll("tr")).indexOf(i)+1;return ve(t,"table",l,e)}return t}function oo(i,t,e){let s=i.data;if(i.parentElement?.tagName==="O:P")return t.insert(s.trim());if(!Ai(i)){if(s.trim().length===0&&s.includes(`
`)&&!Yl(i,e))return t;s=s.replace(/[^\S\u00a0]/g," "),s=s.replace(/ {2,}/g," "),(i.previousSibling==null&&i.parentElement!=null&&ne(i.parentElement,e)||i.previousSibling instanceof Element&&ne(i.previousSibling,e))&&(s=s.replace(/^ /,"")),(i.nextSibling==null&&i.parentElement!=null&&ne(i.parentElement,e)||i.nextSibling instanceof Element&&ne(i.nextSibling,e))&&(s=s.replace(/ $/,"")),s=s.replaceAll(" "," ")}return t.insert(s)}class ao extends Vt{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,e){super(t,e),this.quill.on(N.events.EDITOR_CHANGE,(s,n,l,o)=>{s===N.events.SELECTION_CHANGE?n&&o!==N.sources.SILENT&&(this.currentRange=n):s===N.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||o===N.sources.USER?this.record(n,l):this.transform(n)),this.currentRange=nr(this.currentRange,n))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",s=>{s.inputType==="historyUndo"?(this.undo(),s.preventDefault()):s.inputType==="historyRedo"&&(this.redo(),s.preventDefault())})}change(t,e){if(this.stack[t].length===0)return;const s=this.stack[t].pop();if(!s)return;const n=this.quill.getContents(),l=s.delta.invert(n);this.stack[e].push({delta:l,range:nr(s.range,l)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(s.delta,N.sources.USER),this.ignoreChange=!1,this.restoreSelection(s)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,e){if(t.ops.length===0)return;this.stack.redo=[];let s=t.invert(e),n=this.currentRange;const l=Date.now();if(this.lastRecorded+this.options.delay>l&&this.stack.undo.length>0){const o=this.stack.undo.pop();o&&(s=s.compose(o.delta),n=o.range)}else this.lastRecorded=l;s.length()!==0&&(this.stack.undo.push({delta:s,range:n}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){Vr(this.stack.undo,t),Vr(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,N.sources.USER);else{const e=uo(this.quill.scroll,t.delta);this.quill.setSelection(e,N.sources.USER)}}}function Vr(i,t){let e=t;for(let s=i.length-1;s>=0;s-=1){const n=i[s];i[s]={delta:e.transform(n.delta,!0),range:n.range&&nr(n.range,e)},e=n.delta.transform(e),i[s].delta.length()===0&&i.splice(s,1)}}function co(i,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(s=>i.query(s,M.BLOCK)!=null):!1}function uo(i,t){const e=t.reduce((n,l)=>n+(l.delete||0),0);let s=t.length()-e;return co(i,t)&&(s-=1),s}function nr(i,t){if(!i)return i;const e=t.transformPosition(i.index),s=t.transformPosition(i.index+i.length);return{index:e,length:s-e}}class xi extends Vt{constructor(t,e){super(t,e),t.root.addEventListener("drop",s=>{s.preventDefault();let n=null;if(document.caretRangeFromPoint)n=document.caretRangeFromPoint(s.clientX,s.clientY);else if(document.caretPositionFromPoint){const o=document.caretPositionFromPoint(s.clientX,s.clientY);n=document.createRange(),n.setStart(o.offsetNode,o.offset),n.setEnd(o.offsetNode,o.offset)}const l=n&&t.selection.normalizeNative(n);if(l){const o=t.selection.normalizedToRange(l);s.dataTransfer?.files&&this.upload(o,s.dataTransfer.files)}})}upload(t,e){const s=[];Array.from(e).forEach(n=>{n&&this.options.mimetypes?.includes(n.type)&&s.push(n)}),s.length>0&&this.options.handler.call(this,t,s)}}xi.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(i,t){if(!this.quill.scroll.query("image"))return;const e=t.map(s=>new Promise(n=>{const l=new FileReader;l.onload=()=>{n(l.result)},l.readAsDataURL(s)}));Promise.all(e).then(s=>{const n=s.reduce((l,o)=>l.insert({image:o}),new B().retain(i.index).delete(i.length));this.quill.updateContents(n,I.sources.USER),this.quill.setSelection(i.index+s.length,I.sources.SILENT)})}};const ho=["insertText","insertReplacementText"];class fo extends Vt{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",s=>{this.handleBeforeInput(s)}),/Android/i.test(navigator.userAgent)||t.on(N.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){dr({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const s=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new B().retain(t.index).insert(e,s),N.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,N.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!ho.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const s=go(t);if(s==null)return;const n=this.quill.selection.normalizeNative(e),l=n?this.quill.selection.normalizedToRange(n):null;l&&this.replaceText(l,s)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function go(i){return typeof i.data=="string"?i.data:i.dataTransfer?.types.includes("text/plain")?i.dataTransfer.getData("text/plain"):null}const po=/Mac/i.test(navigator.platform),mo=100,bo=i=>!!(i.key==="ArrowLeft"||i.key==="ArrowRight"||i.key==="ArrowUp"||i.key==="ArrowDown"||i.key==="Home"||po&&i.key==="a"&&i.ctrlKey===!0);class yo extends Vt{isListening=!1;selectionChangeDeadline=0;constructor(t,e){super(t,e),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(t,e){let{line:s,event:n}=e;if(!(s instanceof Mt)||!s.uiNode)return!0;const l=getComputedStyle(s.domNode).direction==="rtl";return l&&n.key!=="ArrowRight"||!l&&n.key!=="ArrowLeft"?!0:(this.quill.setSelection(t.index-1,t.length+(n.shiftKey?1:0),N.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",t=>{!t.defaultPrevented&&bo(t)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+mo,this.isListening)return;this.isListening=!0;const t=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",t,{once:!0})}handleSelectionChange(){const t=document.getSelection();if(!t)return;const e=t.getRangeAt(0);if(e.collapsed!==!0||e.startOffset!==0)return;const s=this.quill.scroll.find(e.startContainer);if(!(s instanceof Mt)||!s.uiNode)return;const n=document.createRange();n.setStartAfter(s.uiNode),n.setEndAfter(s.uiNode),t.removeAllRanges(),t.addRange(n)}}N.register({"blots/block":ft,"blots/block/embed":St,"blots/break":Pt,"blots/container":be,"blots/cursor":Rt,"blots/embed":or,"blots/inline":vt,"blots/scroll":wl,"blots/text":Dt,"modules/clipboard":Xl,"modules/history":ao,"modules/keyboard":Zs,"modules/uploader":xi,"modules/input":fo,"modules/uiNode":yo});class vo extends Ut{add(t,e){let s=0;if(e==="+1"||e==="-1"){const n=this.value(t)||0;s=e==="+1"?n+1:n-1}else typeof e=="number"&&(s=e);return s===0?(this.remove(t),!0):super.add(t,s.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const Eo=new vo("indent","ql-indent",{scope:M.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class No extends ft{static blotName="blockquote";static tagName="blockquote"}class Ao extends ft{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}}class fs extends be{}fs.blotName="list-container";fs.tagName="OL";class ds extends ft{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){N.register(fs)}constructor(t,e){super(t,e);const s=e.ownerDocument.createElement("span"),n=l=>{if(!t.isEnabled())return;const o=this.statics.formats(e,t);o==="checked"?(this.format("list","unchecked"),l.preventDefault()):o==="unchecked"&&(this.format("list","checked"),l.preventDefault())};s.addEventListener("mousedown",n),s.addEventListener("touchstart",n),this.attachUI(s)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}ds.blotName="list";ds.tagName="LI";fs.allowedChildren=[ds];ds.requiredContainer=fs;class pr extends vt{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}class xo extends pr{static blotName="italic";static tagName=["EM","I"]}class zs extends vt{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return wi(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}function wi(i,t){const e=document.createElement("a");e.href=i;const s=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(s)>-1}class wo extends vt{static blotName="script";static tagName=["SUB","SUP"];static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}class To extends pr{static blotName="strike";static tagName=["S","STRIKE"]}class Lo extends vt{static blotName="underline";static tagName="U"}class qo extends or{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}const Kr=["alt","height","width"];class So extends wt{static blotName="image";static tagName="IMG";static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Kr.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return wi(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){Kr.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}const Gr=["height","width"];class ko extends St{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Gr.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static sanitize(t){return zs.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Gr.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}const ls=new Ut("code-token","hljs",{scope:M.INLINE});class Yt extends vt{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(Et.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,s){super(t,e,s),ls.add(this.domNode,s)}format(t,e){t!==Yt.blotName?super.format(t,e):e?ls.add(this.domNode,e):(ls.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),ls.value(this.domNode)||this.unwrap()}}Yt.blotName="code-token";Yt.className="ql-token";class qt extends Et{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),Yt.blotName,!1),super.replaceWith(t,e)}}class os extends ye{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===qt.blotName&&(this.forceNext=!0,this.children.forEach(s=>{s.format(t,e)}))}formatAt(t,e,s,n){s===qt.blotName&&(this.forceNext=!0),super.formatAt(t,e,s,n)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const n=`${Array.from(this.domNode.childNodes).filter(o=>o!==this.uiNode).map(o=>o.textContent).join(`
`)}
`,l=qt.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==n){if(n.trim().length>0||this.cachedText==null){const o=this.children.reduce((u,d)=>u.concat(ci(d,!1)),new B),c=t(n,l);o.diff(c).reduce((u,d)=>{let{retain:b,attributes:E}=d;return b?(E&&Object.keys(E).forEach(g=>{[qt.blotName,Yt.blotName].includes(g)&&this.formatAt(u,b,g,E[g])}),u+b):u},0)}this.cachedText=n,this.forceNext=!1}}html(t,e){const[s]=this.children.find(t);return`<pre data-language="${s?qt.formats(s.domNode):"plain"}">
${Ws(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=qt.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}os.allowedChildren=[qt];qt.requiredContainer=os;qt.allowedChildren=[Yt,Rt,Dt,Pt];const Co=(i,t,e)=>{if(typeof i.versionString=="string"){const s=i.versionString.split(".")[0];if(parseInt(s,10)>=11)return i.highlight(e,{language:t}).value}return i.highlight(t,e).value};class Ti extends Vt{static register(){N.register(Yt,!0),N.register(qt,!0),N.register(os,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((s,n)=>{let{key:l}=n;return s[l]=!0,s},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(N.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof os))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(s=>{let{key:n,label:l}=s;const o=e.ownerDocument.createElement("option");o.textContent=l,o.setAttribute("value",n),e.appendChild(o)}),e.addEventListener("change",()=>{t.format(qt.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=qt.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(N.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(N.sources.USER);const s=this.quill.getSelection();(t==null?this.quill.scroll.descendants(os):[t]).forEach(l=>{l.highlight(this.highlightBlot,e)}),this.quill.update(N.sources.SILENT),s!=null&&this.quill.setSelection(s,N.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return Ws(t).split(`
`).reduce((n,l,o)=>(o!==0&&n.insert(`
`,{[Et.blotName]:e}),n.insert(l)),new B);const s=this.quill.root.ownerDocument.createElement("div");return s.classList.add(Et.className),s.innerHTML=Co(this.options.hljs,e,t),gr(this.quill.scroll,s,[(n,l)=>{const o=ls.value(n);return o?l.compose(new B().retain(l.length(),{[Yt.blotName]:o})):l}],[(n,l)=>n.data.split(`
`).reduce((o,c,u)=>(u!==0&&o.insert(`
`,{[Et.blotName]:e}),o.insert(c)),l)],new WeakMap)}}Ti.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};class Bt extends ft{static blotName="table";static tagName="TD";static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",mr()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===Bt.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}class me extends be{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),s=this.next.children.head.formats(),n=this.next.children.tail.formats();return t.table===e.table&&t.table===s.table&&t.table===n.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const s=e.formats(),n=e.next.formats();if(s.table!==n.table){const l=this.splitAfter(e);l&&l.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}class ie extends be{static blotName="table-body";static tagName="TBODY"}class Vs extends be{static blotName="table-container";static tagName="TABLE";balanceCells(){const t=this.descendants(me),e=t.reduce((s,n)=>Math.max(n.children.length,s),0);t.forEach(s=>{new Array(e-s.children.length).fill(0).forEach(()=>{let n;s.children.head!=null&&(n=Bt.formats(s.children.head.domNode));const l=this.scroll.create(Bt.blotName,n);s.appendChild(l),l.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(ie);e==null||e.children.head==null||e.children.forEach(s=>{const n=s.children.at(t);n?.remove()})}insertColumn(t){const[e]=this.descendant(ie);e==null||e.children.head==null||e.children.forEach(s=>{const n=s.children.at(t),l=Bt.formats(s.children.head.domNode),o=this.scroll.create(Bt.blotName,l);s.insertBefore(o,n)})}insertRow(t){const[e]=this.descendant(ie);if(e==null||e.children.head==null)return;const s=mr(),n=this.scroll.create(me.blotName);e.children.head.children.forEach(()=>{const o=this.scroll.create(Bt.blotName,s);n.appendChild(o)});const l=e.children.at(t);e.insertBefore(n,l)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}Vs.allowedChildren=[ie];ie.requiredContainer=Vs;ie.allowedChildren=[me];me.requiredContainer=ie;me.allowedChildren=[Bt];Bt.requiredContainer=me;function mr(){return`row-${Math.random().toString(36).slice(2,6)}`}class Oo extends Vt{static register(){N.register(Bt),N.register(me),N.register(ie),N.register(Vs)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Vs).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(N.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(N.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(N.sources.USER),this.quill.setSelection(e,N.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,s]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==Bt.blotName)return[null,null,null,-1];const n=e.parent;return[n.parent.parent,n,e,s]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[s,n,l]=this.getTable(e);if(l==null)return;const o=l.cellOffset();s.insertColumn(o+t),this.quill.update(N.sources.USER);let c=n.rowOffset();t===0&&(c+=1),this.quill.setSelection(e.index+c,e.length,N.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[s,n,l]=this.getTable(e);if(l==null)return;const o=n.rowOffset();s.insertRow(o+t),this.quill.update(N.sources.USER),t>0?this.quill.setSelection(e,N.sources.SILENT):this.quill.setSelection(e.index+n.children.length,e.length,N.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const s=this.quill.getSelection();if(s==null)return;const n=new Array(t).fill(0).reduce(l=>{const o=new Array(e).fill(`
`).join("");return l.insert(o,{table:mr()})},new B().retain(s.index));this.quill.updateContents(n,N.sources.USER),this.quill.setSelection(s.index,N.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(N.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(N.events.TEXT_CHANGE,(s,n,l)=>{l===N.sources.USER&&this.balanceTables()}),!0):!1)})}}const Wr=Qt("quill:toolbar");class br extends Vt{constructor(t,e){if(super(t,e),Array.isArray(this.options.container)){const s=document.createElement("div");s.setAttribute("role","toolbar"),_o(s,this.options.container),t.container?.parentNode?.insertBefore(s,t.container),this.container=s}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){Wr.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(s=>{const n=this.options.handlers?.[s];n&&this.addHandler(s,n)}),Array.from(this.container.querySelectorAll("button, select")).forEach(s=>{this.attach(s)}),this.quill.on(N.events.EDITOR_CHANGE,()=>{const[s]=this.quill.selection.getRange();this.update(s)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(n=>n.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){Wr.warn("ignoring attaching to nonexistent format",e,t);return}const s=t.tagName==="SELECT"?"change":"click";t.addEventListener(s,n=>{let l;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const c=t.options[t.selectedIndex];c.hasAttribute("selected")?l=!1:l=c.value||!1}else t.classList.contains("ql-active")?l=!1:l=t.value||!t.hasAttribute("value"),n.preventDefault();this.quill.focus();const[o]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,l);else if(this.quill.scroll.query(e).prototype instanceof wt){if(l=prompt(`Enter ${e}`),!l)return;this.quill.updateContents(new B().retain(o.index).delete(o.length).insert({[e]:l}),N.sources.USER)}else this.quill.format(e,l,N.sources.USER);this.update(o)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(s=>{const[n,l]=s;if(l.tagName==="SELECT"){let o=null;if(t==null)o=null;else if(e[n]==null)o=l.querySelector("option[selected]");else if(!Array.isArray(e[n])){let c=e[n];typeof c=="string"&&(c=c.replace(/"/g,'\\"')),o=l.querySelector(`option[value="${c}"]`)}o==null?(l.value="",l.selectedIndex=-1):o.selected=!0}else if(t==null)l.classList.remove("ql-active"),l.setAttribute("aria-pressed","false");else if(l.hasAttribute("value")){const o=e[n],c=o===l.getAttribute("value")||o!=null&&o.toString()===l.getAttribute("value")||o==null&&!l.getAttribute("value");l.classList.toggle("ql-active",c),l.setAttribute("aria-pressed",c.toString())}else{const o=e[n]!=null;l.classList.toggle("ql-active",o),l.setAttribute("aria-pressed",o.toString())}})}}br.DEFAULTS={};function Zr(i,t,e){const s=document.createElement("button");s.setAttribute("type","button"),s.classList.add(`ql-${t}`),s.setAttribute("aria-pressed","false"),e!=null?(s.value=e,s.setAttribute("aria-label",`${t}: ${e}`)):s.setAttribute("aria-label",t),i.appendChild(s)}function _o(i,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const s=document.createElement("span");s.classList.add("ql-formats"),e.forEach(n=>{if(typeof n=="string")Zr(s,n);else{const l=Object.keys(n)[0],o=n[l];Array.isArray(o)?Io(s,l,o):Zr(s,l,o)}}),i.appendChild(s)})}function Io(i,t,e){const s=document.createElement("select");s.classList.add(`ql-${t}`),e.forEach(n=>{const l=document.createElement("option");n!==!1?l.setAttribute("value",String(n)):l.setAttribute("selected","selected"),s.appendChild(l)}),i.appendChild(s)}br.DEFAULTS={container:null,handlers:{clean(){const i=this.quill.getSelection();if(i!=null)if(i.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,M.INLINE)!=null&&this.quill.format(e,!1,N.sources.USER)})}else this.quill.removeFormat(i.index,i.length,N.sources.USER)},direction(i){const{align:t}=this.quill.getFormat();i==="rtl"&&t==null?this.quill.format("align","right",N.sources.USER):!i&&t==="right"&&this.quill.format("align",!1,N.sources.USER),this.quill.format("direction",i,N.sources.USER)},indent(i){const t=this.quill.getSelection(),e=this.quill.getFormat(t),s=parseInt(e.indent||0,10);if(i==="+1"||i==="-1"){let n=i==="+1"?1:-1;e.direction==="rtl"&&(n*=-1),this.quill.format("indent",s+n,N.sources.USER)}},link(i){i===!0&&(i=prompt("Enter link URL:")),this.quill.format("link",i,N.sources.USER)},list(i){const t=this.quill.getSelection(),e=this.quill.getFormat(t);i==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,N.sources.USER):this.quill.format("list","unchecked",N.sources.USER):this.quill.format("list",i,N.sources.USER)}}};const Ro='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',Bo='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',Mo='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',Do='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',Uo='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',Po='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',jo='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',Ho='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',Xr='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',Fo='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',$o='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',zo='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',Vo='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',Ko='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',Go='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Wo='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Zo='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',Xo='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Yo='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',Qo='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',Jo='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',ta='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',ea='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',sa='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',na='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',ra='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',ia='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',la='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',oa='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',aa='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',ca='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',ua='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',ha='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',us={align:{"":Ro,center:Bo,right:Mo,justify:Do},background:Uo,blockquote:Po,bold:jo,clean:Ho,code:Xr,"code-block":Xr,color:Fo,direction:{"":$o,rtl:zo},formula:Vo,header:{1:Ko,2:Go,3:Wo,4:Zo,5:Xo,6:Yo},italic:Qo,image:Jo,indent:{"+1":ta,"-1":ea},link:sa,list:{bullet:na,check:ra,ordered:ia},script:{sub:la,super:oa},strike:aa,table:ca,underline:ua,video:ha},fa='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let Yr=0;function Qr(i,t){i.setAttribute(t,`${i.getAttribute(t)!=="true"}`)}class Xs{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),Qr(this.label,"aria-expanded"),Qr(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const s=t.getAttribute("value");return s&&e.setAttribute("data-value",s),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",n=>{switch(n.key){case"Enter":this.selectItem(e,!0),n.preventDefault();break;case"Escape":this.escape(),n.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=fa,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${Yr}`,Yr+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const s=this.buildItem(e);t.appendChild(s),e.selected===!0&&this.selectItem(s)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const s=this.container.querySelector(".ql-selected");t!==s&&(s?.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class Li extends Xs{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(s=>{s.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const s=this.label.querySelector(".ql-color-label"),n=t&&t.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=n:s.style.fill=n)}}class qi extends Xs{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(s=>{s.innerHTML=e[s.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const s=t||this.defaultItem;if(s!=null){if(this.label.innerHTML===s.innerHTML)return;this.label.innerHTML=s.innerHTML}}}const da=i=>{const{overflowY:t}=getComputedStyle(i,null);return t!=="visible"&&t!=="clip"};class Si{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,da(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,s=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${s}px`,this.root.classList.remove("ql-flip");const n=this.boundsContainer.getBoundingClientRect(),l=this.root.getBoundingClientRect();let o=0;if(l.right>n.right&&(o=n.right-l.right,this.root.style.left=`${e+o}px`),l.left<n.left&&(o=n.left-l.left,this.root.style.left=`${e+o}px`),l.bottom>n.bottom){const c=l.bottom-l.top,u=t.bottom-t.top+c;this.root.style.top=`${s-u}px`,this.root.classList.add("ql-flip")}return o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const ga=[!1,"center","right","justify"],pa=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],ma=[!1,"serif","monospace"],ba=["1","2","3",!1],ya=["small",!1,"large","huge"];class gs extends je{constructor(t,e){super(t,e);const s=n=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",s);return}this.tooltip!=null&&!this.tooltip.root.contains(n.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(l=>{l.container.contains(n.target)||l.close()})};t.emitter.listenDOM("click",document.body,s)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(s=>{(s.getAttribute("class")||"").split(/\s+/).forEach(l=>{if(l.startsWith("ql-")&&(l=l.slice(3),e[l]!=null))if(l==="direction")s.innerHTML=e[l][""]+e[l].rtl;else if(typeof e[l]=="string")s.innerHTML=e[l];else{const o=s.value||"";o!=null&&e[l][o]&&(s.innerHTML=e[l][o])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(n=>{if(n.classList.contains("ql-align")&&(n.querySelector("option")==null&&ns(n,ga),typeof e.align=="object"))return new qi(n,e.align);if(n.classList.contains("ql-background")||n.classList.contains("ql-color")){const l=n.classList.contains("ql-background")?"background":"color";return n.querySelector("option")==null&&ns(n,pa,l==="background"?"#ffffff":"#000000"),new Li(n,e[l])}return n.querySelector("option")==null&&(n.classList.contains("ql-font")?ns(n,ma):n.classList.contains("ql-header")?ns(n,ba):n.classList.contains("ql-size")&&ns(n,ya)),new Xs(n)});const s=()=>{this.pickers.forEach(n=>{n.update()})};this.quill.on(I.events.EDITOR_CHANGE,s)}}gs.DEFAULTS=re({},je.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let i=this.container.querySelector("input.ql-image[type=file]");i==null&&(i=document.createElement("input"),i.setAttribute("type","file"),i.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),i.classList.add("ql-image"),i.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,i.files),i.value=""}),this.container.appendChild(i)),i.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class ki extends Si{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const s=this.quill.getBounds(this.quill.selection.savedRange);s!=null&&this.position(s),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,I.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,I.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=va(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const s=e.index+e.length;this.quill.insertEmbed(s,this.root.getAttribute("data-mode"),t,I.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(s+1," ",I.sources.USER),this.quill.setSelection(s+2,I.sources.USER)}break}}this.textbox.value="",this.hide()}}function va(i){let t=i.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||i.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=i.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:i}function ns(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(s=>{const n=document.createElement("option");s===e?n.setAttribute("selected","selected"):n.setAttribute("value",String(s)),i.appendChild(n)})}const Ea=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class Na extends ki{static TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join("");constructor(t,e){super(t,e),this.quill.on(I.events.EDITOR_CHANGE,(s,n,l,o)=>{if(s===I.events.SELECTION_CHANGE)if(n!=null&&n.length>0&&o===I.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const c=this.quill.getLines(n.index,n.length);if(c.length===1){const u=this.quill.getBounds(n);u!=null&&this.position(u)}else{const u=c[c.length-1],d=this.quill.getIndex(u),b=Math.min(u.length()-1,n.index+n.length-d),E=this.quill.getBounds(new pe(d,b));E!=null&&this.position(E)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(I.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),s=this.root.querySelector(".ql-tooltip-arrow");return s.style.marginLeft="",e!==0&&(s.style.marginLeft=`${-1*e-s.offsetWidth/2}px`),e}}class Ci extends gs{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Ea),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Na(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),us),this.buildPickers(t.container.querySelectorAll("select"),us))}}Ci.DEFAULTS=re({},gs.DEFAULTS,{modules:{toolbar:{handlers:{link(i){i?this.quill.theme.tooltip.edit():this.quill.format("link",!1,N.sources.USER)}}}}});const Aa=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class xa extends ki{static TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join("");preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",t=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),t.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",t=>{if(this.linkRange!=null){const e=this.linkRange;this.restoreFocus(),this.quill.formatText(e,"link",!1,I.sources.USER),delete this.linkRange}t.preventDefault(),this.hide()}),this.quill.on(I.events.SELECTION_CHANGE,(t,e,s)=>{if(t!=null){if(t.length===0&&s===I.sources.USER){const[n,l]=this.quill.scroll.descendant(zs,t.index);if(n!=null){this.linkRange=new pe(t.index-l,n.length());const o=zs.formats(n.domNode);this.preview.textContent=o,this.preview.setAttribute("href",o),this.show();const c=this.quill.getBounds(this.linkRange);c!=null&&this.position(c);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}class Oi extends gs{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Aa),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),us),this.buildPickers(t.container.querySelectorAll("select"),us),this.tooltip=new xa(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,s)=>{t.handlers.link.call(t,!s.format.link)}))}}Oi.DEFAULTS=re({},gs.DEFAULTS,{modules:{toolbar:{handlers:{link(i){if(i){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:s}=this.quill.theme;s.edit("link",e)}else this.quill.format("link",!1,N.sources.USER)}}}}});N.register({"attributors/attribute/direction":gi,"attributors/class/align":hi,"attributors/class/background":ql,"attributors/class/color":Ll,"attributors/class/direction":pi,"attributors/class/font":yi,"attributors/class/size":Ei,"attributors/style/align":fi,"attributors/style/background":ur,"attributors/style/color":cr,"attributors/style/direction":mi,"attributors/style/font":vi,"attributors/style/size":Ni},!0);N.register({"formats/align":hi,"formats/direction":pi,"formats/indent":Eo,"formats/background":ur,"formats/color":cr,"formats/font":yi,"formats/size":Ei,"formats/blockquote":No,"formats/code-block":Et,"formats/header":Ao,"formats/list":ds,"formats/bold":pr,"formats/code":hr,"formats/italic":xo,"formats/link":zs,"formats/script":wo,"formats/strike":To,"formats/underline":Lo,"formats/formula":qo,"formats/image":So,"formats/video":ko,"modules/syntax":Ti,"modules/table":Oo,"modules/toolbar":br,"themes/bubble":Ci,"themes/snow":Oi,"ui/icons":us,"ui/picker":Xs,"ui/icon-picker":qi,"ui/color-picker":Li,"ui/tooltip":Si},!0);const wa={class:"lb-ueditor"},Ta={__name:"LbUeditor",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:"请输入内容..."},height:{type:String,default:"500px"},destroy:{type:Boolean,default:!1},config:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(i,{expose:t,emit:e}){const s=N.import("formats/size");s.whitelist=["8px","9px","10px","11px","12px","14px","16px","18px","20px","22px","24px","26px","28px","30px","32px","36px","42px","48px","56px","64px","72px"],N.register(s,!0);const n=N.import("formats/font");n.whitelist=["SimSun","SimHei","Microsoft-YaHei","KaiTi","FangSong","Arial","Times-New-Roman","Helvetica","sans-serif","serif","monospace","PingFang-SC","Hiragino-Sans-GB","Source-Han-Sans-CN","Noto-Sans-CJK-SC"],N.register(n,!0);const l=i,o=e,c=Ri();let u=null;const b={...{theme:"snow",placeholder:l.placeholder,modules:{toolbar:[[{header:[1,2,3,4,5,6,!1]}],[{font:["SimSun","SimHei","Microsoft-YaHei","KaiTi","FangSong","Arial","Times-New-Roman","Helvetica","sans-serif","serif","monospace","PingFang-SC","Hiragino-Sans-GB","Source-Han-Sans-CN","Noto-Sans-CJK-SC"]}],[{size:["small",!1,"large","huge","8px","9px","10px","11px","12px","14px","16px","18px","20px","22px","24px","26px","28px","30px","32px","36px","42px","48px","56px","64px","72px"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{align:[]}],["blockquote","code-block"],["link","image"],["clean"]]}},...l.config},E=()=>{if(c.value)try{u=new N(c.value,b);const y=c.value.querySelector(".ql-editor");y&&(y.style.minHeight=l.height),l.modelValue&&(u.root.innerHTML=l.modelValue),u.on("text-change",()=>{const A=u.root.innerHTML,T=u.getText().trim()===""?"":A;o("update:modelValue",T),o("change",T)}),g(),console.log("UEditor初始化完成")}catch(y){console.error("UEditor初始化失败:",y)}},g=()=>{if(!u)return;u.getModule("toolbar");const y=c.value.querySelector(".ql-image");y&&y.addEventListener("click",()=>{const A=document.createElement("input");A.setAttribute("type","file"),A.setAttribute("accept","image/*"),A.style.display="none",A.addEventListener("change",async w=>{const T=w.target.files[0];if(T){if(!T.type.startsWith("image/")){ge.error("请选择图片文件");return}if(T.size>5*1024*1024){ge.error("图片大小不能超过5MB");return}try{const O=new FormData;O.append("multipartFile",T),ge.info("正在上传图片...");const k=await Ii.upload.uploadFile(O,F=>{if(F.total>0){const j=Math.round(F.loaded*100/F.total);console.log(`图片上传进度: ${j}%`)}});if(k.code==="200"&&k.data){const F=u.getSelection(),j=F?F.index:u.getLength(),Y=k.data.url||k.data.file_url||k.data.fileUrl||k.data;Y?(u.insertEmbed(j,"image",Y),u.setSelection(j+1),ge.success("图片上传成功"),console.log("✅ 图片插入成功:",Y)):(ge.error("图片上传成功但未获取到图片地址"),console.error("❌ 图片上传响应数据异常:",k.data))}else ge.error(k.message||k.msg||"图片上传失败"),console.error("❌ 图片上传失败:",k)}catch(O){console.error("❌ 图片上传异常:",O);let k="图片上传失败，请稍后重试";O.response?O.response.status===413?k="图片文件过大，请选择较小的图片":O.response.status===415?k="不支持的图片格式":O.response.status>=500?k="服务器错误，请稍后重试":k=O.response.data?.message||O.response.data?.msg||k:O.request&&(k="网络连接失败，请检查网络后重试"),ge.error(k)}document.body.removeChild(A)}}),document.body.appendChild(A),A.click()})},m=()=>{u&&(u=null)};return vr(()=>l.modelValue,y=>{u&&y!==u.root.innerHTML&&(u.root.innerHTML=y||"")}),vr(()=>l.destroy,y=>{y?m():Er(()=>{E()})}),Bi(()=>{Er(()=>{E()})}),Mi(()=>{l.destroy&&m()}),t({getContent:()=>u?.root.innerHTML||"",setContent:y=>{u&&(u.root.innerHTML=y)},getPlainTxt:()=>u?.getText()||"",focus:()=>u?.focus(),blur:()=>u?.blur(),destroy:m}),(y,A)=>(Ui(),Di("div",wa,[Pi("div",{ref_key:"editorRef",ref:c,class:"editor-container"},null,512)]))}},Ra=_i(Ta,[["__scopeId","data-v-2bbc7b12"]]);export{Ra as L};
