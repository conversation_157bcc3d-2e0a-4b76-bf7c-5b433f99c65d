import{T as ve,L as g}from"./LbButton-BtU4V_Gr.js";import{L as we}from"./LbPage-DnbiQ0Ct.js";import{_ as Ce,a as v}from"./index-C9Xz1oqp.js";import{E as m,q as ke}from"./element-fdzwdDuf.js";import{r as u,X as R,c as G,h as Ve,y as s,Q as t,A as c,I as o,al as f,J as Ne,ar as xe,H as E,z as d,M as _,D as Y,C as Z,O as y,K as ze,P as Pe,a6 as Te}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Le={class:"technician-level"},De={class:"content-container"},Se={class:"search-form-container"},Ie={class:"table-container"},qe=["onClick"],Me=["onClick"],Oe={key:1,class:"child-indent"},Ue={key:0},Re={key:1},Ee={key:0},Fe={key:1},Be={key:0},$e={key:1},Ae={key:0},He={key:1},Je={key:0},Ke={key:1},Qe={key:0},We={key:1},Xe={key:0},je={key:1},Ge={class:"time-column"},Ye={class:"table-operate"},Ze={class:"dialog-footer"},el={class:"dialog-footer"},ll={__name:"TechnicianLevel",setup(al){const P=u(!1),T=u(!1),L=u(!1),D=u([]),S=u([]),I=u(0),F=u(),B=u(),$=u(),C=u(!1),k=u(!1),A=u([]),x=u(new Set),b=R({pageNum:1,pageSize:10}),r=R({id:null,labelName:"",earnestMoney:0,delayTime:0,proportion:0}),n=R({id:null,labelId:null,star:"",scorePeriod:"",fixedPrice:0,comparisonOrder:0,quotationsNum:0}),ee={labelName:[{required:!0,message:"请输入师傅等级名称",trigger:"blur"}]},le={labelId:[{required:!0,message:"请选择师傅等级",trigger:"change"}],star:[{required:!0,message:"请选择星级评价",trigger:"change"}],scorePeriod:[{required:!0,message:"请输入分数区间",trigger:"blur"}]},ae=G(()=>r.id?"编辑师傅等级":"新增师傅等级"),te=G(()=>n.id?"编辑信誉分规则":"新增信誉分规则"),w=async a=>{a&&(b.pageNum=1),P.value=!0;try{const e={pageNum:b.pageNum,pageSize:b.pageSize},i=await v.technician.labelList(e);if(console.log("🏷️ 师傅等级列表数据 (API-V2):",i),i.code===200||i.code==="200"){const h=i.data,O=h.list||[];D.value=O,I.value=h.totalCount||h.total||0,H(),console.log("📊 处理后的数据:",{list:D.value,displayData:S.value,total:I.value,pageNum:h.pageNum,pageSize:h.pageSize})}else console.error("❌ API响应错误:",i),m.error(i.message||i.msg||"获取数据失败")}catch(e){console.error("获取师傅等级列表失败:",e),m.error("获取数据失败")}finally{P.value=!1}},H=()=>{const a=[];D.value.forEach(e=>{const i={...e,level:0,hasChildren:e.creditList&&e.creditList.length>0,children:[]};a.push(i),i.hasChildren&&x.value.has(i.id)&&e.creditList.forEach(h=>{a.push({...h,level:1,hasChildren:!1,labelId:e.id,labelName:e.labelName})})}),S.value=a},J=a=>{x.value.has(a.id)?x.value.delete(a.id):x.value.add(a.id),H()},oe=({row:a})=>a.level===0?"parent-row":"child-row",K=async()=>{try{const a=await v.technician.labelCoachList();a.code==="200"&&(A.value=a.data||[])}catch(a){console.error("获取师傅等级选项失败:",a)}},ne=()=>{w(1)},re=()=>{F.value?.resetFields(),w(1)},ie=()=>{q(),C.value=!0},de=a=>{M(),n.labelId=a.id,K(),k.value=!0},se=async a=>{a.labelId?(M(),n.id=a.id,n.labelId=a.labelId,n.star=a.star,n.scorePeriod=a.scorePeriod,n.fixedPrice=a.fixedPrice,n.comparisonOrder=a.comparisonOrder,n.quotationsNum=a.quotationsNum,K(),k.value=!0):(q(),r.id=a.id,r.labelName=a.labelName,r.earnestMoney=a.earnestMoney,r.delayTime=a.delayTime,r.proportion=a.proportion,C.value=!0)},ue=async a=>{try{await ke.confirm(`确定要删除这个${a.labelId?"信誉分规则":"师傅等级"}吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});let e;a.labelId?e=await v.technician.creditDelete({id:a.id}):e=await v.technician.labelDelete({id:a.id}),e.code==="200"?(m.success("删除成功"),w()):m.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除失败:",e),m.error("删除失败"))}},ce=async()=>{try{await B.value.validate(),T.value=!0;let a;r.id?a=await v.technician.labelEdit(r):a=await v.technician.labelAdd(r),a.code==="200"?(m.success(r.id?"更新成功":"新增成功"),C.value=!1,w()):m.error(a.message||"操作失败")}catch(a){console.error("提交失败:",a),m.error("操作失败")}finally{T.value=!1}},pe=async()=>{try{await $.value.validate(),L.value=!0;let a;n.id?a=await v.technician.creditEdit(n):a=await v.technician.creditAdd(n),a.code==="200"?(m.success(n.id?"更新成功":"新增成功"),k.value=!1,w()):m.error(a.message||"操作失败")}catch(a){console.error("提交失败:",a),m.error("操作失败")}finally{L.value=!1}},me=a=>{b.pageSize=a,Q(1)},Q=a=>{b.pageNum=a,w()},fe=()=>{q()},_e=()=>{M()},q=()=>{r.id=null,r.labelName="",r.earnestMoney=0,r.delayTime=0,r.proportion=0},M=()=>{n.id=null,n.labelId=null,n.star="",n.scorePeriod="",n.fixedPrice=0,n.comparisonOrder=0,n.quotationsNum=0},ye=a=>a?new Date(a).toLocaleDateString("zh-CN"):"",ge=a=>a?new Date(a).toLocaleTimeString("zh-CN",{hour12:!1}):"";return Ve(()=>{w(1)}),(a,e)=>{const i=f("el-form-item"),h=f("el-col"),O=f("el-row"),U=f("el-form"),p=f("el-table-column"),he=f("el-table"),W=f("el-input"),V=f("el-input-number"),X=f("el-dialog"),N=f("el-option"),j=f("el-select"),be=xe("loading");return d(),s("div",Le,[t(ve,{title:"师傅等级管理"}),c("div",De,[c("div",Se,[t(U,{ref_key:"searchFormRef",ref:F,model:b,inline:!0,class:"search-form"},{default:o(()=>[t(O,{gutter:20},{default:o(()=>[t(h,{span:24},{default:o(()=>[t(i,null,{default:o(()=>[t(g,{size:"default",type:"primary",icon:"Search",onClick:ne},{default:o(()=>e[14]||(e[14]=[_(" 搜索 ")])),_:1,__:[14]}),t(g,{size:"default",icon:"RefreshLeft",onClick:re},{default:o(()=>e[15]||(e[15]=[_(" 重置 ")])),_:1,__:[15]}),t(g,{size:"default",type:"primary",icon:"Plus",onClick:ie},{default:o(()=>e[16]||(e[16]=[_(" 新增师傅等级 ")])),_:1,__:[16]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),c("div",Ie,[Ne((d(),E(he,{data:S.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"},"row-key":"id","row-class-name":oe},{default:o(()=>[t(p,{prop:"id",label:"ID",width:"100",align:"center"},{default:o(l=>[c("span",{style:Z({paddingLeft:(l.row.level||0)*20+"px"}),class:Y({"expandable-id":l.row.hasChildren}),onClick:z=>l.row.hasChildren?J(l.row):null},y(l.row.id),15,qe)]),_:1}),t(p,{prop:"labelName",label:"师傅等级","min-width":"150"},{default:o(l=>[c("span",{style:Z({paddingLeft:(l.row.level||0)*20+"px"}),class:"category-name-wrapper"},[l.row.hasChildren?(d(),s("i",{key:0,class:Y(["arrow-icon","el-icon-arrow-right",{expanded:x.value.has(l.row.id)}]),onClick:z=>J(l.row)},null,10,Me)):(d(),s("span",Oe)),_(" "+y(l.row.labelName||l.row.star),1)],4)]),_:1}),t(p,{prop:"earnestMoney",label:"保证金",width:"120"},{default:o(l=>[l.row.earnestMoney!==void 0?(d(),s("span",Ue,y(l.row.earnestMoney),1)):(d(),s("span",Re,"-"))]),_:1}),t(p,{prop:"delayTime",label:"延迟时间(分钟)",width:"140"},{default:o(l=>[l.row.delayTime!==void 0?(d(),s("span",Ee,y(l.row.delayTime),1)):(d(),s("span",Fe,"-"))]),_:1}),t(p,{prop:"proportion",label:"服务费降低比例",width:"140"},{default:o(l=>[l.row.proportion!==void 0?(d(),s("span",Be,y(l.row.proportion)+"%",1)):(d(),s("span",$e,"-"))]),_:1}),t(p,{prop:"scorePeriod",label:"分数区间",width:"120"},{default:o(l=>[l.row.scorePeriod?(d(),s("span",Ae,y(l.row.scorePeriod),1)):(d(),s("span",He,"-"))]),_:1}),t(p,{prop:"fixedPrice",label:"一口价订单数",width:"130"},{default:o(l=>[l.row.fixedPrice!==void 0?(d(),s("span",Je,y(l.row.fixedPrice),1)):(d(),s("span",Ke,"-"))]),_:1}),t(p,{prop:"comparisonOrder",label:"比价订单数",width:"120"},{default:o(l=>[l.row.comparisonOrder!==void 0?(d(),s("span",Qe,y(l.row.comparisonOrder),1)):(d(),s("span",We,"-"))]),_:1}),t(p,{prop:"quotationsNum",label:"报价次数",width:"100"},{default:o(l=>[l.row.quotationsNum!==void 0?(d(),s("span",Xe,y(l.row.quotationsNum),1)):(d(),s("span",je,"-"))]),_:1}),t(p,{prop:"createTime",label:"创建时间","min-width":"120"},{default:o(l=>[c("div",Ge,[c("p",null,y(ye(l.row.createTime)),1),c("p",null,y(ge(l.row.createTime)),1)])]),_:1}),t(p,{label:"操作",width:"200",fixed:"right"},{default:o(l=>[c("div",Ye,[l.row.labelId?ze("",!0):(d(),E(g,{key:0,size:"default",type:"success",onClick:z=>de(l.row)},{default:o(()=>e[17]||(e[17]=[_(" 添加信誉分规则 ")])),_:2,__:[17]},1032,["onClick"])),t(g,{size:"default",type:"primary",onClick:z=>se(l.row)},{default:o(()=>e[18]||(e[18]=[_(" 编辑 ")])),_:2,__:[18]},1032,["onClick"]),t(g,{size:"default",type:"danger",onClick:z=>ue(l.row)},{default:o(()=>e[19]||(e[19]=[_(" 删除 ")])),_:2,__:[19]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[be,P.value]])]),t(we,{page:b.pageNum,"page-size":b.pageSize,total:I.value,onHandleSizeChange:me,onHandleCurrentChange:Q},null,8,["page","page-size","total"])]),t(X,{title:ae.value,modelValue:C.value,"onUpdate:modelValue":e[5]||(e[5]=l=>C.value=l),width:"600px",onClose:fe},{footer:o(()=>[c("span",Ze,[t(g,{onClick:e[4]||(e[4]=l=>C.value=!1)},{default:o(()=>e[21]||(e[21]=[_("取消")])),_:1,__:[21]}),t(g,{type:"primary",onClick:ce,loading:T.value},{default:o(()=>e[22]||(e[22]=[_(" 确定 ")])),_:1,__:[22]},8,["loading"])])]),default:o(()=>[t(U,{ref_key:"formRef",ref:B,model:r,rules:ee,"label-width":"120px"},{default:o(()=>[t(i,{label:"师傅等级名称",prop:"labelName"},{default:o(()=>[t(W,{modelValue:r.labelName,"onUpdate:modelValue":e[0]||(e[0]=l=>r.labelName=l),placeholder:"请输入师傅等级名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"保证金",prop:"earnestMoney"},{default:o(()=>[t(V,{modelValue:r.earnestMoney,"onUpdate:modelValue":e[1]||(e[1]=l=>r.earnestMoney=l),min:0,precision:2,placeholder:"请输入保证金",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"延迟时间(分钟)",prop:"delayTime"},{default:o(()=>[t(V,{modelValue:r.delayTime,"onUpdate:modelValue":e[2]||(e[2]=l=>r.delayTime=l),min:0,placeholder:"请输入订单推送延迟时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"服务费降低比例",prop:"proportion"},{default:o(()=>[t(V,{modelValue:r.proportion,"onUpdate:modelValue":e[3]||(e[3]=l=>r.proportion=l),min:0,max:100,placeholder:"请输入服务费降低比例",style:{width:"100%"}},null,8,["modelValue"]),e[20]||(e[20]=c("span",{style:{"margin-left":"8px",color:"#909399"}},"%",-1))]),_:1,__:[20]})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),t(X,{title:te.value,modelValue:k.value,"onUpdate:modelValue":e[13]||(e[13]=l=>k.value=l),width:"600px",onClose:_e},{footer:o(()=>[c("span",el,[t(g,{onClick:e[12]||(e[12]=l=>k.value=!1)},{default:o(()=>e[23]||(e[23]=[_("取消")])),_:1,__:[23]}),t(g,{type:"primary",onClick:pe,loading:L.value},{default:o(()=>e[24]||(e[24]=[_(" 确定 ")])),_:1,__:[24]},8,["loading"])])]),default:o(()=>[t(U,{ref_key:"creditFormRef",ref:$,model:n,rules:le,"label-width":"140px"},{default:o(()=>[t(i,{label:"师傅等级",prop:"labelId"},{default:o(()=>[t(j,{modelValue:n.labelId,"onUpdate:modelValue":e[6]||(e[6]=l=>n.labelId=l),placeholder:"请选择师傅等级",style:{width:"100%"},size:"default"},{default:o(()=>[(d(!0),s(Pe,null,Te(A.value,l=>(d(),E(N,{key:l.id,label:l.labelName,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"星级评价",prop:"star"},{default:o(()=>[t(j,{modelValue:n.star,"onUpdate:modelValue":e[7]||(e[7]=l=>n.star=l),placeholder:"请选择星级评价",style:{width:"100%"},size:"default"},{default:o(()=>[t(N,{label:"一星",value:"一星"}),t(N,{label:"二星",value:"二星"}),t(N,{label:"三星",value:"三星"}),t(N,{label:"四星",value:"四星"}),t(N,{label:"五星",value:"五星"})]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"分数区间",prop:"scorePeriod"},{default:o(()=>[t(W,{modelValue:n.scorePeriod,"onUpdate:modelValue":e[8]||(e[8]=l=>n.scorePeriod=l),placeholder:"请输入分数区间，如：80,90"},null,8,["modelValue"])]),_:1}),t(i,{label:"一口价订单数",prop:"fixedPrice"},{default:o(()=>[t(V,{modelValue:n.fixedPrice,"onUpdate:modelValue":e[9]||(e[9]=l=>n.fixedPrice=l),min:0,placeholder:"每日可接一口价订单数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"比价订单数",prop:"comparisonOrder"},{default:o(()=>[t(V,{modelValue:n.comparisonOrder,"onUpdate:modelValue":e[10]||(e[10]=l=>n.comparisonOrder=l),min:0,placeholder:"每日可接比价订单数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(i,{label:"报价次数",prop:"quotationsNum"},{default:o(()=>[t(V,{modelValue:n.quotationsNum,"onUpdate:modelValue":e[11]||(e[11]=l=>n.quotationsNum=l),min:0,placeholder:"单个订单可报价次数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},sl=Ce(ll,[["__scopeId","data-v-346bea85"]]);export{sl as default};
