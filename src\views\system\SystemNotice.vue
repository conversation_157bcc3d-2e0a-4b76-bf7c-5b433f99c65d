<!--
  万能通知页面
-->

<template>
  <div class="lb-system-notice">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>万能通知设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="通知模板">
            <el-input v-model="configForm.template" type="textarea" :rows="6" placeholder="请输入通知模板内容" />
          </el-form-item>
          
          <el-form-item label="启用状态">
            <el-radio-group v-model="configForm.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  template: '',
  status: 1
})

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/notice/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    saveLoading.value = true
    
    const response = await fetch('/api/system/notice/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-notice {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}
</style>
