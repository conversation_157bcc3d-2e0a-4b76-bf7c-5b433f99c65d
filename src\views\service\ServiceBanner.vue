<!--
  轮播图管理页面
  基于ServiceDaili.vue模式重构，添加完整的搜索和分页功能
  API: /api/admin/banner/list

  修复说明：
  1. 修复了handleEdit方法中不必要的bannerDetail API调用，改为直接使用列表数据
  2. 添加了防重复调用机制，避免频繁API调用
  3. 优化了状态切换和表单提交的防重复逻辑
  4. 修复了页面初始化时异常触发状态切换API的问题：
     - 统一API返回的状态值格式（0转换为1）
     - 将el-switch改为按钮方式，避免响应式触发问题
     - 添加页面初始化完成标志，防止初始化时的意外调用
-->
<template>
  <div class="service-banner">
    <!-- 顶部导航 -->
    <TopNav title="轮播图管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="不可用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="用户端" :value="1" />
                  <el-option label="师傅端" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增轮播图
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
        <el-table-column prop="id" label="ID" width="60" />

        <el-table-column prop="img" label="轮播图" width="300">
          <template #default="scope">
            <LbImage :src="scope.row.img" width="120" height="50" />
          </template>
        </el-table-column>

        <el-table-column prop="link" label="链接地址" min-width="200">
          <template #default="scope">
            <el-link v-if="scope.row.link" :href="scope.row.link" target="_blank" type="primary">
              {{ scope.row.link }}
            </el-link>
            <span v-else class="no-data">无链接</span>
          </template>
        </el-table-column>

        <el-table-column prop="top" label="排序" width="80" align="center" />

        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'primary' : 'success'" size="default">
              {{ scope.row.type === 1 ? '用户端' : '师傅端' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="-1"
              :loading="scope.row.statusLoading"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="default"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="default"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="轮播图片" prop="img">
          <el-upload
            class="image-upload"
            action="#"
            :auto-upload="false"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :before-upload="beforeImageUpload"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png等图片文件
              </div>
            </template>
          </el-upload>

          <!-- 上传进度显示 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" :show-text="true" />
            <p>上传中... {{ uploadProgress }}%</p>
          </div>
        </el-form-item>

        <el-form-item label="链接地址" prop="link">
          <el-input v-model="form.link" placeholder="请输入链接地址（可选）" />
        </el-form-item>

        <el-form-item label="排序值" prop="top">
          <el-input-number
            v-model="form.top"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :value="1">用户端</el-radio>
            <el-radio :value="2">师傅端</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">可用</el-radio>
            <el-radio :value="-1">不可用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)
const fileList = ref([])
const uploadProgress = ref(0)
const uploading = ref(false)

// 防止频繁调用的标志
const isApiCalling = ref(false)
const isStatusChanging = ref(false)
const isSubmitting = ref(false)
// 页面初始化完成标志，防止初始化时触发状态切换
const isPageInitialized = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  status: null,
  type: null
})

// 编辑表单
const form = reactive({
  id: null,
  img: '',
  link: '',
  top: 0,
  type: 1,
  status: 1
})

// 表单验证规则
const rules = {
  img: [
    { required: true, message: '请上传轮播图片', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑轮播图' : '新增轮播图')

// 方法
const getTableDataList = async (flag) => {
  // 防止频繁调用
  if (isApiCalling.value) {
    console.log('⚠️ API正在调用中，跳过重复请求')
    return
  }

  if (flag) searchForm.pageNum = 1

  isApiCalling.value = true
  loading.value = true

  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    console.log('🔍 轮播图查询参数:', params)

    // 使用整合后的API-V2调用方式
    const result = await api.service.bannerList(params)
    console.log('📋 轮播图列表数据:', result)

    // 处理真实API的响应格式
    if (result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      // 为每个轮播图项添加statusLoading属性，并统一状态值格式
      tableData.value = (data.list || []).map(item => ({
        ...item,
        // 保存原始状态值用于编辑
        originalStatus: item.status,
        // 统一状态值：0转换为1（可用），其他保持原值
        status: item.status === 0 ? 1 : item.status,
        statusLoading: false
      }))
      total.value = data.totalCount || data.total || 0

      console.log('📊 轮播图处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })

      // 数据加载完成后，延迟设置初始化完成标志，避免初始化时触发状态切换
      setTimeout(() => {
        isPageInitialized.value = true
        console.log('✅ 页面初始化完成，状态切换功能已启用')
      }, 100)
    } else {
      console.error('❌ 轮播图API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
    isApiCalling.value = false
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.status = null
  searchForm.type = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()

  // 直接使用列表数据填充表单，避免不必要的API调用
  form.id = row.id
  form.img = row.img || ''
  form.link = row.link || ''
  form.top = row.top || 0
  form.type = row.type || 1
  // 使用原始状态值，确保编辑时状态正确
  form.status = row.originalStatus !== undefined ? row.originalStatus : row.status

  // 如果有图片，设置文件列表用于显示
  if (row.img) {
    fileList.value = [{
      name: 'image',
      url: row.img
    }]
  }

  console.log('📝 编辑轮播图，使用列表数据:', {
    id: form.id,
    img: form.img,
    link: form.link,
    top: form.top,
    type: form.type,
    status: form.status
  })

  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个轮播图吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 使用整合后的API-V2调用方式
    const result = await api.service.bannerDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换处理
const handleStatusChange = async (row) => {
  // 防止重复调用
  if (row.statusLoading) {
    return
  }

  // 记录原始状态
  const originalStatus = row.status === 1 ? -1 : 1

  try {
    // 设置加载状态
    row.statusLoading = true

    console.log(`🔄 切换轮播图状态: ID=${row.id}, 当前状态=${row.status}`)

    // 调用状态切换API
    const result = await api.service.bannerStatus({
      id: row.id,
      status: row.status
    })

    console.log('📊 状态切换API响应:', result)

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
      console.log(`✅ 状态切换成功: ID=${row.id}, 新状态=${row.status}`)
      // 刷新列表以确保数据同步
      await getTableDataList()
    } else {
      // 恢复原状态
      row.status = originalStatus
      console.error('❌ 状态切换失败:', result)
      ElMessage.error(result.message || result.msg || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = originalStatus
    console.error('❌ 状态切换异常:', error)
    ElMessage.error('状态修改失败')
  } finally {
    // 清除加载状态
    row.statusLoading = false
  }
}

const handleSubmit = async () => {
  // 防止重复提交
  if (isSubmitting.value) {
    console.log('⚠️ 表单正在提交中，跳过重复请求')
    return
  }

  try {
    await formRef.value.validate()

    submitLoading.value = true
    isSubmitting.value = true

    let result
    if (form.id) {
      // 编辑轮播图，使用整合后的API-V2调用方式
      result = await api.service.bannerUpdate(form)
      console.log('✏️ 编辑轮播图提交:', form)
    } else {
      // 新增轮播图，使用整合后的API-V2调用方式
      result = await api.service.bannerAdd(form)
      console.log('➕ 新增轮播图提交:', form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
    isSubmitting.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  console.log('📋 图片上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  console.log('✅ 图片验证通过')
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fileList) => {
  console.log('🖼️ 图片文件变更:', file, fileList)

  if (file.status === 'ready') {
    // 文件准备上传，开始上传流程
    await uploadImage(file)
  }
}

// 图片移除处理
const handleImageRemove = (file, fileList) => {
  console.log('🗑️ 移除图片:', file)
  form.img = ''
  uploadProgress.value = 0
}

// 执行图片上传
const uploadImage = async (file) => {
  console.log('📤 开始上传图片:', file)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      // 更新上传进度
      uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', uploadProgress.value + '%')
    })

    console.log('✅ 图片上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // 上传成功，保存文件URL到表单
      form.img = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('图片上传成功')

      // 更新文件列表显示
      fileList.value = [{
        name: file.name,
        url: form.img,
        status: 'success'
      }]

      console.log('💾 图片URL已保存到表单:', form.img)
    } else {
      throw new Error(result.message || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))

    // 清理失败的文件
    fileList.value = []
    form.img = ''
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.img = ''
  form.link = ''
  form.top = 0
  form.type = 1
  form.status = 1
  fileList.value = []
  uploadProgress.value = 0
  uploading.value = false
}

// 生命周期
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* 页面主体样式 */
.service-banner {
  padding: 0px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
}

/* 上传组件样式 */
.image-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.image-upload :deep(.el-upload__tip) {
  font-size: 18px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 18px;
  color: #606266;
  text-align: center;
}

.no-data {
  color: #999;
  font-size: 14px;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .table-operate {
    flex-direction: column;
    gap: 2px;
  }
}
</style>
