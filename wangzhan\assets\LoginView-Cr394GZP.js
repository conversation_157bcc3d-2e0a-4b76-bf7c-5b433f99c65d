import{r as f,X as y,h as V,ax as k,az as L,y as R,A as t,aE as E,Q as s,I as r,a4 as M,al as n,ay as N,z as q,u as _,M as z,O as B}from"./vendor-DmFBDimT.js";import{u as C,l as F,E as v}from"./element-fdzwdDuf.js";import{_ as I}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const S="/logo.jpg",K={class:"login-container"},U={class:"login-box"},j={__name:"LoginView",setup(A){const c=k(),p=L(),b=N(),i=f(),l=f(!1),a=y({username:"admin",password:"admin1118",rememberMe:!1}),h={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在 2 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},m=async()=>{if(i.value)try{await i.value.validate(),l.value=!0,await c.dispatch("auth/login",a),v.success("登录成功");const o=b.query.redirect||"/service/list";console.log("🚀 登录成功，准备跳转到:",o),await p.push(o),console.log("✅ 页面跳转完成")}catch(o){console.error("登录失败:",o),v.error(o.message||"登录失败")}finally{l.value=!1}};return V(()=>{c.getters["auth/isLoggedIn"]&&p.push("/")}),(o,e)=>{const g=n("el-input"),d=n("el-form-item"),w=n("el-button"),x=n("el-form");return q(),R("div",K,[t("div",U,[e[2]||(e[2]=E('<div class="login-header" data-v-278c4fdb><div class="logo" data-v-278c4fdb><img src="'+S+'" alt="Logo" data-v-278c4fdb></div><h1 class="title" data-v-278c4fdb>今师傅</h1><p class="subtitle" data-v-278c4fdb>今师傅后台管理系统</p></div>',1)),s(x,{ref_key:"loginFormRef",ref:i,model:a,rules:h,class:"login-form",size:"large",onKeyup:M(m,["enter"])},{default:r(()=>[s(d,{prop:"username"},{default:r(()=>[s(g,{modelValue:a.username,"onUpdate:modelValue":e[0]||(e[0]=u=>a.username=u),placeholder:"请输入用户名","prefix-icon":_(C),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),s(d,{prop:"password"},{default:r(()=>[s(g,{modelValue:a.password,"onUpdate:modelValue":e[1]||(e[1]=u=>a.password=u),type:"password",placeholder:"请输入密码","prefix-icon":_(F),"show-password":"",clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),s(d,null,{default:r(()=>[s(w,{type:"primary",class:"login-button",loading:l.value,onClick:m},{default:r(()=>[z(B(l.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),e[3]||(e[3]=t("div",{class:"login-bg"},[t("div",{class:"bg-shape shape-1"}),t("div",{class:"bg-shape shape-2"}),t("div",{class:"bg-shape shape-3"})],-1))])}}},X=I(j,[["__scopeId","data-v-278c4fdb"]]);export{X as default};
