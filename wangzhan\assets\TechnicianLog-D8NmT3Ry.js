import{T as H,L as v}from"./LbButton-BtU4V_Gr.js";import{L as O}from"./LbPage-DnbiQ0Ct.js";import{_ as P}from"./index-C9Xz1oqp.js";import{E as y}from"./element-fdzwdDuf.js";import{g as U,r as u,X as $,h as j,y as A,Q as e,A as _,I as a,al as n,J,ar as Q,H as W,z as x,M as p,O as m}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const X={class:"page-container"},q={class:"content-container"},G={class:"search-form-container"},K={class:"table-container"},Y={__name:"TechnicianLog",setup(Z){const{proxy:N}=U(),d=u(!1),h=u([]),g=u(0),o=$({pageNum:1,pageSize:10,coachName:"",status:null}),b=u(null),z=t=>({"-1":"删除",1:"拉出黑名单",2:"审核通过",4:"审核驳回",5:"拉入黑名单",6:"权限操作"})[t]||"未知",w=t=>({"-1":"danger",1:"success",2:"success",4:"warning",5:"danger",6:"primary"})[t]||"info",i=async()=>{try{d.value=!0,console.log("🔍 开始加载师傅日志列表，参数:",o);const t=await N.$api.technician.coachLog(o);console.log("📋 师傅日志列表响应:",t),t.code==="200"?(h.value=t.data.list||[],g.value=t.data.totalCount||0,console.log(`✅ 师傅日志列表加载成功，共 ${g.value} 条数据`)):y.error(t.msg||"获取师傅日志列表失败")}catch(t){console.error("❌ 加载师傅日志列表失败:",t),y.error("获取师傅日志列表失败")}finally{d.value=!1}},S=()=>{o.pageNum=1,i()},C=()=>{b.value?.resetFields(),Object.assign(o,{pageNum:1,pageSize:10,coachName:"",status:null}),i()},I=t=>{o.pageSize=t,o.pageNum=1,i()},L=t=>{o.pageNum=t,i()};return j(()=>{console.log("🚀 师傅日志管理页面初始化"),i()}),(t,s)=>{const T=n("el-input"),f=n("el-form-item"),r=n("el-option"),V=n("el-select"),D=n("el-col"),k=n("el-row"),M=n("el-form"),c=n("el-table-column"),B=n("el-tag"),F=n("el-tooltip"),R=n("el-table"),E=Q("loading");return x(),A("div",X,[e(H,{title:"师傅日志管理"}),_("div",q,[_("div",G,[e(M,{ref_key:"searchFormRef",ref:b,model:o,inline:!0,class:"search-form"},{default:a(()=>[e(k,{gutter:20},{default:a(()=>[e(D,{span:24},{default:a(()=>[e(f,{label:"师傅姓名",prop:"coachName"},{default:a(()=>[e(T,{size:"default",modelValue:o.coachName,"onUpdate:modelValue":s[0]||(s[0]=l=>o.coachName=l),placeholder:"请输入师傅姓名，支持模糊查询",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(f,{label:"操作状态",prop:"status"},{default:a(()=>[e(V,{size:"default",modelValue:o.status,"onUpdate:modelValue":s[1]||(s[1]=l=>o.status=l),placeholder:"请选择操作状态",clearable:"",style:{width:"180px"}},{default:a(()=>[e(r,{label:"删除",value:-1}),e(r,{label:"拉出黑名单",value:1}),e(r,{label:"审核通过",value:2}),e(r,{label:"审核驳回",value:4}),e(r,{label:"拉入黑名单",value:5}),e(r,{label:"权限操作",value:6})]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(v,{size:"default",type:"primary",icon:"Search",onClick:S,loading:d.value},{default:a(()=>s[2]||(s[2]=[p(" 搜索 ")])),_:1,__:[2]},8,["loading"]),e(v,{size:"default",icon:"RefreshLeft",onClick:C},{default:a(()=>s[3]||(s[3]=[p(" 重置 ")])),_:1,__:[3]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_("div",K,[J((x(),W(R,{data:h.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[e(c,{prop:"id",label:"ID",width:"80",align:"center"}),e(c,{prop:"coachId",label:"师傅ID",width:"100",align:"center"}),e(c,{prop:"coachName",label:"师傅姓名",width:"120"}),e(c,{prop:"userId",label:"用户ID",width:"100",align:"center"}),e(c,{prop:"status",label:"操作状态",width:"120",align:"center"},{default:a(({row:l})=>[e(B,{type:w(l.status)},{default:a(()=>[p(m(z(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"adminId",label:"管理员ID",width:"100",align:"center"},{default:a(({row:l})=>[p(m(l.adminId||"-"),1)]),_:1}),e(c,{prop:"ipv4",label:"IP地址",width:"130"},{default:a(({row:l})=>[p(m(l.ipv4||"-"),1)]),_:1}),e(c,{prop:"text",label:"操作描述","min-width":"200"},{default:a(({row:l})=>[e(F,{content:l.text,placement:"top",disabled:!l.text||l.text.length<=30},{default:a(()=>[_("span",null,m(l.text||"-"),1)]),_:2},1032,["content","disabled"])]),_:1}),e(c,{prop:"createTime",label:"操作时间",width:"160"})]),_:1},8,["data"])),[[E,d.value]])]),e(O,{page:o.pageNum,"page-size":o.pageSize,total:g.value,onHandleSizeChange:I,onHandleCurrentChange:L},null,8,["page","page-size","total"])])])}}},se=P(Y,[["__scopeId","data-v-14f8328b"]]);export{se as default};
