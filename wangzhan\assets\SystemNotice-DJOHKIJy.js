import{T as w,L as x}from"./LbButton-BtU4V_Gr.js";import{_ as N}from"./index-C9Xz1oqp.js";import{E as d}from"./element-fdzwdDuf.js";import{r as f,X as C,h as T,y as h,Q as o,A as c,I as t,al as a,z as j,M as m}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const k={class:"lb-system-notice"},B={class:"page-main"},E={__name:"SystemNotice",setup(F){const l=f(!1),_=f(),s=C({template:"",status:1}),u=async()=>{try{const e=await(await fetch("/api/system/notice/config")).json();e.code===200&&Object.assign(s,e.data||{})}catch(n){console.error("获取配置失败:",n)}},g=async()=>{try{l.value=!0;const e=await(await fetch("/api/system/notice/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();e.code===200?d.success("配置保存成功"):d.error(e.message||"保存失败")}catch{d.error("保存失败")}finally{l.value=!1}};return T(()=>{u()}),(n,e)=>{const y=a("el-input"),r=a("el-form-item"),p=a("el-radio"),v=a("el-radio-group"),b=a("el-form"),V=a("el-card");return j(),h("div",k,[o(w),c("div",B,[o(V,{class:"config-card",shadow:"never"},{header:t(()=>e[2]||(e[2]=[c("div",{class:"card-header"},[c("span",null,"万能通知设置")],-1)])),default:t(()=>[o(b,{model:s,ref_key:"configFormRef",ref:_,"label-width":"140px",class:"config-form"},{default:t(()=>[o(r,{label:"通知模板"},{default:t(()=>[o(y,{modelValue:s.template,"onUpdate:modelValue":e[0]||(e[0]=i=>s.template=i),type:"textarea",rows:6,placeholder:"请输入通知模板内容"},null,8,["modelValue"])]),_:1}),o(r,{label:"启用状态"},{default:t(()=>[o(v,{modelValue:s.status,"onUpdate:modelValue":e[1]||(e[1]=i=>s.status=i)},{default:t(()=>[o(p,{value:1},{default:t(()=>e[3]||(e[3]=[m("启用")])),_:1,__:[3]}),o(p,{value:0},{default:t(()=>e[4]||(e[4]=[m("禁用")])),_:1,__:[4]})]),_:1},8,["modelValue"])]),_:1}),o(r,null,{default:t(()=>[o(x,{type:"primary",onClick:g,loading:l.value},{default:t(()=>e[5]||(e[5]=[m("保存配置")])),_:1,__:[5]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},O=N(E,[["__scopeId","data-v-1eb6be71"]]);export{O as default};
