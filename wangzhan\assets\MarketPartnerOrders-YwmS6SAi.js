import{g as Y,r as m,X as Z,h as ee,ay as te,y as U,Q as e,A as u,I as t,al as d,J as ae,ar as oe,H as le,az as re,z as T,M as n,O as i,K as se}from"./vendor-DmFBDimT.js";import{T as ne,L as g}from"./LbButton-BtU4V_Gr.js";import{L as ie}from"./LbPage-DnbiQ0Ct.js";import{_ as de}from"./index-C9Xz1oqp.js";import{E as x}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const pe={class:"market-partner-orders"},ce={class:"content-container"},ue={class:"back-button-container"},_e={class:"search-form-container"},me={class:"table-container"},fe={class:"pay-amount"},ge={class:"service-price"},ye={key:0},ve={class:"pay-amount"},we={class:"service-price"},he={style:{"margin-top":"20px"}},be={class:"dialog-footer"},Ce={__name:"MarketPartnerOrders",setup(xe){const{proxy:B}=Y(),$=te(),E=re(),N=m(),k=m(!1),I=m(!1),V=m([]),z=m(0),h=m(!1),s=m(null),r=Z({userId:"",type:"",pageNum:1,pageSize:10}),y=async()=>{try{k.value=!0;const a={...r,userId:r.userId||void 0,type:r.type!==""?r.type:void 0},o=await B.$api.market.partnerOrdersList(a);o.code==="200"?(V.value=o.data.list||[],z.value=o.data.totalCount||0):x.error(o.msg||"获取推广订单列表失败")}catch(a){console.error("获取推广订单列表失败:",a),x.error("获取推广订单列表失败")}finally{k.value=!1}},M=()=>{r.pageNum=1,y()},H=()=>{N.value?.resetFields(),Object.assign(r,{userId:"",type:"",pageNum:1,pageSize:10}),y()},A=a=>{s.value=a,h.value=!0},j=async()=>{try{I.value=!0,console.log("📤 开始导出合伙人推广订单");const a={};r.userId&&(a.userId=r.userId),r.type!==""&&(a.type=r.type);const v=`合伙人推广订单_${new Date().toISOString().slice(0,19).replace(/[:-]/g,"")}.xlsx`,w="http://192.168.1.29:8889/ims",b=new URLSearchParams(a),C=localStorage.getItem("token");C&&b.append("token",C);const S=`${w}/api/admin/partner/orders/export?${b.toString()}`,_=document.createElement("a");_.href=S,_.download=v,_.style.display="none",document.body.appendChild(_),_.click(),document.body.removeChild(_),x.success("导出成功"),console.log("✅ 合伙人推广订单导出成功")}catch(a){console.error("❌ 导出失败:",a),x.error("导出失败")}finally{I.value=!1}},q=()=>{E.push("/market/partner")},J=a=>{r.pageSize=a,r.pageNum=1,y()},K=a=>{r.pageNum=a,y()},P=a=>({1:"微信支付",2:"支付宝",7:"余额支付"})[a]||"未知",D=a=>({1:"success",2:"primary",7:"warning"})[a]||"info",L=a=>a===null?"未知":a===1?"已评价":"未评价",F=a=>a===null?"info":a===1?"success":"warning";return ee(()=>{const a=$.query.userId;a&&(r.userId=a,console.log("🔗 从URL参数获取userId:",a)),y()}),(a,o)=>{const O=d("el-input"),v=d("el-form-item"),w=d("el-option"),b=d("el-select"),C=d("el-col"),S=d("el-row"),_=d("el-form"),p=d("el-table-column"),R=d("el-image"),f=d("el-tag"),Q=d("el-table"),c=d("el-descriptions-item"),X=d("el-descriptions"),G=d("el-dialog"),W=oe("loading");return T(),U("div",pe,[e(ne,{title:"合伙人推广订单"}),u("div",ce,[u("div",ue,[e(g,{size:"default",icon:"ArrowLeft",onClick:q},{default:t(()=>o[4]||(o[4]=[n(" 返回合伙人管理 ")])),_:1,__:[4]})]),u("div",_e,[e(_,{ref_key:"searchFormRef",ref:N,model:r,inline:!0,class:"search-form"},{default:t(()=>[e(S,{gutter:20},{default:t(()=>[e(C,{span:24},{default:t(()=>[e(v,{label:"用户ID",prop:"userId"},{default:t(()=>[e(O,{size:"default",modelValue:r.userId,"onUpdate:modelValue":o[0]||(o[0]=l=>r.userId=l),placeholder:"请输入用户ID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(v,{label:"订单类型",prop:"type"},{default:t(()=>[e(b,{size:"default",modelValue:r.type,"onUpdate:modelValue":o[1]||(o[1]=l=>r.type=l),placeholder:"请选择订单类型",clearable:"",style:{width:"140px"}},{default:t(()=>[e(w,{label:"全部",value:0}),e(w,{label:"用户订单",value:1}),e(w,{label:"师傅订单",value:2})]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(g,{type:"primary",onClick:M},{default:t(()=>o[5]||(o[5]=[n(" 搜索 ")])),_:1,__:[5]}),e(g,{onClick:H},{default:t(()=>o[6]||(o[6]=[n(" 重置 ")])),_:1,__:[6]}),e(g,{type:"success",onClick:j,loading:I.value},{default:t(()=>o[7]||(o[7]=[n(" 导出 ")])),_:1,__:[7]},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u("div",me,[ae((T(),le(Q,{data:V.value,stripe:"",border:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[e(p,{prop:"id",label:"订单ID",width:"80",align:"center"}),e(p,{prop:"orderCode",label:"订单号",width:"200",align:"center","show-overflow-tooltip":""}),e(p,{prop:"goodsName",label:"商品名称",width:"150",align:"center","show-overflow-tooltip":""}),e(p,{prop:"goodsCover",label:"商品图片",width:"80",align:"center"},{default:t(l=>[e(R,{style:{width:"40px",height:"40px","border-radius":"4px"},src:l.row.goodsCover,fit:"cover","preview-src-list":[l.row.goodsCover]},null,8,["src","preview-src-list"])]),_:1}),e(p,{prop:"nickName",label:"用户昵称",width:"120",align:"center"}),e(p,{prop:"phone",label:"用户手机",width:"130",align:"center"}),e(p,{prop:"payPrice",label:"支付金额",width:"100",align:"center"},{default:t(l=>[u("span",fe,"¥"+i(l.row.payPrice.toFixed(2)),1)]),_:1}),e(p,{prop:"coachServicePrice",label:"服务费",width:"100",align:"center"},{default:t(l=>[u("span",ge,"¥"+i(l.row.coachServicePrice.toFixed(2)),1)]),_:1}),e(p,{prop:"payType",label:"支付方式",width:"100",align:"center"},{default:t(l=>[e(f,{type:D(l.row.payType)},{default:t(()=>[n(i(P(l.row.payType)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"refundStatus",label:"退款状态",width:"100",align:"center"},{default:t(l=>[e(f,{type:l.row.refundStatus===0?"success":"warning"},{default:t(()=>[n(i(l.row.refundStatus===0?"未退款":"已退款"),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"isComment",label:"评价状态",width:"100",align:"center"},{default:t(l=>[e(f,{type:F(l.row.isComment)},{default:t(()=>[n(i(L(l.row.isComment)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"createTime",label:"创建时间",width:"160",align:"center","show-overflow-tooltip":""}),e(p,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(l=>[e(g,{size:"small",type:"primary",onClick:ke=>A(l.row)},{default:t(()=>o[8]||(o[8]=[n(" 查看详情 ")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,k.value]])]),e(ie,{page:r.pageNum,"page-size":r.pageSize,total:z.value,onHandleSizeChange:J,onHandleCurrentChange:K},null,8,["page","page-size","total"])]),e(G,{title:"订单详情",modelValue:h.value,"onUpdate:modelValue":o[3]||(o[3]=l=>h.value=l),width:"800px","close-on-click-modal":!1},{footer:t(()=>[u("span",be,[e(g,{onClick:o[2]||(o[2]=l=>h.value=!1)},{default:t(()=>o[10]||(o[10]=[n("关闭")])),_:1,__:[10]})])]),default:t(()=>[s.value?(T(),U("div",ye,[e(X,{column:2,border:""},{default:t(()=>[e(c,{label:"订单ID"},{default:t(()=>[n(i(s.value.id),1)]),_:1}),e(c,{label:"订单号"},{default:t(()=>[n(i(s.value.orderCode),1)]),_:1}),e(c,{label:"商品名称"},{default:t(()=>[n(i(s.value.goodsName),1)]),_:1}),e(c,{label:"商品ID"},{default:t(()=>[n(i(s.value.goodsId),1)]),_:1}),e(c,{label:"用户昵称"},{default:t(()=>[n(i(s.value.nickName),1)]),_:1}),e(c,{label:"用户手机"},{default:t(()=>[n(i(s.value.phone),1)]),_:1}),e(c,{label:"支付金额"},{default:t(()=>[u("span",ve,"¥"+i(s.value.payPrice.toFixed(2)),1)]),_:1}),e(c,{label:"服务费"},{default:t(()=>[u("span",we,"¥"+i(s.value.coachServicePrice.toFixed(2)),1)]),_:1}),e(c,{label:"支付方式"},{default:t(()=>[e(f,{type:D(s.value.payType)},{default:t(()=>[n(i(P(s.value.payType)),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"退款状态"},{default:t(()=>[e(f,{type:s.value.refundStatus===0?"success":"warning"},{default:t(()=>[n(i(s.value.refundStatus===0?"未退款":"已退款"),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"评价状态"},{default:t(()=>[e(f,{type:F(s.value.isComment)},{default:t(()=>[n(i(L(s.value.isComment)),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"创建时间"},{default:t(()=>[n(i(s.value.createTime),1)]),_:1})]),_:1}),u("div",he,[o[9]||(o[9]=u("h4",null,"商品图片",-1)),e(R,{style:{width:"100px",height:"100px","border-radius":"8px","margin-top":"10px"},src:s.value.goodsCover,fit:"cover","preview-src-list":[s.value.goodsCover]},null,8,["src","preview-src-list"])])])):se("",!0)]),_:1},8,["modelValue"])])}}},Pe=de(Ce,[["__scopeId","data-v-57329b63"]]);export{Pe as default};
