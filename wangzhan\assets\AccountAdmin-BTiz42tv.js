import{T as oe,L as y}from"./LbButton-BtU4V_Gr.js";import{_ as le,a as b}from"./index-C9Xz1oqp.js";import{E as m,q as se}from"./element-fdzwdDuf.js";import{r as c,X as D,h as re,y as _,Q as o,A as z,I as l,al as n,ar as ne,z as d,M as p,J as de,H as w,O as h,K as x,P as T,a6 as U}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ie={class:"lb-account-admin"},ue={class:"page-main"},me={key:1,class:"text-muted"},pe={class:"menu-permissions"},ce={key:0,class:"more-menus"},ge={key:1,class:"text-muted"},fe={class:"table-operate"},_e={class:"pagination-section"},we={__name:"AccountAdmin",setup(ve){const N=c(!1),B=c([]),g=c(!1),u=c("add"),C=c(),A=c(!1),S=c([]),i=D({pageNum:1,pageSize:10,totalCount:0,totalPage:0}),s=D({id:"",username:"",password:"",confirmPassword:"",roleId:""}),$={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{validator:(a,e,r)=>{s.password&&e!==s.password?r(new Error("两次输入密码不一致")):r()},trigger:"blur"}],roleId:[{required:!0,message:"请选择角色",trigger:"change"}]},v=async(a=1)=>{N.value=!0,i.pageNum=a;try{const e={pageNum:i.pageNum,pageSize:i.pageSize},r=await b.admin.getAdminList(e);r.code==="200"?(B.value=r.data.list||[],i.totalCount=r.data.totalCount||0,i.totalPage=r.data.totalPage||0):m.error(r.msg||"获取数据失败")}catch(e){console.error("获取管理员列表失败:",e),m.error("获取数据失败")}finally{N.value=!1}},E=async()=>{try{const a=await b.admin.getAllRoles();a.code==="200"&&(S.value=a.data||[])}catch(a){console.error("获取角色列表失败:",a)}},M=a=>!a||!Array.isArray(a)?[]:a.slice(0,5).sort((e,r)=>e.sort-r.sort),R=a=>a===1?"success":"danger",q=a=>a===1?"启用":"禁用",j=()=>{u.value="add",L(),g.value=!0},O=a=>{u.value="edit";const e=S.value.find(r=>r.roleName===a.roleName);Object.assign(s,{id:a.id,username:a.username,password:"",confirmPassword:"",roleId:e?e.id:""}),g.value=!0},F=async a=>{try{const e=a.status===1?"禁用":"启用";await se.confirm(`确定要${e}管理员 "${a.username}" 吗？`,`${e}确认`,{confirmButtonText:`确定${e}`,cancelButtonText:"取消",type:"warning"});const r=await b.admin.changeAdminStatus(a.id);r.code==="200"?(m.success(`${e}成功`),v()):m.error(r.msg||"操作失败")}catch(e){e!=="cancel"&&(console.error("修改管理员状态失败:",e),m.error("操作失败"))}},H=async()=>{try{await C.value.validate(),A.value=!0;const a={username:s.username,roleId:s.roleId};u.value==="add"&&s.password&&(a.password=s.password),u.value==="edit"&&(a.id=s.id);const e=u.value==="add"?await b.admin.addAdmin(a):await b.admin.editAdmin(a);e.code==="200"?(m.success(u.value==="add"?"新增成功":"编辑成功"),g.value=!1,v()):m.error(e.msg||"操作失败")}catch(a){console.error("提交失败:",a),m.error("操作失败")}finally{A.value=!1}},J=()=>{g.value=!1,L()},L=()=>{Object.assign(s,{id:"",username:"",password:"",confirmPassword:"",roleId:""}),C.value&&C.value.clearValidate()},K=a=>{i.pageSize=a,v(1)},Q=a=>{v(a)};return re(()=>{v(),E()}),(a,e)=>{const r=n("el-row"),f=n("el-table-column"),I=n("el-tag"),X=n("el-table"),G=n("el-card"),W=n("el-pagination"),P=n("el-input"),V=n("el-form-item"),Y=n("el-option"),Z=n("el-select"),ee=n("el-form"),ae=n("el-dialog"),te=ne("loading");return d(),_("div",ie,[o(oe),z("div",ue,[o(r,{class:"page-header"},{default:l(()=>[o(y,{type:"primary",onClick:j},{default:l(()=>e[7]||(e[7]=[p("新增管理员")])),_:1,__:[7]})]),_:1}),o(G,{class:"table-card",shadow:"never"},{default:l(()=>[de((d(),w(X,{data:B.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"},border:""},{default:l(()=>[o(f,{prop:"id",label:"ID",width:"80"}),o(f,{prop:"username",label:"用户名",width:"150"}),o(f,{prop:"roleName",label:"角色",width:"150"},{default:l(t=>[t.row.roleName?(d(),w(I,{key:0,type:"primary",size:"small"},{default:l(()=>[p(h(t.row.roleName),1)]),_:2},1024)):(d(),_("span",me,"未分配角色"))]),_:1}),o(f,{prop:"status",label:"状态",width:"100"},{default:l(t=>[o(I,{type:R(t.row.status),size:"small"},{default:l(()=>[p(h(q(t.row.status)),1)]),_:2},1032,["type"])]),_:1}),o(f,{prop:"menus",label:"权限菜单","min-width":"300"},{default:l(t=>[z("div",pe,[(d(!0),_(T,null,U(M(t.row.menus),k=>(d(),w(I,{key:k.id,size:"small",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:l(()=>[p(h(k.menuName),1)]),_:2},1024))),128)),t.row.menus&&t.row.menus.length>5?(d(),_("span",ce," 等"+h(t.row.menus.length)+"个权限 ",1)):x("",!0),!t.row.menus||t.row.menus.length===0?(d(),_("span",ge," 暂无权限 ")):x("",!0)])]),_:1}),o(f,{label:"操作",width:"180",fixed:"right"},{default:l(t=>[z("div",fe,[o(y,{size:"mini",type:"primary",onClick:k=>O(t.row)},{default:l(()=>e[8]||(e[8]=[p(" 编辑 ")])),_:2,__:[8]},1032,["onClick"]),o(y,{size:"mini",type:t.row.status===1?"danger":"success",onClick:k=>F(t.row)},{default:l(()=>[p(h(t.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])])]),_:1})]),_:1},8,["data"])),[[te,N.value]])]),_:1}),z("div",_e,[o(W,{"current-page":i.pageNum,"onUpdate:currentPage":e[0]||(e[0]=t=>i.pageNum=t),"page-size":i.pageSize,"onUpdate:pageSize":e[1]||(e[1]=t=>i.pageSize=t),"page-sizes":[10,20,50,100],total:i.totalCount,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),o(ae,{modelValue:g.value,"onUpdate:modelValue":e[6]||(e[6]=t=>g.value=t),title:u.value==="add"?"新增管理员":"编辑管理员",width:"40%","close-on-click-modal":!1},{footer:l(()=>[o(y,{onClick:J},{default:l(()=>e[9]||(e[9]=[p("取消")])),_:1,__:[9]}),o(y,{type:"primary",onClick:H,loading:A.value},{default:l(()=>e[10]||(e[10]=[p("确定")])),_:1,__:[10]},8,["loading"])]),default:l(()=>[o(ee,{model:s,rules:$,ref_key:"formRef",ref:C,"label-width":"100px"},{default:l(()=>[o(V,{label:"用户名",prop:"username"},{default:l(()=>[o(P,{modelValue:s.username,"onUpdate:modelValue":e[2]||(e[2]=t=>s.username=t),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),u.value==="add"?(d(),w(V,{key:0,label:"密码",prop:"password"},{default:l(()=>[o(P,{modelValue:s.password,"onUpdate:modelValue":e[3]||(e[3]=t=>s.password=t),type:"password",placeholder:"请输入密码（不填则使用默认密码admin123）","show-password":""},null,8,["modelValue"])]),_:1})):x("",!0),u.value==="add"&&s.password?(d(),w(V,{key:1,label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(P,{modelValue:s.confirmPassword,"onUpdate:modelValue":e[4]||(e[4]=t=>s.confirmPassword=t),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])]),_:1})):x("",!0),o(V,{label:"选择角色",prop:"roleId"},{default:l(()=>[o(Z,{modelValue:s.roleId,"onUpdate:modelValue":e[5]||(e[5]=t=>s.roleId=t),placeholder:"请选择角色",style:{width:"100%"}},{default:l(()=>[(d(!0),_(T,null,U(S.value,t=>(d(),w(Y,{key:t.id,label:t.roleName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},ke=le(we,[["__scopeId","data-v-ab0934f5"]]);export{ke as default};
