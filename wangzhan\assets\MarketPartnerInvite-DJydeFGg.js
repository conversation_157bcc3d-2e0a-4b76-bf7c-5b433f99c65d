import{g as ae,r as m,X as le,h as oe,ay as se,y as v,Q as e,A as l,I as t,al as r,J as ne,ar as re,H,az as ie,z as p,M as i,u as b,O as c,K as R,P as de,a6 as ue}from"./vendor-DmFBDimT.js";import{E as V,u as D,C as ce,D as pe}from"./element-fdzwdDuf.js";import{T as _e,L as w}from"./LbButton-BtU4V_Gr.js";import{L as me}from"./LbPage-DnbiQ0Ct.js";import{_ as fe}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const ve={class:"market-partner-invite"},ge={class:"content-container"},he={class:"back-button-container"},ye={class:"stats-container"},be={class:"stat-card"},we={class:"stat-icon user-icon"},ke={class:"stat-content"},Ie={class:"stat-value"},Ne={class:"stat-card"},Ce={class:"stat-icon shifu-icon"},xe={class:"stat-content"},ze={class:"stat-value"},Ve={class:"stat-card"},De={class:"stat-icon vip-icon"},Se={class:"stat-content"},Le={class:"stat-value"},Ue={class:"search-form-container"},Fe={class:"table-container"},Pe={key:0},Re={key:0,style:{color:"#999","font-size":"12px"}},Be={key:1,style:{color:"#999"}},$e={key:0},Ee={key:0,style:{"margin-top":"20px"}},Me={class:"dialog-footer"},He={__name:"MarketPartnerInvite",setup(Oe){const{proxy:O}=ae(),T=se(),A=ie(),B=m(),S=m(!1),L=m(!1),$=m([]),E=m(0),N=m(!1),d=m(null),C=m({userNum:0,shiFuNum:0,vip:!1}),o=le({userId:"",type:"",pageNum:1,pageSize:10}),k=async()=>{try{S.value=!0;const n={...o,userId:o.userId||void 0,type:o.type!==""?o.type:void 0},a=await O.$api.market.partnerInviteList(n);a.code==="200"?($.value=a.data.pageInfo.list||[],E.value=a.data.pageInfo.totalCount||0,C.value={userNum:a.data.userNum||0,shiFuNum:a.data.shiFuNum||0,vip:a.data.vip||!1}):V.error(a.msg||"获取邀请列表失败")}catch(n){console.error("获取邀请列表失败:",n),V.error("获取邀请列表失败")}finally{S.value=!1}},j=()=>{o.pageNum=1,k()},q=()=>{B.value?.resetFields(),Object.assign(o,{userId:"",type:"",pageNum:1,pageSize:10}),k()},J=n=>{d.value=n,N.value=!0},K=async()=>{try{L.value=!0,console.log("📤 开始导出合伙人邀请列表");const n={};o.userId&&(n.userId=o.userId),o.type!==""&&(n.type=o.type);const g=`合伙人邀请列表_${new Date().toISOString().slice(0,19).replace(/[:-]/g,"")}.xlsx`,x="http://************:8889/ims",z=new URLSearchParams(n),h=localStorage.getItem("token");h&&z.append("token",h);const I=`${x}/api/admin/partner/inviteList/export?${z.toString()}`,_=document.createElement("a");_.href=I,_.download=g,_.style.display="none",document.body.appendChild(_),_.click(),document.body.removeChild(_),V.success("导出成功"),console.log("✅ 合伙人邀请列表导出成功")}catch(n){console.error("❌ 导出失败:",n),V.error("导出失败")}finally{L.value=!1}},Q=()=>{A.push("/market/partner")},X=n=>{o.pageSize=n,o.pageNum=1,k()},G=n=>{o.pageNum=n,k()};return oe(()=>{const n=T.query.userId;n&&(o.userId=n,console.log("🔗 从URL参数获取userId:",n)),k()}),(n,a)=>{const f=r("el-icon"),g=r("el-col"),x=r("el-row"),z=r("el-input"),h=r("el-form-item"),I=r("el-option"),_=r("el-select"),W=r("el-form"),u=r("el-table-column"),U=r("el-avatar"),F=r("el-tag"),M=r("el-table"),y=r("el-descriptions-item"),Y=r("el-descriptions"),Z=r("el-dialog"),ee=re("loading");return p(),v("div",ve,[e(_e,{title:"合伙人邀请列表"}),l("div",ge,[l("div",he,[e(w,{size:"default",icon:"ArrowLeft",onClick:Q},{default:t(()=>a[4]||(a[4]=[i(" 返回合伙人管理 ")])),_:1,__:[4]})]),l("div",ye,[e(x,{gutter:20},{default:t(()=>[e(g,{span:8},{default:t(()=>[l("div",be,[l("div",we,[e(f,null,{default:t(()=>[e(b(D))]),_:1})]),l("div",ke,[l("div",Ie,c(C.value.userNum||0),1),a[5]||(a[5]=l("div",{class:"stat-label"},"用户数量",-1))])])]),_:1}),e(g,{span:8},{default:t(()=>[l("div",Ne,[l("div",Ce,[e(f,null,{default:t(()=>[e(b(ce))]),_:1})]),l("div",xe,[l("div",ze,c(C.value.shiFuNum||0),1),a[6]||(a[6]=l("div",{class:"stat-label"},"师傅数量",-1))])])]),_:1}),e(g,{span:8},{default:t(()=>[l("div",Ve,[l("div",De,[e(f,null,{default:t(()=>[e(b(pe))]),_:1})]),l("div",Se,[l("div",Le,c(C.value.vip?"是":"否"),1),a[7]||(a[7]=l("div",{class:"stat-label"},"VIP状态",-1))])])]),_:1})]),_:1})]),l("div",Ue,[e(W,{ref_key:"searchFormRef",ref:B,model:o,inline:!0,class:"search-form"},{default:t(()=>[e(x,{gutter:20},{default:t(()=>[e(g,{span:24},{default:t(()=>[e(h,{label:"用户ID",prop:"userId"},{default:t(()=>[e(z,{size:"default",modelValue:o.userId,"onUpdate:modelValue":a[0]||(a[0]=s=>o.userId=s),placeholder:"请输入用户ID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"类型",prop:"type"},{default:t(()=>[e(_,{size:"default",modelValue:o.type,"onUpdate:modelValue":a[1]||(a[1]=s=>o.type=s),placeholder:"请选择类型",clearable:"",style:{width:"140px"}},{default:t(()=>[e(I,{label:"全部",value:0}),e(I,{label:"用户",value:1}),e(I,{label:"师傅",value:2})]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(w,{type:"primary",onClick:j},{default:t(()=>a[8]||(a[8]=[i(" 搜索 ")])),_:1,__:[8]}),e(w,{onClick:q},{default:t(()=>a[9]||(a[9]=[i(" 重置 ")])),_:1,__:[9]}),e(w,{type:"success",onClick:K,loading:L.value},{default:t(()=>a[10]||(a[10]=[i(" 导出 ")])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),l("div",Fe,[ne((p(),H(M,{data:$.value,stripe:"",border:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[e(u,{prop:"id",label:"用户ID",width:"100",align:"center"}),e(u,{prop:"nickName",label:"昵称",width:"150",align:"center"}),e(u,{prop:"phone",label:"手机号",width:"130",align:"center"}),e(u,{prop:"avatarUrl",label:"头像",width:"80",align:"center"},{default:t(s=>[e(U,{size:40,src:s.row.avatarUrl,fit:"cover"},{default:t(()=>[e(f,null,{default:t(()=>[e(b(D))]),_:1})]),_:2},1032,["src"])]),_:1}),e(u,{prop:"shifu",label:"类型",width:"100",align:"center"},{default:t(s=>[e(F,{type:s.row.shifu===1?"warning":"primary"},{default:t(()=>[i(c(s.row.shifu===1?"师傅":"用户"),1)]),_:2},1032,["type"])]),_:1}),e(u,{prop:"cnum",label:"邀请数量",width:"100",align:"center"}),e(u,{prop:"children",label:"下级用户","min-width":"200",align:"center"},{default:t(s=>[s.row.children&&s.row.children.length>0?(p(),v("div",Pe,[(p(!0),v(de,null,ue(s.row.children.slice(0,3),(P,te)=>(p(),H(F,{key:te,size:"small",style:{margin:"2px"}},{default:t(()=>[i(c(P.nickName||P.phone),1)]),_:2},1024))),128)),s.row.children.length>3?(p(),v("span",Re," 等"+c(s.row.children.length)+"人 ",1)):R("",!0)])):(p(),v("span",Be,"暂无"))]),_:1}),e(u,{label:"操作",width:"150",align:"center",fixed:"right"},{default:t(s=>[e(w,{size:"small",type:"primary",onClick:P=>J(s.row)},{default:t(()=>a[11]||(a[11]=[i(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,S.value]])]),e(me,{page:o.pageNum,"page-size":o.pageSize,total:E.value,onHandleSizeChange:X,onHandleCurrentChange:G},null,8,["page","page-size","total"])]),e(Z,{title:"邀请详情",modelValue:N.value,"onUpdate:modelValue":a[3]||(a[3]=s=>N.value=s),width:"800px","close-on-click-modal":!1},{footer:t(()=>[l("span",Me,[e(w,{onClick:a[2]||(a[2]=s=>N.value=!1)},{default:t(()=>a[13]||(a[13]=[i("关闭")])),_:1,__:[13]})])]),default:t(()=>[d.value?(p(),v("div",$e,[e(Y,{column:2,border:""},{default:t(()=>[e(y,{label:"用户ID"},{default:t(()=>[i(c(d.value.id),1)]),_:1}),e(y,{label:"昵称"},{default:t(()=>[i(c(d.value.nickName),1)]),_:1}),e(y,{label:"手机号"},{default:t(()=>[i(c(d.value.phone),1)]),_:1}),e(y,{label:"类型"},{default:t(()=>[e(F,{type:d.value.shifu===1?"warning":"primary"},{default:t(()=>[i(c(d.value.shifu===1?"师傅":"用户"),1)]),_:1},8,["type"])]),_:1}),e(y,{label:"邀请数量"},{default:t(()=>[i(c(d.value.cnum),1)]),_:1}),e(y,{label:"头像"},{default:t(()=>[e(U,{size:50,src:d.value.avatarUrl,fit:"cover"},{default:t(()=>[e(f,null,{default:t(()=>[e(b(D))]),_:1})]),_:1},8,["src"])]),_:1})]),_:1}),d.value.children&&d.value.children.length>0?(p(),v("div",Ee,[a[12]||(a[12]=l("h4",null,"下级用户列表",-1)),e(M,{data:d.value.children,border:"",style:{width:"100%","margin-top":"10px"}},{default:t(()=>[e(u,{prop:"nickName",label:"昵称",align:"center"}),e(u,{prop:"phone",label:"手机号",align:"center"}),e(u,{prop:"avatarUrl",label:"头像",width:"80",align:"center"},{default:t(s=>[e(U,{size:30,src:s.row.avatarUrl,fit:"cover"},{default:t(()=>[e(f,null,{default:t(()=>[e(b(D))]),_:1})]),_:2},1032,["src"])]),_:1})]),_:1},8,["data"])])):R("",!0)])):R("",!0)]),_:1},8,["modelValue"])])}}},Qe=fe(He,[["__scopeId","data-v-53a746b2"]]);export{Qe as default};
