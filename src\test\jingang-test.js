/**
 * 金刚区管理页面功能测试
 * 测试ServiceJingang.vue的主要功能
 */

// 模拟测试数据
const mockNavData = {
  code: '200',
  msg: '',
  data: {
    totalCount: 4,
    totalPage: 1,
    pageNum: 1,
    pageSize: 10,
    list: [
      {
        id: 33,
        title: '疏通服务',
        img: 'https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg',
        top: 7,
        link: '/pages/technician?id=24',
        status: 1,
        createTime: '2024-09-06 15:36:34',
        updateTime: '2025-05-25 23:59:38'
      },
      {
        id: 35,
        title: '打孔服务',
        img: 'https://zskj.asia/attachment/image/666/25/05/43b98976bd38456dafe26cfb977367bf.jpg',
        top: 3,
        link: '/pages/technician?id=28',
        status: 1,
        createTime: '2024-09-06 15:37:54',
        updateTime: '2025-05-26 00:00:01'
      },
      {
        id: 37,
        title: '防水服务',
        img: 'https://zskj.asia/attachment/image/666/25/05/5f6e78af16d24ea4aa52d13a89f9c504.jpg',
        top: 4,
        link: '/pages/technician?id=39',
        status: 1,
        createTime: '2024-09-06 15:39:56',
        updateTime: '2025-05-25 23:59:55'
      },
      {
        id: 38,
        title: '出行救援',
        img: 'https://zskj.asia/attachment/image/666/25/05/14bcd9869cb0445e96edaea19003e9e8.jpg',
        top: 2,
        link: '/pages/technician?id=46',
        status: 1,
        createTime: '2024-09-06 15:43:06',
        updateTime: '2025-05-26 00:00:05'
      }
    ]
  }
}

// 测试API接口
console.log('🧪 开始测试金刚区管理功能...')

// 测试1: 数据结构验证
console.log('✅ 测试1: 验证API数据结构')
console.log('- 响应码:', mockNavData.code)
console.log('- 数据总数:', mockNavData.data.totalCount)
console.log('- 列表长度:', mockNavData.data.list.length)
console.log('- 第一项数据:', mockNavData.data.list[0])

// 测试2: 必填字段验证
console.log('\n✅ 测试2: 验证必填字段')
mockNavData.data.list.forEach((item, index) => {
  const hasRequiredFields = item.id && item.title && item.img
  console.log(`- 项目${index + 1} (${item.title}): ${hasRequiredFields ? '✅ 通过' : '❌ 失败'}`)
})

// 测试3: 状态值验证
console.log('\n✅ 测试3: 验证状态值')
mockNavData.data.list.forEach((item, index) => {
  const validStatus = [1, -1].includes(item.status)
  console.log(`- 项目${index + 1} 状态 (${item.status}): ${validStatus ? '✅ 有效' : '❌ 无效'}`)
})

// 测试4: 图片URL验证
console.log('\n✅ 测试4: 验证图片URL')
mockNavData.data.list.forEach((item, index) => {
  const validUrl = item.img && (item.img.startsWith('http') || item.img.startsWith('https'))
  console.log(`- 项目${index + 1} 图片: ${validUrl ? '✅ 有效URL' : '❌ 无效URL'}`)
})

console.log('\n🎉 金刚区管理功能测试完成!')

// 导出测试数据供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    mockNavData,
    testNavData: () => {
      console.log('金刚区测试数据已加载')
      return mockNavData
    }
  }
}
