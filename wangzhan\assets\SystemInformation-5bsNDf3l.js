import{T as V,L as b}from"./LbButton-BtU4V_Gr.js";import{_ as v}from"./index-C9Xz1oqp.js";import{E as i}from"./element-fdzwdDuf.js";import{r as c,X as h,h as w,y as C,Q as o,A as p,I as l,al as d,z as x,M as U}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const I={class:"lb-system-information"},N={class:"page-main"},T={__name:"SystemInformation",setup(j){const m=c(!1),u=c(),a=h({site_name:"",icp_number:"",company_name:"",address:"",phone:""}),f=async()=>{try{const e=await(await fetch("/api/system/information/config")).json();e.code===200&&Object.assign(a,e.data||{})}catch(s){console.error("获取配置失败:",s)}},_=async()=>{try{m.value=!0;const e=await(await fetch("/api/system/information/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).json();e.code===200?i.success("配置保存成功"):i.error(e.message||"保存失败")}catch{i.error("保存失败")}finally{m.value=!1}};return w(()=>{f()}),(s,e)=>{const r=d("el-input"),t=d("el-form-item"),y=d("el-form"),g=d("el-card");return x(),C("div",I,[o(V),p("div",N,[o(g,{class:"config-card",shadow:"never"},{header:l(()=>e[5]||(e[5]=[p("div",{class:"card-header"},[p("span",null,"备案信息设置")],-1)])),default:l(()=>[o(y,{model:a,ref_key:"configFormRef",ref:u,"label-width":"140px",class:"config-form"},{default:l(()=>[o(t,{label:"网站名称"},{default:l(()=>[o(r,{modelValue:a.site_name,"onUpdate:modelValue":e[0]||(e[0]=n=>a.site_name=n),placeholder:"请输入网站名称"},null,8,["modelValue"])]),_:1}),o(t,{label:"备案号"},{default:l(()=>[o(r,{modelValue:a.icp_number,"onUpdate:modelValue":e[1]||(e[1]=n=>a.icp_number=n),placeholder:"请输入ICP备案号"},null,8,["modelValue"])]),_:1}),o(t,{label:"公司名称"},{default:l(()=>[o(r,{modelValue:a.company_name,"onUpdate:modelValue":e[2]||(e[2]=n=>a.company_name=n),placeholder:"请输入公司名称"},null,8,["modelValue"])]),_:1}),o(t,{label:"联系地址"},{default:l(()=>[o(r,{modelValue:a.address,"onUpdate:modelValue":e[3]||(e[3]=n=>a.address=n),placeholder:"请输入联系地址"},null,8,["modelValue"])]),_:1}),o(t,{label:"联系电话"},{default:l(()=>[o(r,{modelValue:a.phone,"onUpdate:modelValue":e[4]||(e[4]=n=>a.phone=n),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),o(t,null,{default:l(()=>[o(b,{type:"primary",onClick:_,loading:m.value},{default:l(()=>e[6]||(e[6]=[U("保存配置")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},M=v(T,[["__scopeId","data-v-6adab9ba"]]);export{M as default};
