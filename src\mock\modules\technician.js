/**
 * 师傅管理模块Mock数据

 */

import Mock from 'mockjs'
import { successResponse, pageResponse, createCrudMock, errorResponse } from '../utils.js'

// 师傅列表数据
const technicianList = Mock.mock({
  'list|30-80': [{
    'id|+1': 1,
    'name': '@cname',
    'phone': '@phone',
    'avatar': '@image("100x100", "#409eff", "#fff", "师傅")',
    'gender|1': [1, 2], // 1-男 2-女
    'age|20-60': 1,
    'idCard': '@id',
    'address': '@county(true)',
    'services|1-3': ['@pick(["家政服务", "维修服务", "清洁服务", "搬家服务"])'],
    'level|1-5': 1, // 师傅等级
    'rating|3-5.1': 1, // 评分
    'orderCount|0-1000': 1, // 接单数量
    'income|0-50000.2': 1, // 收入
    'status|1': [0, 1, 2], // 0-待审核 1-正常 2-禁用
    'isOnline|1': [true, false], // 是否在线
    'joinTime': '@datetime',
    'lastLoginTime': '@datetime',
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 师傅等级数据
const levelList = Mock.mock({
  'list|5-8': [{
    'id|+1': 1,
    'name': '@pick(["初级师傅", "中级师傅", "高级师傅", "专家师傅", "金牌师傅"])',
    'level|1-5': 1,
    'minOrders|0-100': 1, // 最少接单数
    'minRating|3-5.1': 1, // 最低评分
    'commission|5-20.1': 1, // 佣金比例
    'privileges|1-3': ['@pick(["优先派单", "专属客服", "培训机会", "奖金激励"])'],
    'description': '@cparagraph(1, 2)',
    'status|1': [0, 1],
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 师傅押金记录
const depositList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'technicianId|1-80': 1,
    'technicianName': '@cname',
    'amount|500-2000.2': 1,
    'type|1': [1, 2], // 1-缴纳 2-退还
    'status|1': [0, 1, 2], // 0-待处理 1-已完成 2-已拒绝
    'payMethod': '@pick(["微信", "支付宝", "银行卡", "现金"])',
    'remark': '@cparagraph(1, 2)',
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 接单范围设置
const distanceList = Mock.mock({
  'list|10-20': [{
    'id|+1': 1,
    'technicianId|1-80': 1,
    'technicianName': '@cname',
    'latitude|30-40.6': 1,
    'longitude|110-120.6': 1,
    'radius|1-50': 1, // 接单半径(公里)
    'areas|1-5': ['@pick(["朝阳区", "海淀区", "西城区", "东城区", "丰台区"])'],
    'status|1': [0, 1],
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 创建CRUD接口
createCrudMock('/api/technician', technicianList)
createCrudMock('/api/technician/level', levelList)
createCrudMock('/api/technician/deposit', depositList)
createCrudMock('/api/technician/distance', distanceList)

// 师傅统计数据
Mock.mock('/api/technician/statistics', 'get', () => {
  return successResponse({
    totalTechnicians: technicianList.length,
    activeTechnicians: technicianList.filter(item => item.status === 1).length,
    onlineTechnicians: technicianList.filter(item => item.isOnline).length,
    pendingTechnicians: technicianList.filter(item => item.status === 0).length,
    totalDeposit: depositList.reduce((sum, item) => sum + (item.type === 1 ? item.amount : 0), 0),
    averageRating: (technicianList.reduce((sum, item) => sum + item.rating, 0) / technicianList.length).toFixed(1)
  })
})

// ===== 新增师傅管理API（根据用户需求） =====

// 师傅列表数据（根据用户API格式）
const coachList = Mock.mock({
  'list|50-100': [{
    'id|+1': 1000,
    'userId|1': [0, 0, 0, 12888, 12889, 12890], // 确保有一些师傅的userId为0（未认证）
    'selfImg': '@image("100x100", "#409eff", "#fff", "师傅")',
    'coachName': '@cname',
    'sex|0-1': 1, // 0-男 1-女
    'labelName': '@pick(["初级师傅", "中级师傅", "高级师傅", "专家师傅", "金牌师傅"])',
    'cashPledge|0-2000.2': 1,
    'mobile': '@phone',
    'idCard': '@image("200x120", "#409eff", "#fff", "身份证")',
    'isUpdate': '@pick(["0", "1"])',
    'isEnable|1-2': 1, // 1-开启 2-关闭
    'workTime|1-30': 1,
    'address': '@county(true)',
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'adminAdd|0-1': 1,
    'status|1-4': 1, // 1-待审核 2-审核通过 4-审核驳回
    'idCode': '@id',
    'messagePush|0-1': 1,
    'count|0-100': 1, // 订单数
    'credit|0-100': 1, // 信誉分
    // 编辑页面需要的额外字段
    'user_id': function() { return this.userId }, // 关联用户ID
    'coach_name': function() { return this.coachName }, // 师傅姓名
    'id_card': function() { return this.idCard }, // 身份证照片
    'self_img': function() { return this.selfImg }, // 个人照片
    'id_code': function() { return this.idCode }, // 身份证号
    'work_time': function() { return this.workTime }, // 从业年份
    'city_id': [1046, 1127, 1131], // 省市区ID数组（模拟数据）
    'lng|113-114.6': 1, // 经度
    'lat|22-23.6': 1, // 纬度
    'text': '@cparagraph(1, 3)', // 师傅简介
    'nickName': '@cname' // 关联用户昵称
  }]
}).list

// 关联用户列表数据
const associateUserList = Mock.mock({
  'list|30-50': [{
    'id|+1': 10000,
    'avatarUrl': '@image("80x80", "#409eff", "#fff", "用户")',
    'nickName': '@cname',
    'phone': '@phone'
  }]
}).list

// 师傅列表查询
Mock.mock(/\/api\/admin\/coach\/list/, 'get', (options) => {
  console.log('🔍 师傅列表查询Mock API被调用', options.url)

  const url = new URL(options.url, 'http://localhost')
  const params = Object.fromEntries(url.searchParams)

  let filteredList = [...coachList]

  // 根据查询条件过滤
  if (params.coachName) {
    filteredList = filteredList.filter(item =>
      item.coachName.includes(params.coachName)
    )
  }

  if (params.mobile) {
    filteredList = filteredList.filter(item =>
      item.mobile.includes(params.mobile)
    )
  }

  if (params.status) {
    filteredList = filteredList.filter(item =>
      item.status === parseInt(params.status)
    )
  }

  if (params.isEnable) {
    filteredList = filteredList.filter(item =>
      item.isEnable === parseInt(params.isEnable)
    )
  }

  // 分页处理
  const pageNum = parseInt(params.pageNum) || 1
  const pageSize = parseInt(params.pageSize) || 10
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedList = filteredList.slice(startIndex, endIndex)

  // 统计数据
  const stats = {
    all: coachList.length,
    ing: coachList.filter(item => item.status === 1).length,
    pass: coachList.filter(item => item.status === 2).length,
    noPass: coachList.filter(item => item.status === 4).length
  }

  return successResponse({
    coachList: {
      totalCount: filteredList.length,
      totalPage: Math.ceil(filteredList.length / pageSize),
      pageNum: pageNum,
      pageSize: pageSize,
      list: paginatedList
    },
    ...stats
  })
})

// 师傅详情查询
Mock.mock(/\/api\/admin\/coach\/(\d+)/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/coach\/(\d+)/)[1])
  const coach = coachList.find(item => item.id === id)

  if (coach) {
    return successResponse(coach)
  } else {
    return { code: '404', msg: '师傅不存在', data: null }
  }
})

// 师傅状态变更
Mock.mock(/\/api\/admin\/coach\/status/, 'post', (options) => {
  const { id, status } = JSON.parse(options.body)
  const coach = coachList.find(item => item.id === parseInt(id))

  if (coach) {
    if (status === -1) {
      // 删除师傅
      const index = coachList.findIndex(item => item.id === parseInt(id))
      coachList.splice(index, 1)
      return successResponse(null, '师傅已删除')
    } else {
      coach.isEnable = status === 1 ? 1 : 2
      return successResponse(coach, '状态更新成功')
    }
  } else {
    return { code: '404', msg: '师傅不存在', data: null }
  }
})

// 审核通过
Mock.mock(/\/api\/admin\/audit\/coach\/approve/, 'post', (options) => {
  const { coachId } = JSON.parse(options.body)
  const coach = coachList.find(item => item.id === parseInt(coachId))

  if (coach) {
    coach.status = 2 // 审核通过
    return successResponse(coach, '审核通过成功')
  } else {
    return { code: '404', msg: '师傅不存在', data: null }
  }
})

// 审核驳回
Mock.mock(/\/api\/admin\/audit\/coach\/reject/, 'post', (options) => {
  const { coachId, shText } = JSON.parse(options.body)
  const coach = coachList.find(item => item.id === parseInt(coachId))

  if (coach) {
    coach.status = 4 // 审核驳回
    coach.rejectReason = shText
    return successResponse(coach, '审核驳回成功')
  } else {
    return { code: '404', msg: '师傅不存在', data: null }
  }
})

// 师傅等级调整
Mock.mock(/\/api\/admin\/coach\/upDateCoachLevel/, 'post', (options) => {
  const { id, labelId, labelName } = JSON.parse(options.body)
  const coach = coachList.find(item => item.id === parseInt(id))

  if (coach) {
    coach.labelId = labelId
    coach.labelName = labelName
    return successResponse(coach, '等级调整成功')
  } else {
    return { code: '404', msg: '师傅不存在', data: null }
  }
})

// 获取关联用户列表
Mock.mock(/\/api\/admin\/coach\/getAssociateUser/, 'get', (options) => {
  console.log('👥 获取关联用户列表Mock API被调用')

  const url = new URL(options.url, 'http://localhost')
  const params = Object.fromEntries(url.searchParams)

  let filteredList = [...associateUserList]

  // 根据昵称模糊查询
  if (params.nickName) {
    filteredList = filteredList.filter(item =>
      item.nickName.includes(params.nickName)
    )
    console.log(`🔍 按昵称"${params.nickName}"筛选，找到${filteredList.length}条记录`)
  }

  // 根据手机号精确查询
  if (params.phone) {
    filteredList = filteredList.filter(item =>
      item.phone === params.phone
    )
    console.log(`🔍 按手机号"${params.phone}"筛选，找到${filteredList.length}条记录`)
  }

  // 分页处理
  const pageNum = parseInt(params.pageNum) || 1
  const pageSize = parseInt(params.pageSize) || 10
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedList = filteredList.slice(startIndex, endIndex)

  return successResponse({
    totalCount: filteredList.length,
    totalPage: Math.ceil(filteredList.length / pageSize),
    pageNum: pageNum,
    pageSize: pageSize,
    list: paginatedList
  })
})

// 师傅日志数据（根据用户API格式）
const coachLogList = Mock.mock({
  'list|30-50': [{
    'id|+1': 1,
    'coachId|1000-2000': 1,
    'userId|10000-20000': 1,
    'status|1': [-1, 1, 2, 4, 5, 6], // -1删除 2审核通过 4审核驳回 5拉入黑名单 1拉出黑名单 6权限操作
    'adminId|1-10': 1,
    'ipv4': '@ip',
    'text': function() {
      const status = this.status
      const texts = {
        '-1': '删除师傅',
        '1': '拉出黑名单',
        '2': '审核通过',
        '4': '审核驳回：@pick(["资料不完整", "身份证不清晰", "联系方式错误", "其他原因"])',
        '5': '拉入黑名单',
        '6': '@pick(["禁止提现：开启，禁止退款：开启，禁止登录：关闭", "禁止提现：关闭，禁止退款：开启，禁止登录：开启", "禁止提现：开启，禁止退款：关闭，禁止登录：关闭"])'
      }
      return texts[status] || '未知操作'
    },
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'coachName': '@cname'
  }]
}).list

// 新增师傅
Mock.mock(/\/api\/admin\/coach\/addCoach/, 'post', (options) => {
  const coachData = JSON.parse(options.body)

  const newCoach = {
    id: Math.max(...coachList.map(item => item.id)) + 1,
    userId: coachData.userId || 0,
    selfImg: coachData.selfImg ? coachData.selfImg.join(',') : '',
    coachName: coachData.coachName,
    sex: coachData.sex || 0,
    labelName: null,
    cashPledge: null,
    mobile: coachData.mobile,
    idCard: coachData.idCard ? coachData.idCard.join(',') : '',
    isUpdate: "0",
    isEnable: 1,
    workTime: coachData.workTime || 0,
    address: coachData.address || '',
    createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
    adminAdd: 1,
    status: 1, // 待审核
    idCode: coachData.idCode || '',
    messagePush: 0,
    count: 0,
    credit: 0
  }

  coachList.push(newCoach)
  return successResponse(newCoach, '新增师傅成功')
})

// 师傅等级选择列表（用于等级调整）
Mock.mock(/\/api\/admin\/label\/coachList/, 'get', () => {
  console.log('📊 师傅等级选择列表Mock API被调用')

  const levelOptions = [
    { id: 1, labelName: '初级师傅', earnestMoney: 500, delayTime: 30, proportion: 10 },
    { id: 2, labelName: '中级师傅', earnestMoney: 1000, delayTime: 20, proportion: 8 },
    { id: 3, labelName: '高级师傅', earnestMoney: 1500, delayTime: 10, proportion: 6 },
    { id: 4, labelName: '专家师傅', earnestMoney: 2000, delayTime: 5, proportion: 4 },
    { id: 5, labelName: '金牌师傅', earnestMoney: 3000, delayTime: 0, proportion: 2 }
  ]

  return successResponse(levelOptions)
})

// 省市区数据Mock API
Mock.mock(/\/api\/admin\/region\/tree/, 'get', () => {
  console.log('🌍 省市区数据Mock API被调用')

  const regionData = [
    {
      id: 110000,
      name: '北京市',
      children: [
        { id: 110100, name: '北京市', children: [
          { id: 110101, name: '东城区' },
          { id: 110102, name: '西城区' },
          { id: 110105, name: '朝阳区' },
          { id: 110106, name: '丰台区' },
          { id: 110107, name: '石景山区' },
          { id: 110108, name: '海淀区' },
          { id: 110109, name: '门头沟区' },
          { id: 110111, name: '房山区' },
          { id: 110112, name: '通州区' },
          { id: 110113, name: '顺义区' },
          { id: 110114, name: '昌平区' },
          { id: 110115, name: '大兴区' },
          { id: 110116, name: '怀柔区' },
          { id: 110117, name: '平谷区' },
          { id: 110118, name: '密云区' },
          { id: 110119, name: '延庆区' }
        ]}
      ]
    },
    {
      id: 310000,
      name: '上海市',
      children: [
        { id: 310100, name: '上海市', children: [
          { id: 310101, name: '黄浦区' },
          { id: 310104, name: '徐汇区' },
          { id: 310105, name: '长宁区' },
          { id: 310106, name: '静安区' },
          { id: 310107, name: '普陀区' },
          { id: 310109, name: '虹口区' },
          { id: 310110, name: '杨浦区' },
          { id: 310112, name: '闵行区' },
          { id: 310113, name: '宝山区' },
          { id: 310114, name: '嘉定区' },
          { id: 310115, name: '浦东新区' },
          { id: 310116, name: '金山区' },
          { id: 310117, name: '松江区' },
          { id: 310118, name: '青浦区' },
          { id: 310120, name: '奉贤区' },
          { id: 310151, name: '崇明区' }
        ]}
      ]
    },
    {
      id: 440000,
      name: '广东省',
      children: [
        { id: 440100, name: '广州市', children: [
          { id: 440103, name: '荔湾区' },
          { id: 440104, name: '越秀区' },
          { id: 440105, name: '海珠区' },
          { id: 440106, name: '天河区' },
          { id: 440111, name: '白云区' },
          { id: 440112, name: '黄埔区' },
          { id: 440113, name: '番禺区' },
          { id: 440114, name: '花都区' },
          { id: 440115, name: '南沙区' },
          { id: 440117, name: '从化区' },
          { id: 440118, name: '增城区' }
        ]},
        { id: 440300, name: '深圳市', children: [
          { id: 440303, name: '罗湖区' },
          { id: 440304, name: '福田区' },
          { id: 440305, name: '南山区' },
          { id: 440306, name: '宝安区' },
          { id: 440307, name: '龙岗区' },
          { id: 440308, name: '盐田区' },
          { id: 440309, name: '龙华区' },
          { id: 440310, name: '坪山区' },
          { id: 440311, name: '光明区' }
        ]},
        { id: 440200, name: '韶关市', children: [
          { id: 440203, name: '武江区' },
          { id: 440204, name: '浈江区' },
          { id: 440205, name: '曲江区' }
        ]}
      ]
    },
    {
      id: 320000,
      name: '江苏省',
      children: [
        { id: 320100, name: '南京市', children: [
          { id: 320102, name: '玄武区' },
          { id: 320104, name: '秦淮区' },
          { id: 320105, name: '建邺区' },
          { id: 320106, name: '鼓楼区' },
          { id: 320111, name: '浦口区' },
          { id: 320113, name: '栖霞区' },
          { id: 320114, name: '雨花台区' },
          { id: 320115, name: '江宁区' },
          { id: 320116, name: '六合区' },
          { id: 320117, name: '溧水区' },
          { id: 320118, name: '高淳区' }
        ]},
        { id: 320200, name: '无锡市', children: [
          { id: 320205, name: '锡山区' },
          { id: 320206, name: '惠山区' },
          { id: 320211, name: '滨湖区' },
          { id: 320213, name: '梁溪区' },
          { id: 320214, name: '新吴区' }
        ]}
      ]
    }
  ]

  return successResponse(regionData)
})

console.log('✅ 师傅管理Mock API已加载完成')

// 师傅审核
Mock.mock(/\/api\/technician\/audit\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/audit\/(\d+)/)[1])
  const { status, remark } = JSON.parse(options.body)
  const technician = technicianList.find(item => item.id === id)
  
  if (technician) {
    technician.status = status
    technician.auditRemark = remark
    technician.updateTime = Mock.Random.datetime()
    return successResponse(technician, '审核完成')
  } else {
    return errorResponse('师傅不存在', 404)
  }
})

// 师傅状态切换
Mock.mock(/\/api\/technician\/status\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/status\/(\d+)/)[1])
  const { status } = JSON.parse(options.body)
  const technician = technicianList.find(item => item.id === id)
  
  if (technician) {
    technician.status = status
    technician.updateTime = Mock.Random.datetime()
    return successResponse(technician, '状态更新成功')
  } else {
    return errorResponse('师傅不存在', 404)
  }
})

// 师傅在线状态切换
Mock.mock(/\/api\/technician\/online\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/online\/(\d+)/)[1])
  const { isOnline } = JSON.parse(options.body)
  const technician = technicianList.find(item => item.id === id)
  
  if (technician) {
    technician.isOnline = isOnline
    technician.lastLoginTime = Mock.Random.datetime()
    technician.updateTime = Mock.Random.datetime()
    return successResponse(technician, '在线状态更新成功')
  } else {
    return errorResponse('师傅不存在', 404)
  }
})

// 押金处理
Mock.mock(/\/api\/technician\/deposit\/process\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/process\/(\d+)/)[1])
  const { status, remark } = JSON.parse(options.body)
  const deposit = depositList.find(item => item.id === id)
  
  if (deposit) {
    deposit.status = status
    deposit.processRemark = remark
    deposit.updateTime = Mock.Random.datetime()
    return successResponse(deposit, '押金处理完成')
  } else {
    return errorResponse('押金记录不存在', 404)
  }
})

// 批量操作
Mock.mock('/api/technician/batch', 'post', (options) => {
  const { action, ids } = JSON.parse(options.body)
  
  switch (action) {
    case 'audit':
      const { status, remark } = JSON.parse(options.body)
      ids.forEach(id => {
        const technician = technicianList.find(item => item.id === id)
        if (technician) {
          technician.status = status
          technician.auditRemark = remark
          technician.updateTime = Mock.Random.datetime()
        }
      })
      return successResponse(null, `批量审核${ids.length}条记录成功`)
      
    case 'delete':
      ids.forEach(id => {
        const index = technicianList.findIndex(item => item.id === id)
        if (index !== -1) {
          technicianList.splice(index, 1)
        }
      })
      return successResponse(null, `批量删除${ids.length}条记录成功`)
      
    default:
      return errorResponse('不支持的操作类型')
  }
})

// ===== 师傅配置管理相关Mock接口 =====

// 师傅配置数据
const coachConfig = {
  id: 1,
  cashPledge: 5.50,
  commissionRatio: 10,
  entryNotice: "欢迎加入今师傅平台！实名认证：师傅需提供真实有效的身份信息，完成实名认证。上传清晰、准确个人照片及服务技能。服务标准：请遵守平台的服务标准，确保服务质量。平台将根据用户反馈和评价对您的服务进行监督。准时到达：请务必按照预约时间准时到达服务地点。如有特殊情况无法按时到达，请提前与客户沟通并告知平台。服务态度：保持专业、友好的服务态度，尊重客户的需求和意见。结算提现：自师傅发起提现日，一周内，结算到师傅提现账户。评价系统：客户完成服务后，可对师傅的服务进行评价，师傅应重视客户反馈，不断提升服务质量。投诉处理：如遇客户投诉，平台将介入调查，师傅需积极配合处理。安全操作：师傅在提供服务时，需遵守安全操作规程，确保自身和客户的安全。责任划分：如因师傅操作不当导致客户损失，师傅需承担相应责任。违规处理：如发现师傅有违规行为，平台将根据情节严重程度进行处理，包括但不限于警告、暂停服务、永久封号等。客服支持：如遇任何问题，师傅可随时联系平台客服，获取帮助和支持。感谢您选择我们的平台，期待与您携手共创美好未来！\n",
  distance: 50.0,
  quotationSum: 5,
  quotationWaitTime: 10
}

// 获取师傅相关配置
Mock.mock('/api/admin/coach/getCoachConfig', 'get', () => {
  console.log('🔍 Mock: 获取师傅相关配置')
  return successResponse(coachConfig, '获取师傅配置成功')
})

// 师傅相关配置更新
Mock.mock('/api/admin/coach/coachConfigUpdate', 'post', (options) => {
  const body = JSON.parse(options.body)
  console.log('✏️ Mock: 师傅相关配置更新', body)

  // 验证必需字段
  if (body.cashPledge === undefined || body.commissionRatio === undefined || !body.entryNotice || body.distance === undefined || body.quotationSum === undefined || body.quotationWaitTime === undefined) {
    return errorResponse('缺少必需参数')
  }

  // 验证数据范围
  if (body.cashPledge < 0) {
    return errorResponse('保证金不能小于0')
  }

  if (body.commissionRatio < 0 || body.commissionRatio > 100) {
    return errorResponse('抽佣比例必须在0-100之间')
  }

  if (body.distance < 0) {
    return errorResponse('接单距离不能小于0')
  }

  if (body.quotationSum < 1) {
    return errorResponse('最大报价人数不能小于1')
  }

  if (body.quotationWaitTime < 1) {
    return errorResponse('等待时间不能小于1分钟')
  }

  if (body.entryNotice.length < 10) {
    return errorResponse('入驻须知至少10个字符')
  }

  // 更新配置数据
  Object.assign(coachConfig, {
    cashPledge: body.cashPledge,
    commissionRatio: body.commissionRatio,
    entryNotice: body.entryNotice,
    distance: body.distance,
    quotationSum: body.quotationSum,
    quotationWaitTime: body.quotationWaitTime
  })

  return successResponse(coachConfig, '师傅配置更新成功')
})

// ===== 开放城市管理相关Mock接口 =====

// 开放城市列表数据（根据API返回格式）
const openCityListData = [
  {
    id: null,
    cityId: 37,
    name: "河北省",
    leaf: false,
    children: [
      {
        id: 11788,
        cityId: 38,
        name: "石家庄市",
        leaf: true,
        children: null
      },
      {
        id: 11794,
        cityId: 76,
        name: "秦皇岛市",
        leaf: true,
        children: null
      }
    ]
  },
  {
    id: null,
    cityId: 1,
    name: "北京市",
    leaf: false,
    children: [
      {
        id: 11786,
        cityId: 2,
        name: "北京市",
        leaf: true,
        children: null
      }
    ]
  },
  {
    id: null,
    cityId: 3,
    name: "天津市",
    leaf: false,
    children: [
      {
        id: 11787,
        cityId: 4,
        name: "天津市",
        leaf: true,
        children: null
      }
    ]
  }
]

// 城市树数据（用于级联选择器）
const cityTreeData = [
  {
    id: 37,
    name: "河北省",
    leaf: false,
    children: [
      {
        id: 38,
        name: "石家庄市",
        leaf: true,
        children: null
      },
      {
        id: 76,
        name: "秦皇岛市",
        leaf: true,
        children: null
      }
    ]
  },
  {
    id: 1,
    name: "北京市",
    leaf: false,
    children: [
      {
        id: 2,
        name: "北京市",
        leaf: true,
        children: null
      }
    ]
  },
  {
    id: 3,
    name: "天津市",
    leaf: false,
    children: [
      {
        id: 4,
        name: "天津市",
        leaf: true,
        children: null
      }
    ]
  }
]

// 获取城市树数据（用于级联选择器）
Mock.mock('/api/admin/coach/cityTree', 'get', () => {
  console.log('🏙️ Mock: 获取城市树数据')
  return successResponse(cityTreeData, '获取城市树数据成功')
})

// 获取开放城市列表
Mock.mock('/api/admin/coach/openCityList', 'get', () => {
  console.log('📋 Mock: 获取开放城市列表')
  return successResponse(openCityListData, '获取开放城市列表成功')
})

// 省市区三级联动数据
const regionTreeData = [
  {
    id: 1046,
    trueName: '北京市',
    pid: 0,
    children: [
      {
        id: 1127,
        trueName: '北京市',
        pid: 1046,
        children: [
          { id: 1131, trueName: '东城区', pid: 1127 },
          { id: 1132, trueName: '西城区', pid: 1127 },
          { id: 1133, trueName: '朝阳区', pid: 1127 },
          { id: 1134, trueName: '丰台区', pid: 1127 },
          { id: 1135, trueName: '石景山区', pid: 1127 },
          { id: 1136, trueName: '海淀区', pid: 1127 }
        ]
      }
    ]
  },
  {
    id: 2000,
    trueName: '上海市',
    pid: 0,
    children: [
      {
        id: 2100,
        trueName: '上海市',
        pid: 2000,
        children: [
          { id: 2101, trueName: '黄浦区', pid: 2100 },
          { id: 2104, trueName: '徐汇区', pid: 2100 },
          { id: 2105, trueName: '长宁区', pid: 2100 },
          { id: 2106, trueName: '静安区', pid: 2100 },
          { id: 2107, trueName: '普陀区', pid: 2100 },
          { id: 2109, trueName: '虹口区', pid: 2100 }
        ]
      }
    ]
  },
  {
    id: 3000,
    trueName: '广东省',
    pid: 0,
    children: [
      {
        id: 3100,
        trueName: '广州市',
        pid: 3000,
        children: [
          { id: 3103, trueName: '荔湾区', pid: 3100 },
          { id: 3104, trueName: '越秀区', pid: 3100 },
          { id: 3105, trueName: '海珠区', pid: 3100 },
          { id: 3106, trueName: '天河区', pid: 3100 },
          { id: 3111, trueName: '白云区', pid: 3100 }
        ]
      },
      {
        id: 3300,
        trueName: '深圳市',
        pid: 3000,
        children: [
          { id: 3303, trueName: '罗湖区', pid: 3300 },
          { id: 3304, trueName: '福田区', pid: 3300 },
          { id: 3305, trueName: '南山区', pid: 3300 },
          { id: 3306, trueName: '宝安区', pid: 3300 },
          { id: 3307, trueName: '龙岗区', pid: 3300 }
        ]
      }
    ]
  }
]

// 获取省市区三级联动数据
Mock.mock('/api/core/city/tree', 'get', () => {
  console.log('🏙️ Mock: 获取省市区三级联动数据')
  return successResponse(regionTreeData, '获取省市区数据成功')
})

// 删除开放城市
Mock.mock('/api/admin/coach/deleteOpenCity', 'post', (options) => {
  const body = JSON.parse(options.body)
  console.log('🗑️ Mock: 删除开放城市', body)

  if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
    return errorResponse('城市ID数组不能为空')
  }

  // 模拟删除操作 - 从嵌套结构中删除指定ID的城市
  let deletedCount = 0

  openCityListData.forEach(province => {
    if (province.children) {
      const originalLength = province.children.length
      province.children = province.children.filter(city => !body.ids.includes(city.id))
      deletedCount += originalLength - province.children.length
    }
  })

  // 删除空的省份
  const filteredProvinces = openCityListData.filter(province =>
    !province.children || province.children.length > 0
  )

  // 更新原数组
  openCityListData.length = 0
  openCityListData.push(...filteredProvinces)

  if (deletedCount > 0) {
    return successResponse(null, `成功删除${deletedCount}个开放城市`)
  } else {
    return errorResponse('未找到要删除的城市')
  }
})

// ===== 师傅日志管理Mock API =====

// 师傅日志列表查询
Mock.mock(/\/api\/admin\/coach\/log/, 'get', (options) => {
  console.log('📋 师傅日志列表查询Mock API被调用')

  const url = new URL(options.url, 'http://localhost')
  const params = Object.fromEntries(url.searchParams)

  const {
    coachName = '',
    status = '',
    pageNum = 1,
    pageSize = 10
  } = params

  console.log('📋 师傅日志查询参数:', { coachName, status, pageNum, pageSize })

  // 过滤数据
  let filteredList = [...coachLogList]

  // 师傅名模糊查询
  if (coachName) {
    filteredList = filteredList.filter(item =>
      item.coachName.includes(coachName)
    )
  }

  // 状态筛选
  if (status !== '') {
    filteredList = filteredList.filter(item =>
      item.status.toString() === status.toString()
    )
  }

  // 分页处理
  const currentPage = parseInt(pageNum) || 1
  const size = parseInt(pageSize) || 10
  const startIndex = (currentPage - 1) * size
  const endIndex = startIndex + size
  const paginatedList = filteredList.slice(startIndex, endIndex)

  const totalCount = filteredList.length
  const totalPage = Math.ceil(totalCount / size)

  return successResponse({
    totalCount,
    totalPage,
    pageNum: currentPage,
    pageSize: size,
    list: paginatedList
  })
})

console.log('师傅管理模块Mock数据已加载')

export { technicianList, levelList, depositList, distanceList, coachConfig, coachLogList }
