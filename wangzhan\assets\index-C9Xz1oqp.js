const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ServiceList-C_ijMq4v.js","assets/vendor-DmFBDimT.js","assets/LbButton-BtU4V_Gr.js","assets/LbButton-Bdaw5sHp.css","assets/LbImage-CnNh5Udj.js","assets/element-fdzwdDuf.js","assets/LbImage-C2U60XMc.css","assets/LbPage-DnbiQ0Ct.js","assets/LbPage-D-Y9Wy2M.css","assets/utils-DCVfloi1.js","assets/ServiceList-DJge2L6B.css","assets/ServiceEdit-CTw8uyA5.js","assets/LbUeditor-Dh_pKaDh.js","assets/LbUeditor-BWCfcfh-.css","assets/ServiceEdit-CR9DJTEs.css","assets/ServiceBanner-CvwHAArR.js","assets/ServiceBanner-DX9FGW-P.css","assets/ServiceJingang-kYKSqkHW.js","assets/ServiceJingang-CyTfdqN9.css","assets/ServiceFenlei-DPGhA1nA.js","assets/ServiceFenlei-o2SAML_F.css","assets/ServiceDaili-_VLXwr0T.js","assets/ServiceDaili-DCC87PZE.css","assets/ServicePeizhi-DcTL85Ha.js","assets/ServicePeizhi-GiQO7tcP.css","assets/ServiceText-gaaMLfNs.js","assets/ServiceText-BlNm0e4Y.css","assets/TechnicianList-sH3w7gjt.js","assets/TechnicianList-BhVKlYMx.css","assets/TechnicianEdit-7YeauEy9.js","assets/LbToolTips-Bbf_ZQSC.js","assets/LbToolTips-B0RqPhK5.css","assets/TechnicianEdit-CwNjWoYp.css","assets/TechnicianLevel-DQhiYwA1.js","assets/TechnicianLevel-Cf_rC5H_.css","assets/TechnicianDeposit-rc1Rgs-B.js","assets/TechnicianDeposit-DI3SgWF_.css","assets/TechnicianDistance-Bvqt4jIE.js","assets/TechnicianDistance-D__eOG5O.css","assets/TechnicianCity-WzoIq5uv.js","assets/TechnicianCity-A0IjSDRO.css","assets/BlackList-R7FwyMbF.js","assets/BlackList-D2ZZRVYf.css","assets/TechnicianLog-D8NmT3Ry.js","assets/TechnicianLog-Cc5_5LAV.css","assets/MarketList-Br4vq50j.js","assets/MarketList-DTvV0o-F.css","assets/MarketEdit-DAZYHymO.js","assets/MarketEdit-rPs9DuYr.css","assets/MarketNotice-D9mUwHXA.js","assets/MarketNotice-DjHlBvc8.css","assets/MarketPartner-LItPk5bb.js","assets/MarketPartner-Cbk1gUmC.css","assets/MarketPartnerInvite-DJydeFGg.js","assets/MarketPartnerInvite-TVR36aHa.css","assets/MarketPartnerCommission-BJmX5N7P.js","assets/MarketPartnerCommission-DAVexgS2.css","assets/MarketPartnerOrders-YwmS6SAi.js","assets/MarketPartnerOrders-BDSFDPGg.css","assets/ShopOrder-aj0DgRCK.js","assets/ShopOrder---bgHMSD.css","assets/ShopOrderDetail-CiJof6aA.js","assets/ShopOrderDetail-B5oSOH5o.css","assets/ShopRefund-Dp0qA5ut.js","assets/ShopRefund-BbSd4gO-.css","assets/ShopEvaluate-Cc614TnM.js","assets/ShopEvaluate-C5FHMZHA.css","assets/ShopCommission-oL0cli1L.js","assets/ShopCommission-kDAyNgMC.css","assets/ShopAfterSale-BG7PWbUl.js","assets/ShopAfterSale-DuQvAGSM.css","assets/UserList-DcHmdnAj.js","assets/UserList-CItw7gGn.css","assets/UserLog-Cxib_3q5.js","assets/UserLog-pixY1nIo.css","assets/ProfileView-CbzuQ_5j.js","assets/ProfileView-BEokQVcv.css","assets/SettingsView-B9vLO6P3.js","assets/SettingsView-Js9PQJma.css","assets/SecurityView-D2E15HLk.js","assets/SecurityView-DLR77qTn.css","assets/OverviewView-DPAbpO2u.js","assets/OverviewView-ssD2w5qA.css","assets/UserAnalysisView-BfXog_Qw.js","assets/UserAnalysisView-3LXFZbpI.css","assets/ContentAnalysisView-NXsYyLU_.js","assets/ContentAnalysisView-kGcS_ZN1.css","assets/GeneratorView-CskaCTbx.js","assets/GeneratorView-Bvk2Qhkv.css","assets/BackupView-C2JuKHdi.js","assets/BackupView-D_7UbgD5.css","assets/MonitorView-DdexeDm3.js","assets/MonitorView-DJLYVpC3.css","assets/OperationLog-DMUa5SVk.js","assets/OperationLog-B1G65606.css","assets/LoginView-Cr394GZP.js","assets/LoginView-LDRW0t9U.css","assets/RegisterView-CI5RWJca.js","assets/RegisterView-BOibsfL8.css","assets/ForgotPasswordView-BAoUAd-P.js","assets/ForgotPasswordView-oz9V-mke.css","assets/404View-Vazg4c84.js","assets/404View-BOqPWVXL.css","assets/403View-DRjhv5SI.js","assets/403View-C5qLY7Tz.css","assets/500View-ColUvo32.js","assets/500View-BeQeinJ7.css","assets/DashboardView-CAaSJ1GB.js","assets/DashboardView-B41gmlWq.css","assets/MarketActivity-CXos1huc.js","assets/LbCover-Ctxlz8o1.js","assets/LbCover-CRmBWND-.css","assets/MarketActivity-lKkYPDa6.css","assets/DistributionExamine-bBpEgC37.js","assets/DistributionExamine-E5RXDD7e.css","assets/DistributionList-BPSnbGh8.js","assets/DistributionList-BU9nyHA1.css","assets/DistributionSet-BM-qgBU1.js","assets/DistributionSet-DW9VxHOH.css","assets/FinanceList-BzTgD_z5.js","assets/FinanceList-D0yZL05f.css","assets/FinanceDetail-D_4ZTpgk.js","assets/FinanceDetail-DkKArPA3.css","assets/FinanceWithdraw-DSqBwW1r.js","assets/FinanceWithdraw-BOqzIOTu.css","assets/FinanceStored-B6LPQe87.js","assets/FinanceStored-nzCAKaK2.css","assets/FinanceWithdrawHistory-CfS_4iX8.js","assets/FinanceWithdrawHistory-Cbn181b1.css","assets/UserDetail-C7zcO82E.js","assets/UserDetail-Bk5-ycPI.css","assets/AccountAdmin-BTiz42tv.js","assets/AccountAdmin-Ds-xo6aZ.css","assets/AccountRole-CislzUx5.js","assets/AccountRole-Bx29uZbY.css","assets/AccountMenu-DAF2DK-q.js","assets/AccountMenu-D1O1q2PD.css","assets/AccountFranchisee-CxpqJmx1.js","assets/AccountFranchisee-mADtKwMw.css","assets/AccountThird-D8Viqc1F.js","assets/AccountThird-n41EK2DM.css","assets/SystemUpgrade-DjtFQyax.js","assets/SystemUpgrade-B9aVfQFJ.css","assets/SystemExamine-DdTGqtv8.js","assets/SystemExamine-DbC3ZdvI.css","assets/SystemWechat-DauPgmGb.js","assets/SystemWechat-BB8HKZcZ.css","assets/SystemWeb-DjQxemSH.js","assets/SystemWeb-qTKPipP0.css","assets/SystemApp-CwuOKztx.js","assets/SystemApp-CKJfw3WZ.css","assets/SystemInfo-BJuGOUvE.js","assets/SystemInfo-D0XJN2I1.css","assets/SystemPayment-BI3X-jQk.js","assets/SystemPayment-DSWx3cin.css","assets/SystemUpload-DkvbmCM8.js","assets/SystemUpload-Dt-RHKrj.css","assets/SystemTransaction-BAsoigUu.js","assets/SystemTransaction-C4e8xmio.css","assets/SystemNotice-DJOHKIJy.js","assets/SystemNotice-p9FHxK0z.css","assets/SystemMessage-Q3jbgOTr.js","assets/SystemMessage-BY2lwr-f.css","assets/SystemInformation-5bsNDf3l.js","assets/SystemInformation-tznf_39t.css","assets/SystemPrint-D7fAF0TR.js","assets/SystemPrint-D3fx2Mgw.css","assets/SystemCarFee-DlnXriVh.js","assets/SystemCarFee-Doh6IX7H.css","assets/SystemCity-Bwr6V2YI.js","assets/SystemCity-Bm-yXKQ5.css","assets/SystemTravel-6nHM3BS5.js","assets/SystemTravel-BpJ0SApR.css","assets/SystemOther-DYOfJfLO.js","assets/SystemOther-CVNOFeEL.css","assets/SystemVersion-BcxomjNg.js","assets/SystemVersion-Bxap8EDF.css"])))=>i.map(i=>d[i]);
import{al as M,y as $,z as L,Q as A,r as O,c as k,ax as me,h as pe,S as Ae,A as b,I as V,P as q,ay as ve,az as Me,H as x,u as H,a6 as ae,M as Q,O as de,j as se,D as K,K as ke,L as je,T as Se,aA as ze,J as Je,R as Ke,n as Ze,aB as Qe,aC as Xe,aD as ye,au as qe}from"./vendor-DmFBDimT.js";import{a as Ue,f as et,e as tt,b as nt,d as ot,u as it,h as rt,i as at,s as st,E as ne,t as lt,r as ct,j as dt,k as ut}from"./element-fdzwdDuf.js";import{r as mt,a as G}from"./utils-DCVfloi1.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const c of s)if(c.type==="childList")for(const a of c.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&i(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const c={};return s.integrity&&(c.integrity=s.integrity),s.referrerPolicy&&(c.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?c.credentials="include":s.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(s){if(s.ep)return;s.ep=!0;const c=n(s);fetch(s.href,c)}})();const pt={id:"app"},gt={__name:"App",setup(e){return(t,n)=>{const i=M("router-view");return L(),$("div",pt,[A(i)])}}};var le={exports:{}};/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */var ft=le.exports,we;function _t(){return we||(we=1,function(e,t){(function(n,i){e.exports=i()})(ft,function(){var n={};n.version="0.2.0";var i=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};n.configure=function(d){var _,h;for(_ in d)h=d[_],h!==void 0&&d.hasOwnProperty(_)&&(i[_]=h);return this},n.status=null,n.set=function(d){var _=n.isStarted();d=s(d,i.minimum,1),n.status=d===1?null:d;var h=n.render(!_),v=h.querySelector(i.barSelector),T=i.speed,P=i.easing;return h.offsetWidth,p(function(f){i.positionUsing===""&&(i.positionUsing=n.getPositioningCSS()),u(v,a(d,T,P)),d===1?(u(h,{transition:"none",opacity:1}),h.offsetWidth,setTimeout(function(){u(h,{transition:"all "+T+"ms linear",opacity:0}),setTimeout(function(){n.remove(),f()},T)},T)):setTimeout(f,T)}),this},n.isStarted=function(){return typeof n.status=="number"},n.start=function(){n.status||n.set(0);var d=function(){setTimeout(function(){n.status&&(n.trickle(),d())},i.trickleSpeed)};return i.trickle&&d(),this},n.done=function(d){return!d&&!n.status?this:n.inc(.3+.5*Math.random()).set(1)},n.inc=function(d){var _=n.status;return _?(typeof d!="number"&&(d=(1-_)*s(Math.random()*_,.1,.95)),_=s(_+d,0,.994),n.set(_)):n.start()},n.trickle=function(){return n.inc(Math.random()*i.trickleRate)},function(){var d=0,_=0;n.promise=function(h){return!h||h.state()==="resolved"?this:(_===0&&n.start(),d++,_++,h.always(function(){_--,_===0?(d=0,n.done()):n.set((d-_)/d)}),this)}}(),n.render=function(d){if(n.isRendered())return document.getElementById("nprogress");E(document.documentElement,"nprogress-busy");var _=document.createElement("div");_.id="nprogress",_.innerHTML=i.template;var h=_.querySelector(i.barSelector),v=d?"-100":c(n.status||0),T=document.querySelector(i.parent),P;return u(h,{transition:"all 0 linear",transform:"translate3d("+v+"%,0,0)"}),i.showSpinner||(P=_.querySelector(i.spinnerSelector),P&&j(P)),T!=document.body&&E(T,"nprogress-custom-parent"),T.appendChild(_),_},n.remove=function(){D(document.documentElement,"nprogress-busy"),D(document.querySelector(i.parent),"nprogress-custom-parent");var d=document.getElementById("nprogress");d&&j(d)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var d=document.body.style,_="WebkitTransform"in d?"Webkit":"MozTransform"in d?"Moz":"msTransform"in d?"ms":"OTransform"in d?"O":"";return _+"Perspective"in d?"translate3d":_+"Transform"in d?"translate":"margin"};function s(d,_,h){return d<_?_:d>h?h:d}function c(d){return(-1+d)*100}function a(d,_,h){var v;return i.positionUsing==="translate3d"?v={transform:"translate3d("+c(d)+"%,0,0)"}:i.positionUsing==="translate"?v={transform:"translate("+c(d)+"%,0)"}:v={"margin-left":c(d)+"%"},v.transition="all "+_+"ms "+h,v}var p=function(){var d=[];function _(){var h=d.shift();h&&h(_)}return function(h){d.push(h),d.length==1&&_()}}(),u=function(){var d=["Webkit","O","Moz","ms"],_={};function h(f){return f.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(m,I){return I.toUpperCase()})}function v(f){var m=document.body.style;if(f in m)return f;for(var I=d.length,S=f.charAt(0).toUpperCase()+f.slice(1),C;I--;)if(C=d[I]+S,C in m)return C;return f}function T(f){return f=h(f),_[f]||(_[f]=v(f))}function P(f,m,I){m=T(m),f.style[m]=I}return function(f,m){var I=arguments,S,C;if(I.length==2)for(S in m)C=m[S],C!==void 0&&m.hasOwnProperty(S)&&P(f,S,C);else P(f,I[1],I[2])}}();function g(d,_){var h=typeof d=="string"?d:w(d);return h.indexOf(" "+_+" ")>=0}function E(d,_){var h=w(d),v=h+_;g(h,_)||(d.className=v.substring(1))}function D(d,_){var h=w(d),v;g(d,_)&&(v=h.replace(" "+_+" "," "),d.className=v.substring(1,v.length-1))}function w(d){return(" "+(d.className||"")+" ").replace(/\s+/gi," ")}function j(d){d&&d.parentNode&&d.parentNode.removeChild(d)}return n})}(le)),le.exports}var ht=_t();const X=Ue(ht);var Et=function(e){return function(t){return!!t&&typeof t=="object"}(e)&&!function(t){var n=Object.prototype.toString.call(t);return n==="[object RegExp]"||n==="[object Date]"||function(i){return i.$$typeof===It}(t)}(e)},It=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function y(e,t){return t.clone!==!1&&t.isMergeableObject(e)?Z(Array.isArray(e)?[]:{},e,t):e}function Pt(e,t,n){return e.concat(t).map(function(i){return y(i,n)})}function De(e){return Object.keys(e).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(n){return t.propertyIsEnumerable(n)}):[]}(e))}function Ve(e,t){try{return t in e}catch{return!1}}function Z(e,t,n){(n=n||{}).arrayMerge=n.arrayMerge||Pt,n.isMergeableObject=n.isMergeableObject||Et,n.cloneUnlessOtherwiseSpecified=y;var i=Array.isArray(t);return i===Array.isArray(e)?i?n.arrayMerge(e,t,n):function(s,c,a){var p={};return a.isMergeableObject(s)&&De(s).forEach(function(u){p[u]=y(s[u],a)}),De(c).forEach(function(u){(function(g,E){return Ve(g,E)&&!(Object.hasOwnProperty.call(g,E)&&Object.propertyIsEnumerable.call(g,E))})(s,u)||(p[u]=Ve(s,u)&&a.isMergeableObject(c[u])?function(g,E){if(!E.customMerge)return Z;var D=E.customMerge(g);return typeof D=="function"?D:Z}(u,a)(s[u],c[u],a):y(c[u],a))}),p}(e,t,n):y(t,n)}Z.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce(function(n,i){return Z(n,i,t)},{})};var At=Z;function vt(e){var t=(e=e||{}).storage||window&&window.localStorage,n=e.key||"vuex";function i(E,D){var w=D.getItem(E);try{return typeof w=="string"?JSON.parse(w):typeof w=="object"?w:void 0}catch{}}function s(){return!0}function c(E,D,w){return w.setItem(E,JSON.stringify(D))}function a(E,D){return Array.isArray(D)?D.reduce(function(w,j){return function(h,v,T,P){return!/^(__proto__|constructor|prototype)$/.test(v)&&((v=v.split?v.split("."):v.slice(0)).slice(0,-1).reduce(function(f,m){return f[m]=f[m]||{}},h)[v.pop()]=T),h}(w,j,(d=E,(d=((_=j).split?_.split("."):_).reduce(function(h,v){return h&&h[v]},d))===void 0?void 0:d));var d,_},{}):E}function p(E){return function(D){return E.subscribe(D)}}(e.assertStorage||function(){t.setItem("@@",1),t.removeItem("@@")})(t);var u,g=function(){return(e.getState||i)(n,t)};return e.fetchBeforeUse&&(u=g()),function(E){e.fetchBeforeUse||(u=g()),typeof u=="object"&&u!==null&&(E.replaceState(e.overwrite?u:At(E.state,u,{arrayMerge:e.arrayMerger||function(D,w){return w},clone:!1})),(e.rehydrated||function(){})(E)),(e.subscriber||p)(E)(function(D,w){(e.filter||s)(D)&&(e.setState||c)(n,(e.reducer||a)(w,e.paths),t)})}}const St={bannerList(e){return console.log("🔍 轮播图列表API-V2请求参数:",e),r("/api/admin/banner/list",e)},bannerDetail(e){return!e||!e.id?Promise.reject(new Error("轮播图ID不能为空")):(console.log("🔍 获取轮播图详情API-V2请求:",e),r(`/api/admin/banner/detail/${e.id}`))},bannerAdd(e){if(!e||!e.img)return Promise.reject(new Error("轮播图图片不能为空"));const t={img:e.img,top:e.top||0,link:e.link||"",status:e.status||1,type:e.type||1};return console.log("➕ 新增轮播图API-V2请求数据:",t),o("/api/admin/banner/add",t)},bannerUpdate(e){if(!e||!e.id)return Promise.reject(new Error("轮播图ID不能为空"));const t={id:e.id,img:e.img,top:e.top||0,link:e.link||"",status:e.status||1,type:e.type||1};return console.log("✏️ 编辑轮播图API-V2请求数据:",t),o("/api/admin/banner/edit",t)},bannerDelete(e){return!e||!e.id?Promise.reject(new Error("轮播图ID不能为空")):(console.log("🗑️ 删除轮播图API-V2请求:",e),o(`/api/admin/banner/delete/${e.id}`))},bannerStatus(e){return!e||!e.id?Promise.reject(new Error("轮播图ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 修改轮播图状态API-V2请求:",e),o(`/api/admin/banner/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},serviceList(e){return console.log("🔍 服务列表API-V2请求参数:",e),r("/api/admin/service/list",e)},serviceInfo(e){return!e||!e.id?Promise.reject(new Error("服务ID不能为空")):(console.log("🔍 获取服务详情API-V2请求:",e),r(`/api/admin/service/info/${e.id}`))},serviceAdd(e){if(!e||!e.title)return Promise.reject(new Error("服务标题不能为空"));const t={title:e.title,cover:e.cover||"",price:e.price||0,serviceCateId:e.serviceCateId||0,servicePriceType:e.servicePriceType||0,top:e.top||0,status:e.status||1,subTitle:e.subTitle||"",introduce:e.introduce||"",explain:e.explain||"",notice:e.notice||"",cityId:e.cityId||"",cityStr:e.cityStr||"",agentIds:e.agentIds||[]};return console.log("➕ 新增服务API-V2请求数据:",t),o("/api/admin/service/add",t)},serviceUpdate(e){if(!e||!e.id)return Promise.reject(new Error("服务ID不能为空"));const t={id:e.id,title:e.title,cover:e.cover||"",price:e.price||0,serviceCateId:e.serviceCateId||0,servicePriceType:e.servicePriceType||0,top:e.top||0,status:e.status||1,subTitle:e.subTitle||"",introduce:e.introduce||"",explain:e.explain||"",notice:e.notice||"",cityId:e.cityId||"",cityStr:e.cityStr||"",agentIds:e.agentIds||[]};return console.log("✏️ 编辑服务API-V2请求数据:",t),o("/api/admin/service/update",t)},serviceDelete(e){return!e||!e.id?Promise.reject(new Error("服务ID不能为空")):(console.log("🗑️ 删除服务API-V2请求:",e),o(`/api/admin/service/delete/${e.id}`))},serviceStatus(e){return!e||!e.id?Promise.reject(new Error("服务ID不能为空")):(console.log("🔄 修改服务状态API-V2请求:",e),o(`/api/admin/service/status/${e.id}`))},agentList(e){return console.log("🔍 服务点列表API-V2请求参数:",e),r("/api/admin/agent/list",e)},agentDetail(e){return!e||!e.id?Promise.reject(new Error("服务点ID不能为空")):(console.log("🔍 获取服务点详情API-V2请求:",e),r(`/api/admin/agent/detail/${e.id}`))},agentAdd(e){if(!e||!e.name||!e.address||!e.tel)return Promise.reject(new Error("服务点名称、地址和联系电话不能为空"));const t={name:e.name,address:e.address,tel:e.tel,serviceCate:e.serviceCate||"",img:e.img||"",longitude:e.longitude||null,latitude:e.latitude||null,isGrounding:e.isGrounding||1};return console.log("➕ 新增服务点API-V2请求数据:",t),o("/api/admin/agent/add",t)},agentUpdate(e){if(!e||!e.id)return Promise.reject(new Error("服务点ID不能为空"));const t={id:e.id,name:e.name,address:e.address,tel:e.tel,serviceCate:e.serviceCate||"",img:e.img||"",longitude:e.longitude||null,latitude:e.latitude||null,isGrounding:e.isGrounding||1};return console.log("✏️ 编辑服务点API-V2请求:",t),o("/api/admin/agent/edit",t)},agentDelete(e){return!e||!e.id?Promise.reject(new Error("服务点ID不能为空")):(console.log("🗑️ 删除服务点API-V2请求:",e),o(`/api/admin/agent/delete/${e.id}`))},agentStatus(e){return!e||!e.id?Promise.reject(new Error("服务点ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 更新服务点状态API-V2请求:",e),o(`/api/admin/agent/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},priceSettingList(e){return console.log("🔍 服务配置列表API-V2请求参数:",e),r("/api/admin/priceSetting/list",e)},priceSettingInfo(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🔍 获取服务配置详情API-V2请求:",e),r(`/api/admin/priceSetting/detail/${e.id}`))},priceSettingAdd(e){return console.log("➕ 新增服务配置API-V2请求参数:",e),o("/api/admin/priceSetting/add",e)},priceSettingEdit(e){return console.log("✏️ 编辑服务配置API-V2请求参数:",e),o("/api/admin/priceSetting/edit",e)},priceSettingDelete(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🗑️ 删除服务配置API-V2请求:",e),o(`/api/admin/priceSetting/delete/${e.id}`))},priceSettingDeleteByServiceId(e){return!e||!e.serviceId?Promise.reject(new Error("服务ID不能为空")):(console.log("🗑️ 根据服务ID删除配置API-V2请求:",e),o(`/api/admin/priceSetting/deleteByServiceId/${e.serviceId}`))},serviceCateList(e){return console.log("🔍 服务分类列表API-V2请求参数:",e),r("/api/admin/serviceCate/list",e)},serviceCateInfo(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🔍 获取服务分类详情API-V2请求:",e),r(`/api/admin/serviceCate/detail/${e.id}`))},serviceCateAdd(e){if(!e||!e.name)return Promise.reject(new Error("分类名称不能为空"));const t={name:e.name,parentId:e.parentId||0,img:e.img||"",description:e.description||"",sort:e.sort||0,isRecommend:e.isRecommend||2,status:e.status||1};return console.log("➕ 新增服务分类API-V2请求数据:",t),o("/api/admin/serviceCate/add",t)},serviceCateUpdate(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("✏️ 编辑服务分类API-V2请求:",e),o("/api/admin/serviceCate/edit",e))},serviceCateDelete(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🗑️ 删除服务分类API-V2请求:",e),o(`/api/admin/serviceCate/delete/${e.id}`))},serviceCateStatus(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🔄 修改服务分类状态API-V2请求:",e),o(`/api/admin/serviceCate/status/${e.id}`))},serviceCateRecommend(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("⭐ 修改服务分类推荐状态API-V2请求:",e),o(`/api/admin/serviceCate/recommend/${e.id}`))},serviceCateParentList(e){return console.log("📂 获取父级分类列表API-V2请求:",e),r("/api/admin/serviceCate/parent/list",e)},navList(e){return console.log("🔍 金刚区列表API-V2请求参数:",e),r("/api/admin/nav/list",e)},navInfo(e){return!e||!e.id?Promise.reject(new Error("金刚区ID不能为空")):(console.log("🔍 获取金刚区详情API-V2请求:",e),r(`/api/admin/nav/detail/${e.id}`))},navAdd(e){if(!e||!e.title||!e.img)return Promise.reject(new Error("标题和图片不能为空"));const t={title:e.title,img:e.img,link:e.link||"",top:e.top||0,status:e.status||1,type:e.type||1};return console.log("➕ 新增金刚区API-V2请求数据:",t),o("/api/admin/nav/add",t)},navUpdate(e){if(!e||!e.id)return Promise.reject(new Error("金刚区ID不能为空"));const t={id:e.id,title:e.title,img:e.img,link:e.link||"",top:e.top||0,status:e.status||1};return console.log("✏️ 编辑金刚区API-V2请求数据:",t),o("/api/admin/nav/edit",t)},navDelete(e){return!e||!e.id?Promise.reject(new Error("金刚区ID不能为空")):(console.log("🗑️ 删除金刚区API-V2请求:",e),o(`/api/admin/nav/delete/${e.id}`))},navStatus(e){return!e||!e.id?Promise.reject(new Error("金刚区ID不能为空")):(console.log("🔄 修改金刚区状态API-V2请求:",e),o(`/api/admin/nav/status/${e.id}`))},priceSettingList(e){return console.log("🔍 服务项目配置列表API-V2请求参数:",e),r("/api/admin/priceSetting/list",e)},priceSettingDetail(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🔍 获取服务项目配置详情API-V2请求:",e),r(`/api/admin/priceSetting/detail/${e.id}`))},priceSettingAdd(e){if(!e||!e.problemDesc)return Promise.reject(new Error("问题描述不能为空"));const t={type:e.type||1,serviceId:e.serviceId||0,problemDesc:e.problemDesc,problemContent:e.problemContent||"",isRequired:e.isRequired||0,inputType:e.inputType||1,val:e.val||"",options:e.options||""};return console.log("➕ 新增服务项目配置API-V2请求数据:",t),o("/api/admin/priceSetting/add",t)},priceSettingEdit(e){if(!e||!e.id)return Promise.reject(new Error("配置ID不能为空"));const t={id:e.id,type:e.type||1,serviceId:e.serviceId||0,problemDesc:e.problemDesc,problemContent:e.problemContent||"",isRequired:e.isRequired||0,inputType:e.inputType||1,val:e.val||"",options:e.options||""};return console.log("✏️ 编辑服务项目配置API-V2请求数据:",t),o("/api/admin/priceSetting/edit",t)},priceSettingDelete(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🗑️ 删除服务项目配置API-V2请求:",e),o(`/api/admin/priceSetting/delete/${e.id}`))},priceSettingDeleteByServiceId(e){return!e||!e.serviceId?Promise.reject(new Error("服务ID不能为空")):(console.log("🗑️ 根据服务ID删除配置API-V2请求:",e),o(`/api/admin/priceSetting/deleteByServiceId/${e.serviceId}`))},priceSettingTemplate(){return console.log("📥 下载服务配置模板API-V2请求"),r("/api/admin/priceSetting/template")},priceSettingImport(e){return!e||!e.get("file")?Promise.reject(new Error("请选择要导入的文件")):(console.log("📤 批量导入服务配置API-V2请求"),Pe("/api/admin/priceSetting/import",e))}},wt={categoryList(e){return console.log("📂 分类列表API-V2请求参数:",e),r("/api/admin/category/list",e)},categoryTree(e){return console.log("🌳 分类树形结构API-V2请求参数:",e),r("/api/admin/category/tree",e)},categoryInfo(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🔍 获取分类详情API-V2请求:",e),r(`/api/admin/category/info/${e.id}`))},categoryAdd(e){if(!e||!e.name)return Promise.reject(new Error("分类名称不能为空"));const t={name:e.name,icon:e.icon||"",parentId:e.parentId||0,sort:e.sort||0,description:e.description||"",status:e.status||1};return console.log("➕ 新增分类API-V2请求数据:",t),o("/api/admin/category/add",t)},categoryUpdate(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("✏️ 编辑分类API-V2请求:",e),o("/api/admin/category/update",e))},categoryDelete(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🗑️ 删除分类API-V2请求:",e),o(`/api/admin/category/delete/${e.id}`))},categoryStatus(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 更新分类状态API-V2请求:",e),o(`/api/admin/category/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},categoryMove(e){return!e||!e.id?Promise.reject(new Error("分类ID不能为空")):(console.log("🔄 移动分类API-V2请求:",e),o("/api/admin/category/move",e))},categorySortBatch(e){return!e||!e.sortData||!Array.isArray(e.sortData)?Promise.reject(new Error("排序数据不能为空")):(console.log("🔢 批量更新分类排序API-V2请求:",e),o("/api/admin/category/sort/batch",e))},categoryServiceCount(e){return!e||!e.categoryId?Promise.reject(new Error("分类ID不能为空")):(console.log("📊 获取分类服务数量API-V2请求:",e),r(`/api/admin/category/service/count/${e.categoryId}`))},categoryCopy(e){return!e||!e.id||!e.newName?Promise.reject(new Error("源分类ID和新分类名称不能为空")):(console.log("📋 复制分类API-V2请求:",e),o("/api/admin/category/copy",e))},categoryPath(e){return!e||!e.categoryId?Promise.reject(new Error("分类ID不能为空")):(console.log("🛤️ 获取分类路径API-V2请求:",e),r(`/api/admin/category/path/${e.categoryId}`))},categoryNameCheck(e){return!e||!e.name?Promise.reject(new Error("分类名称不能为空")):(console.log("✅ 检查分类名称API-V2请求:",e),o("/api/admin/category/name/check",e))}},Dt={technicianList(e){return console.log("🔍 师傅列表API-V2请求参数:",e),r("/api/admin/technician/list",e)},technicianInfo(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):(console.log("🔍 获取师傅详情API-V2请求:",e),r(`/api/admin/technician/info/${e.id}`))},technicianAdd(e){if(!e||!e.name||!e.phone)return Promise.reject(new Error("师傅姓名和手机号不能为空"));const t={name:e.name,phone:e.phone,avatar:e.avatar||"",level:e.level||1,deposit:e.deposit||0,distance:e.distance||10,status:e.status||1};return console.log("➕ 新增师傅API-V2请求数据:",t),o("/api/admin/technician/add",t)},technicianUpdate(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):(console.log("✏️ 编辑师傅API-V2请求:",e),o("/api/admin/technician/update",e))},technicianDelete(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):(console.log("🗑️ 删除师傅API-V2请求:",e),o(`/api/admin/technician/delete/${e.id}`))},technicianStatus(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 更新师傅状态API-V2请求:",e),o(`/api/admin/technician/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},technicianLevelList(e){return console.log("📊 师傅等级列表API-V2请求参数:",e),r("/api/admin/technician/level/list",e)},technicianLevelAdd(e){return console.log("➕ 新增师傅等级API-V2请求数据:",e),o("/api/admin/technician/level/add",e)},technicianDepositList(e){return console.log("💰 师傅押金记录API-V2请求参数:",e),r("/api/admin/technician/deposit/list",e)},technicianDistanceSet(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):(console.log("📍 设置师傅接单范围API-V2请求:",e),o("/api/admin/technician/distance/set",e))},labelList(e={}){return console.log("🏷️ 师傅等级列表API-V2请求参数:",e),r("/api/admin/label/list",e)},labelCoachList(){return console.log("📋 师傅等级选择列表API-V2请求"),r("/api/admin/label/coachLabelList")},labelAdd(e){if(!e||!e.labelName)return Promise.reject(new Error("标签名不能为空"));const t={labelName:e.labelName,earnestMoney:e.earnestMoney||0,delayTime:e.delayTime||0,proportion:e.proportion||0};return console.log("➕ 新增师傅等级API-V2请求数据:",t),o("/api/admin/label/add",t)},labelEdit(e){return!e||!e.id?Promise.reject(new Error("师傅等级ID不能为空")):(console.log("✏️ 编辑师傅等级API-V2请求:",e),o("/api/admin/label/edit",e))},labelDelete(e){return!e||!e.id?Promise.reject(new Error("师傅等级ID不能为空")):(console.log("🗑️ 删除师傅等级API-V2请求:",e),o(`/api/admin/label/delete/${e.id}`))},creditAdd(e){if(!e||!e.labelId||!e.star||!e.scorePeriod)return Promise.reject(new Error("师傅等级ID、星级评价和分数区间不能为空"));const t={labelId:e.labelId,star:e.star,scorePeriod:e.scorePeriod,fixedPrice:e.fixedPrice||0,comparisonOrder:e.comparisonOrder||0,quotationsNum:e.quotationsNum||0};return console.log("➕ 新增师傅信誉分规则API-V2请求数据:",t),o("/api/admin/credit/add",t)},creditEdit(e){return!e||!e.labelId||!e.star||!e.scorePeriod?Promise.reject(new Error("师傅等级ID、星级评价和分数区间不能为空")):(console.log("✏️ 编辑师傅信誉分规则API-V2请求:",e),o("/api/admin/credit/edit",e))},creditDelete(e){return!e||!e.id?Promise.reject(new Error("信誉分规则ID不能为空")):(console.log("🗑️ 删除师傅信誉分规则API-V2请求:",e),o(`/api/admin/credit/delete/${e.id}`))},coachList(e){return console.log("👨‍🔧 师傅列表查询API-V2请求参数:",e),r("/api/admin/coach/list",e)},coachDetail(e){return e?(console.log("🔍 查询师傅详情API-V2请求:",e),r(`/api/admin/coach/${e}`)):Promise.reject(new Error("师傅ID不能为空"))},coachUpDateLevel(e){return!e||!e.coachId||!e.labelId||!e.labelName?Promise.reject(new Error("师傅ID、等级ID和等级名称不能为空")):(console.log("⬆️ 调整师傅等级API-V2请求:",e),o("/api/admin/coach/upDateCoachLevel",e))},coachStatus(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):[-1,0,1].includes(e.status)?(console.log("🔄 师傅状态变更API-V2请求:",e),o("/api/admin/coach/status",e)):Promise.reject(new Error("状态值无效，必须是-1、0或1"))},coachApprove(e){return!e||!e.id?Promise.reject(new Error("师傅ID不能为空")):(console.log("✅ 师傅审核通过API-V2请求:",e),o("/api/admin/audit/coach/approve",e))},coachReject(e){return!e||!e.coachId||!e.shText?Promise.reject(new Error("师傅ID和驳回原因不能为空")):(console.log("❌ 师傅审核驳回API-V2请求:",e),o("/api/admin/audit/coach/reject",e))},getAssociateUser(e){return console.log("👥 获取关联用户列表API-V2请求参数:",e),r("/api/admin/coach/getAssociateUser",e)},addCoach(e){return!e||!e.coachName||!e.mobile?Promise.reject(new Error("师傅姓名和手机号不能为空")):(console.log("➕ 后台新增师傅API-V2请求数据:",e),o("/api/admin/coach/addCoach",e))},coachLogList(e){return console.log("📋 师傅日志列表查询API-V2请求参数:",e),r("/api/admin/coach/log",e)},cityTree(){return console.log("🏙️ 获取城市树形列表API-V2请求"),r("/api/admin/coach/cityTree")},getRegionData(){return console.log("🌍 获取省市区数据API-V2请求"),r("/api/admin/region/tree")},getCityTree(){return console.log("🏙️ 获取省市区三级联动数据API-V2请求"),r("/api/core/city/tree")},addOpenCity(e){if(!e||!e.cityId||!e.cityStr)return Promise.reject(new Error("城市ID和城市字符串不能为空"));const t={cityId:e.cityId,cityStr:e.cityStr};return console.log("➕ 添加开放城市API-V2请求数据:",t),o("/api/admin/coach/addOpenCity",t)},batchAddOpenCity(e){if(!e||!Array.isArray(e)||e.length===0)return Promise.reject(new Error("城市数据数组不能为空"));for(let t=0;t<e.length;t++){const n=e[t];if(!n.cityId||!n.cityStr)return Promise.reject(new Error(`第${t+1}个城市数据不完整`))}return console.log("📦 批量添加开放城市API-V2请求数据:",e),o("/api/admin/coach/batch",e)},openCityList(){return console.log("📋 查看开放城市列表API-V2请求"),r("/api/admin/coach/openCityList")},deleteOpenCity(e){return!e||!e.ids||!Array.isArray(e.ids)||e.ids.length===0?Promise.reject(new Error("城市ID数组不能为空")):(console.log("🗑️ 删除开放城市API-V2请求数据:",e),o("/api/admin/coach/deleteOpenCity",e))},getCoachConfig(){return console.log("⚙️ 获取师傅相关配置API-V2请求"),r("/api/admin/coach/getCoachConfig")},coachConfigUpdate(e){if(!e||e.cashPledge===void 0||e.commissionRatio===void 0||!e.entryNotice||e.distance===void 0||e.quotationSum===void 0||e.quotationWaitTime===void 0)return Promise.reject(new Error("缴纳保证金、平台抽佣比例、入驻须知、接单距离、最大报价人数和等待时间不能为空"));const t={cashPledge:e.cashPledge,commissionRatio:e.commissionRatio,entryNotice:e.entryNotice,distance:e.distance,quotationSum:e.quotationSum,quotationWaitTime:e.quotationWaitTime};return console.log("✏️ 师傅相关配置更新API-V2请求数据:",t),o("/api/admin/coach/coachConfigUpdate",t)},addBlack(e){if(!e||!e.coachId||!e.text||e.status===void 0)return Promise.reject(new Error("师傅ID、审核备注和操作状态不能为空"));if(![0,1].includes(e.status))return Promise.reject(new Error("操作状态无效，必须是0或1"));const t={coachId:e.coachId,text:e.text,status:e.status};return console.log("🚫 加入/移除黑名单API-V2请求数据:",t),o("/api/admin/coach/addBlack",t)},blackList(e={}){const n={...{pageNum:1,pageSize:10},...e};return console.log("📋 查看黑名单列表API-V2请求参数:",n),r("/api/admin/coach/blackList",n)},blackPermission(e){if(!e||!e.coachId||e.banTx===void 0||e.banTk===void 0||e.banDl===void 0)return Promise.reject(new Error("师傅ID和所有权限设置不能为空"));const t={coachId:e.coachId,banTx:e.banTx,banTk:e.banTk,banDl:e.banDl};return console.log("🔒 权限处理API-V2请求数据:",t),o("/api/admin/coach/blackPermission",t)},coachLog(e){return console.log("📋 师傅日志查询API-V2请求参数:",e),r("/api/admin/coach/log",e)}},Vt={couponList(e){return console.log("🎫 优惠券列表API-V2请求参数:",e),r("/api/admin/coupon/list",e)},couponInfo(e){return!e||!e.id?Promise.reject(new Error("优惠券ID不能为空")):(console.log("🔍 获取优惠券详情API-V2请求:",e),r(`/api/admin/coupon/detail/${e.id}`))},couponAdd(e){if(!e||!e.title)return Promise.reject(new Error("优惠券标题不能为空"));const n={coupon:{title:e.title,type:e.type||0,full:e.full||0,discount:e.discount||0,sendType:e.sendType||0,timeLimit:e.timeLimit||0,startTime:e.startTime||"",endTime:e.endTime||"",day:e.day||0,stock:e.stock||100,userLimit:e.userLimit||1,rule:e.rule||"",text:e.text||"",top:e.top||0,status:e.status||1},goodsIds:e.goodsIds||[]};return console.log("➕ 新增优惠券API-V2请求数据:",n),o("/api/admin/coupon/add",n)},couponUpdate(e){if(!e||!e.id)return Promise.reject(new Error("优惠券ID不能为空"));const{goodsIds:t,...n}=e,i={coupon:n,goodsIds:t||[]};return console.log("✏️ 编辑优惠券API-V2请求数据:",i),o("/api/admin/coupon/edit",i)},couponDelete(e){return!e||!e.id?Promise.reject(new Error("优惠券ID不能为空")):(console.log("🗑️ 删除优惠券API-V2请求:",e),o(`/api/admin/coupon/delete/${e.id}`))},couponStatus(e){return!e||!e.id?Promise.reject(new Error("优惠券ID不能为空")):(console.log("🔄 修改优惠券状态API-V2请求:",e),o(`/api/admin/coupon/status/${e.id}`))},noticeList(e){return console.log("📢 公告列表API-V2请求参数:",e),r("/api/admin/notice/list",e)},noticeInfo(e){return!e||!e.id?Promise.reject(new Error("公告ID不能为空")):(console.log("🔍 获取公告详情API-V2请求:",e),r(`/api/admin/notice/detail/${e.id}`))},noticeAdd(e){if(!e||!e.content)return Promise.reject(new Error("公告内容不能为空"));const t={content:e.content,type:e.type||1,status:e.status||1};return console.log("➕ 新增公告API-V2请求数据:",t),o("/api/admin/notice/add",t)},noticeUpdate(e){return!e||!e.id?Promise.reject(new Error("公告ID不能为空")):(console.log("✏️ 编辑公告API-V2请求:",e),o("/api/admin/notice/edit",e))},noticeDelete(e){return!e||!e.id?Promise.reject(new Error("公告ID不能为空")):(console.log("🗑️ 删除公告API-V2请求:",e),o(`/api/admin/notice/delete/${e.id}`))},noticeStatus(e){return!e||!e.id?Promise.reject(new Error("公告ID不能为空")):(console.log("🔄 修改公告状态API-V2请求:",e),o(`/api/admin/notice/status/${e.id}`))},partnerIds(){return console.log("🆔 获取合伙人ID集合API-V2请求"),r("/api/admin/partner/ids")},partnerList(e){return console.log("🤝 合伙人列表API-V2请求参数:",e),r("/api/admin/partner/list",e)},partnerAdd(e){if(!e||!e.userId)return Promise.reject(new Error("用户ID不能为空"));const t={userId:e.userId,level:e.level||1,commissionRate1:e.commissionRate1||0,commissionRate2:e.commissionRate2||0,status:e.status||1};return console.log("➕ 新增合伙人API-V2请求数据:",t),o("/api/admin/partner/add",t)},partnerStatus(e){return!e||!e.id?Promise.reject(new Error("合伙人ID不能为空")):(console.log("🔄 修改合伙人状态API-V2请求:",e),o(`/api/admin/partner/status/${e.id}`))},partnerUpdateLevelAndCommission(e){if(!e||!e.id)return Promise.reject(new Error("合伙人ID不能为空"));const t=new FormData;return e.level!==void 0&&t.append("level",e.level),e.commissionRate1!==void 0&&t.append("commissionRate1",e.commissionRate1),e.commissionRate2!==void 0&&t.append("commissionRate2",e.commissionRate2),console.log("📊 等级/分佣比例调整API-V2请求:",e),o(`/api/admin/partner/updateLevelAndCommission/${e.id}`,t,"multipart/form-data")},partnerInviteList(e){return console.log("📋 合伙人邀请列表API-V2请求参数:",e),r("/api/admin/partner/inviteList",e)},partnerCommissionList(e){return console.log("💰 合伙人佣金统计列表API-V2请求参数:",e),r("/api/admin/partner/commission",e)},partnerOrdersList(e){return console.log("📦 合伙人推广订单列表API-V2请求参数:",e),r("/api/admin/partner/orders",e)},partnerInviteListExport(e){return console.log("📤 合伙人邀请列表导出API-V2请求参数:",e),r("/api/admin/partner/inviteList/export",e)},partnerCommissionExport(e){return console.log("📤 合伙人佣金统计导出API-V2请求参数:",e),r("/api/admin/partner/commission/export",e)},partnerOrdersExport(e){return console.log("📤 合伙人推广订单导出API-V2请求参数:",e),r("/api/admin/partner/orders/export",e)}},Tt={orderList(e){return console.log("📋 订单列表API-V2请求参数:",e),o("/api/admin/order/list",e)},orderExport(e){return console.log("📤 导出订单列表API-V2请求参数:",e),o("/api/admin/order/export",e)},orderDetail(e){return e?(console.log("🔍 获取订单详情API-V2请求:",e),r(`/api/admin/order/detail/${e}`)):Promise.reject(new Error("订单ID不能为空"))},coachIncomeRank(e){return console.log("💰 师傅收入排行榜API-V2请求参数:",e),r("/api/admin/order/coach-income",e)},coachCancelRank(e){return console.log("🏃 师傅跑单排行榜API-V2请求参数:",e),r("/api/admin/order/coach-cancel",e)},userCancelRank(e){return console.log("👤 用户跑单排行榜API-V2请求参数:",e),r("/api/admin/order/user-cancel",e)},refundList(e){return console.log("💰 退款列表查询API-V2请求参数:",e),r("/api/admin/refund/list",e)},orderStatus(e){return!e||!e.id?Promise.reject(new Error("订单ID不能为空")):(console.log("🔄 更新订单状态API-V2请求:",e),o(`/api/admin/order/status/${e.id}`,e))},orderCancel(e){return!e||!e.id?Promise.reject(new Error("订单ID不能为空")):(console.log("❌ 取消订单API-V2请求:",e),o(`/api/admin/order/cancel/${e.id}`,e))},refundList(e){return console.log("💰 退款列表API-V2请求参数:",e),r("/api/admin/refund/list",e)},refundInfo(e){return!e||!e.id?Promise.reject(new Error("退款ID不能为空")):(console.log("🔍 获取退款详情API-V2请求:",e),r(`/api/admin/refund/info/${e.id}`))},refundHandle(e){return!e||!e.id?Promise.reject(new Error("退款ID不能为空")):[2,3].includes(e.status)?(console.log("✅ 处理退款申请API-V2请求:",e),o(`/api/admin/refund/handle/${e.id}`,e)):Promise.reject(new Error("处理状态无效"))},orderRefundList(e){return console.log("💰 订单退款列表API-V2请求参数:",e),r("/api/admin/order/refundList",e)},passRefund(e){return!e||!e.id?Promise.reject(new Error("申请退款记录ID不能为空")):e.price===void 0||e.price===null?Promise.reject(new Error("退款金额不能为空")):(console.log("✅ 同意退款API-V2请求:",e),o("/api/admin/order/passRefund",e))},noPassRefund(e){return!e||!e.id?Promise.reject(new Error("申请退款记录ID不能为空")):e.price===void 0||e.price===null?Promise.reject(new Error("退款金额不能为空")):e.text?(console.log("❌ 拒绝退款API-V2请求:",e),o("/api/admin/order/noPassRefund",e)):Promise.reject(new Error("备注文本不能为空"))},orderRefundDetail(e){return!e||!e.id?Promise.reject(new Error("退款记录ID不能为空")):(console.log("🔍 获取订单退款详情API-V2请求:",e),r(`/api/admin/order/refundDetail/${e.id}`))},diffPassRefund(e){return!e||!e.id?Promise.reject(new Error("差价退款记录ID不能为空")):e.price===void 0||e.price===null?Promise.reject(new Error("退款金额不能为空")):(console.log("✅ 差价同意退款API-V2请求:",e),o("/api/admin/order/diffPassRefund",e))},diffNoPassRefund(e){return!e||!e.id?Promise.reject(new Error("差价退款记录ID不能为空")):e.price===void 0||e.price===null?Promise.reject(new Error("退款金额不能为空")):e.text?(console.log("❌ 差价拒绝退款API-V2请求:",e),o("/api/admin/order/diffNoPassRefund",e)):Promise.reject(new Error("拒绝原因不能为空"))},commentList(e){return console.log("⭐ 评价列表API-V2请求参数:",e),r("/api/admin/comment/list",e)},commentDetail(e){return!e||!e.id?Promise.reject(new Error("评价ID不能为空")):(console.log("🔍 获取评价详情API-V2请求:",e),r(`/api/admin/comment/detail/${e.id}`))},commentDelete(e){return!e||!e.id?Promise.reject(new Error("评价ID不能为空")):(console.log("🗑️ 删除评价API-V2请求:",e),o(`/api/admin/comment/delete/${e.id}`))},commentStatus(e){return!e||!e.id?Promise.reject(new Error("评价ID不能为空")):(console.log("🔄 修改评价状态API-V2请求:",e),o(`/api/admin/comment/status/${e.id}`))},commissionList(e){return console.log("💵 分销佣金列表API-V2请求参数:",e),o("/api/admin/commission/list",e)},commissionSettle(e){return!e||!e.id?Promise.reject(new Error("佣金记录ID不能为空")):(console.log("💰 结算分销佣金API-V2请求:",e),o(`/api/admin/commission/settle/${e.id}`))},commissionWithdraw(e){return console.log("💸 申请佣金提现API-V2请求:",e),o("/api/admin/commission/withdraw",e)},afterSaleList(e){return console.log("🔧 售后列表API-V2请求参数:",e),r("/api/admin/aftersale/list",e)},afterSaleHandle(e){return!e||!e.id?Promise.reject(new Error("售后ID不能为空")):(console.log("✅ 处理售后申请API-V2请求:",e),o(`/api/admin/aftersale/handle/${e.id}`,e))}},bt={distributorExamineList(e){return console.log("📋 分销商审核列表API-V2请求参数:",e),r("/api/admin/distribution/examine/list",e)},distributorExamineInfo(e){return!e||!e.id?Promise.reject(new Error("申请ID不能为空")):(console.log("🔍 获取分销商审核详情API-V2请求:",e),r(`/api/admin/distribution/examine/info/${e.id}`))},distributorExamineHandle(e){return!e||!e.id?Promise.reject(new Error("申请ID不能为空")):[2,3].includes(e.status)?(console.log("✅ 审核分销商申请API-V2请求:",e),o(`/api/admin/distribution/examine/handle/${e.id}`,e)):Promise.reject(new Error("审核状态无效"))},distributorList(e){return console.log("👥 分销商列表API-V2请求参数:",e),r("/api/admin/distribution/list",e)},distributorInfo(e){return!e||!e.id?Promise.reject(new Error("分销商ID不能为空")):(console.log("🔍 获取分销商详情API-V2请求:",e),r(`/api/admin/distribution/info/${e.id}`))},distributorAdd(e){if(!e||!e.name||!e.phone)return Promise.reject(new Error("分销商姓名和手机号不能为空"));const t={name:e.name,phone:e.phone,avatar:e.avatar||"",level:e.level||1,commissionRate:e.commissionRate||10,status:e.status||1};return console.log("➕ 新增分销商API-V2请求数据:",t),o("/api/admin/distribution/add",t)},distributorUpdate(e){return!e||!e.id?Promise.reject(new Error("分销商ID不能为空")):(console.log("✏️ 编辑分销商API-V2请求:",e),o("/api/admin/distribution/update",e))},distributorDelete(e){return!e||!e.id?Promise.reject(new Error("分销商ID不能为空")):(console.log("🗑️ 删除分销商API-V2请求:",e),o(`/api/admin/distribution/delete/${e.id}`))},distributorStatus(e){return!e||!e.id?Promise.reject(new Error("分销商ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 更新分销商状态API-V2请求:",e),o(`/api/admin/distribution/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},distributionSettingInfo(){return console.log("⚙️ 获取分销设置API-V2请求"),r("/api/admin/distribution/setting/info")},distributionSettingUpdate(e){return console.log("⚙️ 更新分销设置API-V2请求:",e),o("/api/admin/distribution/setting/update",e)},distributorPerformance(e){return console.log("📊 分销商业绩统计API-V2请求参数:",e),r("/api/admin/distribution/performance",e)},distributorSubordinateList(e){return!e||!e.distributorId?Promise.reject(new Error("分销商ID不能为空")):(console.log("👥 分销商下级列表API-V2请求参数:",e),r("/api/admin/distribution/subordinate/list",e))}},Lt={financeList(e){return console.log("💰 财务列表API-V2请求参数:",e),r("/api/admin/finance/list",e)},financeInfo(e){return!e||!e.id?Promise.reject(new Error("财务记录ID不能为空")):(console.log("🔍 获取财务详情API-V2请求:",e),r(`/api/admin/finance/info/${e.id}`))},financeStatistics(e){return console.log("📊 财务统计API-V2请求参数:",e),r("/api/admin/finance/statistics",e)},financeExport(e){return console.log("📤 导出财务报表API-V2请求参数:",e),o("/api/admin/finance/export",e)},withdrawList(e){return console.log("💸 提现列表API-V2请求参数:",e),r("/api/admin/finance/withdraw/list",e)},withdrawInfo(e){return!e||!e.id?Promise.reject(new Error("提现记录ID不能为空")):(console.log("🔍 获取提现详情API-V2请求:",e),r(`/api/admin/finance/withdraw/info/${e.id}`))},withdrawHandle(e){return!e||!e.id?Promise.reject(new Error("提现记录ID不能为空")):[2,3].includes(e.status)?(console.log("✅ 审核提现申请API-V2请求:",e),o(`/api/admin/finance/withdraw/handle/${e.id}`,e)):Promise.reject(new Error("审核状态无效"))},withdrawConfirm(e){return!e||!e.id?Promise.reject(new Error("提现记录ID不能为空")):(console.log("✅ 确认提现到账API-V2请求:",e),o(`/api/admin/finance/withdraw/confirm/${e.id}`,e))},storedList(e){return console.log("💳 储值列表API-V2请求参数:",e),r("/api/admin/finance/stored/list",e)},storedInfo(e){return!e||!e.id?Promise.reject(new Error("储值记录ID不能为空")):(console.log("🔍 获取储值详情API-V2请求:",e),r(`/api/admin/finance/stored/info/${e.id}`))},storedRecharge(e){return!e||!e.userId||!e.amount?Promise.reject(new Error("用户ID和充值金额不能为空")):e.amount<=0?Promise.reject(new Error("充值金额必须大于0")):(console.log("💰 手动充值API-V2请求:",e),o("/api/admin/finance/stored/recharge",e))},userBalance(e){return!e||!e.userId?Promise.reject(new Error("用户ID不能为空")):(console.log("💰 获取用户余额API-V2请求:",e),r(`/api/admin/finance/balance/${e.userId}`))},financeOverview(e){return console.log("📊 财务概览API-V2请求参数:",e),r("/api/admin/finance/overview",e)},walletList(e){return console.log("💸 提现管理列表API-V2请求参数:",e),r("/api/admin/wallet/list",e)},walletStats(e){return console.log("📊 提现汇总统计API-V2请求参数:",e),r("/api/admin/wallet/stats",e)},walletAudit(e){if(!e||!e.id)return Promise.reject(new Error("提现记录ID不能为空"));if(![0,1,2].includes(e.lock))return Promise.reject(new Error("审核状态无效"));console.log("✅ 审核提现记录API-V2请求:",e);const t=new FormData;return t.append("id",e.id),t.append("lock",e.lock),t.append("adminId",e.adminId),t.append("text",e.text),o("/api/admin/wallet/audit",t,"multipart/form-data")},operatorBalance(){return console.log("💰 查询运营账户余额API-V2请求"),r("/api/admin/wallet/operatorBalance")},operatorBalanceSet(e){if(!e||!e.amount)return Promise.reject(new Error("金额不能为空"));console.log("💰 设置运营账户余额API-V2请求:",e);const t=new FormData;return t.append("amount",e.amount),o("/api/admin/wallet/operatorBalance/set",t,"multipart/form-data")},operatorBalanceIncrease(e){if(!e||!e.amount)return Promise.reject(new Error("金额不能为空"));console.log("💰 增加运营账户余额API-V2请求:",e);const t=new FormData;return t.append("amount",e.amount),o("/api/admin/wallet/operatorBalance/increase",t,"multipart/form-data")},operatorBalanceDecrease(e){if(!e||!e.amount)return Promise.reject(new Error("金额不能为空"));console.log("💰 扣减运营账户余额API-V2请求:",e);const t=new FormData;return t.append("amount",e.amount),o("/api/admin/wallet/operatorBalance/decrease",t,"multipart/form-data")},list(e){return console.log("💰 财务流水明细查询API-V2请求参数:",e),o("/api/admin/finance/list",e)},export(e){return console.log("📤 财务流水明细导出API-V2请求参数:",e),o("/api/admin/finance/export",e)},withdrawHistoryList(e){return console.log("📋 历史提现列表API-V2请求参数:",e),r("/api/admin/wallet/history/page",e)},withdrawHistoryExport(e){return console.log("📤 历史提现列表导出API-V2请求参数:",e),r("/api/admin/wallet/history/export",e)}},Rt={userList(e){return console.log("👥 用户列表API-V2请求参数:",e),r("/api/admin/user/list",e)},userInfo(e){return!e||!e.id?Promise.reject(new Error("用户ID不能为空")):(console.log("🔍 获取用户详情API-V2请求:",e),r(`/api/admin/user/info/${e.id}`))},userAdd(e){if(!e||!e.nickname||!e.phone)return Promise.reject(new Error("用户昵称和手机号不能为空"));const t={nickname:e.nickname,phone:e.phone,avatar:e.avatar||"",gender:e.gender||0,birthday:e.birthday||"",status:e.status||1};return console.log("➕ 新增用户API-V2请求数据:",t),o("/api/admin/user/add",t)},userUpdate(e){if(!e||!e.id)return Promise.reject(new Error("用户ID不能为空"));if(!e.nickName)return Promise.reject(new Error("用户昵称不能为空"));const t={id:e.id,nickName:e.nickName,avatarUrl:e.avatarUrl||""};return console.log("✏️ 编辑用户API-V2请求:",t),o("/api/admin/user/update",t)},userAddBlack(e){if(!e||!e.id)return Promise.reject(new Error("用户ID不能为空"));if(!e.text)return Promise.reject(new Error("审核备注不能为空"));if(![0,1].includes(e.status))return Promise.reject(new Error("操作状态无效"));const t={id:e.id,text:e.text,status:e.status};return console.log("🚫 加入/移除黑名单API-V2请求:",t),o("/api/admin/user/addBlack",t)},userStatus(e){return console.log("🔄 用户状态变更API-V2请求:",e),o("/api/admin/user/status",e)},userLog(e){return console.log("📋 用户操作日志API-V2请求参数:",e),r("/api/admin/user/log",e)},userDelete(e){return!e||!e.id?Promise.reject(new Error("用户ID不能为空")):(console.log("🗑️ 删除用户API-V2请求:",e),o(`/api/admin/user/delete/${e.id}`))},userResetPassword(e){return!e||!e.id||!e.newPassword?Promise.reject(new Error("用户ID和新密码不能为空")):(console.log("🔑 重置用户密码API-V2请求:",{id:e.id}),o(`/api/admin/user/resetPassword/${e.id}`,{newPassword:e.newPassword}))},userOrderList(e){return!e||!e.userId?Promise.reject(new Error("用户ID不能为空")):(console.log("📋 用户订单列表API-V2请求参数:",e),r("/api/admin/user/orders",e))},userConsumptionStats(e){return!e||!e.userId?Promise.reject(new Error("用户ID不能为空")):(console.log("📊 用户消费统计API-V2请求参数:",e),r("/api/admin/user/consumption/stats",e))},userBalanceRecord(e){return!e||!e.userId?Promise.reject(new Error("用户ID不能为空")):(console.log("💰 用户余额记录API-V2请求参数:",e),r("/api/admin/user/balance/record",e))},userPointRecord(e){return!e||!e.userId?Promise.reject(new Error("用户ID不能为空")):(console.log("⭐ 用户积分记录API-V2请求参数:",e),r("/api/admin/user/point/record",e))},userPointAdjust(e){return!e||!e.userId||e.points===void 0?Promise.reject(new Error("用户ID和积分数量不能为空")):(console.log("⭐ 调整用户积分API-V2请求:",e),o("/api/admin/user/point/adjust",e))},userExport(e){return console.log("📤 导出用户列表API-V2请求参数:",e),o("/api/admin/user/export",e)}},Ct={adminList(e){return console.log("👤 管理员列表API-V2请求参数:",e),r("/api/admin/account/admin/list",e)},adminInfo(e){return!e||!e.id?Promise.reject(new Error("管理员ID不能为空")):(console.log("🔍 获取管理员详情API-V2请求:",e),r(`/api/admin/account/admin/info/${e.id}`))},adminAdd(e){if(!e||!e.username||!e.password)return Promise.reject(new Error("用户名和密码不能为空"));const t={username:e.username,password:e.password,realName:e.realName||"",phone:e.phone||"",email:e.email||"",roleId:e.roleId||1,status:e.status||1};return console.log("➕ 新增管理员API-V2请求数据:",t),o("/api/admin/account/admin/add",t)},adminUpdate(e){return!e||!e.id?Promise.reject(new Error("管理员ID不能为空")):(console.log("✏️ 编辑管理员API-V2请求:",e),o("/api/admin/account/admin/update",e))},adminDelete(e){return!e||!e.id?Promise.reject(new Error("管理员ID不能为空")):(console.log("🗑️ 删除管理员API-V2请求:",e),o(`/api/admin/account/admin/delete/${e.id}`))},adminStatus(e){return!e||!e.id?Promise.reject(new Error("管理员ID不能为空")):[1,-1].includes(e.status)?(console.log("🔄 更新管理员状态API-V2请求:",e),o(`/api/admin/account/admin/status/${e.id}`,{status:e.status})):Promise.reject(new Error("状态值无效"))},roleList(e){return console.log("🎭 角色列表API-V2请求参数:",e),r("/api/admin/account/role/list",e)},roleInfo(e){return!e||!e.id?Promise.reject(new Error("角色ID不能为空")):(console.log("🔍 获取角色详情API-V2请求:",e),r(`/api/admin/account/role/info/${e.id}`))},roleAdd(e){if(!e||!e.name)return Promise.reject(new Error("角色名称不能为空"));const t={name:e.name,description:e.description||"",permissions:e.permissions||[],status:e.status||1};return console.log("➕ 新增角色API-V2请求数据:",t),o("/api/admin/account/role/add",t)},roleUpdate(e){return!e||!e.id?Promise.reject(new Error("角色ID不能为空")):(console.log("✏️ 编辑角色API-V2请求:",e),o("/api/admin/account/role/update",e))},roleDelete(e){return!e||!e.id?Promise.reject(new Error("角色ID不能为空")):(console.log("🗑️ 删除角色API-V2请求:",e),o(`/api/admin/account/role/delete/${e.id}`))},menuList(e){return console.log("📋 菜单列表API-V2请求参数:",e),r("/api/admin/account/menu/list",e)},menuTree(){return console.log("🌳 菜单树形结构API-V2请求"),r("/api/admin/account/menu/tree")},menuAdd(e){if(!e||!e.name||!e.path)return Promise.reject(new Error("菜单名称和路径不能为空"));const t={name:e.name,path:e.path,icon:e.icon||"",parentId:e.parentId||0,sort:e.sort||0,status:e.status||1};return console.log("➕ 新增菜单API-V2请求数据:",t),o("/api/admin/account/menu/add",t)},menuUpdate(e){return!e||!e.id?Promise.reject(new Error("菜单ID不能为空")):(console.log("✏️ 编辑菜单API-V2请求:",e),o("/api/admin/account/menu/update",e))},menuDelete(e){return!e||!e.id?Promise.reject(new Error("菜单ID不能为空")):(console.log("🗑️ 删除菜单API-V2请求:",e),o(`/api/admin/account/menu/delete/${e.id}`))},franchiseeList(e){return console.log("🏢 代理商列表API-V2请求参数:",e),r("/api/admin/account/franchisee/list",e)},franchiseeAdd(e){return!e||!e.name||!e.contact?Promise.reject(new Error("代理商名称和联系人不能为空")):(console.log("➕ 新增代理商API-V2请求数据:",e),o("/api/admin/account/franchisee/add",e))},franchiseeUpdate(e){return!e||!e.id?Promise.reject(new Error("代理商ID不能为空")):(console.log("✏️ 编辑代理商API-V2请求:",e),o("/api/admin/account/franchisee/update",e))},franchiseeDelete(e){return!e||!e.id?Promise.reject(new Error("代理商ID不能为空")):(console.log("🗑️ 删除代理商API-V2请求:",e),o(`/api/admin/account/franchisee/delete/${e.id}`))},agentList(e){return console.log("🏢 代理商列表API-V2请求参数:",e),r("/api/admin/manage/agent/list",e)},agentStatus(e){return!e||!e.id?Promise.reject(new Error("代理商ID不能为空")):(console.log("🔄 修改代理商状态API-V2请求:",e),o(`/api/admin/manage/agent/status/${e.id}`))},agentExamine(e){return!e||!e.id?Promise.reject(new Error("代理商ID不能为空")):(console.log("✅ 代理商审核API-V2请求:",e),o("/api/admin/manage/agent/examine",e))},agentAdd(e){return!e||!e.username?Promise.reject(new Error("账号不能为空")):(console.log("➕ 新增代理商API-V2请求数据:",e),o("/api/admin/manage/agent/add",e))},agentEdit(e){return!e||!e.id?Promise.reject(new Error("代理商ID不能为空")):(console.log("✏️ 修改代理商API-V2请求:",e),o("/api/admin/manage/agent/edit",e))},agentDefaultPassword(){return console.log("🔑 修改代理商默认密码API-V2请求"),o("/api/admin/manage/default/102")},agentCityTree(){return console.log("🌳 获取代理城市树API-V2请求"),r("/api/admin/manage/tree")},updatePassword(e){return console.log("🔐 更新管理员密码API-V2请求"),o("/api/admin/login/updatePass",e)},thirdPartyList(e){return console.log("🔗 第三方配置列表API-V2请求参数:",e),r("/api/admin/account/third/list",e)},thirdPartyInfo(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🔍 获取第三方配置详情API-V2请求:",e),r(`/api/admin/account/third/info/${e.id}`))},thirdPartyUpdate(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("✏️ 更新第三方配置API-V2请求:",e),o("/api/admin/account/third/update",e))}},Ot={getRoleList(e={}){return console.log("📋 获取角色列表API-V2请求:",e),r("/api/admin/role/list",e).then(t=>(console.log("📋 角色列表API响应:",t),t)).catch(t=>{throw console.error("📋 获取角色列表失败:",t),t})},getMenuList(){return console.log("🌲 获取菜单权限列表API-V2请求"),r("/api/admin/role/menus").then(e=>(console.log("🌲 菜单权限列表API响应:",e),e)).catch(e=>{throw console.error("🌲 获取菜单权限列表失败:",e),e})},getUserMenuTree(){return console.log("🌳 获取用户权限菜单树API-V2请求"),r("/api/admin/role/menus").then(e=>{if(console.log("🌳 用户权限菜单树API响应:",e),e.code==="200"){const t=this.buildMenuTree(e.data);return{...e,data:t}}return e}).catch(e=>{throw console.error("🌳 获取用户权限菜单树失败:",e),e})},buildMenuTree(e){return e},getAllRoles(){return console.log("📋 获取所有角色API-V2请求"),r("/api/admin/role/all").then(e=>(console.log("📋 所有角色API响应:",e),e)).catch(e=>{throw console.error("📋 获取所有角色失败:",e),e})},getRoleDetail(e){return console.log("🔍 获取角色详情API-V2请求:",{id:e}),r(`/api/admin/role/detail/${e}`).then(t=>(console.log("🔍 角色详情API响应:",t),t)).catch(t=>{throw console.error("🔍 获取角色详情失败:",t),t})},addRole(e){return console.log("➕ 新增角色API-V2请求:",e),o("/api/admin/role/add",e).then(t=>(console.log("➕ 新增角色API响应:",t),t)).catch(t=>{throw console.error("➕ 新增角色失败:",t),t})},editRole(e){return console.log("✏️ 编辑角色API-V2请求:",e),o("/api/admin/role/edit",e).then(t=>(console.log("✏️ 编辑角色API响应:",t),t)).catch(t=>{throw console.error("✏️ 编辑角色失败:",t),t})},deleteRole(e){return console.log("🗑️ 删除角色API-V2请求:",{id:e}),o(`/api/admin/role/delete/${e}`).then(t=>(console.log("🗑️ 删除角色API响应:",t),t)).catch(t=>{throw console.error("🗑️ 删除角色失败:",t),t})}};var Mt=mt();const ue=Ue(Mt),kt={getAdminList(e={}){return console.log("👥 获取管理员列表API-V2请求:",e),r("/api/admin/manage/list",e).then(t=>(console.log("👥 管理员列表API响应:",t),t)).catch(t=>{throw console.error("👥 获取管理员列表失败:",t),t})},addAdmin(e){console.log("➕ 新增管理员API-V2请求:",e);const t={username:e.username,roleId:e.roleId};return e.password&&e.password.trim()?t.password=ue.MD5(e.password).toString().toUpperCase():t.password=null,o("/api/admin/manage/add",t).then(n=>(console.log("➕ 新增管理员API响应:",n),n)).catch(n=>{throw console.error("➕ 新增管理员失败:",n),n})},editAdmin(e){console.log("✏️ 编辑管理员API-V2请求:",e);const t={id:e.id,username:e.username,roleId:e.roleId};return o("/api/admin/manage/edit",t).then(n=>(console.log("✏️ 编辑管理员API响应:",n),n)).catch(n=>{throw console.error("✏️ 编辑管理员失败:",n),n})},changeAdminStatus(e){return console.log("🔄 修改管理员状态API-V2请求:",{id:e}),o(`/api/admin/manage/status/${e}`).then(t=>(console.log("🔄 修改管理员状态API响应:",t),t)).catch(t=>{throw console.error("🔄 修改管理员状态失败:",t),t})},getAllRoles(){return console.log("📋 获取所有角色API-V2请求（管理员管理用）"),r("/api/admin/role/all").then(e=>(console.log("📋 所有角色API响应（管理员管理用）:",e),e)).catch(e=>{throw console.error("📋 获取所有角色失败（管理员管理用）:",e),e})},formatMenuNames(e){if(!e||!Array.isArray(e)||e.length===0)return"暂无权限";const n=e.sort((i,s)=>i.sort-s.sort).map(i=>i.menuName);return n.length>5?n.slice(0,5).join("、")+`等${n.length}个权限`:n.join("、")},getMenuCount(e){return e&&Array.isArray(e)?e.length:0},hasPermission(e,t){return!e||!Array.isArray(e)?!1:e.some(n=>n.route===t)},generatePermissionSummary(e){if(!e||!Array.isArray(e)||e.length===0)return{total:0,summary:"暂无权限",hasFullAccess:!1};const t=e.length,i=t>=["/service","/technician","/market","/shop","/distribution","/finance","/user","/account","/sys","/log"].length;let s="";return i?s="全部权限":t<=3?s=e.map(c=>c.menuName).join("、"):s=`${e.slice(0,2).map(c=>c.menuName).join("、")}等${t}个权限`,{total:t,summary:s,hasFullAccess:i}}},jt={versionInfo(){return console.log("📱 获取系统版本信息API-V2请求"),r("/api/admin/sys/version/info")},systemUpgrade(e){return console.log("⬆️ 系统升级API-V2请求:",e),o("/api/admin/sys/upgrade",e)},wechatExamine(e){return console.log("📤 上传微信审核API-V2请求:",e),o("/api/admin/sys/wechat/examine",e)},versionList(e){return console.log("📋 版本管理列表API-V2请求参数:",e),r("/api/admin/sys/version/list",e)},wechatConfig(){return console.log("📱 获取小程序配置API-V2请求"),r("/api/admin/sys/wechat/config")},wechatConfigUpdate(e){return console.log("✏️ 更新小程序配置API-V2请求:",e),o("/api/admin/sys/wechat/config/update",e)},webConfig(){return console.log("🌐 获取公众号配置API-V2请求"),r("/api/admin/sys/web/config")},webConfigUpdate(e){return console.log("✏️ 更新公众号配置API-V2请求:",e),o("/api/admin/sys/web/config/update",e)},appConfig(){return console.log("📱 获取APP配置API-V2请求"),r("/api/admin/sys/app/config")},appConfigUpdate(e){return console.log("✏️ 更新APP配置API-V2请求:",e),o("/api/admin/sys/app/config/update",e)},privacyInfo(){return console.log("📄 获取隐私协议API-V2请求"),r("/api/admin/sys/privacy/info")},privacyUpdate(e){return console.log("✏️ 更新隐私协议API-V2请求:",e),o("/api/admin/sys/privacy/update",e)},getPrivacyAgreement(){return console.log("📄 获取隐私协议配置API-V2请求"),r("/api/admin/config/getPrivacyAgreement")},updatePrivacyAgreement(e){return console.log("✏️ 更新隐私协议配置API-V2请求:",e),o("/api/admin/config/updatePrivacyAgreement",e)},getTradeSettings(){return console.log("💰 获取交易设置配置API-V2请求"),r("/api/admin/config/getTradeSettings")},updateTradeSettings(e){return console.log("✏️ 更新交易设置配置API-V2请求:",e),o("/api/admin/config/updateTradeSettings",e)},paymentConfig(){return console.log("💳 获取支付配置API-V2请求"),r("/api/admin/sys/payment/config")},paymentConfigUpdate(e){return console.log("✏️ 更新支付配置API-V2请求:",e),o("/api/admin/sys/payment/config/update",e)},uploadConfig(){return console.log("📤 获取上传配置API-V2请求"),r("/api/admin/sys/upload/config")},uploadConfigUpdate(e){return console.log("✏️ 更新上传配置API-V2请求:",e),o("/api/admin/sys/upload/config/update",e)},transactionConfig(){return console.log("💰 获取交易设置API-V2请求"),r("/api/admin/sys/transaction/config")},transactionConfigUpdate(e){return console.log("✏️ 更新交易设置API-V2请求:",e),o("/api/admin/sys/transaction/config/update",e)},noticeConfig(){return console.log("📢 获取万能通知配置API-V2请求"),r("/api/admin/sys/notice/config")},noticeConfigUpdate(e){return console.log("✏️ 更新万能通知配置API-V2请求:",e),o("/api/admin/sys/notice/config/update",e)},messageConfig(){return console.log("📱 获取短信通知配置API-V2请求"),r("/api/admin/sys/message/config")},messageConfigUpdate(e){return console.log("✏️ 更新短信通知配置API-V2请求:",e),o("/api/admin/sys/message/config/update",e)},informationConfig(){return console.log("📋 获取备案信息API-V2请求"),r("/api/admin/sys/information/config")},informationConfigUpdate(e){return console.log("✏️ 更新备案信息API-V2请求:",e),o("/api/admin/sys/information/config/update",e)},printConfig(){return console.log("🖨️ 获取打印机设置API-V2请求"),r("/api/admin/sys/print/config")},printConfigUpdate(e){return console.log("✏️ 更新打印机设置API-V2请求:",e),o("/api/admin/sys/print/config/update",e)},carFeeConfig(){return console.log("🚗 获取车费设置API-V2请求"),r("/api/admin/sys/car_fee/config")},carFeeConfigUpdate(e){return console.log("✏️ 更新车费设置API-V2请求:",e),o("/api/admin/sys/car_fee/config/update",e)},cityList(e){return console.log("🏙️ 城市设置列表API-V2请求参数:",e),r("/api/admin/sys/city/list",e)},cityAdd(e){return!e||!e.name?Promise.reject(new Error("城市名称不能为空")):(console.log("➕ 新增城市API-V2请求数据:",e),o("/api/admin/sys/city/add",e))},cityUpdate(e){return!e||!e.id?Promise.reject(new Error("城市ID不能为空")):(console.log("✏️ 编辑城市API-V2请求:",e),o("/api/admin/sys/city/update",e))},cityDelete(e){return!e||!e.id?Promise.reject(new Error("城市ID不能为空")):(console.log("🗑️ 删除城市API-V2请求:",e),o(`/api/admin/sys/city/delete/${e.id}`))},travelConfig(){return console.log("🚌 获取出行设置API-V2请求"),r("/api/admin/sys/travel/config")},travelConfigUpdate(e){return console.log("✏️ 更新出行设置API-V2请求:",e),o("/api/admin/sys/travel/config/update",e)},otherConfig(){return console.log("⚙️ 获取其他设置API-V2请求"),r("/api/admin/sys/other/config")},otherConfigUpdate(e){return console.log("✏️ 更新其他设置API-V2请求:",e),o("/api/admin/sys/other/config/update",e)}};function Ut(){return typeof window<"u"&&window.APP_CONFIG?window.APP_CONFIG.api:null}const oe={REAL_BASE_URL:"http://************:8889/ims",API_MODE:"real",TIMEOUT:1e4,ENABLE_LOG:!1};function Nt(){const e=Ut();return e?{REAL_BASE_URL:e.baseURL||oe.REAL_BASE_URL,API_MODE:"real",TIMEOUT:e.timeout||oe.TIMEOUT,ENABLE_LOG:e.enableLog||oe.ENABLE_LOG}:oe}Nt();const Ft={OPERATION_LOG:{LIST:"/api/admin/operationLog/list"}},$t={list(e={}){return r(Ft.OPERATION_LOG.LIST,{adminUserId:e.adminUserId||void 0,method:e.method||void 0,uri:e.uri||void 0,clientIp:e.clientIp||void 0,resultCode:e.resultCode||void 0,pageNum:e.pageNum||1,pageSize:e.pageSize||10})}},Bt={operationLog:$t};function xt(e){return e?ue.MD5(e).toString().toUpperCase():""}const Ht={async login(e){console.log("🔐 用户登录API-V2请求:",{username:e.username,rememberMe:e.rememberMe});const t=xt(e.password),n={username:e.username,password:t};console.log("🔐 登录请求数据:",{username:n.username,password:"***已加密***"});try{const i=await o("/api/admin/login/loginByPass",n);if(console.log("🔐 登录API响应:",i),i.code==="200"||i.code===200){console.log("🔐 登录成功，后端已返回成功响应");const s=()=>{const p=document.cookie;console.log("🍪 当前所有Cookies:",p);const u=p.split("; ").find(g=>g.startsWith("autograph="));if(u){const g=u.split("=")[1];return console.log("✅ autograph Cookie已成功设置!"),console.log("🔑 Cookie值:",g),console.log("📝 Cookie完整信息:",u),!0}else return console.warn("⚠️ 未检测到autograph Cookie"),console.log("🔍 可能的原因:"),console.log("   1. 后端Set-Cookie头未正确设置"),console.log("   2. Cookie域名/路径不匹配"),console.log("   3. 浏览器安全策略阻止"),!1};s()||setTimeout(()=>{console.log("🔄 延迟检查Cookie状态..."),s()},200);let a=[];return i.data&&i.data.menus?(a=i.data.menus,console.log("🌲 登录接口直接返回菜单数据:",a)):i.data&&Array.isArray(i.data)?(a=i.data,console.log("🌲 登录接口返回菜单数组:",a)):(console.log("🌲 登录接口未返回菜单数据，使用空数组"),a=[]),{code:"200",msg:i.msg||"登录成功",data:{userInfo:i.data||{id:1,username:e.username,name:"管理员",avatar:"",roles:["admin"],permissions:["*:*:*"]},menus:a,cookieAuth:!0}}}else throw new Error(i.msg||i.message||"登录失败")}catch(i){console.error("🔐 登录失败:",i);const s=i.response?.data?.msg||i.response?.data?.message||i.message||"登录失败，请检查用户名和密码";throw new Error(s)}},logout(){return console.log("🚪 用户登出API-V2请求"),o("/api/admin/login/logout").then(e=>(console.log("🚪 登出API响应:",e),e)).catch(e=>(console.error("🚪 登出失败:",e),{code:"200",msg:"登出成功",data:null}))},getUserInfo(){return console.log("👤 获取用户信息API-V2请求"),r("/api/admin/auth/userinfo").then(e=>{if(console.log("👤 获取用户信息API响应:",e),e.code==="200"||e.code===200)return{code:"200",msg:e.msg||"获取成功",data:e.data||{id:1,username:"admin",name:"管理员",avatar:"",roles:["admin"],permissions:["*:*:*"],email:"<EMAIL>",phone:"13800138000",status:1,createTime:new Date().toISOString(),updateTime:new Date().toISOString()}};throw new Error(e.msg||"获取用户信息失败")}).catch(e=>{throw console.error("👤 获取用户信息失败:",e),e})},changePassword(e){console.log("🔑 修改密码API-V2请求");const t={newPassword:e.newPassword,confirmPassword:e.confirmPassword};return o("/api/admin/login/updatePass",t)},getConfig(){return console.log("⚙️ 获取系统配置API-V2请求"),r("/api/admin/config/info")},updateConfig(e){return console.log("⚙️ 更新系统配置API-V2请求:",e),o("/api/admin/config/update",e)}},Gt={logList(e){return console.log("📋 系统日志列表API-V2请求参数:",e),r("/api/admin/system/logs",e)},clearLogs(){return console.log("🗑️ 清空系统日志API-V2请求"),o("/api/admin/system/clearLogs")},getStatistics(){return console.log("📊 获取系统统计API-V2请求"),r("/api/admin/system/statistics")},backup(){return console.log("💾 系统备份API-V2请求"),o("/api/admin/system/backup")},backupList(e){return console.log("📦 备份列表API-V2请求参数:",e),r("/api/admin/system/backups",e)},restore(e){return console.log("🔄 恢复备份API-V2请求:",e),o("/api/admin/system/restore",e)}};function Wt(e,t={},n,i={}){return new Promise((s,c)=>{const a={...i};G.post(e,t,{headers:a,withCredentials:!0,onUploadProgress:n}).then(p=>{s(p.data)},p=>{console.log("文件上传错误:",p),c(p)})})}const Yt={uploadFile(e,t){return console.log("📤 核心文件上传API-V2请求 - /api/ admin/file/upload"),Wt("/api/admin/file/upload",e,t).then(n=>(console.log("📤 文件上传成功:",n),n)).catch(n=>{throw console.error("📤 文件上传失败:",n),n})},downloadFile(e){return console.log("📥 文件下载API-V2请求 - /api/admin/file/downloadFile:",e),r("/api/admin/file/downloadFile",e)},uploadFiles(e,t){return console.log("📤 文件上传API-V2请求（旧版本兼容）"),Pe("/api/admin/upload/file",e,t)},uploadImage(e,t){return console.log("🖼️ 图片上传API-V2请求（旧版本兼容）"),Pe("/api/admin/upload/image",e,t)},uploadAddFile(e){return console.log("☁️ 云存储文件回调API-V2请求:",e),o("/api/admin/upload/addFile",e)},fileList(e){return console.log("📁 文件列表API-V2请求参数:",e),r("/api/admin/upload/files",e)},delFiles(e){return!e||!e.id?Promise.reject(new Error("文件ID不能为空")):(console.log("🗑️ 删除文件API-V2请求:",e),o("/api/admin/upload/delete",e))},createGroup(e){return console.log("📂 创建文件分组API-V2请求:",e),o("/api/admin/upload/createGroup",e)},groupList(){return console.log("📂 文件分组列表API-V2请求"),r("/api/admin/upload/groups")}},zt={dashboardOverview(e){return console.log("📊 首页统计概览API-V2请求参数:",e),r("/api/admin/statistics/dashboard/overview",e)},orderStatistics(e){return console.log("📈 订单统计API-V2请求参数:",e),r("/api/admin/statistics/order",e)},userStatistics(e){return console.log("👥 用户统计API-V2请求参数:",e),r("/api/admin/statistics/user",e)},revenueStatistics(e){return console.log("💰 收入统计API-V2请求参数:",e),r("/api/admin/statistics/revenue",e)},technicianStatistics(e){return console.log("👨‍🔧 师傅统计API-V2请求参数:",e),r("/api/admin/statistics/technician",e)},serviceStatistics(e){return console.log("🛠️ 服务统计API-V2请求参数:",e),r("/api/admin/statistics/service",e)},regionStatistics(e){return console.log("🗺️ 地区统计API-V2请求参数:",e),r("/api/admin/statistics/region",e)},distributionStatistics(e){return console.log("📊 分销统计API-V2请求参数:",e),r("/api/admin/statistics/distribution",e)},marketingStatistics(e){return console.log("🎯 营销统计API-V2请求参数:",e),r("/api/admin/statistics/marketing",e)},realtimeStatistics(){return console.log("⚡ 实时统计API-V2请求"),r("/api/admin/statistics/realtime")},rankingStatistics(e){return console.log("🏆 排行榜统计API-V2请求参数:",e),r("/api/admin/statistics/ranking",e)},trendAnalysis(e){return console.log("📈 趋势分析API-V2请求参数:",e),r("/api/admin/statistics/trend",e)},compareAnalysis(e){return console.log("⚖️ 对比分析API-V2请求参数:",e),r("/api/admin/statistics/compare",e)},exportReport(e){return console.log("📤 导出统计报表API-V2请求参数:",e),o("/api/admin/statistics/export",e)},customStatistics(e){return console.log("🎛️ 自定义统计API-V2请求参数:",e),o("/api/admin/statistics/custom",e)},saveStatisticsConfig(e){return console.log("💾 保存统计配置API-V2请求:",e),o("/api/admin/statistics/config/save",e)},statisticsConfigList(){return console.log("📋 统计配置列表API-V2请求"),r("/api/admin/statistics/config/list")},deleteStatisticsConfig(e){return!e||!e.id?Promise.reject(new Error("配置ID不能为空")):(console.log("🗑️ 删除统计配置API-V2请求:",e),o(`/api/admin/statistics/config/delete/${e.id}`))}};let Jt={service:St,category:wt,technician:Dt,user:Rt,market:Vt,shop:Tt,distribution:bt,finance:Lt,account:Ct,role:Ot,admin:kt,sys:jt,log:Bt,base:Ht,system:Gt,upload:Yt,statistics:zt};const Kt={...Jt};function Zt(){return typeof window<"u"&&window.APP_CONFIG?window.APP_CONFIG.api:null}let Ne="http://************:8889/ims";{const e=Zt();e&&e.baseURL&&(Ne=e.baseURL)}G.defaults.timeout=3e5;G.defaults.baseURL=Ne;G.defaults.withCredentials=!0;G.interceptors.request.use(e=>(e.withCredentials=!0,console.log("🚀 API-V2请求:",{method:e.method?.toUpperCase(),url:e.url,baseURL:e.baseURL,withCredentials:e.withCredentials,params:e.params,data:e.data}),e),e=>(console.error("❌ 请求配置错误:",e),Promise.reject(e)));G.interceptors.response.use(e=>(console.log("✅ API-V2响应:",{url:e.config.url,status:e.status,data:e.data}),e.data.code===401?(console.error("❌ 未授权，请重新登录:",e.data.error),sessionStorage.removeItem("minitk"),sessionStorage.removeItem("ms_username"),document.cookie="autograph=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/",te.push("/login")):e.data.code===402?console.error("❌ 业务错误:",e.data.error):e.data.code===400&&console.error("❌ 参数错误:",e.data.error),e),e=>(console.error("❌ API-V2响应错误:",e),console.error("❌ 网络错误，请稍后再试:",e.message),Promise.reject(e.response)));function r(e,t={},n="application/json"){return new Promise((i,s)=>{G.get(e,{params:t,headers:{"Content-Type":n},withCredentials:!0}).then(c=>{i(c.data)}).catch(c=>{s(c)})})}function o(e,t={},n="application/json"){return new Promise((i,s)=>{G.post(e,t,{headers:{"Content-Type":n},withCredentials:!0}).then(c=>{i(c.data)},c=>{console.log("POST请求错误:",c),s(c)})})}function Pe(e,t={},n){return new Promise((i,s)=>{G.post(e,t,{headers:{"Content-Type":"multipart/form-data"},withCredentials:!0,onUploadProgress:n}).then(c=>{i(c.data)},c=>{console.log("文件上传错误:",c),s(c)})})}const ee={...Kt},Fe="admin-token",Qt="admin-refresh-token",$e="autograph";function Xt(e){const n=`; ${document.cookie}`.split(`; ${e}=`);return n.length===2?n.pop().split(";").shift():null}function yt(e){document.cookie=`${e}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`}function Be(){const e=Xt($e);return e||localStorage.getItem(Fe)}function he(){yt($e),localStorage.removeItem(Fe),localStorage.removeItem(Qt)}function Te(){const e=te;te.matcher=e.matcher}const qt={token:Be(),refreshToken:"",userInfo:null,roles:[],permissions:[],isLoggedIn:!1},en={SET_TOKEN(e,t){e.token=t,e.isLoggedIn=!!t},SET_REFRESH_TOKEN(e,t){e.refreshToken=t},SET_USER_INFO(e,t){e.userInfo=t},SET_ROLES(e,t){e.roles=Array.isArray(t)?t:[]},SET_PERMISSIONS(e,t){e.permissions=t},RESET_STATE(e){e.token="",e.refreshToken="",e.userInfo=null,e.roles=[],e.permissions=[],e.isLoggedIn=!1}},tn={async login({commit:e,dispatch:t},n){const{username:i,password:s,rememberMe:c}=n;try{const a=await ee.base.login({username:i.trim(),password:s,rememberMe:c});if(console.log("🔐 Store登录响应:",a),a.code==="200"||a.code===200){console.log("🔐 Store: 登录成功，开始设置状态"),e("SET_TOKEN","cookie-authenticated"),localStorage.setItem("admin-token","cookie-authenticated"),console.log("✅ Store: 登录状态已设置"),setTimeout(()=>{const g=document.cookie.split("; ").find(E=>E.startsWith("autograph="));if(g){const E=g.split("=")[1];console.log("✅ Store: 检测到后端设置的autograph cookie:",E),e("SET_TOKEN",E)}else console.warn("⚠️ Store: 未检测到autograph cookie，但保持登录状态")},100);const p={id:1,username:n.username,name:"管理员",avatar:"",email:"<EMAIL>",phone:"13800138000",status:1,createTime:new Date().toISOString(),updateTime:new Date().toISOString()},u=a.data&&a.data.userInfo?a.data.userInfo:p;if(e("SET_USER_INFO",u),e("SET_ROLES",u.roles||["admin"]),e("SET_PERMISSIONS",u.permissions||["*:*:*"]),console.log("✅ Store: 用户信息已设置:",u),a.data&&a.data.menus&&a.data.menus.length>0){console.log("🌲 登录响应包含菜单数据，直接设置菜单:",a.data.menus);try{await t("menu/setMenuData",a.data.menus,{root:!0}),console.log("🌲 登录时菜单数据设置成功")}catch(g){console.warn("⚠️ 登录时菜单数据设置失败，使用降级菜单:",g),await t("menu/useFallbackMenus",null,{root:!0})}}else{console.log("🌲 登录响应不包含菜单数据，使用降级菜单");try{await t("menu/useFallbackMenus",null,{root:!0}),console.log("🌲 降级菜单设置成功")}catch(g){console.warn("⚠️ 降级菜单设置失败:",g)}}return console.log("🎯 登录流程完成"),a}else throw new Error(a.msg||"登录失败")}catch(a){throw console.error("🔐 Store登录失败:",a),a}},getUserInfo({commit:e,state:t}){return new Promise((n,i)=>{ee.base.getUserInfo().then(s=>{const{data:c}=s;c||i("验证失败，请重新登录");const{roles:a,permissions:p,...u}=c;(!a||a.length<=0)&&i("用户角色不能为空"),e("SET_USER_INFO",u),e("SET_ROLES",a),e("SET_PERMISSIONS",p||[]),n(c)}).catch(s=>{i(s)})})},logout({commit:e,dispatch:t}){return new Promise((n,i)=>{ee.base.logout().then(()=>{e("RESET_STATE"),he(),Te(),t("routes/resetState",null,{root:!0}),t("ui/resetState",null,{root:!0}),t("menu/resetMenus",null,{root:!0}),console.log("🚪 登出成功，已清除所有认证信息和菜单缓存"),n()}).catch(s=>{console.error("🚪 登出接口调用失败，但仍清除本地状态:",s),e("RESET_STATE"),he(),Te(),t("menu/resetMenus",null,{root:!0}),n()})})},resetToken({commit:e}){return new Promise(t=>{e("RESET_STATE"),he(),t()})},refreshToken({commit:e,state:t}){return new Promise((n,i)=>{if(!t.refreshToken){i("没有刷新令牌");return}setTimeout(()=>{n(t.token)},1e3)})},resetState({commit:e}){e("RESET_STATE")}},nn={token:e=>e.token,refreshToken:e=>e.refreshToken,userInfo:e=>e.userInfo,roles:e=>e.roles||[],permissions:e=>e.permissions||[],isLoggedIn:e=>{const t=document.cookie.split("; ").find(n=>n.startsWith("autograph="));if(t){const n=t.split("=")[1];return!!n&&n!=="undefined"}return e.isLoggedIn},username:e=>e.userInfo?.username||"",nickname:e=>e.userInfo?.nickname||e.userInfo?.username||"",avatar:e=>e.userInfo?.avatar||"",hasRole:e=>t=>(e.roles||[]).includes(t),hasPermission:e=>t=>(e.permissions||[]).includes(t),hasAnyRole:e=>t=>{const n=e.roles||[];return Array.isArray(t)?t.some(i=>n.includes(i)):!1},hasAnyPermission:e=>t=>{const n=e.permissions||[];return Array.isArray(t)?t.some(i=>n.includes(i)):!1}},on={namespaced:!0,state:qt,mutations:en,actions:tn,getters:nn},rn={preferences:{pageSize:20,tableDensity:"default",showRowNumber:!0,defaultSort:"desc"},behavior:{recentPages:[],searchHistory:[],actionHistory:[]},profile:{avatar:"",signature:"",contact:{email:"",phone:"",address:""}}},an={SET_PREFERENCE(e,{key:t,value:n}){e.preferences.hasOwnProperty(t)&&(e.preferences[t]=n)},ADD_RECENT_PAGE(e,t){const n=e.behavior.recentPages.findIndex(i=>i.path===t.path);n>-1&&e.behavior.recentPages.splice(n,1),e.behavior.recentPages.unshift(t),e.behavior.recentPages.length>10&&e.behavior.recentPages.pop()},ADD_SEARCH_HISTORY(e,t){if(!t||t.trim()==="")return;const n=e.behavior.searchHistory.indexOf(t);n>-1&&e.behavior.searchHistory.splice(n,1),e.behavior.searchHistory.unshift(t),e.behavior.searchHistory.length>20&&e.behavior.searchHistory.pop()},CLEAR_SEARCH_HISTORY(e){e.behavior.searchHistory=[]},ADD_ACTION_HISTORY(e,t){e.behavior.actionHistory.unshift({...t,timestamp:Date.now()}),e.behavior.actionHistory.length>50&&e.behavior.actionHistory.pop()},SET_PROFILE(e,t){e.profile={...e.profile,...t}},RESET_STATE(e){e.behavior.recentPages=[],e.behavior.searchHistory=[],e.behavior.actionHistory=[]}},sn={setPreference({commit:e},t){e("SET_PREFERENCE",t)},recordPageVisit({commit:e},t){e("ADD_RECENT_PAGE",{path:t.path,title:t.meta?.title||"Unknown",timestamp:Date.now()})},recordSearch({commit:e},t){e("ADD_SEARCH_HISTORY",t)},clearSearchHistory({commit:e}){e("CLEAR_SEARCH_HISTORY")},recordAction({commit:e},t){e("ADD_ACTION_HISTORY",t)},updateProfile({commit:e},t){e("SET_PROFILE",t)},resetState({commit:e}){e("RESET_STATE")}},ln={preferences:e=>e.preferences,recentPages:e=>e.behavior.recentPages,searchHistory:e=>e.behavior.searchHistory,actionHistory:e=>e.behavior.actionHistory,profile:e=>e.profile},cn={namespaced:!0,state:rn,mutations:an,actions:sn,getters:ln},dn={sidebarCollapsed:!1,sidebarSubmenuOpen:!1,isMobile:!1,theme:"light",language:"zh-CN",breadcrumbList:[],visitedViews:[],cachedViews:[],globalLoading:!1,loadingText:"加载中...",pageTitle:"",showSettings:!1,layoutSettings:{showHeader:!0,showSidebar:!0,showFooter:!0,showBreadcrumb:!0,showTabs:!1,fixedHeader:!0,sidebarLogo:!0}},un={TOGGLE_SIDEBAR(e){e.sidebarCollapsed=!e.sidebarCollapsed},SET_SIDEBAR_COLLAPSED(e,t){e.sidebarCollapsed=t},CLOSE_SIDEBAR(e){e.sidebarCollapsed=!0},SET_SIDEBAR_SUBMENU_OPEN(e,t){e.sidebarSubmenuOpen=t},SET_MOBILE(e,t){e.isMobile=t},SET_THEME(e,t){e.theme=t,document.documentElement.setAttribute("data-theme",t)},SET_LANGUAGE(e,t){e.language=t},SET_BREADCRUMB(e,t){e.breadcrumbList=t},ADD_VISITED_VIEW(e,t){e.visitedViews.some(n=>n.path===t.path)||e.visitedViews.push({name:t.name,path:t.path,title:t.meta?.title||"No Title",affix:t.meta?.affix||!1})},DEL_VISITED_VIEW(e,t){const n=e.visitedViews.findIndex(i=>i.path===t.path);n>-1&&e.visitedViews.splice(n,1)},DEL_OTHERS_VISITED_VIEWS(e,t){e.visitedViews=e.visitedViews.filter(n=>n.affix||n.path===t.path)},DEL_ALL_VISITED_VIEWS(e){e.visitedViews=e.visitedViews.filter(t=>t.affix)},ADD_CACHED_VIEW(e,t){e.cachedViews.includes(t.name)||t.meta?.noCache||e.cachedViews.push(t.name)},DEL_CACHED_VIEW(e,t){const n=e.cachedViews.indexOf(t.name);n>-1&&e.cachedViews.splice(n,1)},DEL_OTHERS_CACHED_VIEWS(e,t){const n=e.cachedViews.indexOf(t.name);n>-1?e.cachedViews=e.cachedViews.slice(n,n+1):e.cachedViews=[]},DEL_ALL_CACHED_VIEWS(e){e.cachedViews=[]},SET_GLOBAL_LOADING(e,{loading:t,text:n="加载中..."}){e.globalLoading=t,e.loadingText=n},SET_PAGE_TITLE(e,t){e.pageTitle=t},TOGGLE_SETTINGS(e){e.showSettings=!e.showSettings},SET_LAYOUT_SETTING(e,{key:t,value:n}){e.layoutSettings.hasOwnProperty(t)&&(e.layoutSettings[t]=n)},RESET_STATE(e){e.sidebarCollapsed=!1,e.breadcrumbList=[],e.visitedViews=[],e.cachedViews=[],e.globalLoading=!1,e.loadingText="加载中...",e.pageTitle="",e.showSettings=!1}},mn={toggleSidebar({commit:e}){e("TOGGLE_SIDEBAR")},closeSidebar({commit:e}){e("CLOSE_SIDEBAR")},setMobile({commit:e},t){e("SET_MOBILE",t)},setTheme({commit:e},t){e("SET_THEME",t)},setLanguage({commit:e},t){e("SET_LANGUAGE",t)},generateBreadcrumb({commit:e},t){const n=[];t.matched.filter(s=>s.meta&&s.meta.title).forEach(s=>{n.push({path:s.path,title:s.meta.title,redirect:s.redirect})}),e("SET_BREADCRUMB",n)},addVisitedView({commit:e},t){e("ADD_VISITED_VIEW",t),e("ADD_CACHED_VIEW",t)},delVisitedView({commit:e,state:t},n){return new Promise(i=>{e("DEL_VISITED_VIEW",n),e("DEL_CACHED_VIEW",n),i([...t.visitedViews])})},delOthersVisitedViews({commit:e,state:t},n){return new Promise(i=>{e("DEL_OTHERS_VISITED_VIEWS",n),e("DEL_OTHERS_CACHED_VIEWS",n),i([...t.visitedViews])})},delAllVisitedViews({commit:e,state:t}){return new Promise(n=>{e("DEL_ALL_VISITED_VIEWS"),e("DEL_ALL_CACHED_VIEWS"),n([...t.visitedViews])})},setGlobalLoading({commit:e},t){e("SET_GLOBAL_LOADING",t)},setPageTitle({commit:e},t){e("SET_PAGE_TITLE",t)},toggleSettings({commit:e}){e("TOGGLE_SETTINGS")},setLayoutSetting({commit:e},t){e("SET_LAYOUT_SETTING",t)},initializeApp({commit:e,dispatch:t}){return new Promise(n=>{const i=window.innerWidth<768;e("SET_MOBILE",i),i&&e("SET_SIDEBAR_COLLAPSED",!0);const s=localStorage.getItem("admin-theme")||"light";t("setTheme",s),n()})},resetState({commit:e}){e("RESET_STATE")}},pn={sidebarCollapsed:e=>e.sidebarCollapsed,sidebarSubmenuOpen:e=>e.sidebarSubmenuOpen,isMobile:e=>e.isMobile,theme:e=>e.theme,language:e=>e.language,breadcrumbList:e=>e.breadcrumbList,visitedViews:e=>e.visitedViews,cachedViews:e=>e.cachedViews,globalLoading:e=>e.globalLoading,loadingText:e=>e.loadingText,pageTitle:e=>e.pageTitle,showSettings:e=>e.showSettings,layoutSettings:e=>e.layoutSettings},gn={namespaced:!0,state:dn,mutations:un,actions:mn,getters:pn},fn="modulepreload",_n=function(e){return"/"+e},be={},l=function(t,n,i){let s=Promise.resolve();if(n&&n.length>0){let u=function(g){return Promise.all(g.map(E=>Promise.resolve(E).then(D=>({status:"fulfilled",value:D}),D=>({status:"rejected",reason:D}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),p=a?.nonce||a?.getAttribute("nonce");s=u(n.map(g=>{if(g=_n(g),g in be)return;be[g]=!0;const E=g.endsWith(".css"),D=E?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${D}`))return;const w=document.createElement("link");if(w.rel=E?"stylesheet":fn,E||(w.as="script"),w.crossOrigin="",w.href=g,p&&w.setAttribute("nonce",p),document.head.appendChild(w),E)return new Promise((j,d)=>{w.addEventListener("load",j),w.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${g}`)))})}))}function c(a){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=a,window.dispatchEvent(p),!p.defaultPrevented)throw a}return s.then(a=>{for(const p of a||[])p.status==="rejected"&&c(p.reason);return t().catch(c)})},ge=(e,t)=>{const n=e.__vccOpts||e;for(const[i,s]of t)n[i]=s;return n},hn={class:"layout-header"},En={class:"header-left"},In={class:"header-logo"},Pn={class:"header-center"},An={class:"header-right"},vn={class:"user-info"},Sn={class:"user-name"},wn={class:"dialog-footer"},Dn={__name:"LayoutHeader",setup(e){const t=me();ve();const n=Me();O("");const i=O(!1),s=O(!1),c=O(null),a=O(!1),p=O({username:"",newPassword:"",confirmPassword:""}),u={newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(T,P,f)=>{P!==p.value.newPassword?f(new Error("两次输入的密码不一致")):f()},trigger:"blur"}]};O(5);const g=k(()=>t.getters["ui/sidebarCollapsed"]);k(()=>t.getters["ui/theme"]);const E=k(()=>t.getters["auth/userInfo"]),D=k(()=>t.getters["ui/breadcrumbList"]),w=()=>{t.dispatch("ui/toggleSidebar")},j=()=>{document.fullscreenElement?(document.exitFullscreen(),i.value=!1):(document.documentElement.requestFullscreen(),i.value=!0)},d=()=>{p.value={username:E.value.name||"admin",newPassword:"",confirmPassword:""},s.value=!0,c.value&&c.value.clearValidate()},_=async()=>{if(c.value)try{await c.value.validate(),a.value=!0;const T=ue.MD5(p.value.newPassword).toString().toUpperCase(),P=ue.MD5(p.value.confirmPassword).toString().toUpperCase();console.log("🔐 开始更新密码:",{原始密码:p.value.newPassword,加密后密码:T});const f=await ee.account.updatePassword({newPassword:T,confirmPassword:P});f.code==="200"||f.code===200?(ne.success("密码修改成功"),s.value=!1,ne.info("密码已修改，请重新登录"),setTimeout(()=>{h("logout")},1500)):ne.error(f.msg||"密码修改失败")}catch(T){console.error("❌ 密码修改失败:",T),ne.error("密码修改失败")}finally{a.value=!1}},h=async T=>{switch(T){case"editUser":d();break;case"logout":try{await t.dispatch("auth/logout"),console.log("🚪 退出登录成功，跳转到登录页"),n.replace("/login")}catch(P){console.error("🚪 退出登录失败:",P),n.replace("/login")}break;default:console.log("未知命令:",T)}},v=()=>{i.value=!!document.fullscreenElement};return pe(()=>{document.addEventListener("fullscreenchange",v)}),Ae(()=>{document.removeEventListener("fullscreenchange",v)}),(T,P)=>{const f=M("el-icon"),m=M("el-avatar"),I=M("el-breadcrumb-item"),S=M("el-breadcrumb"),C=M("el-dropdown-item"),J=M("el-dropdown-menu"),U=M("el-dropdown"),B=M("el-input"),W=M("el-form-item"),fe=M("el-form"),_e=M("el-button"),Ye=M("el-dialog");return L(),$(q,null,[b("header",hn,[b("div",En,[b("div",{class:"sidebar-toggle",onClick:w},[A(f,{size:18},{default:V(()=>[g.value?(L(),x(H(tt),{key:1})):(L(),x(H(et),{key:0}))]),_:1})]),b("div",In,[A(m,{size:32,src:"/logo.jpg"}),P[5]||(P[5]=b("span",{class:"logo-text"},"今师傅管理后台",-1))])]),b("div",Pn,[A(S,{separator:"/",class:"header-breadcrumb"},{default:V(()=>[(L(!0),$(q,null,ae(D.value,N=>(L(),x(I,{key:N.path,to:N.path},{default:V(()=>[Q(de(N.title),1)]),_:2},1032,["to"]))),128))]),_:1})]),b("div",An,[b("div",{class:"header-action",onClick:j},[A(f,{size:18},{default:V(()=>[i.value?(L(),x(H(ot),{key:1})):(L(),x(H(nt),{key:0}))]),_:1})]),A(U,{class:"user-dropdown",onCommand:h},{dropdown:V(()=>[A(J,null,{default:V(()=>[A(C,{command:"editUser"},{default:V(()=>[A(f,null,{default:V(()=>[A(H(at))]),_:1}),P[6]||(P[6]=Q(" 编辑用户 "))]),_:1,__:[6]}),A(C,{divided:"",command:"logout"},{default:V(()=>[A(f,null,{default:V(()=>[A(H(st))]),_:1}),P[7]||(P[7]=Q(" 退出登录 "))]),_:1,__:[7]})]),_:1})]),default:V(()=>[b("div",vn,[A(m,{size:32,src:"/logo.jpg"},{default:V(()=>[A(f,null,{default:V(()=>[A(H(it))]),_:1})]),_:1}),b("span",Sn,de(E.value.name),1),A(f,{class:"dropdown-icon"},{default:V(()=>[A(H(rt))]),_:1})])]),_:1})])]),A(Ye,{modelValue:s.value,"onUpdate:modelValue":P[4]||(P[4]=N=>s.value=N),title:"编辑用户",width:"500px","close-on-click-modal":!1},{footer:V(()=>[b("div",wn,[A(_e,{onClick:P[3]||(P[3]=N=>s.value=!1)},{default:V(()=>P[8]||(P[8]=[Q("取消")])),_:1,__:[8]}),A(_e,{type:"primary",onClick:_,loading:a.value},{default:V(()=>P[9]||(P[9]=[Q(" 确定 ")])),_:1,__:[9]},8,["loading"])])]),default:V(()=>[A(fe,{ref_key:"editUserFormRef",ref:c,model:p.value,rules:u,"label-width":"100px"},{default:V(()=>[A(W,{label:"用户名"},{default:V(()=>[A(B,{modelValue:p.value.username,"onUpdate:modelValue":P[0]||(P[0]=N=>p.value.username=N),disabled:"",placeholder:"管理员账号名不能修改"},null,8,["modelValue"])]),_:1}),A(W,{label:"新密码",prop:"newPassword"},{default:V(()=>[A(B,{modelValue:p.value.newPassword,"onUpdate:modelValue":P[1]||(P[1]=N=>p.value.newPassword=N),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),A(W,{label:"确认密码",prop:"confirmPassword"},{default:V(()=>[A(B,{modelValue:p.value.confirmPassword,"onUpdate:modelValue":P[2]||(P[2]=N=>p.value.confirmPassword=N),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}},Vn=ge(Dn,[["__scopeId","data-v-3dd18753"]]),Tn={class:"lb-sidebar"},bn={class:"menu"},Ln={class:"menu-scroll"},Rn={class:"menu-top"},Cn=["onClick"],On={class:"menu-text"},Mn={class:"submenu-scroll"},kn=["onClick"],jn={__name:"LayoutSidebar",setup(e){const t=me(),n=ve(),i=Me(),s=O({}),c=O([]),a=O([]),p=O([]),u=O(""),g=k(()=>t.getters["menu/mainMenuList"]),E=k(()=>t.getters["menu/submenuMap"]),D=k(()=>t.getters["menu/menuLoaded"]),w=m=>E.value[m]||[],j=m=>{const I=E.value[m];return I&&I.length>0&&I[0].url&&I[0].url.length>0?I[0].url[0].url:m},d=m=>{for(const I of g.value)if(m.startsWith(I.path))return I.path;return g.value.length>0?g.value[0].path:"/service"},_=m=>{if(console.log("点击菜单:",m.name,m.path),u.value=m.path,m.path==="/custom"){i.push("/user/list"),t.commit("ui/SET_SIDEBAR_SUBMENU_OPEN",!1),a.value=[];return}h(m.path),T(m.path),t.commit("ui/SET_SIDEBAR_SUBMENU_OPEN",a.value.length>0),localStorage.setItem("currentMenu",m.path)},h=m=>{const I=performance.now(),S=w(m);a.value=S,S.length>0&&v(),t.commit("ui/SET_SIDEBAR_SUBMENU_OPEN",S.length>0);const C=performance.now();console.log(`加载子菜单 ${m}: ${S.length}个分组, 耗时: ${(C-I).toFixed(2)}ms`),S.length>0&&localStorage.setItem(`submenu_${m}`,JSON.stringify(p.value))},v=()=>{const m=[];a.value.forEach((I,S)=>{m.push(S.toString())}),p.value=m},T=m=>{const I=w(m);if(I.length>0){const S=I[0];if(S&&S.url&&S.url.length>0){const C=S.url[0];I.some(U=>U.url.some(B=>B.url===n.path))?console.log("当前已在子菜单中，无需跳转"):(console.log("自动跳转到第一个子菜单:",C.name,C.url),i.push(C.url))}}else{const S=j(m);S&&n.path!==S&&(console.log("跳转到默认路由:",S),i.push(S))}},P=m=>{console.log("点击子菜单项:",m.name,m.url),i.push(m.url)},f=()=>{if(!D.value||g.value.length===0){console.log("⏳ 菜单数据未加载完成，等待中...");return}c.value=g.value;const m=localStorage.getItem("currentMenu");m&&g.value.some(I=>I.path===m)?u.value=m:u.value=d(n.path),h(u.value),console.log("🎯 动态菜单初始化完成:",u.value,c.value)};return pe(()=>{s.value=JSON.parse(window.sessionStorage.getItem("userInfo")||"{}"),D.value&&f()}),se(()=>n.path,m=>{const I=d(m);I!==u.value&&(u.value=I,h(I)),t.dispatch("ui/generateBreadcrumb",n)},{immediate:!0}),se(()=>D.value,m=>{m&&(console.log("🌲 菜单数据加载完成，初始化侧边栏"),f())},{immediate:!0}),se(()=>g.value,m=>{m&&m.length>0&&(console.log("🔄 主菜单数据更新:",m),c.value=m,m.some(I=>I.path===u.value)||(u.value=m[0].path,h(u.value)))},{immediate:!0,deep:!0}),(m,I)=>{const S=M("el-icon"),C=M("el-collapse-item"),J=M("el-collapse");return L(),$("div",Tn,[b("div",bn,[b("div",Ln,[b("ul",Rn,[(L(!0),$(q,null,ae(c.value,(U,B)=>(L(),$("li",{key:B,class:K({"menu-active":u.value===U.path}),onClick:W=>_(U)},[U.icon?(L(),x(S,{key:0,size:20},{default:V(()=>[(L(),x(je(U.icon)))]),_:2},1024)):ke("",!0),b("span",On,de(U.name),1)],10,Cn))),128))])])]),b("div",{class:K([a.value.length>0?"isopen":"","submenu"])},[b("div",Mn,[A(J,{modelValue:p.value,"onUpdate:modelValue":I[0]||(I[0]=U=>p.value=U)},{default:V(()=>[(L(!0),$(q,null,ae(a.value,(U,B)=>(L(),x(C,{key:B,title:U.name,name:B.toString()},{default:V(()=>[(L(!0),$(q,null,ae(U.url,(W,fe)=>(L(),$("div",{class:"item",key:fe},[b("span",{class:K({"el-collapse-item-active":m.$route.path===W.url}),onClick:_e=>P(W)},de(W.name),11,kn)]))),128))]),_:2},1032,["title","name"]))),128))]),_:1},8,["modelValue"])])],2)])}}},Un=ge(jn,[["__scopeId","data-v-6acc99e1"]]),Nn={class:"layout-footer"},Fn={__name:"LayoutFooter",setup(e){me(),O("3.0.0");const t=O(!0),n=O(45),i=O(23),s=O(null);k(()=>new Date().getFullYear());const c=()=>{n.value=Math.floor(Math.random()*30)+40,i.value=Math.floor(Math.random()*40)+10,t.value=navigator.onLine};return pe(()=>{c(),s.value=setInterval(c,3e4),window.addEventListener("online",()=>{t.value=!0}),window.addEventListener("offline",()=>{t.value=!1})}),Ae(()=>{s.value&&clearInterval(s.value)}),(a,p)=>(L(),$("footer",Nn,p[0]||(p[0]=[b("div",{class:"footer-content"},[b("div",{class:"footer-right"})],-1)])))}},$n=ge(Fn,[["__scopeId","data-v-46675d97"]]),Bn={class:"main-content"},xn={__name:"LayoutContainer",setup(e){const t=me(),n=ve(),i=O("fade"),s=O(!1),c=O(0),a=k(()=>t.getters["ui/sidebarCollapsed"]),p=k(()=>t.getters["ui/cachedViews"]),u=k(()=>t.getters["ui/isMobile"]),g=k(()=>t.getters["ui/theme"]);k(()=>!1);const E=k(()=>({"is-mobile":u.value,"sidebar-collapsed":a.value,[`theme-${g.value}`]:!0})),D=k(()=>({"sidebar-collapsed":a.value,"sidebar-submenu-open":j.value,"is-mobile":u.value})),w=k(()=>({"sidebar-collapsed":a.value,"sidebar-submenu-open":j.value})),j=k(()=>t.getters["ui/sidebarSubmenuOpen"]),d=()=>{t.dispatch("ui/closeSidebar")},_=()=>{const f=document.querySelector(".layout-main");f&&f.scrollTo({top:0,behavior:"smooth"})},h=f=>{c.value=f.target.scrollTop,s.value=c.value>300},v=()=>{},T=()=>{Ze(()=>{const f=document.querySelector(".layout-main");f&&(f.scrollTop=0)})};se(()=>n.path,(f,m)=>{if(!m){i.value="fade";return}const I=f.split("/").length,S=m.split("/").length;I>S?i.value="slide-left":I<S?i.value="slide-right":i.value="fade"});const P=()=>{const m=window.innerWidth<768;t.dispatch("ui/setMobile",m),m&&!a.value&&t.dispatch("ui/toggleSidebar")};return pe(()=>{P(),window.addEventListener("resize",P);const f=document.querySelector(".layout-main");f&&f.addEventListener("scroll",h)}),Ae(()=>{window.removeEventListener("resize",P);const f=document.querySelector(".layout-main");f&&f.removeEventListener("scroll",h)}),(f,m)=>{const I=M("router-view"),S=M("el-icon");return L(),$("div",{class:K(["layout-container",E.value])},[A(Vn),A(Un),b("main",{class:K(["layout-main",D.value])},[b("div",Bn,[A(I,null,{default:V(({Component:C,route:J})=>[A(Se,{name:i.value,mode:"out-in",onBeforeEnter:v,onAfterEnter:T},{default:V(()=>[(L(),x(ze,{include:p.value,max:10},[(L(),x(je(C),{key:J.fullPath}))],1032,["include"]))]),_:2},1032,["name"])]),_:1})])],2),A($n,{class:K(w.value)},null,8,["class"]),u.value&&!a.value?(L(),$("div",{key:0,class:"mobile-overlay",onClick:d})):ke("",!0),A(Se,{name:"fade"},{default:V(()=>[Je(b("div",{class:"back-top",onClick:_},[A(S,null,{default:V(()=>[A(H(lt))]),_:1})],512),[[Ke,s.value]])]),_:1})],2)}}},R=ge(xn,[["__scopeId","data-v-ce917046"]]),Le=[{path:"/service",name:"Service",component:R,redirect:"/service/list",meta:{title:"服务项目",icon:"Service",menuName:"Service",roles:["admin","manager"]},children:[{path:"list",name:"ServiceList",component:()=>l(()=>import("./ServiceList-C_ijMq4v.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])),meta:{title:"服务项目",keepAlive:!0}},{path:"edit",name:"ServiceEdit",component:()=>l(()=>import("./ServiceEdit-CTw8uyA5.js"),__vite__mapDeps([11,1,5,2,3,12,13,9,14])),meta:{title:"编辑服务项目",hidden:!0}},{path:"banner",name:"ServiceBanner",component:()=>l(()=>import("./ServiceBanner-CvwHAArR.js"),__vite__mapDeps([15,5,1,2,3,4,6,7,8,9,16])),meta:{title:"轮播图设置"}},{path:"jingang",name:"ServiceJingang",component:()=>l(()=>import("./ServiceJingang-kYKSqkHW.js"),__vite__mapDeps([17,5,1,2,3,4,6,7,8,9,18])),meta:{title:"金刚区设置"}},{path:"fenlei",name:"ServiceFenlei",component:()=>l(()=>import("./ServiceFenlei-DPGhA1nA.js"),__vite__mapDeps([19,2,1,3,7,8,5,9,20])),meta:{title:"分类设置"}},{path:"daili",name:"ServiceDaili",component:()=>l(()=>import("./ServiceDaili-_VLXwr0T.js"),__vite__mapDeps([21,5,1,2,3,4,6,7,8,9,22])),meta:{title:"服务点设置"}},{path:"peizhi",name:"ServicePeizhi",component:()=>l(()=>import("./ServicePeizhi-DcTL85Ha.js"),__vite__mapDeps([23,5,1,2,3,7,8,9,24])),meta:{title:"项目配置"}},{path:"text",name:"ServiceText",component:()=>l(()=>import("./ServiceText-gaaMLfNs.js"),__vite__mapDeps([25,5,1,2,3,4,6,7,8,9,26])),meta:{title:"测试项目"}}]},{path:"/technician",name:"Technician",component:R,redirect:"/technician/list",meta:{title:"师傅管理",icon:"User",menuName:"Technician",roles:["admin","manager"]},children:[{path:"list",name:"TechnicianList",component:()=>l(()=>import("./TechnicianList-sH3w7gjt.js"),__vite__mapDeps([27,1,5,2,3,7,8,9,28])),meta:{title:"师傅管理"}},{path:"edit",name:"TechnicianEdit",component:()=>l(()=>import("./TechnicianEdit-7YeauEy9.js"),__vite__mapDeps([29,1,5,2,3,30,31,9,32])),meta:{title:"新增师傅"}},{path:"level",name:"TechnicianLevel",component:()=>l(()=>import("./TechnicianLevel-DQhiYwA1.js"),__vite__mapDeps([33,2,1,3,7,8,5,9,34])),meta:{title:"师傅等级"}},{path:"deposit",name:"TechnicianDeposit",component:()=>l(()=>import("./TechnicianDeposit-rc1Rgs-B.js"),__vite__mapDeps([35,2,1,3,5,9,36])),meta:{title:"师傅押金"}},{path:"distance",name:"TechnicianDistance",component:()=>l(()=>import("./TechnicianDistance-Bvqt4jIE.js"),__vite__mapDeps([37,2,1,3,5,9,38])),meta:{title:"接单范围"}},{path:"city",name:"TechnicianCity",component:()=>l(()=>import("./TechnicianCity-WzoIq5uv.js"),__vite__mapDeps([39,5,1,2,3,9,40])),meta:{title:"城市管理"}},{path:"blacklist",name:"TechnicianBlacklist",component:()=>l(()=>import("./BlackList-R7FwyMbF.js"),__vite__mapDeps([41,1,5,2,3,7,8,9,42])),meta:{title:"黑名单管理"}},{path:"log",name:"TechnicianLog",component:()=>l(()=>import("./TechnicianLog-D8NmT3Ry.js"),__vite__mapDeps([43,2,1,3,7,8,5,9,44])),meta:{title:"日志管理"}}]},{path:"/market",name:"Market",component:R,redirect:"/market/list",meta:{title:"营销管理",icon:"Promotion",menuName:"Market",roles:["admin","manager"]},children:[{path:"list",name:"MarketList",component:()=>l(()=>import("./MarketList-Br4vq50j.js"),__vite__mapDeps([45,1,2,3,7,8,5,9,46])),meta:{title:"卡券管理"}},{path:"edit",name:"MarketEdit",component:()=>l(()=>import("./MarketEdit-DAZYHymO.js"),__vite__mapDeps([47,1,2,3,30,5,31,9,48])),meta:{title:"编辑卡券",hidden:!0}},{path:"notice",name:"MarketNotice",component:()=>l(()=>import("./MarketNotice-D9mUwHXA.js"),__vite__mapDeps([49,2,1,3,7,8,5,9,50])),meta:{title:"公告设置"}},{path:"partner",name:"MarketPartner",component:()=>l(()=>import("./MarketPartner-LItPk5bb.js"),__vite__mapDeps([51,1,2,3,7,8,5,9,52])),meta:{title:"合伙人管理"}},{path:"partner/invite",name:"MarketPartnerInvite",component:()=>l(()=>import("./MarketPartnerInvite-DJydeFGg.js"),__vite__mapDeps([53,1,5,2,3,7,8,9,54])),meta:{title:"合伙人邀请列表"}},{path:"partner/commission",name:"MarketPartnerCommission",component:()=>l(()=>import("./MarketPartnerCommission-BJmX5N7P.js"),__vite__mapDeps([55,1,2,3,7,8,5,9,56])),meta:{title:"合伙人佣金统计"}},{path:"partner/orders",name:"MarketPartnerOrders",component:()=>l(()=>import("./MarketPartnerOrders-YwmS6SAi.js"),__vite__mapDeps([57,1,2,3,7,8,5,9,58])),meta:{title:"合伙人推广订单"}}]},{path:"/shop",name:"Shop",component:R,redirect:"/shop/order",meta:{title:"订单管理",icon:"Document",menuName:"Shop",roles:["admin","manager"]},children:[{path:"order",name:"ShopOrder",component:()=>l(()=>import("./ShopOrder-aj0DgRCK.js"),__vite__mapDeps([59,2,1,3,7,8,5,9,60])),meta:{title:"订单管理",keepAlive:!0}},{path:"order/detail",name:"ShopOrderDetail",component:()=>l(()=>import("./ShopOrderDetail-CiJof6aA.js"),__vite__mapDeps([61,1,2,3,5,9,62])),meta:{title:"订单详情",hidden:!0}},{path:"refund",name:"ShopRefund",component:()=>l(()=>import("./ShopRefund-Dp0qA5ut.js"),__vite__mapDeps([63,2,1,3,4,5,6,7,8,9,64])),meta:{title:"退款管理"}},{path:"evaluate",name:"ShopEvaluate",component:()=>l(()=>import("./ShopEvaluate-Cc614TnM.js"),__vite__mapDeps([65,5,1,2,3,4,6,7,8,9,66])),meta:{title:"评价管理"}},{path:"commission",name:"ShopCommission",component:()=>l(()=>import("./ShopCommission-oL0cli1L.js"),__vite__mapDeps([67,5,1,2,3,7,8,9,68])),meta:{title:"分销佣金"}},{path:"aftersale",name:"ShopAfterSale",component:()=>l(()=>import("./ShopAfterSale-BG7PWbUl.js"),__vite__mapDeps([69,2,1,3,5,9,70])),meta:{title:"售后管理"}}]},{path:"/user",name:"User",component:R,redirect:"/user/list",meta:{title:"用户管理",icon:"User",menuName:"User",roles:["admin","manager"]},children:[{path:"list",name:"UserList",component:()=>l(()=>import("./UserList-DcHmdnAj.js"),__vite__mapDeps([71,5,1,2,3,7,8,9,72])),meta:{title:"用户管理",keepAlive:!0}},{path:"log",name:"UserLog",component:()=>l(()=>import("./UserLog-Cxib_3q5.js"),__vite__mapDeps([73,2,1,3,7,8,5,9,74])),meta:{title:"操作日志"}}]},{path:"/user-center",component:R,redirect:"/user-center/profile",meta:{title:"用户中心",icon:"User"},children:[{path:"profile",name:"UserProfile",component:()=>l(()=>import("./ProfileView-CbzuQ_5j.js"),__vite__mapDeps([75,1,5,9,76])),meta:{title:"个人资料",icon:"User"}},{path:"settings",name:"UserSettings",component:()=>l(()=>import("./SettingsView-B9vLO6P3.js"),__vite__mapDeps([77,1,5,9,78])),meta:{title:"账户设置",icon:"Setting"}},{path:"security",name:"UserSecurity",component:()=>l(()=>import("./SecurityView-D2E15HLk.js"),__vite__mapDeps([79,1,2,3,5,9,80])),meta:{title:"安全设置",icon:"Lock"}}]},{path:"/statistics",component:R,redirect:"/statistics/overview",meta:{title:"数据统计",icon:"DataAnalysis",roles:["admin","super_admin"]},children:[{path:"overview",name:"StatisticsOverview",component:()=>l(()=>import("./OverviewView-DPAbpO2u.js"),__vite__mapDeps([81,5,1,9,82])),meta:{title:"数据概览",icon:"Odometer",roles:["admin","super_admin"]}},{path:"user-analysis",name:"StatisticsUserAnalysis",component:()=>l(()=>import("./UserAnalysisView-BfXog_Qw.js"),__vite__mapDeps([83,5,1,9,84])),meta:{title:"用户分析",icon:"TrendCharts",roles:["admin","super_admin"]}},{path:"content-analysis",name:"StatisticsContentAnalysis",component:()=>l(()=>import("./ContentAnalysisView-NXsYyLU_.js"),__vite__mapDeps([85,5,1,9,86])),meta:{title:"内容分析",icon:"PieChart",roles:["admin","super_admin"]}}]},{path:"/tools",component:R,redirect:"/tools/generator",meta:{title:"工具箱",icon:"Tools",roles:["admin","super_admin"]},children:[{path:"generator",name:"ToolsGenerator",component:()=>l(()=>import("./GeneratorView-CskaCTbx.js"),__vite__mapDeps([87,5,1,9,88])),meta:{title:"代码生成",icon:"DocumentAdd",roles:["admin","super_admin"]}},{path:"backup",name:"ToolsBackup",component:()=>l(()=>import("./BackupView-C2JuKHdi.js"),__vite__mapDeps([89,1,5,9,90])),meta:{title:"数据备份",icon:"FolderAdd",roles:["super_admin"]}},{path:"monitor",name:"ToolsMonitor",component:()=>l(()=>import("./MonitorView-DdexeDm3.js"),__vite__mapDeps([91,1,5,9,92])),meta:{title:"系统监控",icon:"Monitor",roles:["super_admin"]}}]},{path:"/log",name:"Log",component:R,redirect:"/log/operation",meta:{title:"日志管理",icon:"Document",menuName:"Log",roles:["admin","manager"]},children:[{path:"operation",name:"LogOperation",component:()=>l(()=>import("./OperationLog-DMUa5SVk.js"),__vite__mapDeps([93,2,1,3,7,8,5,9,94])),meta:{title:"操作日志",keepAlive:!0}}]},{path:"/external-link",component:R,meta:{title:"外部链接",icon:"Link"},children:[{path:"vue-docs",name:"VueDocs",meta:{title:"Vue文档",icon:"Document",isExternal:!0,url:"https://vuejs.org/"}},{path:"element-plus-docs",name:"ElementPlusDocs",meta:{title:"Element Plus文档",icon:"Document",isExternal:!0,url:"https://element-plus.org/"}}]}],xe=[{path:"/login",name:"Login",component:()=>l(()=>import("./LoginView-Cr394GZP.js"),__vite__mapDeps([95,1,5,9,96])),meta:{title:"登录",hidden:!0,noAuth:!0}},{path:"/register",name:"Register",component:()=>l(()=>import("./RegisterView-CI5RWJca.js"),__vite__mapDeps([97,1,5,9,98])),meta:{title:"注册",hidden:!0,noAuth:!0}},{path:"/forgot-password",name:"ForgotPassword",component:()=>l(()=>import("./ForgotPasswordView-BAoUAd-P.js"),__vite__mapDeps([99,1,5,9,100])),meta:{title:"忘记密码",hidden:!0,noAuth:!0}},{path:"/404",name:"NotFound",component:()=>l(()=>import("./404View-Vazg4c84.js"),__vite__mapDeps([101,1,5,9,102])),meta:{title:"页面不存在",hidden:!0,noAuth:!0}},{path:"/403",name:"Forbidden",component:()=>l(()=>import("./403View-DRjhv5SI.js"),__vite__mapDeps([103,1,5,9,104])),meta:{title:"访问被拒绝",hidden:!0,noAuth:!0}},{path:"/500",name:"ServerError",component:()=>l(()=>import("./500View-ColUvo32.js"),__vite__mapDeps([105,1,5,9,106])),meta:{title:"服务器错误",hidden:!0,noAuth:!0}},{path:"/",component:R,redirect:"/service/list",meta:{title:"首页",icon:"House"},children:[{path:"dashboard",name:"Dashboard",component:()=>l(()=>import("./DashboardView-CAaSJ1GB.js"),__vite__mapDeps([107,1,5,9,108])),meta:{title:"仪表盘",icon:"Odometer",affix:!0}}]},{path:"/service",name:"Service",component:R,redirect:"/service/list",meta:{title:"服务项目",icon:"Service",menuName:"Service"},children:[{path:"list",name:"ServiceList",component:()=>l(()=>import("./ServiceList-C_ijMq4v.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])),meta:{title:"服务项目",keepAlive:!0}},{path:"edit",name:"ServiceEdit",component:()=>l(()=>import("./ServiceEdit-CTw8uyA5.js"),__vite__mapDeps([11,1,5,2,3,12,13,9,14])),meta:{title:"编辑服务项目",hidden:!0}},{path:"banner",name:"ServiceBanner",component:()=>l(()=>import("./ServiceBanner-CvwHAArR.js"),__vite__mapDeps([15,5,1,2,3,4,6,7,8,9,16])),meta:{title:"轮播图设置"}},{path:"jingang",name:"ServiceJingang",component:()=>l(()=>import("./ServiceJingang-kYKSqkHW.js"),__vite__mapDeps([17,5,1,2,3,4,6,7,8,9,18])),meta:{title:"金刚区设置"}},{path:"fenlei",name:"ServiceFenlei",component:()=>l(()=>import("./ServiceFenlei-DPGhA1nA.js"),__vite__mapDeps([19,2,1,3,7,8,5,9,20])),meta:{title:"分类设置"}},{path:"daili",name:"ServiceDaili",component:()=>l(()=>import("./ServiceDaili-_VLXwr0T.js"),__vite__mapDeps([21,5,1,2,3,4,6,7,8,9,22])),meta:{title:"服务点设置"}},{path:"peizhi",name:"ServicePeizhi",component:()=>l(()=>import("./ServicePeizhi-DcTL85Ha.js"),__vite__mapDeps([23,5,1,2,3,7,8,9,24])),meta:{title:"项目配置"}},{path:"text",name:"ServiceText",component:()=>l(()=>import("./ServiceText-gaaMLfNs.js"),__vite__mapDeps([25,5,1,2,3,4,6,7,8,9,26])),meta:{title:"测试项目"}}]},{path:"/technician",name:"Technician",component:R,redirect:"/technician/list",meta:{title:"师傅管理",icon:"User",menuName:"Technician"},children:[{path:"list",name:"TechnicianList",component:()=>l(()=>import("./TechnicianList-sH3w7gjt.js"),__vite__mapDeps([27,1,5,2,3,7,8,9,28])),meta:{title:"师傅列表",keepAlive:!0}},{path:"edit",name:"TechnicianEdit",component:()=>l(()=>import("./TechnicianEdit-7YeauEy9.js"),__vite__mapDeps([29,1,5,2,3,30,31,9,32])),meta:{title:"新增师傅",hidden:!0}},{path:"level",name:"TechnicianLevel",component:()=>l(()=>import("./TechnicianLevel-DQhiYwA1.js"),__vite__mapDeps([33,2,1,3,7,8,5,9,34])),meta:{title:"师傅等级"}},{path:"deposit",name:"TechnicianDeposit",component:()=>l(()=>import("./TechnicianDeposit-rc1Rgs-B.js"),__vite__mapDeps([35,2,1,3,5,9,36])),meta:{title:"师傅押金"}},{path:"distance",name:"TechnicianDistance",component:()=>l(()=>import("./TechnicianDistance-Bvqt4jIE.js"),__vite__mapDeps([37,2,1,3,5,9,38])),meta:{title:"接单范围"}},{path:"log",name:"TechnicianLog",component:()=>l(()=>import("./TechnicianLog-D8NmT3Ry.js"),__vite__mapDeps([43,2,1,3,7,8,5,9,44])),meta:{title:"日志管理"}},{path:"city",name:"TechnicianCity",component:()=>l(()=>import("./TechnicianCity-WzoIq5uv.js"),__vite__mapDeps([39,5,1,2,3,9,40])),meta:{title:"城市管理"}},{path:"blacklist",name:"TechnicianBlacklist",component:()=>l(()=>import("./BlackList-R7FwyMbF.js"),__vite__mapDeps([41,1,5,2,3,7,8,9,42])),meta:{title:"黑名单管理"}}]},{path:"/market",name:"Market",component:R,redirect:"/market/list",meta:{title:"营销管理",icon:"Promotion",menuName:"Market"},children:[{path:"list",name:"MarketList",component:()=>l(()=>import("./MarketList-Br4vq50j.js"),__vite__mapDeps([45,1,2,3,7,8,5,9,46])),meta:{title:"卡券管理",keepAlive:!0}},{path:"edit",name:"MarketEdit",component:()=>l(()=>import("./MarketEdit-DAZYHymO.js"),__vite__mapDeps([47,1,2,3,30,5,31,9,48])),meta:{title:"编辑卡券",hidden:!0}},{path:"activity",name:"MarketActivity",component:()=>l(()=>import("./MarketActivity-CXos1huc.js"),__vite__mapDeps([109,1,2,3,30,5,31,110,111,9,112])),meta:{title:"公告设置"}},{path:"notice",name:"MarketNotice",component:()=>l(()=>import("./MarketNotice-D9mUwHXA.js"),__vite__mapDeps([49,2,1,3,7,8,5,9,50])),meta:{title:"公告设置"}},{path:"partner",name:"MarketPartner",component:()=>l(()=>import("./MarketPartner-LItPk5bb.js"),__vite__mapDeps([51,1,2,3,7,8,5,9,52])),meta:{title:"合伙人管理"}},{path:"partner/invite",name:"MarketPartnerInvite",component:()=>l(()=>import("./MarketPartnerInvite-DJydeFGg.js"),__vite__mapDeps([53,1,5,2,3,7,8,9,54])),meta:{title:"合伙人邀请列表",hidden:!0}},{path:"partner/commission",name:"MarketPartnerCommission",component:()=>l(()=>import("./MarketPartnerCommission-BJmX5N7P.js"),__vite__mapDeps([55,1,2,3,7,8,5,9,56])),meta:{title:"合伙人佣金统计",hidden:!0}},{path:"partner/orders",name:"MarketPartnerOrders",component:()=>l(()=>import("./MarketPartnerOrders-YwmS6SAi.js"),__vite__mapDeps([57,1,2,3,7,8,5,9,58])),meta:{title:"合伙人推广订单",hidden:!0}}]},{path:"/shop",name:"Shop",component:R,redirect:"/shop/order",meta:{title:"订单管理",icon:"Document",menuName:"Shop",roles:["admin","manager"]},children:[{path:"order",name:"ShopOrder",component:()=>l(()=>import("./ShopOrder-aj0DgRCK.js"),__vite__mapDeps([59,2,1,3,7,8,5,9,60])),meta:{title:"订单管理",keepAlive:!0}},{path:"order/detail",name:"ShopOrderDetail",component:()=>l(()=>import("./ShopOrderDetail-CiJof6aA.js"),__vite__mapDeps([61,1,2,3,5,9,62])),meta:{title:"订单详情",hidden:!0}},{path:"refund",name:"ShopRefund",component:()=>l(()=>import("./ShopRefund-Dp0qA5ut.js"),__vite__mapDeps([63,2,1,3,4,5,6,7,8,9,64])),meta:{title:"退款管理"}},{path:"evaluate",name:"ShopEvaluate",component:()=>l(()=>import("./ShopEvaluate-Cc614TnM.js"),__vite__mapDeps([65,5,1,2,3,4,6,7,8,9,66])),meta:{title:"评价管理"}},{path:"commission",name:"ShopCommission",component:()=>l(()=>import("./ShopCommission-oL0cli1L.js"),__vite__mapDeps([67,5,1,2,3,7,8,9,68])),meta:{title:"分销佣金"}},{path:"aftersale",name:"ShopAfterSale",component:()=>l(()=>import("./ShopAfterSale-BG7PWbUl.js"),__vite__mapDeps([69,2,1,3,5,9,70])),meta:{title:"售后管理"}}]},{path:"/distribution",name:"Distribution",component:R,redirect:"/distribution/examine",meta:{title:"分销管理",icon:"Share",menuName:"Distribution"},children:[{path:"examine",name:"DistributionExamine",component:()=>l(()=>import("./DistributionExamine-bBpEgC37.js"),__vite__mapDeps([113,1,2,3,5,9,114])),meta:{title:"分销审核",keepAlive:!0}},{path:"list",name:"DistributionList",component:()=>l(()=>import("./DistributionList-BPSnbGh8.js"),__vite__mapDeps([115,1,2,3,5,9,116])),meta:{title:"分销商列表"}},{path:"set",name:"DistributionSet",component:()=>l(()=>import("./DistributionSet-BM-qgBU1.js"),__vite__mapDeps([117,1,2,3,110,5,111,9,118])),meta:{title:"分销设置"}}]},{path:"/finance",name:"Finance",component:R,redirect:"/finance/list",meta:{title:"财务管理",icon:"Money",menuName:"Finance"},children:[{path:"list",name:"FinanceList",component:()=>l(()=>import("./FinanceList-BzTgD_z5.js"),__vite__mapDeps([119,2,1,3,7,8,5,9,120])),meta:{title:"财务列表",keepAlive:!0}},{path:"detail",name:"FinanceDetail",component:()=>l(()=>import("./FinanceDetail-D_4ZTpgk.js"),__vite__mapDeps([121,1,2,3,5,9,122])),meta:{title:"财务详情",hidden:!0}},{path:"withdraw",name:"FinanceWithdraw",component:()=>l(()=>import("./FinanceWithdraw-DSqBwW1r.js"),__vite__mapDeps([123,2,1,3,7,8,5,9,124])),meta:{title:"提现管理"}},{path:"stored",name:"FinanceStored",component:()=>l(()=>import("./FinanceStored-B6LPQe87.js"),__vite__mapDeps([125,1,2,3,5,9,126])),meta:{title:"储值管理"}},{path:"withdraw-history",name:"FinanceWithdrawHistory",component:()=>l(()=>import("./FinanceWithdrawHistory-CfS_4iX8.js"),__vite__mapDeps([127,2,1,3,7,8,5,9,128])),meta:{title:"历史提现"}}]},{path:"/user",name:"User",component:R,redirect:"/user/list",meta:{title:"用户管理",icon:"UserFilled",menuName:"User"},children:[{path:"list",name:"UserList",component:()=>l(()=>import("./UserList-DcHmdnAj.js"),__vite__mapDeps([71,5,1,2,3,7,8,9,72])),meta:{title:"用户列表",keepAlive:!0}},{path:"detail/:id",name:"UserDetail",component:()=>l(()=>import("./UserDetail-C7zcO82E.js"),__vite__mapDeps([129,1,2,3,5,9,130])),meta:{title:"用户详情",hidden:!0}},{path:"log",name:"UserLog",component:()=>l(()=>import("./UserLog-Cxib_3q5.js"),__vite__mapDeps([73,2,1,3,7,8,5,9,74])),meta:{title:"操作日志"}}]},{path:"/account",name:"Account",component:R,redirect:"/account/admin",meta:{title:"账号设置",icon:"Setting",menuName:"Account"},children:[{path:"admin",name:"AccountAdmin",component:()=>l(()=>import("./AccountAdmin-BTiz42tv.js"),__vite__mapDeps([131,2,1,3,5,9,132])),meta:{title:"管理员管理",keepAlive:!0}},{path:"role",name:"AccountRole",component:()=>l(()=>import("./AccountRole-CislzUx5.js"),__vite__mapDeps([133,2,1,3,5,9,134])),meta:{title:"角色管理"}},{path:"menu",name:"AccountMenu",component:()=>l(()=>import("./AccountMenu-DAF2DK-q.js"),__vite__mapDeps([135,2,1,3,5,9,136])),meta:{title:"菜单管理"}},{path:"franchisee",name:"AccountFranchisee",component:()=>l(()=>import("./AccountFranchisee-CxpqJmx1.js"),__vite__mapDeps([137,5,1,2,3,4,6,7,8,9,138])),meta:{title:"代理商管理"}},{path:"third",name:"AccountThird",component:()=>l(()=>import("./AccountThird-D8Viqc1F.js"),__vite__mapDeps([139,2,1,3,5,9,140])),meta:{title:"第三方管理"}}]},{path:"/sys",name:"System",component:R,redirect:"/sys/wechat",meta:{title:"系统设置",icon:"Tools",menuName:"System"},children:[{path:"upgrade",name:"SystemUpgrade",component:()=>l(()=>import("./SystemUpgrade-DjtFQyax.js"),__vite__mapDeps([141,2,1,3,5,9,142])),meta:{title:"系统升级"}},{path:"examine",name:"SystemExamineOld",component:()=>l(()=>import("./SystemExamine-DdTGqtv8.js"),__vite__mapDeps([143,2,1,3,5,9,144])),meta:{title:"上传微信审核"}},{path:"wechat",name:"SystemWechat",component:()=>l(()=>import("./SystemWechat-DauPgmGb.js"),__vite__mapDeps([145,2,1,3,5,9,146])),meta:{title:"小程序设置"}},{path:"web",name:"SystemWeb",component:()=>l(()=>import("./SystemWeb-DjQxemSH.js"),__vite__mapDeps([147,2,1,3,5,9,148])),meta:{title:"公众号设置"}},{path:"app",name:"SystemApp",component:()=>l(()=>import("./SystemApp-CwuOKztx.js"),__vite__mapDeps([149,2,1,3,5,9,150])),meta:{title:"APP设置"}},{path:"info",name:"SystemInfo",component:()=>l(()=>import("./SystemInfo-BJuGOUvE.js"),__vite__mapDeps([151,5,1,2,3,12,13,9,152])),meta:{title:"隐私协议"}},{path:"payment",name:"SystemPayment",component:()=>l(()=>import("./SystemPayment-BI3X-jQk.js"),__vite__mapDeps([153,2,1,3,5,9,154])),meta:{title:"支付配置"}},{path:"upload",name:"SystemUpload",component:()=>l(()=>import("./SystemUpload-DkvbmCM8.js"),__vite__mapDeps([155,2,1,3,5,9,156])),meta:{title:"上传配置"}},{path:"transaction",name:"SystemTransaction",component:()=>l(()=>import("./SystemTransaction-BAsoigUu.js"),__vite__mapDeps([157,5,1,2,3,12,13,9,158])),meta:{title:"交易设置"}},{path:"notice",name:"SystemNotice",component:()=>l(()=>import("./SystemNotice-DJOHKIJy.js"),__vite__mapDeps([159,2,1,3,5,9,160])),meta:{title:"万能通知"}},{path:"message",name:"SystemMessage",component:()=>l(()=>import("./SystemMessage-Q3jbgOTr.js"),__vite__mapDeps([161,2,1,3,5,9,162])),meta:{title:"短信通知"}},{path:"information",name:"SystemInformation",component:()=>l(()=>import("./SystemInformation-5bsNDf3l.js"),__vite__mapDeps([163,2,1,3,5,9,164])),meta:{title:"备案信息"}},{path:"print",name:"SystemPrint",component:()=>l(()=>import("./SystemPrint-D7fAF0TR.js"),__vite__mapDeps([165,2,1,3,5,9,166])),meta:{title:"打印机设置"}},{path:"car_fee",name:"SystemCarFee",component:()=>l(()=>import("./SystemCarFee-DlnXriVh.js"),__vite__mapDeps([167,2,1,3,5,9,168])),meta:{title:"车费设置"}},{path:"city",name:"SystemCity",component:()=>l(()=>import("./SystemCity-Bwr6V2YI.js"),__vite__mapDeps([169,2,1,3,5,9,170])),meta:{title:"城市设置"}},{path:"travel",name:"SystemTravel",component:()=>l(()=>import("./SystemTravel-6nHM3BS5.js"),__vite__mapDeps([171,2,1,3,5,9,172])),meta:{title:"出行设置"}},{path:"other",name:"SystemOther",component:()=>l(()=>import("./SystemOther-DYOfJfLO.js"),__vite__mapDeps([173,2,1,3,5,9,174])),meta:{title:"其他设置"}},{path:"version",name:"SystemVersion",component:()=>l(()=>import("./SystemVersion-BcxomjNg.js"),__vite__mapDeps([175,2,1,3,5,9,176])),meta:{title:"版本管理"}}]},{path:"/log",name:"Log",component:R,redirect:"/log/operation",meta:{title:"日志管理",icon:"Document",menuName:"Log",roles:["admin","manager"]},children:[{path:"operation",name:"LogOperation",component:()=>l(()=>import("./OperationLog-DMUa5SVk.js"),__vite__mapDeps([93,2,1,3,7,8,5,9,94])),meta:{title:"操作日志",keepAlive:!0}}]},{path:"/:pathMatch(.*)*",redirect:"/404",meta:{hidden:!0}}],Hn={routes:[],addRoutes:[],menuRoutes:[]},Gn={SET_ROUTES(e,t){e.addRoutes=t,e.routes=xe.concat(t)},SET_MENU_ROUTES(e,t){e.menuRoutes=t},RESET_STATE(e){e.routes=[],e.addRoutes=[],e.menuRoutes=[]}},Wn={generateRoutes({commit:e},t){return new Promise(n=>{let i;const s=Array.isArray(t)?t:[];s.includes("super_admin")?i=Le||[]:i=We(Le,s),e("SET_ROUTES",i),e("SET_MENU_ROUTES",He(i)),n(i)})},resetState({commit:e}){e("RESET_STATE")}},Yn={routes:e=>e.routes,addRoutes:e=>e.addRoutes,menuRoutes:e=>e.menuRoutes};function He(e){const t=[];return e.forEach(n=>{if(n.meta&&n.meta.hidden)return;const i={path:n.path,name:n.name,meta:n.meta,children:[]};if(n.children&&n.children.length>0){const s=He(n.children);s.length>0&&(i.children=s)}t.push(i)}),t}const zn={namespaced:!0,state:Hn,mutations:Gn,actions:Wn,getters:Yn},Jn={settings:{appName:"今师傅",appDescription:"Vue3后台管理系统",copyright:"© 2025 今师傅. All rights reserved.",showLogo:!0,showCopyright:!0,defaultLanguage:"zh-CN",supportedLanguages:[{code:"zh-CN",name:"简体中文"},{code:"en-US",name:"English"}]},systemInfo:{version:"3.0.0",buildTime:"",environment:"production",apiBaseUrl:"/api"},config:{requestTimeout:1e4,uploadSizeLimit:10,supportedFileTypes:["jpg","jpeg","png","gif","pdf","doc","docx","xls","xlsx"],pagination:{pageSizes:[10,20,50,100],defaultPageSize:20}},features:{enableI18n:!0,enableThemeSwitch:!0,enableFullscreen:!0,enableSearch:!0,enableNotification:!0}},Kn={SET_APP_SETTING(e,{key:t,value:n}){e.settings.hasOwnProperty(t)&&(e.settings[t]=n)},SET_SYSTEM_INFO(e,t){e.systemInfo={...e.systemInfo,...t}},SET_CONFIG(e,{key:t,value:n}){e.config.hasOwnProperty(t)&&(e.config[t]=n)},SET_FEATURE(e,{key:t,value:n}){e.features.hasOwnProperty(t)&&(e.features[t]=n)},RESET_STATE(e){}},Zn={loadSettings({commit:e}){return new Promise(t=>{const n=localStorage.getItem("app-settings");if(n)try{const i=JSON.parse(n);Object.keys(i).forEach(s=>{e("SET_APP_SETTING",{key:s,value:i[s]})})}catch(i){console.error("加载应用设置失败:",i)}t()})},saveSettings({state:e}){return new Promise(t=>{try{localStorage.setItem("app-settings",JSON.stringify(e.settings)),t()}catch(n){console.error("保存应用设置失败:",n),t()}})},setAppSetting({commit:e,dispatch:t},n){e("SET_APP_SETTING",n),t("saveSettings")},setSystemInfo({commit:e},t){e("SET_SYSTEM_INFO",t)},setConfig({commit:e},t){e("SET_CONFIG",t)},setFeature({commit:e},t){e("SET_FEATURE",t)},resetState({commit:e}){e("RESET_STATE")}},Qn={settings:e=>e.settings,systemInfo:e=>e.systemInfo,config:e=>e.config,features:e=>e.features,appName:e=>e.settings.appName,version:e=>e.systemInfo.version,buildTime:e=>e.systemInfo.buildTime,environment:e=>e.systemInfo.environment,apiBaseUrl:e=>e.systemInfo.apiBaseUrl,isDevelopment:e=>e.systemInfo.environment==="development",isProduction:e=>e.systemInfo.environment==="production"},Xn={namespaced:!0,state:Jn,mutations:Kn,actions:Zn,getters:Qn},yn={rawMenuData:[],mainMenuList:[],submenuMap:{},menuLoaded:!1},qn={SET_RAW_MENU_DATA(e,t){e.rawMenuData=t},SET_MAIN_MENU_LIST(e,t){e.mainMenuList=t},SET_SUBMENU_MAP(e,t){e.submenuMap=t},SET_MENU_LOADED(e,t){e.menuLoaded=t},RESET_MENU_STATE(e){e.rawMenuData=[],e.mainMenuList=[],e.submenuMap={},e.menuLoaded=!1}},eo={async setMenuData({commit:e,state:t},n){if(console.log("🌲 直接设置菜单数据:",n),!n||!Array.isArray(n)){console.warn("⚠️ 菜单数据格式无效，使用降级菜单");const c=ie();return e("SET_MAIN_MENU_LIST",c.mainMenuList),e("SET_SUBMENU_MAP",c.submenuMap),e("SET_MENU_LOADED",!0),c}e("SET_RAW_MENU_DATA",n);const{mainMenuList:i,submenuMap:s}=Re(n);if(i.length===0){console.warn("⚠️ 处理后的菜单数据为空，使用降级菜单");const c=ie();e("SET_MAIN_MENU_LIST",c.mainMenuList),e("SET_SUBMENU_MAP",c.submenuMap)}else e("SET_MAIN_MENU_LIST",i),e("SET_SUBMENU_MAP",s);return e("SET_MENU_LOADED",!0),console.log("🎯 菜单数据设置完成:",{主菜单数量:i.length,子菜单数量:Object.keys(s).length}),{mainMenuList:i,submenuMap:s}},async fetchUserMenus({commit:e,state:t}){if(console.log("🔄 fetchUserMenus 被调用，但不再调用API接口"),t.menuLoaded&&t.mainMenuList.length>0)return console.log("✅ 使用内存缓存的菜单数据"),{mainMenuList:t.mainMenuList,submenuMap:t.submenuMap};try{const i=localStorage.getItem("user_menu_data"),s=localStorage.getItem("user_menu_timestamp"),c=30*60*1e3;if(i&&s)if(Date.now()-parseInt(s)>c)console.log("⏰ 本地缓存已过期，清除缓存"),localStorage.removeItem("user_menu_data"),localStorage.removeItem("user_menu_timestamp");else{console.log("✅ 使用本地存储的菜单数据");const p=JSON.parse(i);e("SET_RAW_MENU_DATA",p);const{mainMenuList:u,submenuMap:g}=Re(p);return e("SET_MAIN_MENU_LIST",u),e("SET_SUBMENU_MAP",g),e("SET_MENU_LOADED",!0),{mainMenuList:u,submenuMap:g}}}catch(i){console.warn("⚠️ 恢复本地菜单缓存失败:",i),localStorage.removeItem("user_menu_data"),localStorage.removeItem("user_menu_timestamp")}console.log("🔄 不再调用API，使用降级菜单");const n=ie();return e("SET_MAIN_MENU_LIST",n.mainMenuList),e("SET_SUBMENU_MAP",n.submenuMap),e("SET_MENU_LOADED",!0),console.log("🎯 降级菜单设置完成:",{主菜单数量:n.mainMenuList.length,子菜单数量:Object.keys(n.submenuMap).length}),n},async useFallbackMenus({commit:e}){console.log("🔄 使用降级菜单配置...");const t=ie();return e("SET_MAIN_MENU_LIST",t.mainMenuList),e("SET_SUBMENU_MAP",t.submenuMap),e("SET_MENU_LOADED",!0),console.log("✅ 降级菜单设置完成:",{主菜单数量:t.mainMenuList.length,子菜单数量:Object.keys(t.submenuMap).length}),t},async refreshUserMenus({commit:e,dispatch:t}){console.log("🔄 强制刷新菜单数据...");try{localStorage.removeItem("user_menu_data"),localStorage.removeItem("user_menu_timestamp"),console.log("🗑️ 已清除本地菜单缓存")}catch(n){console.warn("⚠️ 清除本地菜单缓存失败:",n)}return e("RESET_MENU_STATE"),await t("useFallbackMenus")},resetMenus({commit:e}){e("RESET_MENU_STATE");try{localStorage.removeItem("user_menu_data"),localStorage.removeItem("user_menu_timestamp"),console.log("🗑️ 已清除本地菜单缓存")}catch(t){console.warn("⚠️ 清除本地菜单缓存失败:",t)}},clearMenuCache({commit:e}){console.log("🗑️ 手动清除菜单缓存"),e("RESET_MENU_STATE");try{localStorage.removeItem("user_menu_data"),localStorage.removeItem("user_menu_timestamp"),console.log("✅ 菜单缓存清除完成")}catch(t){console.warn("⚠️ 清除本地菜单缓存失败:",t)}}},to={mainMenuList:e=>e.mainMenuList,submenuMap:e=>e.submenuMap,menuLoaded:e=>e.menuLoaded,getSubmenuByPath:e=>t=>e.submenuMap[t]||[],hasSubmenu:e=>t=>{const n=e.submenuMap[t];return n&&n.length>0},getDefaultRoute:e=>t=>{const n=e.submenuMap[t];return n&&n.length>0&&n[0].url&&n[0].url.length>0?n[0].url[0].url:t},getMainMenuByRoute:e=>t=>{for(const n of e.mainMenuList)if(t.startsWith(n.path))return n.path;return e.mainMenuList.length>0?e.mainMenuList[0].path:"/service"}};function Re(e){console.log("🔄 开始处理菜单数据:",e);const t=[...e].sort((a,p)=>a.sort-p.sort),n=t.filter(a=>a.isShow===1&&a.isMenu===1&&(!a.parentId||a.parentId===0)),i=t.filter(a=>a.isShow===1&&a.isMenu===1&&a.parentId&&a.parentId!==0);console.log("📊 菜单分类:",{父菜单数量:n.length,子菜单数量:i.length,父菜单:n.map(a=>a.menuName),子菜单:i.map(a=>`${a.menuName}(父ID:${a.parentId})`)});const s=n.map(a=>({name:a.menuName,path:a.route,icon:no(a.menuName),menuName:a.menuName,id:a.id,sort:a.sort})),c={};return n.forEach(a=>{const p=i.filter(u=>u.parentId===a.id).sort((u,g)=>u.sort-g.sort);if(p.length>0)console.log(`🌿 使用后端子菜单数据 ${a.menuName}:`,p),c[a.route]=[{name:a.menuName,url:p.map(u=>({name:u.menuName,url:u.route,id:u.id,sort:u.sort}))}];else{console.log(`🌿 使用前端配置子菜单 ${a.menuName}`);const u=Ge(a.route,a.menuName);c[a.route]=u}}),console.log("🎯 菜单处理结果:",{主菜单:s,子菜单映射:c}),{mainMenuList:s,submenuMap:c}}function no(e){return{服务项目:"Service",师傅管理:"User",营销管理:"Promotion",订单管理:"Document",分销管理:"Share",财务管理:"Money",用户管理:"UserFilled",账号设置:"Setting",系统设置:"Tools",日志管理:"Document"}[e]||"Menu"}function Ge(e,t){const i={"/service":[{name:"服务管理",url:[{name:"服务项目",url:"/service/list"},{name:"轮播图设置",url:"/service/banner"},{name:"金刚区设置",url:"/service/jingang"},{name:"分类设置",url:"/service/fenlei"},{name:"服务点设置",url:"/service/daili"},{name:"项目配置",url:"/service/peizhi"}]}],"/technician":[{name:"师傅管理",url:[{name:"师傅列表",url:"/technician/list"},{name:"师傅等级",url:"/technician/level"},{name:"师傅押金",url:"/technician/deposit"},{name:"接单范围",url:"/technician/distance"},{name:"城市管理",url:"/technician/city"},{name:"黑名单管理",url:"/technician/blacklist"},{name:"日志管理",url:"/technician/log"}]}],"/market":[{name:"营销管理",url:[{name:"卡券管理",url:"/market/list"},{name:"公告设置",url:"/market/notice"},{name:"合伙人管理",url:"/market/partner"}]}],"/shop":[{name:"订单管理",url:[{name:"订单列表",url:"/shop/order"},{name:"退款管理",url:"/shop/refund"},{name:"评价管理",url:"/shop/evaluate"},{name:"分销佣金",url:"/shop/commission"}]}],"/distribution":[{name:"分销管理",url:[{name:"分销审核",url:"/distribution/examine"},{name:"分销商列表",url:"/distribution/list"},{name:"分销设置",url:"/distribution/config"}]}],"/finance":[{name:"财务管理",url:[{name:"财务列表",url:"/finance/list"},{name:"提现管理",url:"/finance/withdraw"},{name:"历史提现",url:"/finance/withdraw-history"}]}],"/user":[{name:"用户管理",url:[{name:"用户列表",url:"/user/list"},{name:"操作日志",url:"/user/log"}]}],"/account":[{name:"账号设置",url:[{name:"管理员管理",url:"/account/admin"},{name:"角色管理",url:"/account/role"},{name:"代理商管理",url:"/account/franchisee"}]}],"/sys":[{name:"系统设置",url:[{name:"隐私协议",url:"/sys/info"},{name:"交易设置",url:"/sys/transaction"}]}],"/log":[{name:"日志管理",url:[{name:"操作日志",url:"/log/operation"}]}]}[e];if(i&&i.length>0){const s=i.map(c=>({...c,url:c.url.filter(a=>a&&a.url)})).filter(c=>c.url.length>0);if(s.length>0)return s}return[{name:t,url:[{name:t,url:`${e}/list`}]}]}function ie(){console.log("🔄 使用降级菜单配置");const e=[{name:"服务项目",path:"/service",icon:"Service",menuName:"Service",id:4,sort:1},{name:"师傅管理",path:"/technician",icon:"User",menuName:"Technician",id:7,sort:2},{name:"营销管理",path:"/market",icon:"Promotion",menuName:"Market",id:8,sort:3},{name:"订单管理",path:"/shop",icon:"Document",menuName:"Shop",id:9,sort:4},{name:"分销管理",path:"/distribution",icon:"Share",menuName:"Distribution",id:10,sort:5},{name:"财务管理",path:"/finance",icon:"Money",menuName:"Finance",id:11,sort:6},{name:"用户管理",path:"/user",icon:"UserFilled",menuName:"User",id:12,sort:7},{name:"账号设置",path:"/account",icon:"Setting",menuName:"Account",id:15,sort:8},{name:"系统设置",path:"/sys",icon:"Tools",menuName:"System",id:16,sort:9},{name:"日志管理",path:"/log",icon:"Document",menuName:"Log",id:17,sort:10}],t={};return e.forEach(n=>{const i=Ge(n.path,n.name);i&&i.length>0&&(t[n.path]=i)}),console.log("✅ 降级菜单生成完成:",{主菜单数量:e.length,子菜单数量:Object.keys(t).length}),{mainMenuList:e,submenuMap:t}}const oo={namespaced:!0,state:yn,mutations:qn,actions:eo,getters:to},F=Qe({modules:{auth:on,user:cn,ui:gn,routes:zn,app:Xn,menu:oo},state:{version:"3.0.0",buildTime:new Date().toISOString()},getters:{version:e=>e.version,buildTime:e=>e.buildTime,allModules:e=>Object.keys(e)},mutations:{RESET_ALL_STATE(e){console.log("重置所有状态")}},actions:{resetApp({commit:e,dispatch:t}){return new Promise(n=>{t("auth/resetState"),t("user/resetState"),t("ui/resetState"),t("routes/resetState"),t("app/resetState"),t("menu/resetMenus"),e("RESET_ALL_STATE"),n()})},initApp({dispatch:e}){return new Promise(async t=>{try{await e("ui/initializeApp"),await e("app/loadSettings"),console.log("📱 应用初始化完成，跳过菜单数据获取"),t()}catch(n){console.error("应用初始化失败:",n),t()}})}},plugins:[vt({key:"admin-system-v3",paths:["auth.token","auth.refreshToken","user.userInfo","ui.theme","ui.sidebarCollapsed","ui.language","app.settings"],storage:window.localStorage})],strict:!1}),io=["/login","/register","/forgot-password","/404","/403","/500"];function ro(e){e.beforeEach(async(t,n,i)=>{console.log(`🚀 路由守卫执行: ${n.path} -> ${t.path}`),console.log("🔍 目标路由信息:",{path:t.path,name:t.name,matched:t.matched.length}),X.start(),document.title=ao(t.meta.title);const s=Be();if(console.log("🔑 Token状态:",s?"存在":"不存在"),s)if(t.path==="/login")i({path:"/service/list"}),X.done();else{console.log("🔐 用户已登录，直接进入后台"),F.getters["auth/userInfo"]?.id||(F.commit("auth/SET_USER_INFO",{id:1,username:"admin",name:"管理员",avatar:"",email:"<EMAIL>",phone:"13800138000"}),F.commit("auth/SET_ROLES",["admin"]),F.commit("auth/SET_PERMISSIONS",["*:*:*"]));const a=F.getters["auth/roles"]||["admin"];if(F.getters["menu/menuLoaded"])console.log("✅ 菜单数据已存在，跳过加载");else{console.log("⚠️ 菜单数据未加载，使用降级菜单");try{await F.dispatch("menu/useFallbackMenus"),console.log("🌲 降级菜单加载成功")}catch(g){console.warn("⚠️ 降级菜单加载失败:",g)}}console.log("🔄 开始生成动态路由，用户角色:",a);const u=await F.dispatch("routes/generateRoutes",a);console.log("🛣️ 生成的动态路由数量:",u.length),console.log("🛣️ 生成的动态路由:",u.map(g=>({path:g.path,name:g.name}))),u.forEach(g=>{try{e.addRoute(g),console.log(`✅ 成功添加路由: ${g.path} (${g.name})`)}catch(E){console.error(`❌ 添加路由失败: ${g.path} (${g.name})`,E)}}),console.log("🔍 当前所有路由:",e.getRoutes().map(g=>({path:g.path,name:g.name}))),i()}else io.includes(t.path)?i():(i(`/login?redirect=${t.path}`),X.done())}),e.afterEach((t,n)=>{X.done(),F.dispatch("ui/generateBreadcrumb",t),F.dispatch("ui/addVisitedView",t)}),e.onError(t=>{console.error("路由错误:",t),X.done()})}function ao(e){const t="今师傅";return e?`${e} - ${t}`:t}function so(e,t){return t?!e||e.length===0?!1:e.some(n=>t.includes(n)):!0}function lo(e,t){return!e.meta||!e.meta.roles?!0:so(t,e.meta.roles)}function We(e,t){const n=[];return e.forEach(i=>{const s={...i};lo(s,t)&&(s.children&&(s.children=We(s.children,t)),n.push(s))}),n}const te=Xe({history:ye(),routes:xe,scrollBehavior(e,t,n){return n||{top:0}}});ro(te);var co={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 Delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}},ce={exports:{}},uo=ce.exports,Ce;function mo(){return Ce||(Ce=1,function(e,t){(function(n,i){e.exports=i(ct())})(uo,function(n){function i(a){return a&&typeof a=="object"&&"default"in a?a:{default:a}}var s=i(n),c={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(a,p){return p==="W"?a+"周":a+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(a,p){var u=100*a+p;return u<600?"凌晨":u<900?"早上":u<1100?"上午":u<1300?"中午":u<1800?"下午":"晚上"}};return s.default.locale(c,null,!0),c})}(ce)),ce.exports}mo();const Ee={key:"OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77",version:"2.exp",baseUrl:"https://map.qq.com/api/js"};let Y=!1,Ie=!1,re=null;function po(){return Ie&&window.qq&&window.qq.maps?Promise.resolve():(Y&&re||(Y=!0,re=new Promise((e,t)=>{try{if(window.qq&&window.qq.maps){Ie=!0,Y=!1,e();return}console.log("🗺️ 开始动态加载腾讯地图API...");const n=document.createElement("script");n.type="text/javascript",n.charset="utf-8",n.async=!0,n.defer=!0;const i=`${Ee.baseUrl}?v=${Ee.version}&key=${Ee.key}`;n.src=i,n.onload=()=>{console.log("✅ 腾讯地图API加载成功"),window.qq&&window.qq.maps?(Ie=!0,Y=!1,e()):(console.error("❌ 腾讯地图API加载后验证失败"),Y=!1,t(new Error("腾讯地图API加载后验证失败")))},n.onerror=c=>{console.error("❌ 腾讯地图API加载失败:",c),Y=!1,t(new Error("腾讯地图API加载失败"))};const s=setTimeout(()=>{console.error("❌ 腾讯地图API加载超时"),Y=!1,t(new Error("腾讯地图API加载超时"))},1e4);n.onload=(c=>function(...a){return clearTimeout(s),c.apply(this,a)})(n.onload),n.onerror=(c=>function(...a){return clearTimeout(s),c.apply(this,a)})(n.onerror),document.head.appendChild(n)}catch(n){console.error("❌ 腾讯地图API加载器异常:",n),Y=!1,t(n)}})),re)}function Oe(){setTimeout(()=>{po().then(()=>{console.log("🎯 腾讯地图API预加载成功")}).catch(e=>{console.warn("⚠️ 腾讯地图API预加载失败:",e)})},2e3)}console.log("🔧 开发环境，Mock数据由Vite插件自动处理");const z=qe(gt);for(const[e,t]of Object.entries(dt))z.component(e,t);z.config.globalProperties.$ELEMENT={size:"default"};z.config.globalProperties.$api=ee;console.log("🔧 API-V2系统已挂载到Vue实例，可通过this.$api调用");z.use(ut,{locale:co});z.use(F);z.use(te);F.dispatch("initApp").then(()=>{z.mount("#app"),Oe()}).catch(e=>{console.error("应用初始化失败:",e),z.mount("#app"),Oe()});export{ge as _,ee as a,Kt as b,xt as m};
