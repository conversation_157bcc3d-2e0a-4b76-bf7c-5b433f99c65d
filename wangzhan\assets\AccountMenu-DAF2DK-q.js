import{T as G,L as v}from"./LbButton-BtU4V_Gr.js";import{_ as W}from"./index-C9Xz1oqp.js";import{E as _,q as Y}from"./element-fdzwdDuf.js";import{r as f,X as Z,h as ee,y as h,Q as l,A as C,I as t,al as s,ar as le,z as w,M as i,J as oe,H as L,D as j,O as D,K as $}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const te={class:"lb-account-menu"},ae={class:"page-main"},ne={key:1},re={class:"table-operate"},se={__name:"AccountMenu",setup(ie){const T=f(!1),x=f([]),p=f(!1),c=f("add"),y=f(),U=f(!1),g=f(""),o=Z({id:"",menu_name:"",pid:0,icon:"",route:"",sort:0,is_show:1,is_menu:1,remark:""}),O={menu_name:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],route:[{required:!0,message:"请输入路由地址",trigger:"blur"}]},B=async()=>{T.value=!0;try{const e=await(await fetch("/api/account/menu/tree")).json();e.code===200?x.value=e.data||[]:_.error(e.message||"获取数据失败")}catch(n){console.error("获取菜单列表失败:",n),_.error("获取数据失败")}finally{T.value=!1}},M=(n,e)=>{if(!n)return"";const u=new Date(n);return e===1?u.toLocaleDateString():u.toLocaleTimeString()},S=()=>c.value==="add"?o.pid>0?"新增子菜单":"新增菜单":"编辑菜单",A=()=>{c.value="add",E(),p.value=!0},q=n=>{c.value="add",E(),o.pid=n.id,o.is_menu=0,g.value=n.menu_name,p.value=!0},P=n=>{if(c.value="edit",Object.assign(o,{id:n.id,menu_name:n.menu_name,pid:n.pid||0,icon:n.icon||"",route:n.route||"",sort:n.sort||0,is_show:n.is_show,is_menu:n.is_menu,remark:n.remark||""}),o.pid>0){const e=(u,r)=>{for(const d of u){if(d.id===r)return d.menu_name;if(d.children&&d.children.length>0){const V=e(d.children,r);if(V)return V}}return""};g.value=e(x.value,o.pid)}p.value=!0},R=async n=>{try{await Y.confirm(`确定要删除菜单 "${n.menu_name}" 吗？`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const u=await(await fetch(`/api/account/menu/delete/${n.id}`,{method:"DELETE"})).json();u.code===200?(_.success("删除成功"),B()):_.error(u.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除菜单失败:",e),_.error("删除失败"))}},I=async()=>{try{await y.value.validate(),U.value=!0;const n=c.value==="add"?"/api/account/menu/add":`/api/account/menu/update/${o.id}`,e=c.value==="add"?"POST":"PUT",r=await(await fetch(n,{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify({menu_name:o.menu_name,pid:o.pid,icon:o.icon,route:o.route,sort:o.sort,is_show:o.is_show,is_menu:o.is_menu,remark:o.remark})})).json();r.code===200?(_.success(c.value==="add"?"新增成功":"编辑成功"),p.value=!1,B()):_.error(r.message||"操作失败")}catch(n){console.error("提交失败:",n),_.error("操作失败")}finally{U.value=!1}},J=()=>{p.value=!1,E()},E=()=>{Object.assign(o,{id:"",menu_name:"",pid:0,icon:"",route:"",sort:0,is_show:1,is_menu:1,remark:""}),g.value="",y.value&&y.value.clearValidate()};return ee(()=>{B()}),(n,e)=>{const u=s("el-row"),r=s("el-table-column"),d=s("el-tag"),V=s("el-table"),F=s("el-card"),b=s("el-input"),m=s("el-form-item"),H=s("el-input-number"),k=s("el-radio"),N=s("el-radio-group"),K=s("el-form"),Q=s("el-dialog"),X=le("loading");return w(),h("div",te,[l(G),C("div",ae,[l(u,{class:"page-header"},{default:t(()=>[l(v,{type:"success",onClick:A},{default:t(()=>e[9]||(e[9]=[i("新增菜单")])),_:1,__:[9]})]),_:1}),l(F,{class:"table-card",shadow:"never"},{default:t(()=>[oe((w(),L(V,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"},"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},border:""},{default:t(()=>[l(r,{prop:"menu_name",label:"菜单名称",width:"200"}),l(r,{prop:"icon",label:"图标",width:"100"},{default:t(a=>[a.row.icon?(w(),h("i",{key:0,class:j(a.row.icon)},null,2)):(w(),h("span",ne,"-"))]),_:1}),l(r,{prop:"route",label:"菜单路由",width:"200"}),l(r,{prop:"sort",label:"排序",width:"80"}),l(r,{prop:"is_show",label:"是否显示",width:"100"},{default:t(a=>[l(d,{type:a.row.is_show?"success":"danger",size:"small"},{default:t(()=>[i(D(a.row.is_show?"显示":"隐藏"),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"is_menu",label:"菜单类型",width:"100"},{default:t(a=>[l(d,{type:a.row.is_menu?"primary":"info",size:"small"},{default:t(()=>[i(D(a.row.is_menu?"菜单":"按钮"),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"create_time",label:"创建时间",width:"170"},{default:t(a=>[C("div",null,D(M(a.row.create_time,1)),1),C("div",null,D(M(a.row.create_time,2)),1)]),_:1}),l(r,{label:"操作",width:"250",fixed:"right"},{default:t(a=>[C("div",re,[l(v,{size:"mini",type:"success",onClick:z=>q(a.row)},{default:t(()=>e[10]||(e[10]=[i(" 新增子菜单 ")])),_:2,__:[10]},1032,["onClick"]),l(v,{size:"mini",type:"primary",onClick:z=>P(a.row)},{default:t(()=>e[11]||(e[11]=[i(" 编辑 ")])),_:2,__:[11]},1032,["onClick"]),l(v,{size:"mini",type:"danger",onClick:z=>R(a.row)},{default:t(()=>e[12]||(e[12]=[i(" 删除 ")])),_:2,__:[12]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[X,T.value]])]),_:1})]),l(Q,{modelValue:p.value,"onUpdate:modelValue":e[8]||(e[8]=a=>p.value=a),title:S(),width:"40%","close-on-click-modal":!1},{footer:t(()=>[l(v,{onClick:J},{default:t(()=>e[17]||(e[17]=[i("取消")])),_:1,__:[17]}),l(v,{type:"primary",onClick:I,loading:U.value},{default:t(()=>e[18]||(e[18]=[i("确定")])),_:1,__:[18]},8,["loading"])]),default:t(()=>[l(K,{model:o,rules:O,ref_key:"formRef",ref:y,"label-width":"120px"},{default:t(()=>[l(m,{label:"菜单名称",prop:"menu_name"},{default:t(()=>[l(b,{modelValue:o.menu_name,"onUpdate:modelValue":e[0]||(e[0]=a=>o.menu_name=a),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1}),o.pid>0?(w(),L(m,{key:0,label:"上级菜单",prop:"pid"},{default:t(()=>[l(b,{modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=a=>g.value=a),disabled:""},null,8,["modelValue"])]),_:1})):$("",!0),l(m,{label:"图标",prop:"icon"},{default:t(()=>[l(b,{modelValue:o.icon,"onUpdate:modelValue":e[2]||(e[2]=a=>o.icon=a),placeholder:"请输入图标类名"},{prepend:t(()=>[o.icon?(w(),h("i",{key:0,class:j(o.icon)},null,2)):$("",!0)]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"路由",prop:"route"},{default:t(()=>[l(b,{modelValue:o.route,"onUpdate:modelValue":e[3]||(e[3]=a=>o.route=a),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1}),l(m,{label:"排序值",prop:"sort"},{default:t(()=>[l(H,{modelValue:o.sort,"onUpdate:modelValue":e[4]||(e[4]=a=>o.sort=a),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(m,{label:"是否显示",prop:"is_show"},{default:t(()=>[l(N,{modelValue:o.is_show,"onUpdate:modelValue":e[5]||(e[5]=a=>o.is_show=a)},{default:t(()=>[l(k,{value:1},{default:t(()=>e[13]||(e[13]=[i("显示")])),_:1,__:[13]}),l(k,{value:0},{default:t(()=>e[14]||(e[14]=[i("隐藏")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"菜单类型",prop:"is_menu"},{default:t(()=>[l(N,{modelValue:o.is_menu,"onUpdate:modelValue":e[6]||(e[6]=a=>o.is_menu=a)},{default:t(()=>[l(k,{value:1},{default:t(()=>e[15]||(e[15]=[i("菜单")])),_:1,__:[15]}),l(k,{value:0},{default:t(()=>e[16]||(e[16]=[i("按钮")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:t(()=>[l(b,{modelValue:o.remark,"onUpdate:modelValue":e[7]||(e[7]=a=>o.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},ce=W(se,[["__scopeId","data-v-3faf5a0d"]]);export{ce as default};
