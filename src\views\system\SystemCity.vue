<!--
  城市设置页面
-->

<template>
  <div class="lb-system-city">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>城市设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="默认城市">
            <el-select v-model="configForm.default_city" placeholder="请选择默认城市">
              <el-option label="北京" value="beijing" />
              <el-option label="上海" value="shanghai" />
              <el-option label="广州" value="guangzhou" />
              <el-option label="深圳" value="shenzhen" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="开放城市">
            <el-checkbox-group v-model="configForm.open_cities">
              <el-checkbox label="beijing">北京</el-checkbox>
              <el-checkbox label="shanghai">上海</el-checkbox>
              <el-checkbox label="guangzhou">广州</el-checkbox>
              <el-checkbox label="shenzhen">深圳</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  default_city: '',
  open_cities: []
})

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/city/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    saveLoading.value = true
    
    const response = await fetch('/api/system/city/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-city {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}
</style>
