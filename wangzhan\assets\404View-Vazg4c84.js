import{y as d,A as e,Q as t,I as s,al as a,az as p,z as m,u,M as _}from"./vendor-DmFBDimT.js";import{w as f}from"./element-fdzwdDuf.js";import{_ as g}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const v={class:"error-container"},k={class:"error-content"},w={class:"error-image"},x={class:"error-actions"},V={__name:"404View",setup(B){const r=p(),i=()=>{r.push("/")},c=()=>{r.go(-1)};return(C,o)=>{const l=a("el-icon"),n=a("el-button");return m(),d("div",v,[e("div",k,[e("div",w,[t(l,{size:120},{default:s(()=>[t(u(f))]),_:1})]),o[2]||(o[2]=e("h1",{class:"error-title"},"404",-1)),o[3]||(o[3]=e("p",{class:"error-message"},"抱歉，您访问的页面不存在",-1)),e("div",x,[t(n,{type:"primary",onClick:i},{default:s(()=>o[0]||(o[0]=[_("返回首页")])),_:1,__:[0]}),t(n,{onClick:c},{default:s(()=>o[1]||(o[1]=[_("返回上页")])),_:1,__:[1]})])])])}}},I=g(V,[["__scopeId","data-v-57d188a3"]]);export{I as default};
