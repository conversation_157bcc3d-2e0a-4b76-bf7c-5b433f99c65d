import{T as se,L as g}from"./LbButton-BtU4V_Gr.js";import{_ as le}from"./index-C9Xz1oqp.js";import{E as d,q as H}from"./element-fdzwdDuf.js";import{r as m,X as S,h as oe,y as re,Q as t,A as i,H as $,K as O,I as a,al as p,ar as ne,z as B,M as o,O as n,J as ie}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const de={class:"lb-system-version"},pe={class:"page-main"},ue={class:"card-header"},ce={class:"upgrade-actions"},_e={class:"card-header"},fe={class:"table-operate"},me={class:"pagination-section"},ve=["innerHTML"],ge={class:"upgrade-progress"},ye={class:"progress-content"},we={class:"progress-text"},be={__name:"SystemVersion",setup(he){const M=m(!1),I=m(!1),z=m(!1),C=m(!1),j=m([]),y=m(0),v=m(0),U=m(""),h=m(""),u=S({version:"v1.0.0",release_time:"2024-01-01",type:"stable",description:"系统初始版本，包含基础功能模块",has_update:!1}),f=S({version:"v1.1.0",release_time:"2024-01-15",type:"stable",size:"25.6MB",description:"新增用户管理功能，修复已知问题，优化系统性能"}),_=S({page:1,pageSize:10,total:0}),w=S({version:"",type:"",release_time:"",size:"",changelog:""}),k=async(s=1)=>{M.value=!0,_.page=s;try{const e=new URLSearchParams({page:_.page,pageSize:_.pageSize}),c=await(await fetch(`/api/system/version/history?${e}`)).json();c.code===200?(j.value=c.data.list||[],_.total=c.data.total||0):d.error(c.message||"获取数据失败")}catch(e){console.error("获取版本历史失败:",e),d.error("获取数据失败")}finally{M.value=!1}},P=async()=>{try{const e=await(await fetch("/api/system/version/check-update")).json();e.code===200?e.data.has_update?(u.has_update=!0,Object.assign(f,e.data.latest_version),d.success("发现新版本")):d.info("当前已是最新版本"):d.error(e.message||"检查更新失败")}catch(s){console.error("检查更新失败:",s),d.error("检查更新失败")}},q=async()=>{try{await H.confirm("升级过程中系统将暂时不可用，确定要开始升级吗？","升级确认",{confirmButtonText:"开始升级",cancelButtonText:"取消",type:"warning"}),z.value=!0,y.value=0,v.value=0,U.value="",h.value="准备下载升级包...",await A()}catch(s){s!=="cancel"&&(console.error("升级失败:",s),d.error("升级失败"))}},A=async()=>{y.value=1,h.value="正在下载升级包...";for(let s=0;s<=100;s+=10)v.value=s,await new Promise(e=>setTimeout(e,200));y.value=2,h.value="正在备份数据...",v.value=0;for(let s=0;s<=100;s+=20)v.value=s,await new Promise(e=>setTimeout(e,300));y.value=3,h.value="正在安装更新...",v.value=0;for(let s=0;s<=100;s+=15)v.value=s,await new Promise(e=>setTimeout(e,250));y.value=4,h.value="升级完成，正在重启系统...",v.value=100,U.value="success",setTimeout(()=>{z.value=!1,d.success("系统升级成功"),u.version=f.version,u.has_update=!1},2e3)},J=()=>{d.info("开始下载升级包...")},K=()=>{z.value=!1},V=s=>({stable:"success",beta:"warning",alpha:"danger"})[s]||"info",T=s=>({stable:"稳定版",beta:"测试版",alpha:"内测版"})[s]||"未知",N=(s,e)=>{if(!s)return"";const l=new Date(s);return e===1?l.toLocaleDateString():l.toLocaleTimeString()},Q=async s=>{try{const l=await(await fetch(`/api/system/version/detail/${s.id}`)).json();l.code===200?(Object.assign(w,l.data),C.value=!0):d.error(l.message||"获取详情失败")}catch(e){console.error("获取版本详情失败:",e),d.error("获取详情失败")}},R=async s=>{try{await H.confirm(`确定要回滚到版本 "${s.version}" 吗？此操作可能导致数据丢失。`,"版本回滚确认",{confirmButtonText:"确定回滚",cancelButtonText:"取消",type:"warning"});const l=await(await fetch(`/api/system/version/rollback/${s.id}`,{method:"PUT"})).json();l.code===200?(d.success("版本回滚成功"),k()):d.error(l.message||"回滚失败")}catch(e){e!=="cancel"&&(console.error("版本回滚失败:",e),d.error("回滚失败"))}},X=s=>{_.pageSize=s,k(1)},F=s=>{k(s)};return oe(()=>{k(),P()}),(s,e)=>{const l=p("el-descriptions-item"),c=p("el-tag"),L=p("el-descriptions"),D=p("el-card"),G=p("el-alert"),b=p("el-table-column"),W=p("el-table"),Y=p("el-pagination"),E=p("el-dialog"),x=p("el-step"),Z=p("el-steps"),ee=p("el-progress"),te=ne("loading");return B(),re("div",de,[t(se),i("div",pe,[t(D,{class:"current-version-card",shadow:"never"},{header:a(()=>[i("div",ue,[e[6]||(e[6]=i("span",null,"当前版本信息",-1)),t(g,{type:"primary",onClick:P},{default:a(()=>e[5]||(e[5]=[o("检查更新")])),_:1,__:[5]})])]),default:a(()=>[t(L,{column:2,border:""},{default:a(()=>[t(l,{label:"系统版本"},{default:a(()=>[o(n(u.version),1)]),_:1}),t(l,{label:"发布时间"},{default:a(()=>[o(n(u.release_time),1)]),_:1}),t(l,{label:"版本类型"},{default:a(()=>[t(c,{type:V(u.type),size:"small"},{default:a(()=>[o(n(T(u.type)),1)]),_:1},8,["type"])]),_:1}),t(l,{label:"更新状态"},{default:a(()=>[t(c,{type:u.has_update?"warning":"success",size:"small"},{default:a(()=>[o(n(u.has_update?"有新版本":"已是最新"),1)]),_:1},8,["type"])]),_:1}),t(l,{label:"版本描述",span:"2"},{default:a(()=>[o(n(u.description),1)]),_:1})]),_:1})]),_:1}),u.has_update?(B(),$(D,{key:0,class:"upgrade-card",shadow:"never"},{header:a(()=>e[7]||(e[7]=[i("div",{class:"card-header"},[i("span",null,"版本升级")],-1)])),default:a(()=>[t(G,{title:"发现新版本",type:"warning",description:`新版本 ${f.version} 已发布，建议及时升级以获得最新功能和安全修复。`,"show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["description"]),t(L,{column:2,border:"",style:{"margin-bottom":"20px"}},{default:a(()=>[t(l,{label:"最新版本"},{default:a(()=>[o(n(f.version),1)]),_:1}),t(l,{label:"发布时间"},{default:a(()=>[o(n(f.release_time),1)]),_:1}),t(l,{label:"版本类型"},{default:a(()=>[t(c,{type:V(f.type),size:"small"},{default:a(()=>[o(n(T(f.type)),1)]),_:1},8,["type"])]),_:1}),t(l,{label:"升级大小"},{default:a(()=>[o(n(f.size),1)]),_:1}),t(l,{label:"更新内容",span:"2"},{default:a(()=>[o(n(f.description),1)]),_:1})]),_:1}),i("div",ce,[t(g,{type:"primary",onClick:q,loading:I.value},{default:a(()=>e[8]||(e[8]=[o(" 立即升级 ")])),_:1,__:[8]},8,["loading"]),t(g,{onClick:J},{default:a(()=>e[9]||(e[9]=[o(" 下载升级包 ")])),_:1,__:[9]})])]),_:1})):O("",!0),t(D,{class:"history-card",shadow:"never"},{header:a(()=>[i("div",_e,[e[11]||(e[11]=i("span",null,"版本历史",-1)),t(g,{type:"primary",onClick:k},{default:a(()=>e[10]||(e[10]=[o("刷新")])),_:1,__:[10]})])]),default:a(()=>[ie((B(),$(W,{data:j.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:a(()=>[t(b,{prop:"version",label:"版本号",width:"120"}),t(b,{prop:"type",label:"版本类型",width:"120"},{default:a(r=>[t(c,{type:V(r.row.type),size:"small"},{default:a(()=>[o(n(T(r.row.type)),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"size",label:"大小",width:"100"}),t(b,{prop:"description",label:"更新内容","min-width":"300"}),t(b,{prop:"release_time",label:"发布时间",width:"170"},{default:a(r=>[i("div",null,n(N(r.row.release_time,1)),1),i("div",null,n(N(r.row.release_time,2)),1)]),_:1}),t(b,{prop:"is_current",label:"状态",width:"100"},{default:a(r=>[t(c,{type:r.row.is_current?"success":"info",size:"small"},{default:a(()=>[o(n(r.row.is_current?"当前版本":"历史版本"),1)]),_:2},1032,["type"])]),_:1}),t(b,{label:"操作",width:"150",fixed:"right"},{default:a(r=>[i("div",fe,[t(g,{size:"mini",type:"primary",onClick:ae=>Q(r.row)},{default:a(()=>e[12]||(e[12]=[o(" 查看详情 ")])),_:2,__:[12]},1032,["onClick"]),r.row.is_current?O("",!0):(B(),$(g,{key:0,size:"mini",type:"warning",onClick:ae=>R(r.row)},{default:a(()=>e[13]||(e[13]=[o(" 回滚 ")])),_:2,__:[13]},1032,["onClick"]))])]),_:1})]),_:1},8,["data"])),[[te,M.value]]),i("div",me,[t(Y,{"current-page":_.page,"onUpdate:currentPage":e[0]||(e[0]=r=>_.page=r),"page-size":_.pageSize,"onUpdate:pageSize":e[1]||(e[1]=r=>_.pageSize=r),"page-sizes":[10,20,50,100],total:_.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:F},null,8,["current-page","page-size","total"])])]),_:1})]),t(E,{modelValue:C.value,"onUpdate:modelValue":e[3]||(e[3]=r=>C.value=r),title:"版本详情",width:"60%"},{footer:a(()=>[t(g,{onClick:e[2]||(e[2]=r=>C.value=!1)},{default:a(()=>e[14]||(e[14]=[o("关闭")])),_:1,__:[14]})]),default:a(()=>[t(L,{column:2,border:""},{default:a(()=>[t(l,{label:"版本号"},{default:a(()=>[o(n(w.version),1)]),_:1}),t(l,{label:"版本类型"},{default:a(()=>[t(c,{type:V(w.type),size:"small"},{default:a(()=>[o(n(T(w.type)),1)]),_:1},8,["type"])]),_:1}),t(l,{label:"发布时间"},{default:a(()=>[o(n(w.release_time),1)]),_:1}),t(l,{label:"大小"},{default:a(()=>[o(n(w.size),1)]),_:1}),t(l,{label:"更新内容",span:"2"},{default:a(()=>[i("div",{innerHTML:w.changelog},null,8,ve)]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(E,{modelValue:z.value,"onUpdate:modelValue":e[4]||(e[4]=r=>z.value=r),title:"系统升级",width:"50%","close-on-click-modal":!1},{footer:a(()=>[t(g,{onClick:K,disabled:y.value>0},{default:a(()=>e[15]||(e[15]=[o("取消升级")])),_:1,__:[15]},8,["disabled"])]),default:a(()=>[i("div",ge,[t(Z,{active:y.value,"finish-status":"success"},{default:a(()=>[t(x,{title:"下载升级包"}),t(x,{title:"备份数据"}),t(x,{title:"安装更新"}),t(x,{title:"重启系统"})]),_:1},8,["active"]),i("div",ye,[t(ee,{percentage:v.value,status:U.value,"stroke-width":20},null,8,["percentage","status"]),i("p",we,n(h.value),1)])])]),_:1},8,["modelValue"])])}}},xe=le(be,[["__scopeId","data-v-0040a3fe"]]);export{xe as default};
