import{T as R,L as f}from"./LbButton-BtU4V_Gr.js";import{_ as x,a as _}from"./index-C9Xz1oqp.js";import{E as u}from"./element-fdzwdDuf.js";import{r as b,X as C,h as P,y as S,Q as t,A as s,I as i,al as r,z as W,M as y}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const z={class:"technician-deposit"},U={class:"content-container"},k={class:"config-form-container"},B={__name:"TechnicianDeposit",setup(E){const c=b(!1),p=b(),o=C({cashPledge:0,commissionRatio:0,entryNotice:"",distance:0,quotationSum:1,quotationWaitTime:5}),q={cashPledge:[{required:!0,message:"请输入缴纳保证金",trigger:"blur"},{type:"number",min:0,message:"保证金不能小于0",trigger:"blur"}],commissionRatio:[{required:!0,message:"请输入平台抽佣比例",trigger:"blur"},{type:"number",min:0,max:100,message:"抽佣比例必须在0-100之间",trigger:"blur"}],entryNotice:[{required:!0,message:"请输入入驻须知",trigger:"blur"},{min:10,message:"入驻须知至少10个字符",trigger:"blur"}],distance:[{required:!0,message:"请输入接单距离",trigger:"blur"},{type:"number",min:0,message:"接单距离不能小于0",trigger:"blur"}],quotationSum:[{required:!0,message:"请输入单笔订单最大报价人数",trigger:"blur"},{type:"number",min:1,message:"最大报价人数不能小于1",trigger:"blur"}],quotationWaitTime:[{required:!0,message:"请输入订单重新报价等待时间",trigger:"blur"},{type:"number",min:1,message:"等待时间不能小于1分钟",trigger:"blur"}]},V=async()=>{try{console.log("🔍 开始获取师傅配置数据");const a=await _.technician.getCoachConfig();a.code==="200"&&a.data?(Object.assign(o,{cashPledge:a.data.cashPledge||0,commissionRatio:a.data.commissionRatio||0,entryNotice:a.data.entryNotice||"",distance:a.data.distance||0,quotationSum:a.data.quotationSum||1,quotationWaitTime:a.data.quotationWaitTime||5}),console.log("✅ 师傅配置数据获取成功:",a.data)):(console.warn("⚠️ 获取师傅配置数据失败:",a.msg),u.warning(a.msg||"获取配置数据失败"))}catch(a){console.error("❌ 获取师傅配置数据异常:",a),u.error("获取配置数据失败")}},v=async()=>{try{await p.value.validate(),c.value=!0,console.log("💾 开始保存师傅配置:",o);const a=await _.technician.coachConfigUpdate({cashPledge:o.cashPledge,commissionRatio:o.commissionRatio,entryNotice:o.entryNotice,distance:o.distance,quotationSum:o.quotationSum,quotationWaitTime:o.quotationWaitTime});a.code==="200"?(u.success("配置保存成功"),console.log("✅ 师傅配置保存成功")):(console.warn("⚠️ 师傅配置保存失败:",a.msg),u.error(a.msg||"配置保存失败"))}catch(a){a!==!1&&(console.error("❌ 师傅配置保存异常:",a),u.error("配置保存失败"))}finally{c.value=!1}},w=()=>{Object.assign(o,{cashPledge:0,commissionRatio:0,entryNotice:"",distance:0,quotationSum:1,quotationWaitTime:5}),p.value?.clearValidate(),u.info("配置已重置")};return P(()=>{V()}),(a,e)=>{const m=r("el-input-number"),n=r("el-form-item"),d=r("el-col"),g=r("el-row"),T=r("el-input"),N=r("el-form"),h=r("el-card");return W(),S("div",z,[t(R,{title:"师傅押金"}),s("div",U,[s("div",k,[t(h,{class:"config-card"},{header:i(()=>e[6]||(e[6]=[s("div",{class:"card-header"},[s("span",null,"师傅相关配置")],-1)])),default:i(()=>[t(N,{ref_key:"configFormRef",ref:p,model:o,rules:q,"label-width":"120px",class:"config-form"},{default:i(()=>[t(g,{gutter:20},{default:i(()=>[t(d,{span:12},{default:i(()=>[t(n,{label:"缴纳保证金",prop:"cashPledge"},{default:i(()=>[t(m,{size:"default",modelValue:o.cashPledge,"onUpdate:modelValue":e[0]||(e[0]=l=>o.cashPledge=l),min:0,precision:2,step:.01,placeholder:"请输入保证金金额",style:{width:"100%"}},null,8,["modelValue"]),e[7]||(e[7]=s("div",{class:"form-tip"},"单位：元",-1))]),_:1,__:[7]})]),_:1}),t(d,{span:12},{default:i(()=>[t(n,{label:"平台抽佣比例",prop:"commissionRatio"},{default:i(()=>[t(m,{size:"default",modelValue:o.commissionRatio,"onUpdate:modelValue":e[1]||(e[1]=l=>o.commissionRatio=l),min:0,max:100,precision:0,step:1,placeholder:"请输入抽佣比例",style:{width:"100%"}},null,8,["modelValue"]),e[8]||(e[8]=s("div",{class:"form-tip"},"单位：%",-1))]),_:1,__:[8]})]),_:1})]),_:1}),t(g,{gutter:20},{default:i(()=>[t(d,{span:12},{default:i(()=>[t(n,{label:"接单距离",prop:"distance"},{default:i(()=>[t(m,{size:"default",modelValue:o.distance,"onUpdate:modelValue":e[2]||(e[2]=l=>o.distance=l),min:0,precision:1,step:.1,placeholder:"请输入接单距离",style:{width:"100%"}},null,8,["modelValue"]),e[9]||(e[9]=s("div",{class:"form-tip"},"单位：公里",-1))]),_:1,__:[9]})]),_:1}),t(d,{span:12},{default:i(()=>[t(n,{label:"最大报价人数",prop:"quotationSum"},{default:i(()=>[t(m,{size:"default",modelValue:o.quotationSum,"onUpdate:modelValue":e[3]||(e[3]=l=>o.quotationSum=l),min:1,precision:0,step:1,placeholder:"请输入单笔订单最大报价人数",style:{width:"100%"}},null,8,["modelValue"]),e[10]||(e[10]=s("div",{class:"form-tip"},"单位：人",-1))]),_:1,__:[10]})]),_:1})]),_:1}),t(g,{gutter:20},{default:i(()=>[t(d,{span:12},{default:i(()=>[t(n,{label:"重新报价等待时间",prop:"quotationWaitTime"},{default:i(()=>[t(m,{size:"default",modelValue:o.quotationWaitTime,"onUpdate:modelValue":e[4]||(e[4]=l=>o.quotationWaitTime=l),min:1,precision:0,step:1,placeholder:"请输入订单重新报价等待时间",style:{width:"100%"}},null,8,["modelValue"]),e[11]||(e[11]=s("div",{class:"form-tip"},"单位：分钟",-1))]),_:1,__:[11]})]),_:1})]),_:1}),t(n,{label:"入驻须知",prop:"entryNotice"},{default:i(()=>[t(T,{size:"default",modelValue:o.entryNotice,"onUpdate:modelValue":e[5]||(e[5]=l=>o.entryNotice=l),type:"textarea",rows:8,placeholder:"请输入入驻须知内容",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(n,null,{default:i(()=>[t(f,{type:"primary",onClick:v,loading:c.value,size:"default"},{default:i(()=>e[12]||(e[12]=[y(" 保存配置 ")])),_:1,__:[12]},8,["loading"]),t(f,{onClick:w,size:"default",style:{"margin-left":"10px"}},{default:i(()=>e[13]||(e[13]=[y(" 重置 ")])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1})])])])}}},I=x(B,[["__scopeId","data-v-b5b147df"]]);export{I as default};
