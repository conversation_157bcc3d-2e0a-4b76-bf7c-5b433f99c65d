import{g as Oe,r as c,X as B,h as Je,y as z,Q as l,A as a,I as s,al as m,J as _e,ar as Ke,H as b,az as Qe,z as p,D as O,O as i,M as u,u as J,K as T,P as se,a6 as oe}from"./vendor-DmFBDimT.js";import{E as r,u as ye,q as we}from"./element-fdzwdDuf.js";import{T as Xe,L as f}from"./LbButton-BtU4V_Gr.js";import{L as Ge}from"./LbPage-DnbiQ0Ct.js";import{_ as Ze}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const el={class:"technician-list"},ll={class:"content-container"},tl={class:"stat-content"},al={class:"stat-value"},sl={class:"stat-content"},ol={class:"stat-value"},nl={class:"stat-content"},il={class:"stat-value"},rl={class:"stat-content"},dl={class:"stat-value"},ul={class:"search-form-container"},pl={class:"table-container"},cl={class:"user-pagination",style:{"margin-top":"20px","text-align":"center"}},ml={class:"coach-detail"},vl={class:"detail-section"},fl={class:"detail-item"},gl={class:"detail-item"},bl={class:"detail-item"},_l={class:"detail-item"},yl={class:"detail-item"},wl={class:"detail-item"},kl={class:"detail-item"},hl={class:"detail-section"},xl={class:"detail-item"},Cl={class:"detail-item"},Vl={class:"detail-item"},zl={class:"detail-item"},Tl={class:"detail-item"},Nl={class:"detail-item"},Il={class:"detail-item"},Ul={class:"detail-item"},$l={class:"detail-item"},Ll={class:"detail-item"},Sl={class:"detail-item"},Dl={class:"detail-item"},El={class:"detail-item"},Al={key:1,class:"detail-section"},Bl={class:"image-section"},jl={class:"image-list"},Ml={class:"image-section"},Pl={class:"image-list"},Rl={key:2,class:"detail-section"},Fl={key:3,class:"detail-section"},Yl={__name:"TechnicianList",setup(Hl){const{proxy:x}=Oe(),j=Qe(),ne=c(),K=c(!1),ie=c([]),re=c(0),Q=c([]),$=c(!1),L=c(!1),M=c(!1),P=c(!1),S=c(!1),X=c(!1),G=c(!1),Z=c(!1),ee=c(!1),de=c([]),le=c([]),ue=c({}),n=c({}),D=c("all"),E=c({all:0,ing:0,pass:0,noPass:0}),w=B({pageNum:1,pageSize:10,total:0}),d=B({coachName:"",mobile:"",beginTime:"",endTime:"",status:"",isEnable:"",orderCount:null,creatTime:null,pageNum:1,pageSize:10}),V=B({coachId:"",shText:""}),k=B({id:"",labelId:"",labelName:""}),g=B({coachId:"",text:"",status:1}),ke=t=>{t&&t.length===2?(d.beginTime=t[0],d.endTime=t[1]):(d.beginTime="",d.endTime="")},he=t=>typeof t=="string"?t.split(",")[0]:t,pe=t=>({1:"warning",2:"success",4:"danger"})[t]||"info",ce=t=>({1:"待审核",2:"审核通过",4:"审核驳回"})[t]||"未知",xe=()=>{d.pageNum=1,_()},Ce=()=>{ne.value?.resetFields(),Q.value=[],d.beginTime="",d.endTime="",d.pageNum=1,_()},Ve=()=>{console.log("🔄 点击新增师傅按钮"),console.log("🔍 当前路由:",j.currentRoute.value.path),console.log("🔍 目标路由:","/technician/edit?mode=add"),j.push("/technician/edit?mode=add").then(()=>{console.log("✅ 路由跳转成功")}).catch(t=>{console.error("❌ 路由跳转失败:",t)})};window.handleAdd=Ve;const ze=async t=>{try{Z.value=!0,P.value=!0;const e=await x.$api.technician.coachDetail(t.id);e.code==="200"?n.value=e.data:r.error(e.msg||"获取师傅详情失败")}catch(e){console.error("获取师傅详情失败:",e),r.error("获取师傅详情失败")}finally{Z.value=!1}},Te=async t=>{try{const e=await x.$api.technician.coachStatus({id:t.id,status:t.isEnable===1?1:0});e.code==="200"?(r.success("状态更新成功"),_()):(r.error(e.msg||"状态更新失败"),t.isEnable=t.isEnable===1?2:1)}catch(e){console.error("状态更新失败:",e),r.error("状态更新失败"),t.isEnable=t.isEnable===1?2:1}},Ne=async t=>{try{await we.confirm("确认删除该师傅吗？删除后无法恢复！","危险操作",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"});const e=await x.$api.technician.coachStatus({id:t.id,status:-1});e.code==="200"?(r.success("删除成功"),_()):r.error(e.msg||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除失败:",e),r.error("删除失败"))}},Ie=async t=>{try{await we.confirm("确认审核通过该师傅吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await x.$api.technician.coachApprove({id:t.id});e.code==="200"?(r.success("审核通过成功"),_()):r.error(e.msg||"审核通过失败")}catch(e){e!=="cancel"&&(console.error("审核通过失败:",e),r.error("审核通过失败"))}},Ue=t=>{V.coachId=t.id,V.shText="",$.value=!0},$e=async()=>{if(!V.shText.trim()){r.warning("请输入驳回原因");return}try{X.value=!0;const t=await x.$api.technician.coachReject({coachId:V.coachId,shText:V.shText});t.code==="200"?(r.success("审核驳回成功"),$.value=!1,_()):r.error(t.msg||"审核驳回失败")}catch(t){console.error("审核驳回失败:",t),r.error("审核驳回失败")}finally{X.value=!1}},Le=t=>{ue.value=t,k.id=t.id,k.labelId="",k.labelName="",Fe(),L.value=!0},Se=t=>{const e=le.value.find(N=>N.id===t);e&&(k.labelName=e.labelName)},De=async()=>{if(!k.labelId){r.warning("请选择等级");return}try{G.value=!0;const t=await x.$api.technician.coachUpDateLevel({coachId:k.id,labelId:k.labelId,labelName:k.labelName});t.code==="200"?(r.success("等级调整成功"),L.value=!1,_()):r.error(t.msg||"等级调整失败")}catch(t){console.error("等级调整失败:",t),r.error("等级调整失败")}finally{G.value=!1}},Ee=t=>{g.coachId=t.id,g.text="",g.status=1,S.value=!0},Ae=async()=>{if(!g.text.trim()){r.warning("请输入审核备注");return}try{ee.value=!0;const t=await x.$api.technician.addBlack({coachId:g.coachId,text:g.text,status:g.status});t.code==="200"?(r.success(g.status===1?"加入黑名单成功":"移除黑名单成功"),S.value=!1,_()):r.error(t.msg||"操作失败")}catch(t){console.error("黑名单操作失败:",t),r.error("操作失败")}finally{ee.value=!1}},Be=t=>{r.success(`已选择用户：${t.nickName}`),M.value=!1},R=t=>{switch(D.value=t,t){case"all":d.status="";break;case"pending":d.status=1;break;case"approved":d.status=2;break;case"rejected":d.status=4;break}d.pageNum=1,_()},je=t=>{w.pageSize=t,w.pageNum=1,te()},Me=t=>{w.pageNum=t,te()},F=t=>t?typeof t=="string"?t.split(",").filter(e=>e.trim()):Array.isArray(t)?t:[]:[],Pe=t=>{d.pageSize=t,d.pageNum=1,_()},Re=t=>{d.pageNum=t,_()},_=async()=>{try{K.value=!0;const t=await x.$api.technician.coachList(d);t.code==="200"?(ie.value=t.data.coachList.list||[],re.value=t.data.coachList.totalCount||0,E.value={all:t.data.all||0,ing:t.data.ing||0,pass:t.data.pass||0,noPass:t.data.noPass||0}):r.error(t.msg||"获取师傅列表失败")}catch(t){console.error("获取师傅列表失败:",t),r.error("获取师傅列表失败")}finally{K.value=!1}},Fe=async()=>{try{const t=await x.$api.technician.labelCoachList();t.code==="200"?le.value=t.data||[]:r.error(t.msg||"获取等级列表失败")}catch(t){console.error("获取等级列表失败:",t),r.error("获取等级列表失败")}},te=async()=>{try{const t=await x.$api.technician.getAssociateUser({pageNum:w.pageNum,pageSize:w.pageSize});t.code==="200"?(de.value=t.data.list||[],w.total=t.data.totalCount||0):r.error(t.msg||"获取用户列表失败")}catch(t){console.error("获取用户列表失败:",t),r.error("获取用户列表失败")}};return Je(()=>{_(),te()}),(t,e)=>{const N=m("el-card"),C=m("el-col"),Y=m("el-row"),H=m("el-input"),h=m("el-form-item"),Ye=m("el-date-picker"),I=m("el-option"),ae=m("el-select"),q=m("el-form"),v=m("el-table-column"),W=m("el-avatar"),me=m("el-icon"),U=m("el-tag"),He=m("el-switch"),ve=m("el-table"),A=m("el-dialog"),qe=m("el-pagination"),fe=m("el-radio"),We=m("el-radio-group"),ge=m("el-image"),be=Ke("loading");return p(),z("div",el,[l(Xe,{title:"师傅管理"}),a("div",ll,[l(Y,{gutter:20,class:"stats-cards"},{default:s(()=>[l(C,{span:6},{default:s(()=>[l(N,{class:O(["stat-card clickable",{active:D.value==="all"}]),onClick:e[0]||(e[0]=o=>R("all"))},{default:s(()=>[a("div",tl,[a("div",al,i(E.value.all||0),1),e[27]||(e[27]=a("div",{class:"stat-label"},"全部",-1))])]),_:1},8,["class"])]),_:1}),l(C,{span:6},{default:s(()=>[l(N,{class:O(["stat-card clickable",{active:D.value==="pending"}]),onClick:e[1]||(e[1]=o=>R("pending"))},{default:s(()=>[a("div",sl,[a("div",ol,i(E.value.ing||0),1),e[28]||(e[28]=a("div",{class:"stat-label"},"待审核",-1))])]),_:1},8,["class"])]),_:1}),l(C,{span:6},{default:s(()=>[l(N,{class:O(["stat-card clickable",{active:D.value==="approved"}]),onClick:e[2]||(e[2]=o=>R("approved"))},{default:s(()=>[a("div",nl,[a("div",il,i(E.value.pass||0),1),e[29]||(e[29]=a("div",{class:"stat-label"},"审核通过",-1))])]),_:1},8,["class"])]),_:1}),l(C,{span:6},{default:s(()=>[l(N,{class:O(["stat-card clickable",{active:D.value==="rejected"}]),onClick:e[3]||(e[3]=o=>R("rejected"))},{default:s(()=>[a("div",rl,[a("div",dl,i(E.value.noPass||0),1),e[30]||(e[30]=a("div",{class:"stat-label"},"审核驳回",-1))])]),_:1},8,["class"])]),_:1})]),_:1}),a("div",ul,[l(q,{ref_key:"searchFormRef",ref:ne,model:d,inline:!0,class:"search-form"},{default:s(()=>[l(Y,{gutter:20},{default:s(()=>[l(C,{span:24},{default:s(()=>[l(h,{label:"师傅姓名",prop:"coachName"},{default:s(()=>[l(H,{size:"default",modelValue:d.coachName,"onUpdate:modelValue":e[4]||(e[4]=o=>d.coachName=o),placeholder:"请输入师傅姓名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(h,{label:"手机号",prop:"mobile"},{default:s(()=>[l(H,{size:"default",modelValue:d.mobile,"onUpdate:modelValue":e[5]||(e[5]=o=>d.mobile=o),placeholder:"请输入手机号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(h,{label:"审核时间",prop:"auditTime"},{default:s(()=>[l(Ye,{size:"default",modelValue:Q.value,"onUpdate:modelValue":e[6]||(e[6]=o=>Q.value=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:ke},null,8,["modelValue"])]),_:1}),l(h,{label:"状态",prop:"status"},{default:s(()=>[l(ae,{size:"default",modelValue:d.status,"onUpdate:modelValue":e[7]||(e[7]=o=>d.status=o),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:s(()=>[l(I,{label:"待审核",value:1}),l(I,{label:"审核通过",value:2}),l(I,{label:"审核驳回",value:4})]),_:1},8,["modelValue"])]),_:1}),l(h,{label:"开启状态",prop:"isEnable"},{default:s(()=>[l(ae,{size:"default",modelValue:d.isEnable,"onUpdate:modelValue":e[8]||(e[8]=o=>d.isEnable=o),placeholder:"请选择开启状态",clearable:"",style:{width:"150px"}},{default:s(()=>[l(I,{label:"开启",value:1}),l(I,{label:"关闭",value:2})]),_:1},8,["modelValue"])]),_:1}),l(h,null,{default:s(()=>[l(f,{size:"default",type:"primary",icon:"Search",onClick:xe},{default:s(()=>e[31]||(e[31]=[u(" 搜索 ")])),_:1,__:[31]}),l(f,{size:"default",icon:"RefreshLeft",onClick:Ce},{default:s(()=>e[32]||(e[32]=[u(" 重置 ")])),_:1,__:[32]}),l(f,{size:"default",type:"primary",icon:"Plus",onClick:e[9]||(e[9]=()=>{console.log("🔄 点击新增师傅按钮"),J(j).push("/technician/edit?mode=add")})},{default:s(()=>e[33]||(e[33]=[u(" 新增师傅 ")])),_:1,__:[33]}),l(f,{size:"default",type:"warning",icon:"Warning",onClick:e[10]||(e[10]=()=>{J(j).push("/technician/blacklist")})},{default:s(()=>e[34]||(e[34]=[u(" 黑名单管理 ")])),_:1,__:[34]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),a("div",pl,[_e((p(),b(ve,{data:ie.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:s(()=>[l(v,{prop:"id",label:"ID",width:"80",align:"center"}),l(v,{prop:"selfImg",label:"师傅头像",width:"80",align:"center"},{default:s(o=>[o.row.selfImg?(p(),b(W,{key:0,src:he(o.row.selfImg),size:50,shape:"circle"},null,8,["src"])):(p(),b(W,{key:1,size:50,shape:"circle"},{default:s(()=>[l(me,null,{default:s(()=>[l(J(ye))]),_:1})]),_:1}))]),_:1}),l(v,{prop:"coachName",label:"师傅姓名",width:"100"}),l(v,{prop:"sex",label:"性别",width:"80",align:"center"},{default:s(o=>[l(U,{type:o.row.sex===0?"primary":"success",size:"small"},{default:s(()=>[u(i(o.row.sex===0?"男":"女"),1)]),_:2},1032,["type"])]),_:1}),l(v,{prop:"mobile",label:"手机号",width:"150"}),l(v,{prop:"idCode",label:"身份证号",width:"180"}),l(v,{prop:"workTime",label:"从业年份",width:"100",align:"center"}),l(v,{prop:"address",label:"详细地址","min-width":"150","show-overflow-tooltip":""}),l(v,{prop:"createTime",label:"申请时间",width:"180"}),l(v,{prop:"status",label:"审核状态",width:"100",align:"center"},{default:s(o=>[l(U,{type:pe(o.row.status),size:"small"},{default:s(()=>[u(i(ce(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),l(v,{prop:"isEnable",label:"开启状态",width:"100",align:"center"},{default:s(o=>[l(He,{modelValue:o.row.isEnable,"onUpdate:modelValue":y=>o.row.isEnable=y,"active-value":1,"inactive-value":2,onChange:y=>Te(o.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(v,{prop:"count",label:"订单数",width:"80",align:"center"}),l(v,{prop:"credit",label:"信誉分",width:"80",align:"center"}),l(v,{label:"操作",width:"350",align:"center",fixed:"right"},{default:s(o=>[l(f,{size:"small",type:"primary",onClick:y=>ze(o.row)},{default:s(()=>e[35]||(e[35]=[u(" 查看 ")])),_:2,__:[35]},1032,["onClick"]),o.row.status===1?(p(),b(f,{key:0,size:"small",type:"success",onClick:y=>Ie(o.row)},{default:s(()=>e[36]||(e[36]=[u(" 审核通过 ")])),_:2,__:[36]},1032,["onClick"])):T("",!0),o.row.status===1?(p(),b(f,{key:1,size:"small",type:"danger",onClick:y=>Ue(o.row)},{default:s(()=>e[37]||(e[37]=[u(" 审核驳回 ")])),_:2,__:[37]},1032,["onClick"])):T("",!0),l(f,{size:"small",type:"warning",onClick:y=>Le(o.row)},{default:s(()=>e[38]||(e[38]=[u(" 等级调整 ")])),_:2,__:[38]},1032,["onClick"]),l(f,{size:"small",type:"danger",onClick:y=>Ne(o.row)},{default:s(()=>e[39]||(e[39]=[u(" 删除 ")])),_:2,__:[39]},1032,["onClick"]),l(f,{size:"small",type:"warning",onClick:y=>Ee(o.row)},{default:s(()=>e[40]||(e[40]=[u(" 黑名单 ")])),_:2,__:[40]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[be,K.value]])]),l(Ge,{page:d.pageNum,"page-size":d.pageSize,total:re.value,onHandleSizeChange:Pe,onHandleCurrentChange:Re},null,8,["page","page-size","total"])]),l(A,{modelValue:$.value,"onUpdate:modelValue":e[13]||(e[13]=o=>$.value=o),title:"审核驳回",width:"500px"},{footer:s(()=>[l(f,{onClick:e[12]||(e[12]=o=>$.value=!1)},{default:s(()=>e[41]||(e[41]=[u("取消")])),_:1,__:[41]}),l(f,{type:"primary",onClick:$e,loading:X.value},{default:s(()=>e[42]||(e[42]=[u(" 确认驳回 ")])),_:1,__:[42]},8,["loading"])]),default:s(()=>[l(q,{model:V,"label-width":"100px"},{default:s(()=>[l(h,{label:"驳回原因",required:""},{default:s(()=>[l(H,{modelValue:V.shText,"onUpdate:modelValue":e[11]||(e[11]=o=>V.shText=o),type:"textarea",rows:4,placeholder:"请输入驳回原因",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(A,{modelValue:L.value,"onUpdate:modelValue":e[16]||(e[16]=o=>L.value=o),title:"等级调整",width:"500px"},{footer:s(()=>[l(f,{onClick:e[15]||(e[15]=o=>L.value=!1)},{default:s(()=>e[43]||(e[43]=[u("取消")])),_:1,__:[43]}),l(f,{type:"primary",onClick:De,loading:G.value},{default:s(()=>e[44]||(e[44]=[u(" 确认调整 ")])),_:1,__:[44]},8,["loading"])]),default:s(()=>[l(q,{model:k,"label-width":"100px"},{default:s(()=>[l(h,{label:"当前等级"},{default:s(()=>[a("span",null,i(ue.value.labelName||"暂无等级"),1)]),_:1}),l(h,{label:"调整等级",required:""},{default:s(()=>[l(ae,{modelValue:k.labelId,"onUpdate:modelValue":e[14]||(e[14]=o=>k.labelId=o),placeholder:"请选择等级",style:{width:"100%"},onChange:Se},{default:s(()=>[(p(!0),z(se,null,oe(le.value,o=>(p(),b(I,{key:o.id,label:o.labelName,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(A,{modelValue:M.value,"onUpdate:modelValue":e[20]||(e[20]=o=>M.value=o),title:"选择关联用户",width:"800px"},{footer:s(()=>[l(f,{onClick:e[19]||(e[19]=o=>M.value=!1)},{default:s(()=>e[45]||(e[45]=[u("取消")])),_:1,__:[45]})]),default:s(()=>[l(ve,{data:de.value,onRowClick:Be,style:{cursor:"pointer"}},{default:s(()=>[l(v,{prop:"id",label:"用户ID",width:"80"}),l(v,{prop:"avatarUrl",label:"头像",width:"80"},{default:s(o=>[o.row.avatarUrl?(p(),b(W,{key:0,src:o.row.avatarUrl,size:40},null,8,["src"])):(p(),b(W,{key:1,size:40},{default:s(()=>[l(me,null,{default:s(()=>[l(J(ye))]),_:1})]),_:1}))]),_:1}),l(v,{prop:"nickName",label:"昵称"}),l(v,{prop:"phone",label:"手机号"})]),_:1},8,["data"]),a("div",cl,[l(qe,{"current-page":w.pageNum,"onUpdate:currentPage":e[17]||(e[17]=o=>w.pageNum=o),"page-size":w.pageSize,"onUpdate:pageSize":e[18]||(e[18]=o=>w.pageSize=o),"page-sizes":[10,20,50,100],total:w.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:je,onCurrentChange:Me},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"]),l(A,{modelValue:S.value,"onUpdate:modelValue":e[24]||(e[24]=o=>S.value=o),title:"黑名单操作",width:"500px"},{footer:s(()=>[l(f,{onClick:e[23]||(e[23]=o=>S.value=!1)},{default:s(()=>e[48]||(e[48]=[u("取消")])),_:1,__:[48]}),l(f,{type:"primary",onClick:Ae,loading:ee.value},{default:s(()=>e[49]||(e[49]=[u(" 确认操作 ")])),_:1,__:[49]},8,["loading"])]),default:s(()=>[l(q,{model:g,"label-width":"100px"},{default:s(()=>[l(h,{label:"操作类型",required:""},{default:s(()=>[l(We,{modelValue:g.status,"onUpdate:modelValue":e[21]||(e[21]=o=>g.status=o)},{default:s(()=>[l(fe,{label:1},{default:s(()=>e[46]||(e[46]=[u("加入黑名单")])),_:1,__:[46]}),l(fe,{label:0},{default:s(()=>e[47]||(e[47]=[u("移除黑名单")])),_:1,__:[47]})]),_:1},8,["modelValue"])]),_:1}),l(h,{label:"审核备注",required:""},{default:s(()=>[l(H,{modelValue:g.text,"onUpdate:modelValue":e[22]||(e[22]=o=>g.text=o),type:"textarea",rows:4,placeholder:"请输入审核备注",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(A,{modelValue:P.value,"onUpdate:modelValue":e[26]||(e[26]=o=>P.value=o),title:"师傅详情",width:"900px"},{footer:s(()=>[l(f,{onClick:e[25]||(e[25]=o=>P.value=!1)},{default:s(()=>e[77]||(e[77]=[u("关闭")])),_:1,__:[77]})]),default:s(()=>[_e((p(),z("div",ml,[n.value?(p(),b(Y,{key:0,gutter:20},{default:s(()=>[l(C,{span:12},{default:s(()=>[a("div",vl,[e[57]||(e[57]=a("h4",null,"基本信息",-1)),a("div",fl,[e[50]||(e[50]=a("label",null,"师傅姓名：",-1)),a("span",null,i(n.value.coachName),1)]),a("div",gl,[e[51]||(e[51]=a("label",null,"手机号：",-1)),a("span",null,i(n.value.mobile),1)]),a("div",bl,[e[52]||(e[52]=a("label",null,"性别：",-1)),a("span",null,i(n.value.sex===0?"男":"女"),1)]),a("div",_l,[e[53]||(e[53]=a("label",null,"从业年份：",-1)),a("span",null,i(n.value.workTime)+"年",1)]),a("div",yl,[e[54]||(e[54]=a("label",null,"身份证号：",-1)),a("span",null,i(n.value.idCode),1)]),a("div",wl,[e[55]||(e[55]=a("label",null,"地址：",-1)),a("span",null,i(n.value.address),1)]),a("div",kl,[e[56]||(e[56]=a("label",null,"经纬度：",-1)),a("span",null,i(n.value.lng)+", "+i(n.value.lat),1)])])]),_:1}),l(C,{span:12},{default:s(()=>[a("div",hl,[e[71]||(e[71]=a("h4",null,"状态信息",-1)),a("div",xl,[e[58]||(e[58]=a("label",null,"审核状态：",-1)),l(U,{type:pe(n.value.status)},{default:s(()=>[u(i(ce(n.value.status)),1)]),_:1},8,["type"])]),a("div",Cl,[e[59]||(e[59]=a("label",null,"开启状态：",-1)),l(U,{type:n.value.isEnable===1?"success":"danger"},{default:s(()=>[u(i(n.value.isEnable===1?"开启":"关闭"),1)]),_:1},8,["type"])]),a("div",Vl,[e[60]||(e[60]=a("label",null,"工作状态：",-1)),l(U,{type:n.value.isWork===1?"success":"info"},{default:s(()=>[u(i(n.value.isWork===1?"工作中":"休息中"),1)]),_:1},8,["type"])]),a("div",zl,[e[61]||(e[61]=a("label",null,"师傅等级：",-1)),a("span",null,i(n.value.labelName||"暂无等级"),1)]),a("div",Tl,[e[62]||(e[62]=a("label",null,"服务次数：",-1)),a("span",null,i(n.value.count)+"次",1)]),a("div",Nl,[e[63]||(e[63]=a("label",null,"信誉分：",-1)),a("span",null,i(n.value.credit)+"分",1)]),a("div",Il,[e[64]||(e[64]=a("label",null,"星级评分：",-1)),a("span",null,i(n.value.star)+"星",1)]),a("div",Ul,[e[65]||(e[65]=a("label",null,"服务收入：",-1)),a("span",null,"￥"+i(n.value.servicePrice),1)]),a("div",$l,[e[66]||(e[66]=a("label",null,"上门费：",-1)),a("span",null,"￥"+i(n.value.carPrice),1)]),a("div",Ll,[e[67]||(e[67]=a("label",null,"入驻时间：",-1)),a("span",null,i(n.value.createTime),1)]),a("div",Sl,[e[68]||(e[68]=a("label",null,"审核时间：",-1)),a("span",null,i(n.value.shTime||"暂无"),1)]),a("div",Dl,[e[69]||(e[69]=a("label",null,"更新时间：",-1)),a("span",null,i(n.value.updateTime||"暂无"),1)]),a("div",El,[e[70]||(e[70]=a("label",null,"是否后台添加：",-1)),l(U,{type:n.value.adminAdd===1?"warning":"info",size:"small"},{default:s(()=>[u(i(n.value.adminAdd===1?"后台添加":"用户注册"),1)]),_:1},8,["type"])])])]),_:1})]),_:1})):T("",!0),n.value?(p(),z("div",Al,[e[74]||(e[74]=a("h4",null,"图片信息",-1)),l(Y,{gutter:20},{default:s(()=>[n.value.selfImg?(p(),b(C,{key:0,span:8},{default:s(()=>[a("div",Bl,[e[72]||(e[72]=a("label",null,"个人照片：",-1)),a("div",jl,[(p(!0),z(se,null,oe(F(n.value.selfImg),(o,y)=>(p(),b(ge,{key:y,src:o,"preview-src-list":F(n.value.selfImg),fit:"cover",style:{width:"80px",height:"80px",margin:"5px"}},null,8,["src","preview-src-list"]))),128))])])]),_:1})):T("",!0),n.value.idCard?(p(),b(C,{key:1,span:8},{default:s(()=>[a("div",Ml,[e[73]||(e[73]=a("label",null,"身份证照片：",-1)),a("div",Pl,[(p(!0),z(se,null,oe(F(n.value.idCard),(o,y)=>(p(),b(ge,{key:y,src:o,"preview-src-list":F(n.value.idCard),fit:"cover",style:{width:"80px",height:"80px",margin:"5px"}},null,8,["src","preview-src-list"]))),128))])])]),_:1})):T("",!0)]),_:1})])):T("",!0),n.value&&n.value.text?(p(),z("div",Rl,[e[75]||(e[75]=a("h4",null,"师傅简介",-1)),a("p",null,i(n.value.text),1)])):T("",!0),n.value&&n.value.shText?(p(),z("div",Fl,[e[76]||(e[76]=a("h4",null,"审核备注",-1)),a("p",null,i(n.value.shText),1)])):T("",!0)])),[[be,Z.value]])]),_:1},8,["modelValue"])])}}},Xl=Ze(Yl,[["__scopeId","data-v-f33da8a7"]]);export{Xl as default};
