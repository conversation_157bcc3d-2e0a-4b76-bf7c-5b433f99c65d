/**
 * 服务项目管理模块 - V2版本（整合版）
 * 按照API封装规范文档实现服务项目相关接口
 *
 * 包含以下功能模块：
 * 1. 服务管理 - 服务列表、新增、编辑、删除等
 * 2. 轮播图管理 - 轮播图列表、新增、编辑、删除等
 * 3. 服务点管理 - 服务点列表、新增、编辑、删除等
 * 4. 金刚区（导航区）管理 - 导航列表、新增、编辑、删除等
 * 5. 服务分类管理 - 分类列表、新增、编辑、删除、状态管理等
 */

import { get, post, postUpload } from '../index'

export default {
  // ==================== 轮播图管理相关接口 ====================

  /**
   * 获取轮播图列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，非必填，-1不可用，1可用
   * @param {number} querys.type 类型，非必填，1用户端，2师傅端
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认5
   * @returns {Promise} 返回轮播图列表数据
   */
  bannerList(querys) {
    console.log('🔍 轮播图列表API-V2请求参数:', querys)
    return get('/api/admin/banner/list', querys)
  },

  /**
   * 获取轮播图详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 轮播图ID
   * @returns {Promise} 返回轮播图详情数据
   */
  bannerDetail(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('轮播图ID不能为空'))
    }
    console.log('🔍 获取轮播图详情API-V2请求:', querys)
    return get(`/api/admin/banner/detail/${querys.id}`)
  },

  /**
   * 新增轮播图
   * @param {Object} querys 轮播图数据
   * @param {string} querys.img 轮播图图片URL，必填
   * @param {number} querys.top 排序值，默认0
   * @param {string} querys.link 链接地址，可选
   * @param {number} querys.status 状态，1可用，-1不可用，0默认
   * @param {number} querys.type 类型，1用户端，2师傅端
   * @returns {Promise} 返回新增结果
   */
  bannerAdd(querys) {
    if (!querys || !querys.img) {
      return Promise.reject(new Error('轮播图图片不能为空'))
    }

    const apiData = {
      img: querys.img,
      top: querys.top || 0,
      link: querys.link || '',
      status: querys.status || 1,
      type: querys.type || 1
    }

    console.log('➕ 新增轮播图API-V2请求数据:', apiData)
    return post('/api/admin/banner/add', apiData)
  },

  /**
   * 编辑轮播图
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 轮播图ID
   * @param {string} querys.img 轮播图图片URL
   * @param {number} querys.top 排序值
   * @param {string} querys.link 链接地址
   * @param {number} querys.status 状态，1可用，-1不可用
   * @param {number} querys.type 类型，1用户端，2师傅端
   * @returns {Promise} 返回编辑结果
   */
  bannerUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('轮播图ID不能为空'))
    }

    const apiData = {
      id: querys.id,
      img: querys.img,
      top: querys.top || 0,
      link: querys.link || '',
      status: querys.status || 1,
      type: querys.type || 1
    }

    console.log('✏️ 编辑轮播图API-V2请求数据:', apiData)
    return post('/api/admin/banner/edit', apiData)
  },

  /**
   * 删除轮播图
   * @param {Object} querys 删除参数
   * @param {number} querys.id 轮播图ID
   * @returns {Promise} 返回删除结果
   */
  bannerDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('轮播图ID不能为空'))
    }
    console.log('🗑️ 删除轮播图API-V2请求:', querys)
    return post(`/api/admin/banner/delete/${querys.id}`)
  },

  /**
   * 修改轮播图状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 轮播图ID
   * @param {number} querys.status 状态，1可用，-1不可用
   * @returns {Promise} 返回状态修改结果
   */
  bannerStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('轮播图ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 修改轮播图状态API-V2请求:', querys)
    return post(`/api/admin/banner/status/${querys.id}`, { status: querys.status })
  },

  // ==================== 服务管理相关接口 ====================

  /**
   * 获取服务列表
   * @param {Object} querys 查询参数
   * @param {string} querys.title 服务名称，非必填
   * @param {number} querys.status 状态，非必填，1可用，-1禁用
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @param {string} querys.serviceCateName 服务分类名称，非必填
   * @param {number} querys.servicePriceType 报价类型，非必填，0一口价模式，1报价模式，2两者都有
   * @returns {Promise} 返回服务列表数据
   */
  serviceList(querys) {
    console.log('🔍 服务列表API-V2请求参数:', querys)
    return get('/api/admin/service/list', querys)
  },

  /**
   * 获取服务详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 服务ID
   * @returns {Promise} 返回服务详情数据
   */
  serviceInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务ID不能为空'))
    }
    console.log('🔍 获取服务详情API-V2请求:', querys)
    return get(`/api/admin/service/info/${querys.id}`)
  },

  /**
   * 新增服务
   * @param {Object} querys 服务数据
   * @param {string} querys.title 服务标题
   * @param {string} querys.cover 服务封面图片URL
   * @param {number} querys.price 服务价格
   * @param {number} querys.serviceCateId 服务分类ID
   * @param {number} querys.servicePriceType 报价类型，0一口价模式，1报价模式，2两者都有
   * @param {number} querys.top 排序值
   * @param {number} querys.status 状态，1可用，-1禁用
   * @param {string} querys.subTitle 副标题
   * @param {string} querys.introduce 服务介绍
   * @param {string} querys.explain 服务说明
   * @param {string} querys.notice 注意事项
   * @returns {Promise} 返回新增结果
   */
  serviceAdd(querys) {
    if (!querys || !querys.title) {
      return Promise.reject(new Error('服务标题不能为空'))
    }

    const apiData = {
      title: querys.title,
      cover: querys.cover || '',
      price: querys.price || 0,
      serviceCateId: querys.serviceCateId || 0,
      servicePriceType: querys.servicePriceType || 0,
      top: querys.top || 0,
      status: querys.status || 1,
      subTitle: querys.subTitle || '',
      introduce: querys.introduce || '',
      explain: querys.explain || '',
      notice: querys.notice || '',
      cityId: querys.cityId || '',
      cityStr: querys.cityStr || '',
      agentIds: querys.agentIds || []
    }

    console.log('➕ 新增服务API-V2请求数据:', apiData)
    return post('/api/admin/service/add', apiData)
  },

  /**
   * 编辑服务
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 服务ID
   * @param {string} querys.title 服务标题
   * @param {string} querys.cover 服务封面图片URL
   * @param {number} querys.price 服务价格
   * @param {number} querys.serviceCateId 服务分类ID
   * @param {number} querys.servicePriceType 报价类型，0一口价模式，1报价模式，2两者都有
   * @param {number} querys.top 排序值
   * @param {number} querys.status 状态，1可用，-1禁用
   * @param {string} querys.subTitle 副标题
   * @param {string} querys.introduce 服务介绍
   * @param {string} querys.explain 服务说明
   * @param {string} querys.notice 注意事项
   * @returns {Promise} 返回编辑结果
   */
  serviceUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务ID不能为空'))
    }

    const apiData = {
      id: querys.id,
      title: querys.title,
      cover: querys.cover || '',
      price: querys.price || 0,
      serviceCateId: querys.serviceCateId || 0,
      servicePriceType: querys.servicePriceType || 0,
      top: querys.top || 0,
      status: querys.status || 1,
      subTitle: querys.subTitle || '',
      introduce: querys.introduce || '',
      explain: querys.explain || '',
      notice: querys.notice || '',
      cityId: querys.cityId || '',
      cityStr: querys.cityStr || '',
      agentIds: querys.agentIds || []
    }

    console.log('✏️ 编辑服务API-V2请求数据:', apiData)
    return post('/api/admin/service/update', apiData)
  },

  /**
   * 删除服务
   * @param {Object} querys 删除参数
   * @param {number} querys.id 服务ID
   * @returns {Promise} 返回删除结果
   */
  serviceDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务ID不能为空'))
    }
    console.log('🗑️ 删除服务API-V2请求:', querys)
    return post(`/api/admin/service/delete/${querys.id}`)
  },

  /**
   * 修改服务状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 服务ID
   * @returns {Promise} 返回状态修改结果
   */
  serviceStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务ID不能为空'))
    }
    console.log('🔄 修改服务状态API-V2请求:', querys)
    return post(`/api/admin/service/status/${querys.id}`)
  },














  

  

  // ==================== 服务点管理相关接口 ====================

  /**
   * 获取服务点列表
   * @param {Object} querys 查询参数
   * @param {string} querys.name 服务点名称，非必填
   * @param {string} querys.address 地址，非必填
   * @param {string} querys.tel 联系电话，非必填
   * @param {string} querys.serviceCate 主营业务，非必填
   * @param {number} querys.isGrounding 是否上架，非必填，1上架，-1下架
   * @param {number} querys.pageNum 当前页数，非必填，默认1
   * @param {number} querys.pageSize 每页数量，非必填，默认10
   * @returns {Promise} 返回服务点列表数据
   */
  agentList(querys) {
    console.log('🔍 服务点列表API-V2请求参数:', querys)
    return get('/api/admin/agent/list', querys)
  },

  /**
   * 获取服务点详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 服务点ID
   * @returns {Promise} 返回服务点详情数据
   */
  agentDetail(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务点ID不能为空'))
    }
    console.log('🔍 获取服务点详情API-V2请求:', querys)
    return get(`/api/admin/agent/detail/${querys.id}`)
  },

  /**
   * 新增服务点
   * @param {Object} querys 服务点数据
   * @param {string} querys.name 服务点名称
   * @param {string} querys.address 地址
   * @param {string} querys.tel 联系电话
   * @param {string} querys.serviceCate 主营业务
   * @param {string} querys.img 图片URL
   * @param {number} querys.longitude 经度
   * @param {number} querys.latitude 纬度
   * @param {number} querys.isGrounding 是否上架，1上架，-1下架
   * @returns {Promise} 返回新增结果
   */
  agentAdd(querys) {
    if (!querys || !querys.name || !querys.address || !querys.tel) {
      return Promise.reject(new Error('服务点名称、地址和联系电话不能为空'))
    }

    // 转换字段名：前端 -> 后端
    const apiData = {
      name: querys.name,
      address: querys.address,
      tel: querys.tel,
      serviceCate: querys.serviceCate || '',
      img: querys.img || '',
      longitude: querys.longitude || null,
      latitude: querys.latitude || null,
      isGrounding: querys.isGrounding || 1
    }

    console.log('➕ 新增服务点API-V2请求数据:', apiData)
    return post('/api/admin/agent/add', apiData)
  },

  /**
   * 编辑服务点
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 服务点ID
   * @param {string} querys.name 服务点名称
   * @param {string} querys.address 地址
   * @param {string} querys.tel 联系电话
   * @param {string} querys.serviceCate 主营业务
   * @param {string} querys.img 图片URL
   * @param {number} querys.longitude 经度
   * @param {number} querys.latitude 纬度
   * @param {number} querys.isGrounding 是否上架，1上架，-1下架
   * @returns {Promise} 返回编辑结果
   */
  agentUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务点ID不能为空'))
    }

    // 转换字段名：前端 -> 后端
    const apiData = {
      id: querys.id,
      name: querys.name,
      address: querys.address,
      tel: querys.tel,
      serviceCate: querys.serviceCate || '',
      img: querys.img || '',
      longitude: querys.longitude || null,
      latitude: querys.latitude || null,
      isGrounding: querys.isGrounding || 1
    }

    console.log('✏️ 编辑服务点API-V2请求:', apiData)
    return post('/api/admin/agent/edit', apiData)
  },

  /**
   * 删除服务点
   * @param {Object} querys 删除参数
   * @param {number} querys.id 服务点ID
   * @returns {Promise} 返回删除结果
   */
  agentDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务点ID不能为空'))
    }

    console.log('🗑️ 删除服务点API-V2请求:', querys)
    return post(`/api/admin/agent/delete/${querys.id}`)
  },

  /**
   * 更新服务点状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 服务点ID
   * @param {number} querys.status 状态，1上架，-1下架
   * @returns {Promise} 返回更新结果
   */
  agentStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('服务点ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 更新服务点状态API-V2请求:', querys)
    return post(`/api/admin/agent/status/${querys.id}`, { status: querys.status })
  },

  // ==================== 服务配置管理相关接口 ====================

  /**
   * 获取服务项目配置列表
   * @param {Object} querys 查询参数
   * @param {number} querys.serviceId 服务ID，非必填
   * @param {string} querys.name 服务名称，非必填
   * @param {number} querys.pageNum 当前页数，非必填，默认1
   * @param {number} querys.pageSize 每页数量，非必填，默认10
   * @returns {Promise} 返回服务配置列表数据
   */
  priceSettingList(querys) {
    console.log('🔍 服务配置列表API-V2请求参数:', querys)
    return get('/api/admin/priceSetting/list', querys)
  },

  /**
   * 获取服务项目配置详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回服务配置详情数据
   */
  priceSettingInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }
    console.log('🔍 获取服务配置详情API-V2请求:', querys)
    return get(`/api/admin/priceSetting/detail/${querys.id}`)
  },

  /**
   * 新增服务项目配置
   * @param {Object} querys 配置数据
   * @param {number} querys.type 类型
   * @param {number} querys.serviceId 服务ID
   * @param {string} querys.problemDesc 问题描述
   * @param {string} querys.problemContent 问题内容
   * @param {number} querys.isRequired 是否必填，1必填，0非必填
   * @param {number} querys.inputType 输入类型，1文本，2图片，3选择
   * @param {string} querys.val 默认值
   * @param {string} querys.options 选项数据，JSON字符串
   * @returns {Promise} 返回新增结果
   */
  priceSettingAdd(querys) {
    console.log('➕ 新增服务配置API-V2请求参数:', querys)
    return post('/api/admin/priceSetting/add', querys)
  },

  /**
   * 编辑服务项目配置
   * @param {Object} querys 配置数据
   * @param {number} querys.id 配置ID
   * @param {number} querys.type 类型
   * @param {number} querys.serviceId 服务ID
   * @param {string} querys.problemDesc 问题描述
   * @param {string} querys.problemContent 问题内容
   * @param {number} querys.isRequired 是否必填，1必填，0非必填
   * @param {number} querys.inputType 输入类型，1文本，2图片，3选择
   * @param {string} querys.val 默认值
   * @param {string} querys.options 选项数据，JSON字符串
   * @returns {Promise} 返回编辑结果
   */
  priceSettingEdit(querys) {
    console.log('✏️ 编辑服务配置API-V2请求参数:', querys)
    return post('/api/admin/priceSetting/edit', querys)
  },

  /**
   * 删除服务项目配置
   * @param {Object} querys 查询参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回删除结果
   */
  priceSettingDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }
    console.log('🗑️ 删除服务配置API-V2请求:', querys)
    return post(`/api/admin/priceSetting/delete/${querys.id}`)
  },

  /**
   * 根据服务ID删除配置
   * @param {Object} querys 查询参数
   * @param {number} querys.serviceId 服务ID
   * @returns {Promise} 返回删除结果
   */
  priceSettingDeleteByServiceId(querys) {
    if (!querys || !querys.serviceId) {
      return Promise.reject(new Error('服务ID不能为空'))
    }
    console.log('🗑️ 根据服务ID删除配置API-V2请求:', querys)
    return post(`/api/admin/priceSetting/deleteByServiceId/${querys.serviceId}`)
  },

  // ==================== 服务分类管理相关接口 ====================

  /**
   * 获取服务分类列表
   * @param {Object} querys 查询参数
   * @param {string} querys.name 分类名称，非必填
   * @param {number} querys.status 状态，非必填，1启用，0禁用
   * @param {number} querys.isRecommend 推荐状态，非必填，1推荐，2不推荐
   * @param {number} querys.parentId 父级分类ID，非必填，0为顶级分类
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回服务分类列表数据
   */
  serviceCateList(querys) {
    console.log('🔍 服务分类列表API-V2请求参数:', querys)
    return get('/api/admin/serviceCate/list', querys)
  },

  /**
   * 获取服务分类详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回服务分类详情数据
   */
  serviceCateInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }
    console.log('🔍 获取服务分类详情API-V2请求:', querys)
    return get(`/api/admin/serviceCate/detail/${querys.id}`)
  },

  /**
   * 新增服务分类
   * @param {Object} querys 分类数据
   * @param {string} querys.name 分类名称
   * @param {number} querys.parentId 父级分类ID，0为顶级分类
   * @param {string} querys.img 分类图片URL
   * @param {string} querys.description 分类描述
   * @param {number} querys.sort 排序值
   * @param {number} querys.isRecommend 推荐状态，1推荐，2不推荐
   * @param {number} querys.status 状态，1启用，0禁用
   * @returns {Promise} 返回新增结果
   */
  serviceCateAdd(querys) {
    if (!querys || !querys.name) {
      return Promise.reject(new Error('分类名称不能为空'))
    }

    const apiData = {
      name: querys.name,
      parentId: querys.parentId || 0,
      img: querys.img || '',
      description: querys.description || '',
      sort: querys.sort || 0,
      isRecommend: querys.isRecommend || 2,
      status: querys.status || 1
    }

    console.log('➕ 新增服务分类API-V2请求数据:', apiData)
    return post('/api/admin/serviceCate/add', apiData)
  },

  /**
   * 编辑服务分类
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  serviceCateUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('✏️ 编辑服务分类API-V2请求:', querys)
    return post('/api/admin/serviceCate/edit', querys)
  },

  /**
   * 删除服务分类
   * @param {Object} querys 删除参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回删除结果
   */
  serviceCateDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('🗑️ 删除服务分类API-V2请求:', querys)
    return post(`/api/admin/serviceCate/delete/${querys.id}`)
  },

  /**
   * 修改服务分类状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回状态修改结果
   */
  serviceCateStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('🔄 修改服务分类状态API-V2请求:', querys)
    return post(`/api/admin/serviceCate/status/${querys.id}`)
  },

  /**
   * 修改服务分类推荐状态
   * @param {Object} querys 推荐状态修改参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回推荐状态修改结果
   */
  serviceCateRecommend(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('⭐ 修改服务分类推荐状态API-V2请求:', querys)
    return post(`/api/admin/serviceCate/recommend/${querys.id}`)
  },

  /**
   * 获取父级分类列表
   * @param {Object} querys 查询参数
   * @returns {Promise} 返回父级分类列表数据
   */
  serviceCateParentList(querys) {
    console.log('📂 获取父级分类列表API-V2请求:', querys)
    return get('/api/admin/serviceCate/parent/list', querys)
  },

  // ==================== 金刚区（导航）管理相关接口 ====================

  /**
   * 获取金刚区列表
   * @param {Object} querys 查询参数
   * @param {string} querys.title 标题，非必填
   * @param {number} querys.status 状态，非必填，1启用，0禁用
   * @param {number} querys.type 类型，非必填，1用户端，2师傅端
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回金刚区列表数据
   */
  navList(querys) {
    console.log('🔍 金刚区列表API-V2请求参数:', querys)
    return get('/api/admin/nav/list', querys)
  },

  /**
   * 获取金刚区详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 金刚区ID
   * @returns {Promise} 返回金刚区详情数据
   */
  navInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('金刚区ID不能为空'))
    }
    console.log('🔍 获取金刚区详情API-V2请求:', querys)
    return get(`/api/admin/nav/detail/${querys.id}`)
  },

  /**
   * 新增金刚区
   * @param {Object} querys 金刚区数据
   * @param {string} querys.title 标题
   * @param {string} querys.img 图片URL
   * @param {string} querys.link 链接地址
   * @param {number} querys.top 排序值
   * @param {number} querys.status 状态，1启用，0禁用
   * @param {number} querys.type 类型，1用户端，2师傅端
   * @returns {Promise} 返回新增结果
   */
  navAdd(querys) {
    if (!querys || !querys.title || !querys.img) {
      return Promise.reject(new Error('标题和图片不能为空'))
    }

    const apiData = {
      title: querys.title,
      img: querys.img,
      link: querys.link || '',
      top: querys.top || 0,
      status: querys.status || 1,
      type: querys.type || 1
    }

    console.log('➕ 新增金刚区API-V2请求数据:', apiData)
    return post('/api/admin/nav/add', apiData)
  },

  /**
   * 编辑金刚区
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 金刚区ID
   * @param {string} querys.title 标题
   * @param {string} querys.img 图片URL
   * @param {string} querys.link 链接地址
   * @param {number} querys.top 排序值
   * @param {number} querys.status 状态，1启用，0禁用
   * @param {number} querys.type 类型，1用户端，2师傅端
   * @returns {Promise} 返回编辑结果
   */
  navUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('金刚区ID不能为空'))
    }

    const apiData = {
      id: querys.id,
      title: querys.title,
      img: querys.img,
      link: querys.link || '',
      top: querys.top || 0,
      status: querys.status || 1,
    
    }

    console.log('✏️ 编辑金刚区API-V2请求数据:', apiData)
    return post('/api/admin/nav/edit', apiData)
  },

  /**
   * 删除金刚区
   * @param {Object} querys 删除参数
   * @param {number} querys.id 金刚区ID
   * @returns {Promise} 返回删除结果
   */
  navDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('金刚区ID不能为空'))
    }
    console.log('🗑️ 删除金刚区API-V2请求:', querys)
    return post(`/api/admin/nav/delete/${querys.id}`)
  },

  /**
   * 修改金刚区状态
   * @param {Object} querys 状态参数
   * @param {number} querys.id 金刚区ID
   * @returns {Promise} 返回修改结果
   */
  navStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('金刚区ID不能为空'))
    }
    console.log('🔄 修改金刚区状态API-V2请求:', querys)
    return post(`/api/admin/nav/status/${querys.id}`)
  },

  // ==================== 服务项目配置管理相关接口 ====================

  /**
   * 获取服务项目配置列表
   * @param {Object} querys 查询参数
   * @param {number} querys.serviceId 服务ID，非必填
   * @param {string} querys.name 服务名称，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回服务项目配置列表数据
   */
  priceSettingList(querys) {
    console.log('🔍 服务项目配置列表API-V2请求参数:', querys)
    return get('/api/admin/priceSetting/list', querys)
  },

  /**
   * 获取服务项目配置详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回服务项目配置详情数据
   */
  priceSettingDetail(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }
    console.log('🔍 获取服务项目配置详情API-V2请求:', querys)
    return get(`/api/admin/priceSetting/detail/${querys.id}`)
  },

  /**
   * 新增服务项目配置
   * @param {Object} querys 配置数据
   * @param {number} querys.type 类型
   * @param {number} querys.serviceId 服务ID
   * @param {string} querys.problemDesc 问题描述
   * @param {string} querys.problemContent 问题内容
   * @param {number} querys.isRequired 是否必填，1必填，0非必填
   * @param {number} querys.inputType 输入类型，1文本输入，2图片上传，3选择器
   * @param {string} querys.val 默认值
   * @param {string} querys.options 选项（JSON字符串格式）
   * @returns {Promise} 返回新增结果
   */
  priceSettingAdd(querys) {
    if (!querys || !querys.problemDesc) {
      return Promise.reject(new Error('问题描述不能为空'))
    }

    const apiData = {
      type: querys.type || 1,
      serviceId: querys.serviceId || 0,
      problemDesc: querys.problemDesc,
      problemContent: querys.problemContent || '',
      isRequired: querys.isRequired || 0,
      inputType: querys.inputType || 1,
      val: querys.val || '',
      options: querys.options || ''
    }

    console.log('➕ 新增服务项目配置API-V2请求数据:', apiData)
    return post('/api/admin/priceSetting/add', apiData)
  },

  /**
   * 编辑服务项目配置
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 配置ID
   * @param {number} querys.type 类型
   * @param {number} querys.serviceId 服务ID
   * @param {string} querys.problemDesc 问题描述
   * @param {string} querys.problemContent 问题内容
   * @param {number} querys.isRequired 是否必填，1必填，0非必填
   * @param {number} querys.inputType 输入类型，1文本输入，2图片上传，3选择器
   * @param {string} querys.val 默认值
   * @param {string} querys.options 选项（JSON字符串格式）
   * @returns {Promise} 返回编辑结果
   */
  priceSettingEdit(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }

    const apiData = {
      id: querys.id,
      type: querys.type || 1,
      serviceId: querys.serviceId || 0,
      problemDesc: querys.problemDesc,
      problemContent: querys.problemContent || '',
      isRequired: querys.isRequired || 0,
      inputType: querys.inputType || 1,
      val: querys.val || '',
      options: querys.options || ''
    }

    console.log('✏️ 编辑服务项目配置API-V2请求数据:', apiData)
    return post('/api/admin/priceSetting/edit', apiData)
  },

  /**
   * 删除服务项目配置
   * @param {Object} querys 删除参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回删除结果
   */
  priceSettingDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }
    console.log('🗑️ 删除服务项目配置API-V2请求:', querys)
    return post(`/api/admin/priceSetting/delete/${querys.id}`)
  },

  /**
   * 根据服务ID删除配置
   * @param {Object} querys 删除参数
   * @param {number} querys.serviceId 服务ID
   * @returns {Promise} 返回删除结果
   */
  priceSettingDeleteByServiceId(querys) {
    if (!querys || !querys.serviceId) {
      return Promise.reject(new Error('服务ID不能为空'))
    }
    console.log('🗑️ 根据服务ID删除配置API-V2请求:', querys)
    return post(`/api/admin/priceSetting/deleteByServiceId/${querys.serviceId}`)
  },

  /**
   * 下载服务配置模板
   * @returns {Promise} 返回模板文件下载结果
   */
  priceSettingTemplate() {
    console.log('📥 下载服务配置模板API-V2请求')
    return get('/api/admin/priceSetting/template')
  },

  /**
   * 批量导入服务配置
   * @param {FormData} formData 包含文件的表单数据
   * @param {File} formData.file Excel文件（服务价格配置导入模板.xlsx）
   * @returns {Promise} 返回导入结果
   */
  priceSettingImport(formData) {
    if (!formData || !formData.get('file')) {
      return Promise.reject(new Error('请选择要导入的文件'))
    }
    console.log('📤 批量导入服务配置API-V2请求')
    return postUpload('/api/admin/priceSetting/import', formData)
  }

}
