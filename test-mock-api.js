/**
 * 服务项目模块Mock API测试脚本
 * 使用Node.js fetch API测试所有Mock接口
 */

const baseUrl = 'http://localhost:3000';

// 测试结果统计
let testResults = {
  total: 0,
  success: 0,
  failed: 0,
  results: []
};

// 通用请求函数
async function makeRequest(url, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(baseUrl + url, options);
    const result = await response.json();
    
    return {
      success: response.ok && result.code === 200,
      status: response.status,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 记录测试结果
function logTest(testName, result) {
  testResults.total++;
  const status = result.success ? '✅ 成功' : '❌ 失败';
  
  if (result.success) {
    testResults.success++;
    console.log(`${status} ${testName}`);
    console.log(`   响应: ${JSON.stringify(result.data).substring(0, 100)}...`);
  } else {
    testResults.failed++;
    console.log(`${status} ${testName}`);
    console.log(`   错误: ${result.error || JSON.stringify(result.data)}`);
  }
  
  testResults.results.push({
    name: testName,
    success: result.success,
    result: result
  });
  
  console.log('─'.repeat(80));
}

// 测试函数
async function runTests() {
  console.log('🧪 开始测试服务项目模块Mock API');
  console.log('═'.repeat(80));
  
  // 1. 测试服务项目管理
  console.log('\n📋 1. 服务项目管理 (/api/service)');
  console.log('─'.repeat(50));
  
  let result = await makeRequest('/api/service/list?page=1&pageSize=5');
  logTest('获取服务项目列表', result);
  
  result = await makeRequest('/api/service/add', 'POST', {
    title: '测试服务项目',
    category: '家政服务',
    price: 199.99,
    description: '这是一个测试服务项目'
  });
  logTest('新增服务项目', result);
  
  result = await makeRequest('/api/service/update/1', 'PUT', {
    title: '更新后的服务项目',
    price: 299.99
  });
  logTest('更新服务项目', result);
  
  result = await makeRequest('/api/service/delete/1', 'DELETE');
  logTest('删除服务项目', result);
  
  result = await makeRequest('/api/service/statistics');
  logTest('获取服务项目统计', result);
  
  // 2. 测试轮播图设置
  console.log('\n🖼️ 2. 轮播图设置 (/api/service/banner)');
  console.log('─'.repeat(50));
  
  result = await makeRequest('/api/service/banner?page=1&pageSize=5');
  logTest('获取轮播图列表', result);
  
  result = await makeRequest('/api/service/banner/add', 'POST', {
    title: '测试轮播图',
    image: 'https://example.com/banner.jpg',
    link: '/service/list'
  });
  logTest('新增轮播图', result);
  
  result = await makeRequest('/api/service/banner/update/1', 'PUT', {
    title: '更新后的轮播图'
  });
  logTest('更新轮播图', result);
  
  result = await makeRequest('/api/service/banner/delete/1', 'DELETE');
  logTest('删除轮播图', result);
  
  result = await makeRequest('/api/service/banner/click/1', 'POST');
  logTest('轮播图点击统计', result);
  
  // 3. 测试分类设置
  console.log('\n📂 3. 分类设置 (/api/service/category)');
  console.log('─'.repeat(50));
  
  result = await makeRequest('/api/service/category?page=1&pageSize=5');
  logTest('获取分类列表', result);
  
  result = await makeRequest('/api/service/category/tree');
  logTest('获取分类树', result);
  
  result = await makeRequest('/api/service/category/add', 'POST', {
    name: '测试分类',
    parent_id: 0,
    icon: 'Service'
  });
  logTest('新增分类', result);
  
  result = await makeRequest('/api/service/category/recommend/1', 'PUT', {
    is_recommend: 1
  });
  logTest('分类推荐切换', result);
  
  // 4. 测试金刚区设置
  console.log('\n💎 4. 金刚区设置 (/api/service/jingang)');
  console.log('─'.repeat(50));
  
  result = await makeRequest('/api/service/jingang?page=1&pageSize=5');
  logTest('获取金刚区列表', result);
  
  result = await makeRequest('/api/service/jingang/add', 'POST', {
    name: '测试金刚区',
    icon: 'Service',
    link: '/service/list'
  });
  logTest('新增金刚区', result);
  
  result = await makeRequest('/api/service/jingang/sort', 'PUT', {
    sortData: [
      { id: 1, sort: 1, position: 1 },
      { id: 2, sort: 2, position: 2 }
    ]
  });
  logTest('金刚区排序更新', result);
  
  // 5. 测试服务点设置
  console.log('\n📍 5. 服务点设置 (/api/service/point)');
  console.log('─'.repeat(50));
  
  result = await makeRequest('/api/service/point?page=1&pageSize=5');
  logTest('获取服务点列表', result);
  
  result = await makeRequest('/api/service/point/add', 'POST', {
    name: '测试服务点',
    address: '北京市朝阳区测试地址',
    contact: '张三',
    phone: '13800138000'
  });
  logTest('新增服务点', result);
  
  result = await makeRequest('/api/service/point/batch-status', 'PUT', {
    ids: [1, 2, 3],
    status: 1
  });
  logTest('服务点批量状态更新', result);
  
  // 6. 测试项目配置
  console.log('\n⚙️ 6. 项目配置 (/api/service/config)');
  console.log('─'.repeat(50));
  
  result = await makeRequest('/api/service/config?page=1&pageSize=5');
  logTest('获取配置列表', result);
  
  result = await makeRequest('/api/service/config/groups');
  logTest('获取配置分组', result);
  
  result = await makeRequest('/api/service/config/batch', 'PUT', {
    configs: [
      { id: 1, value: '0.05' },
      { id: 2, value: '100' }
    ]
  });
  logTest('批量更新配置', result);
  
  // 输出测试总结
  console.log('\n📊 测试总结');
  console.log('═'.repeat(80));
  console.log(`总测试数: ${testResults.total}`);
  console.log(`成功: ${testResults.success} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${(testResults.success / testResults.total * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.results.filter(r => !r.success).forEach(r => {
      console.log(`   - ${r.name}`);
    });
  } else {
    console.log('\n🎉 所有测试通过！Mock数据系统运行正常。');
  }
}

// 运行测试
runTests().catch(console.error);
