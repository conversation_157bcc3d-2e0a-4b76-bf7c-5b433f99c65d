import{E as d,O as w,N as P,q as m}from"./element-fdzwdDuf.js";import{T as N,L as f}from"./LbButton-BtU4V_Gr.js";import{L as i}from"./LbUeditor-Dh_pKaDh.js";import{_ as x,a as g}from"./index-C9Xz1oqp.js";import{r as C,X as S,h as B,y as U,Q as t,A as o,I as a,al as h,z as A,M as v,u as p}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const T={class:"lb-system-info"},b={class:"page-main"},L={class:"editor-container"},Q={class:"editor-container"},k={class:"editor-container"},E={class:"editor-container"},I={class:"editor-container"},M={class:"editor-container"},z={class:"editor-container"},O={class:"editor-container"},j={class:"action-buttons"},q={__name:"SystemInfo",setup(F){const c=C(!1),l=S({loginProtocol:"",informationProtection:"",content:"",shifuServiceAgreement:"",shifuSecurityProtocol:"",shifuQualityCommitment:"",userNotice:"",masterNotice:""}),y=async()=>{try{console.log("📄 开始获取隐私协议配置...");const n=await g.sys.getPrivacyAgreement();n.code==="200"?(Object.assign(l,n.data||{}),console.log("✅ 隐私协议配置获取成功")):d.error(n.message||"获取配置失败")}catch(n){console.error("❌ 获取隐私协议配置失败:",n),d.error("获取配置失败，请稍后重试")}},V=async()=>{try{await m.confirm("确定要保存所有隐私协议配置吗？","确认保存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),c.value=!0,console.log("💾 开始保存隐私协议配置...");const n=await g.sys.updatePrivacyAgreement(l);n.code==="200"?(d.success("隐私协议配置保存成功"),console.log("✅ 隐私协议配置保存成功")):d.error(n.message||"保存失败")}catch(n){n!=="cancel"&&(console.error("❌ 保存隐私协议配置失败:",n),d.error("保存失败，请稍后重试"))}finally{c.value=!1}},_=async()=>{try{await m.confirm("确定要重置所有配置吗？此操作将清空所有内容！","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),Object.assign(l,{loginProtocol:"",informationProtection:"",content:"",shifuServiceAgreement:"",shifuSecurityProtocol:"",shifuQualityCommitment:"",userNotice:"",masterNotice:""}),d.success("配置已重置")}catch{}};return B(()=>{y()}),(n,e)=>{const r=h("el-card"),u=h("el-icon");return A(),U("div",T,[t(N),o("div",b,[t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[8]||(e[8]=[o("div",{class:"card-header"},[o("span",null,"用户隐私协议")],-1)])),default:a(()=>[o("div",L,[t(i,{modelValue:l.loginProtocol,"onUpdate:modelValue":e[0]||(e[0]=s=>l.loginProtocol=s),height:"90vh",placeholder:"请输入用户隐私协议内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[9]||(e[9]=[o("div",{class:"card-header"},[o("span",null,"个人信息保护指引")],-1)])),default:a(()=>[o("div",Q,[t(i,{modelValue:l.informationProtection,"onUpdate:modelValue":e[1]||(e[1]=s=>l.informationProtection=s),height:"90vh",placeholder:"请输入个人信息保护指引内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[10]||(e[10]=[o("div",{class:"card-header"},[o("span",null,"师傅端隐私协议")],-1)])),default:a(()=>[o("div",k,[t(i,{modelValue:l.content,"onUpdate:modelValue":e[2]||(e[2]=s=>l.content=s),height:"90vh",placeholder:"请输入师傅端隐私协议内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[11]||(e[11]=[o("div",{class:"card-header"},[o("span",null,"师傅端服务协议")],-1)])),default:a(()=>[o("div",E,[t(i,{modelValue:l.shifuServiceAgreement,"onUpdate:modelValue":e[3]||(e[3]=s=>l.shifuServiceAgreement=s),height:"90vh",placeholder:"请输入师傅端服务协议内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[12]||(e[12]=[o("div",{class:"card-header"},[o("span",null,"师傅端安全协议")],-1)])),default:a(()=>[o("div",I,[t(i,{modelValue:l.shifuSecurityProtocol,"onUpdate:modelValue":e[4]||(e[4]=s=>l.shifuSecurityProtocol=s),height:"90vh",placeholder:"请输入师傅端安全协议内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[13]||(e[13]=[o("div",{class:"card-header"},[o("span",null,"服务质量保障承诺")],-1)])),default:a(()=>[o("div",M,[t(i,{modelValue:l.shifuQualityCommitment,"onUpdate:modelValue":e[5]||(e[5]=s=>l.shifuQualityCommitment=s),height:"90vh",placeholder:"请输入服务质量保障承诺内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[14]||(e[14]=[o("div",{class:"card-header"},[o("span",null,"用户须知")],-1)])),default:a(()=>[o("div",z,[t(i,{modelValue:l.userNotice,"onUpdate:modelValue":e[6]||(e[6]=s=>l.userNotice=s),height:"90vh",placeholder:"请输入用户须知内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{header:a(()=>e[15]||(e[15]=[o("div",{class:"card-header"},[o("span",null,"师傅须知")],-1)])),default:a(()=>[o("div",O,[t(i,{modelValue:l.masterNotice,"onUpdate:modelValue":e[7]||(e[7]=s=>l.masterNotice=s),height:"90vh",placeholder:"请输入师傅须知内容..."},null,8,["modelValue"])])]),_:1}),t(r,{class:"config-card",shadow:"never"},{default:a(()=>[o("div",j,[t(f,{type:"primary",onClick:V,loading:c.value,size:"large"},{default:a(()=>[t(u,null,{default:a(()=>[t(p(w))]),_:1}),e[16]||(e[16]=v(" 保存所有配置 "))]),_:1,__:[16]},8,["loading"]),t(f,{onClick:_,size:"large",style:{"margin-left":"15px"}},{default:a(()=>[t(u,null,{default:a(()=>[t(p(P))]),_:1}),e[17]||(e[17]=v(" 重置配置 "))]),_:1,__:[17]})])]),_:1})])])}}},R=x(q,[["__scopeId","data-v-95e1087a"]]);export{R as default};
