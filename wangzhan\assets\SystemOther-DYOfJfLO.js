import{T as b,L as w}from"./LbButton-BtU4V_Gr.js";import{_ as x}from"./index-C9Xz1oqp.js";import{E as m}from"./element-fdzwdDuf.js";import{r as u,X as C,h as N,y as T,Q as o,A as c,I as a,al as n,z as U,M as p}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const j={class:"lb-system-other"},k={class:"page-main"},B={__name:"SystemOther",setup(O){const d=u(!1),f=u(),t=C({maintenance:0,maintenance_msg:"",service_phone:"",service_email:""}),g=async()=>{try{const e=await(await fetch("/api/system/other/config")).json();e.code===200&&Object.assign(t,e.data||{})}catch(s){console.error("获取配置失败:",s)}},v=async()=>{try{d.value=!0;const e=await(await fetch("/api/system/other/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).json();e.code===200?m.success("配置保存成功"):m.error(e.message||"保存失败")}catch{m.error("保存失败")}finally{d.value=!1}};return N(()=>{g()}),(s,e)=>{const _=n("el-radio"),y=n("el-radio-group"),r=n("el-form-item"),i=n("el-input"),V=n("el-form"),h=n("el-card");return U(),T("div",j,[o(b),c("div",k,[o(h,{class:"config-card",shadow:"never"},{header:a(()=>e[4]||(e[4]=[c("div",{class:"card-header"},[c("span",null,"其他设置")],-1)])),default:a(()=>[o(V,{model:t,ref_key:"configFormRef",ref:f,"label-width":"140px",class:"config-form"},{default:a(()=>[o(r,{label:"系统维护"},{default:a(()=>[o(y,{modelValue:t.maintenance,"onUpdate:modelValue":e[0]||(e[0]=l=>t.maintenance=l)},{default:a(()=>[o(_,{value:1},{default:a(()=>e[5]||(e[5]=[p("开启")])),_:1,__:[5]}),o(_,{value:0},{default:a(()=>e[6]||(e[6]=[p("关闭")])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"维护提示"},{default:a(()=>[o(i,{modelValue:t.maintenance_msg,"onUpdate:modelValue":e[1]||(e[1]=l=>t.maintenance_msg=l),type:"textarea",rows:3,placeholder:"请输入维护提示信息"},null,8,["modelValue"])]),_:1}),o(r,{label:"客服电话"},{default:a(()=>[o(i,{modelValue:t.service_phone,"onUpdate:modelValue":e[2]||(e[2]=l=>t.service_phone=l),placeholder:"请输入客服电话"},null,8,["modelValue"])]),_:1}),o(r,{label:"客服邮箱"},{default:a(()=>[o(i,{modelValue:t.service_email,"onUpdate:modelValue":e[3]||(e[3]=l=>t.service_email=l),placeholder:"请输入客服邮箱"},null,8,["modelValue"])]),_:1}),o(r,null,{default:a(()=>[o(w,{type:"primary",onClick:v,loading:d.value},{default:a(()=>e[7]||(e[7]=[p("保存配置")])),_:1,__:[7]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},I=x(B,[["__scopeId","data-v-ed906c91"]]);export{I as default};
