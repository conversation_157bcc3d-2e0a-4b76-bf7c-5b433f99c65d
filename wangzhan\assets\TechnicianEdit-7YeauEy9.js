import{r as m,j as Z,H as j,I as s,A as v,Q as o,a4 as ke,al as _,M as b,y as K,K as qe,z as N,g as Ue,c as ee,ay as Ne,X as te,h as Le,V as $e,az as ze,ar as Me,u as D,O as le,J as Fe}from"./vendor-DmFBDimT.js";import{E as p,u as W,x as ae}from"./element-fdzwdDuf.js";import{L as B,T as Se}from"./LbButton-BtU4V_Gr.js";import{L as oe}from"./LbToolTips-Bbf_ZQSC.js";import{_ as ie}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const Ae={class:"dialog-inner"},De={class:"map-search"},Re=["id"],Te={key:0,class:"map-loading"},je={class:"dialog-footer"},Be={__name:"LbMapSimple",props:{dialogVisible:{type:Boolean,default:!1},address:{type:String,default:""}},emits:["update:dialogVisible","selectedLatLng"],setup(H,{emit:O}){const I=H,S=O,f=m(!1),C=m(""),L=m(!1),k=m(`map-container-${Date.now()}`),V=m(null),h=m(null),q=m(null),$=m({lat:39.916527,lng:116.397128}),x=()=>{console.log("对话框已打开，开始初始化地图"),setTimeout(()=>{z()},100)},z=()=>{if(console.log("开始初始化地图..."),typeof qq>"u"||!qq.maps){p.error("地图API未加载");return}const t=document.getElementById(k.value);if(!t){console.error("地图容器未找到");return}console.log("容器尺寸:",t.offsetWidth,"x",t.offsetHeight);try{const c=new qq.maps.LatLng($.value.lat,$.value.lng),d=new qq.maps.Map(t,{center:c,zoom:13});V.value=d;const w=new qq.maps.Marker({position:c,map:d});h.value=w,qq.maps.event.addListener(d,"click",R=>{h.value&&h.value.setMap(null);const{lat:y,lng:A}=R.latLng;$.value={lat:y,lng:A},h.value=new qq.maps.Marker({position:new qq.maps.LatLng(y,A),map:d});const T=new qq.maps.InfoWindow({map:d});T.open(),T.setContent(`
        <div style="margin:10px;">
          <p>纬度：${y}</p>
          <p>经度：${A}</p>
        </div>
      `),T.setPosition(new qq.maps.LatLng(y,A))}),L.value=!0,console.log("地图初始化成功")}catch(c){console.error("地图初始化失败:",c),p.error("地图初始化失败")}},U=()=>{if(!C.value.trim()){p.warning("请输入搜索地址");return}if(!V.value){p.error("地图未初始化");return}try{q.value||(q.value=new qq.maps.Geocoder),q.value.setComplete(t=>{const{lat:c,lng:d}=t.detail.location;$.value={lat:c,lng:d},V.value.setCenter(t.detail.location),h.value&&h.value.setMap(null),h.value=new qq.maps.Marker({map:V.value,position:t.detail.location});const w=new qq.maps.InfoWindow({map:V.value});w.open(),w.setContent(`
        <div style="margin:10px;">
          <p>纬度：${c}</p>
          <p>经度：${d}</p>
        </div>
      `),w.setPosition(new qq.maps.LatLng(c,d))}),q.value.setError(()=>{p.error("请输入包含市级的地址！")}),q.value.getLocation(C.value)}catch(t){console.error("地址搜索失败:",t),p.error("地址搜索失败")}},M=()=>{f.value=!1,S("selectedLatLng",$.value)},E=()=>{f.value=!1,S("update:dialogVisible",!1)};return Z(()=>I.dialogVisible,t=>{t&&(f.value=!0,C.value=I.address||"",L.value=!1)}),Z(f,t=>{t||S("update:dialogVisible",!1)}),(t,c)=>{const d=_("el-input"),w=_("el-button"),R=_("el-dialog");return N(),j(R,{modelValue:f.value,"onUpdate:modelValue":c[1]||(c[1]=y=>f.value=y),title:"获取经纬度",width:"650px",center:!0,onClose:E,onOpened:x},{footer:s(()=>[v("div",je,[o(w,{onClick:E},{default:s(()=>c[3]||(c[3]=[b("取消")])),_:1,__:[3]}),o(w,{type:"primary",onClick:M},{default:s(()=>c[4]||(c[4]=[b("确定")])),_:1,__:[4]})])]),default:s(()=>[v("div",Ae,[v("div",De,[o(d,{modelValue:C.value,"onUpdate:modelValue":c[0]||(c[0]=y=>C.value=y),placeholder:"输入地址",onKeydown:ke(U,["enter"])},null,8,["modelValue"]),o(B,{size:"small",type:"primary",onClick:U},{default:s(()=>c[2]||(c[2]=[b(" 搜 索 ")])),_:1,__:[2]})]),v("div",{id:k.value,class:"map-container"},[L.value?qe("",!0):(N(),K("div",Te," 地图加载中... "))],8,Re)])]),_:1},8,["modelValue"])}}},Ee=ie(Be,[["__scopeId","data-v-bf7baedb"]]),Pe={class:"lb-system-banner-edit"},Oe={class:"page-main"},We={class:"user-select-container"},Ke={class:"image-upload-container"},He={class:"image-upload-wrapper"},Xe={class:"upload-tips"},Ge={class:"image-upload-container"},Je={class:"image-upload-wrapper"},Qe={class:"upload-tips"},Ye={class:"submit-buttons"},Ze={class:"user-pagination",style:{"margin-top":"20px","text-align":"center"}},et={__name:"TechnicianEdit",setup(H,{expose:O}){const I=Ne(),S=ze(),{proxy:f}=Ue(),C=m(),L=m(!1),k=m(!1),V=m(!1),h=m(!1),q=m([]),$=m([]),x=m([]),z=m([]),U=m([]),M=ee(()=>!!I.query.id&&I.query.mode!=="add"),E=ee(()=>I.query.mode==="view"||M.value?"查看师傅":"新增师傅"),t=te({id:null,user_id:"",nickName:"",coach_name:"",sex:0,mobile:"",work_time:"",city_id:[],address:"",lng:"",lat:"",text:"",id_code:"",id_card:"",self_img:"",level_id:"",skills:[],status:1,is_enable:1}),c={value:"id",label:"trueName",children:"children",expandTrigger:"hover",emitPath:!0,checkStrictly:!1},d=te({pageNum:1,pageSize:10,total:0}),w={coach_name:[{required:!0,message:"请输入师傅姓名",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],work_time:[{required:!0,message:"请输入从业年份",trigger:"blur"}],city_id:[{required:!0,message:"请选择所在区域",trigger:"change"}],address:[{required:!0,message:"请输入所在地址",trigger:"blur"}],id_code:[{required:!0,message:"请输入身份证号",trigger:"blur"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号",trigger:"blur"}]},R=()=>{k.value=!0,y()},y=async()=>{try{V.value=!0;const l=await f.$api.technician.getAssociateUser({pageNum:d.pageNum,pageSize:d.pageSize});l.code==="200"?(q.value=l.data.list||[],d.total=l.data.totalCount||0):p.error(l.msg||"获取用户列表失败")}catch(l){console.error("获取用户列表失败:",l),p.error("获取用户列表失败")}finally{V.value=!1}},A=l=>{t.user_id=l.id,t.nickName=l.nickName,k.value=!1},T=()=>{t.user_id="",t.nickName=""},re=l=>{d.pageSize=l,d.pageNum=1,y()},se=l=>{d.pageNum=l,y()},X=l=>{const e=l.type.startsWith("image/"),a=l.size/1024/1024<5;return e?a?!0:(p.error("图片大小不能超过 5MB!"),!1):(p.error("只能上传图片文件!"),!1)},ne=async(l,e)=>{if(l.status==="ready")try{const a=await G(l.raw);if(a){const i=e.findIndex(r=>r.uid===l.uid);if(i!==-1){const r=[...e];r[i]={...r[i],url:a,status:"success"},z.value=r;const u=r.filter(g=>g.url&&g.status==="success").map(g=>g.url);t.id_card=u.join(","),console.log("✅ 身份证照片上传成功:",{当前文件:l.name,上传URL:a,所有照片字符串:t.id_card,照片数量:t.id_card?t.id_card.split(",").length:0})}}}catch(a){console.error("❌ 身份证照片上传失败:",a),p.error("图片上传失败");const i=e.findIndex(r=>r.uid===l.uid);i!==-1&&(e.splice(i,1),z.value=[...e])}},de=(l,e)=>{z.value=[...e];const a=e.filter(i=>i.url&&i.status==="success").map(i=>i.url);t.id_card=a.join(","),console.log("🗑️ 移除身份证照片后剩余:",t.id_card)},ue=async(l,e)=>{if(l.status==="ready")try{const a=await G(l.raw);if(a){const i=e.findIndex(r=>r.uid===l.uid);if(i!==-1){const r=[...e];r[i]={...r[i],url:a,status:"success"},U.value=r;const u=r.filter(g=>g.url&&g.status==="success").map(g=>g.url);t.self_img=u.join(","),console.log("✅ 个人照片上传成功:",{当前文件:l.name,上传URL:a,所有照片字符串:t.self_img,照片数量:t.self_img?t.self_img.split(",").length:0})}}}catch(a){console.error("❌ 个人生活照上传失败:",a),p.error("图片上传失败");const i=e.findIndex(r=>r.uid===l.uid);i!==-1&&(e.splice(i,1),U.value=[...e])}},ce=(l,e)=>{U.value=[...e];const a=e.filter(i=>i.url&&i.status==="success").map(i=>i.url);t.self_img=a.join(","),console.log("🗑️ 移除个人照片后剩余:",t.self_img)},G=async l=>{try{const e=new FormData;e.append("multipartFile",l);const a=await f.$api.upload.uploadFile(e);if(a.code===200||a.code==="200")return a.data.url||a.data.fileUrl||a.data;throw new Error(a.message||a.msg||"上传失败")}catch(e){throw console.error("图片上传失败:",e),e}},pe=l=>{t.lat=l.lat,t.lng=l.lng},me=async()=>{try{const l=await f.$api.technician.labelCoachList();l.code==="200"?$.value=l.data||[]:p.error(l.msg||"获取等级列表失败")}catch(l){console.error("获取等级列表失败:",l),p.error("获取等级列表失败")}},ge=l=>{if(console.log("🔄 区域选择变化:",l),l&&l.length===3){const[e,a,i]=l;console.log("✅ 选择的区域:",{provinceId:e,cityId:a,districtId:i}),t.city_id=l;const r=_e(l);r&&console.log("📍 区域详情:",r)}else t.city_id=[]},_e=l=>{if(!l||l.length!==3)return null;const[e,a,i]=l;let r,u,g;return r=x.value.find(F=>F.id===e),!r||(u=r.children?.find(F=>F.id===a),!u)||(g=u.children?.find(F=>F.id===i),!g)?null:{province:r.trueName,city:u.trueName,district:g.trueName,fullName:`${r.trueName}${u.trueName}${g.trueName}`}},fe=async()=>{try{const l=await f.$api.technician.getCityTree();console.log("🏙️ 获取省市区数据结果:",l),l.code==="200"||l.code===200?(x.value=ve(l.data||[]),console.log("✅ 省市区数据加载成功，共",x.value.length,"个省份")):(console.error("❌ 获取省市区数据失败:",l.msg),p.error(l.msg||"获取省市区数据失败"),x.value=[])}catch(l){console.error("❌ 获取省市区数据异常:",l),p.error("获取省市区数据失败，请检查网络连接"),x.value=[]}},ve=l=>Array.isArray(l)?l.map(e=>({id:e.id,trueName:e.trueName,children:(e.children||[]).map(a=>({id:a.id,trueName:a.trueName,children:(a.children||[]).map(i=>({id:i.id,trueName:i.trueName}))}))})):[],ye=async l=>{try{const e=await f.$api.technician.coachDetail(l);if(e.code==="200"){const a=e.data;if(Object.assign(t,a),a.city_id)if(Array.isArray(a.city_id))t.city_id=a.city_id,console.log("🔄 回显区域数据(数组):",a.city_id);else if(typeof a.city_id=="string"&&a.city_id.includes(","))t.city_id=a.city_id.split(",").map(i=>parseInt(i)),console.log("🔄 回显区域数据(字符串转数组):",t.city_id);else{const i=be(a.city_id);i?(t.city_id=i,console.log("🔄 回显区域数据(路径):",i)):(t.city_id=a.city_id,console.log("🔄 回显区域数据(原值):",a.city_id))}if(a.id_card){let i=[];Array.isArray(a.id_card)?i=a.id_card:typeof a.id_card=="string"&&a.id_card.trim()&&(i=a.id_card.split(",").filter(r=>r.trim())),i.length>0&&(z.value=i.map((r,u)=>({name:`id_card_${u}`,url:r.trim(),status:"success"})),t.id_card=i.join(","))}if(a.self_img){let i=[];Array.isArray(a.self_img)?i=a.self_img:typeof a.self_img=="string"&&a.self_img.trim()&&(i=a.self_img.split(",").filter(r=>r.trim())),i.length>0&&(U.value=i.map((r,u)=>({name:`self_img_${u}`,url:r.trim(),status:"success"})),t.self_img=i.join(","))}console.log("🔄 师傅详情回显完成:",{身份证照片:t.id_card,个人照片:t.self_img,城市ID:t.city_id,数据格式:"所有字段均为字符串格式，逗号分隔"})}else p.error(e.msg||"获取师傅详情失败")}catch(e){console.error("获取师傅详情失败:",e),p.error("获取师傅详情失败")}},be=l=>{for(const e of x.value)for(const a of e.children||[])for(const i of a.children||[])if(i.id===l)return[e.id,a.id,i.id];return null},J=async()=>{try{await C.value.validate(),L.value=!0;const l={userId:t.user_id||0,coachName:t.coach_name,mobile:t.mobile,sex:t.sex,workTime:t.work_time,cityId:Array.isArray(t.city_id)&&t.city_id.length===3||Array.isArray(t.city_id)?t.city_id.join(","):t.city_id,lng:parseFloat(t.lng)||0,lat:parseFloat(t.lat)||0,address:t.address,text:t.text,idCode:t.id_code,idCard:t.id_card,selfImg:t.self_img};console.log("📤 提交师傅数据:",l),console.log("🏙️ 字符串格式数据:",{城市ID:{值:l.cityId,类型:typeof l.cityId},身份证照片:{值:l.idCard,类型:typeof l.idCard},个人照片:{值:l.selfImg,类型:typeof l.selfImg}});let e;M.value?(l.id=t.id,e=await f.$api.technician.technicianUpdate(l)):e=await f.$api.technician.addCoach(l),e.code==="200"||e.code===200?(p.success(M.value?"更新成功":"新增成功"),S.push("/technician/list")):p.error(e.msg||"操作失败")}catch(l){console.error("提交失败:",l),p.error("操作失败")}finally{L.value=!1}};return O({submitForm:J}),Le(async()=>{y(),me(),await fe(),M.value&&I.query.id&&ye(I.query.id)}),(l,e)=>{const a=_("el-icon"),i=_("el-tag"),r=_("el-form-item"),u=_("el-input"),g=_("el-radio"),F=_("el-radio-group"),he=_("el-cascader"),Q=_("el-upload"),we=_("el-form"),P=_("el-table-column"),Y=_("el-avatar"),Ve=_("el-table"),xe=_("el-pagination"),Ie=_("el-dialog"),Ce=Me("loading");return N(),K("div",Pe,[o(Se,{title:E.value,isBack:!0},null,8,["title"]),v("div",Oe,[o(we,{onSubmit:e[12]||(e[12]=$e(()=>{},["prevent"])),model:t,ref_key:"subFormRef",ref:C,rules:w,"label-width":"130px"},{default:s(()=>[o(r,{label:"关联用户",prop:"user_id"},{default:s(()=>[v("div",We,[t.user_id?(N(),j(i,{key:1,type:"success",closable:"",onClose:T,style:{cursor:"pointer",padding:"8px 16px"}},{default:s(()=>[o(a,{style:{"margin-right":"5px"}},{default:s(()=>[o(D(W))]),_:1}),b(" "+le(t.nickName),1)]),_:1})):(N(),j(i,{key:0,onClick:R,type:"info",style:{cursor:"pointer",padding:"8px 16px"}},{default:s(()=>[o(a,{style:{"margin-right":"5px"}},{default:s(()=>[o(D(W))]),_:1}),e[18]||(e[18]=b(" 点击选择关联用户 "))]),_:1,__:[18]}))])]),_:1}),o(r,{label:"师傅姓名",prop:"coach_name"},{default:s(()=>[o(u,{modelValue:t.coach_name,"onUpdate:modelValue":e[0]||(e[0]=n=>t.coach_name=n),maxlength:"15","show-word-limit":"",placeholder:"请输入师傅姓名"},null,8,["modelValue"])]),_:1}),o(r,{label:"性别",prop:"sex"},{default:s(()=>[o(F,{modelValue:t.sex,"onUpdate:modelValue":e[1]||(e[1]=n=>t.sex=n)},{default:s(()=>[o(g,{value:0},{default:s(()=>e[19]||(e[19]=[b("男")])),_:1,__:[19]}),o(g,{value:1},{default:s(()=>e[20]||(e[20]=[b("女")])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"手机号",prop:"mobile"},{default:s(()=>[o(u,{modelValue:t.mobile,"onUpdate:modelValue":e[2]||(e[2]=n=>t.mobile=n),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),o(r,{label:"从业年份",prop:"work_time"},{default:s(()=>[o(u,{modelValue:t.work_time,"onUpdate:modelValue":e[3]||(e[3]=n=>t.work_time=n),modelModifiers:{number:!0},placeholder:"请输入从业年份"},null,8,["modelValue"])]),_:1}),o(r,{label:"所在区域",prop:"city_id"},{default:s(()=>[o(he,{modelValue:t.city_id,"onUpdate:modelValue":e[4]||(e[4]=n=>t.city_id=n),options:x.value,props:c,placeholder:"请选择省市区",style:{width:"100%"},clearable:"",filterable:"",onChange:ge},null,8,["modelValue","options"])]),_:1}),o(r,{label:"所在地址",prop:"address"},{default:s(()=>[o(u,{modelValue:t.address,"onUpdate:modelValue":e[5]||(e[5]=n=>t.address=n),placeholder:"请输入所在地址"},null,8,["modelValue"])]),_:1}),o(r,{label:"经度",prop:"lng"},{default:s(()=>[o(u,{modelValue:t.lng,"onUpdate:modelValue":e[6]||(e[6]=n=>t.lng=n),placeholder:"请输入经度"},null,8,["modelValue"])]),_:1}),o(r,{label:"纬度",prop:"lat"},{default:s(()=>[o(u,{modelValue:t.lat,"onUpdate:modelValue":e[7]||(e[7]=n=>t.lat=n),placeholder:"请输入纬度"},null,8,["modelValue"]),o(B,{onClick:e[8]||(e[8]=n=>h.value=!0),type:"primary",size:"small",plain:""},{default:s(()=>e[21]||(e[21]=[b("获取经纬度")])),_:1,__:[21]})]),_:1}),o(r,{label:"师傅简介",prop:"text"},{default:s(()=>[o(u,{type:"textarea",rows:10,maxlength:"300",resize:"none","show-word-limit":"",placeholder:"请输入师傅简介",modelValue:t.text,"onUpdate:modelValue":e[9]||(e[9]=n=>t.text=n)},null,8,["modelValue"])]),_:1}),o(r,{label:"身份证号",prop:"id_code"},{default:s(()=>[o(u,{modelValue:t.id_code,"onUpdate:modelValue":e[10]||(e[10]=n=>t.id_code=n),placeholder:"请输入身份证号"},null,8,["modelValue"])]),_:1}),o(r,{label:"身份证照片",prop:"id_card"},{default:s(()=>[v("div",Ke,[v("div",He,[o(Q,{class:"id-card-upload",action:"#","auto-upload":!1,"on-change":ne,"on-remove":de,"before-upload":X,"file-list":z.value,"list-type":"picture-card",limit:3,accept:"image/*",multiple:""},{default:s(()=>[o(a,null,{default:s(()=>[o(D(ae))]),_:1})]),_:1},8,["file-list"])]),v("div",Xe,[o(oe,null,{default:s(()=>e[22]||(e[22]=[b("请分别上传身份证人像面、身份证国徽面、手持身份证照片（最多3张）")])),_:1,__:[22]})])])]),_:1}),o(r,{label:"个人照片",prop:"self_img"},{default:s(()=>[v("div",Ge,[v("div",Je,[o(Q,{class:"self-img-upload",action:"#","auto-upload":!1,"on-change":ue,"on-remove":ce,"before-upload":X,"file-list":U.value,"list-type":"picture-card",limit:9,accept:"image/*",multiple:""},{default:s(()=>[o(a,null,{default:s(()=>[o(D(ae))]),_:1})]),_:1},8,["file-list"])]),v("div",Qe,[o(oe,null,{default:s(()=>e[23]||(e[23]=[b("建议尺寸：750px * n，最多上传9张照片")])),_:1,__:[23]})])])]),_:1}),o(r,null,{default:s(()=>[v("div",Ye,[o(B,{type:"primary",loading:L.value,onClick:J},{default:s(()=>[b(le(M.value?"更新师傅":"新增师傅"),1)]),_:1},8,["loading"]),o(B,{onClick:e[11]||(e[11]=n=>D(S).push("/technician/list"))},{default:s(()=>e[24]||(e[24]=[b(" 取消 ")])),_:1,__:[24]})])]),_:1})]),_:1},8,["model"])]),o(Ie,{modelValue:k.value,"onUpdate:modelValue":e[16]||(e[16]=n=>k.value=n),title:"选择关联用户",width:"800px"},{footer:s(()=>[o(B,{onClick:e[15]||(e[15]=n=>k.value=!1)},{default:s(()=>e[25]||(e[25]=[b("取消")])),_:1,__:[25]})]),default:s(()=>[Fe((N(),K("div",null,[o(Ve,{data:q.value,onRowClick:A,style:{cursor:"pointer"},"highlight-current-row":""},{default:s(()=>[o(P,{prop:"id",label:"用户ID",width:"80"}),o(P,{prop:"avatarUrl",label:"头像",width:"80"},{default:s(n=>[n.row.avatarUrl?(N(),j(Y,{key:0,src:n.row.avatarUrl,size:40},null,8,["src"])):(N(),j(Y,{key:1,size:40},{default:s(()=>[o(a,null,{default:s(()=>[o(D(W))]),_:1})]),_:1}))]),_:1}),o(P,{prop:"nickName",label:"昵称"}),o(P,{prop:"phone",label:"手机号"})]),_:1},8,["data"]),v("div",Ze,[o(xe,{"current-page":d.pageNum,"onUpdate:currentPage":e[13]||(e[13]=n=>d.pageNum=n),"page-size":d.pageSize,"onUpdate:pageSize":e[14]||(e[14]=n=>d.pageSize=n),"page-sizes":[10,20,50],total:d.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:re,onCurrentChange:se},null,8,["current-page","page-size","total"])])])),[[Ce,V.value]])]),_:1},8,["modelValue"]),o(Ee,{dialogVisible:h.value,"onUpdate:dialogVisible":e[17]||(e[17]=n=>h.value=n),address:t.address,onSelectedLatLng:pe},null,8,["dialogVisible","address"])])}}},st=ie(et,[["__scopeId","data-v-c3c77467"]]);export{st as default};
