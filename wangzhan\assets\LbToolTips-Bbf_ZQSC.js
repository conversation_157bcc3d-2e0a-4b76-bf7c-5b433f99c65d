import{B as t}from"./element-fdzwdDuf.js";import{_ as a}from"./index-C9Xz1oqp.js";import{y as n,Q as e,A as c,I as _,u as l,al as r,B as i,z as p}from"./vendor-DmFBDimT.js";const d={class:"lb-tool-tips"},f={class:"tips-text"},m={__name:"LbToolTips",setup(u){return(o,B)=>{const s=r("el-icon");return p(),n("div",d,[e(s,{class:"tips-icon"},{default:_(()=>[e(l(t))]),_:1}),c("span",f,[i(o.$slots,"default",{},void 0,!0)])])}}},b=a(m,[["__scopeId","data-v-ee4f9466"]]);export{b as L};
