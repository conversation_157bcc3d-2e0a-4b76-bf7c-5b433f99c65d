<template>
  <div class="technician-level">
    <!-- 顶部导航 -->
    <TopNav title="师傅等级管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <!-- <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton> -->
               
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增师傅等级
                </LbButton>
                 <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="displayData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
          row-key="uniqueKey"
          :row-class-name="getRowClassName"
        >
          <el-table-column prop="id" label="ID" width="100" align="center">
            <template #default="scope">
              <span
                :style="{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }"
                :class="{ 'expandable-id': scope.row.hasChildren }"
                @click="scope.row.hasChildren ? toggleExpand(scope.row) : null"
              >
                {{ scope.row.id }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="labelName" label="师傅等级" min-width="150">
            <template #default="scope">
              <span
                :style="{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }"
                class="category-name-wrapper"
              >
                <i
                  v-if="scope.row.hasChildren"
                  :class="[
                    'arrow-icon',
                    'el-icon-arrow-right',
                    { 'expanded': expandedRows.has(scope.row.id) }
                  ]"
                  @click="toggleExpand(scope.row)"
                ></i>
                <span v-else class="child-indent"></span>
                {{ scope.row.labelName || scope.row.star }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="earnestMoney" label="保证金" width="120">
            <template #default="scope">
              <span v-if="scope.row.earnestMoney !== undefined">{{ scope.row.earnestMoney }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="delayTime" label="延迟时间(分钟)" width="140">
            <template #default="scope">
              <span v-if="scope.row.delayTime !== undefined">{{ scope.row.delayTime }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="proportion" label="服务费降低比例" width="140">
            <template #default="scope">
              <span v-if="scope.row.proportion !== undefined">{{ scope.row.proportion }}%</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="scorePeriod" label="分数区间" width="120">
            <template #default="scope">
              <span v-if="scope.row.scorePeriod">{{ scope.row.scorePeriod }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="fixedPrice" label="一口价订单数" width="130">
            <template #default="scope">
              <span v-if="scope.row.fixedPrice !== undefined">{{ scope.row.fixedPrice }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="comparisonOrder" label="比价订单数" width="120">
            <template #default="scope">
              <span v-if="scope.row.comparisonOrder !== undefined">{{ scope.row.comparisonOrder }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="quotationsNum" label="报价次数" width="100">
            <template #default="scope">
              <span v-if="scope.row.quotationsNum !== undefined">{{ scope.row.quotationsNum }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" min-width="120">
            <template #default="scope">
              <div class="time-column">
                <p>{{ formatDate(scope.row.createTime) }}</p>
                <p>{{ formatTime(scope.row.createTime) }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" min-width="200" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  v-if="!scope.row.labelId"
                  size="default"
                  type="success"
                  @click="handleAddCredit(scope.row)"
                >
                  添加信誉分规则
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="default"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 师傅等级编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="师傅等级名称" prop="labelName">
          <el-input v-model="form.labelName" placeholder="请输入师傅等级名称" />
        </el-form-item>

        <el-form-item label="保证金" prop="earnestMoney">
          <el-input-number
            v-model="form.earnestMoney"
            :min="0"
            :precision="2"
            placeholder="请输入保证金"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="延迟时间(分钟)" prop="delayTime">
          <el-input-number
            v-model="form.delayTime"
            :min="0"
            placeholder="请输入订单推送延迟时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="服务费降低比例" prop="proportion">
          <el-input-number
            v-model="form.proportion"
            :min="0"
            :max="100"
            placeholder="请输入服务费降低比例"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399;">%</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 信誉分规则编辑对话框 -->
    <el-dialog
      :title="creditDialogTitle"
      v-model="creditDialogVisible"
      width="600px"
      @close="handleCreditDialogClose"
    >
      <el-form
        ref="creditFormRef"
        :model="creditForm"
        :rules="creditRules"
        label-width="140px"
      >
        <el-form-item label="师傅等级" prop="labelId">
          <el-select
            v-model="creditForm.labelId"
            placeholder="请选择师傅等级"
            style="width: 100%"
            size="default"
          >
            <el-option
              v-for="label in labelOptions"
              :key="label.id"
              :label="label.labelName"
              :value="label.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="星级评价" prop="star">
          <el-select
            v-model="creditForm.star"
            placeholder="请选择星级评价"
            style="width: 100%"
            size="default"
          >
            <el-option label="一星" value="一星" />
            <el-option label="二星" value="二星" />
            <el-option label="三星" value="三星" />
            <el-option label="四星" value="四星" />
            <el-option label="五星" value="五星" />
          </el-select>
        </el-form-item>

        <el-form-item label="分数区间" prop="scorePeriod">
          <el-input v-model="creditForm.scorePeriod" placeholder="请输入分数区间，如：80,90" />
        </el-form-item>

        <el-form-item label="一口价订单数" prop="fixedPrice">
          <el-input-number
            v-model="creditForm.fixedPrice"
            :min="0"
            placeholder="每日可接一口价订单数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="比价订单数" prop="comparisonOrder">
          <el-input-number
            v-model="creditForm.comparisonOrder"
            :min="0"
            placeholder="每日可接比价订单数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="报价次数" prop="quotationsNum">
          <el-input-number
            v-model="creditForm.quotationsNum"
            :min="0"
            placeholder="单个订单可报价次数"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="creditDialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleCreditSubmit" :loading="creditSubmitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const creditSubmitLoading = ref(false)
const tableData = ref([])
const displayData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const creditFormRef = ref()
const dialogVisible = ref(false)
const creditDialogVisible = ref(false)
const labelOptions = ref([])
const expandedRows = ref(new Set())

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10
})

// 师傅等级编辑表单
const form = reactive({
  id: null,
  labelName: '',
  earnestMoney: 0,
  delayTime: 0,
  proportion: 0
})

// 信誉分规则编辑表单
const creditForm = reactive({
  id: null,
  labelId: null,
  star: '',
  scorePeriod: '',
  fixedPrice: 0,
  comparisonOrder: 0,
  quotationsNum: 0
})

// 表单验证规则
const rules = {
  labelName: [
    { required: true, message: '请输入师傅等级名称', trigger: 'blur' }
  ]
}

const creditRules = {
  labelId: [
    { required: true, message: '请选择师傅等级', trigger: 'change' }
  ],
  star: [
    { required: true, message: '请选择星级评价', trigger: 'change' }
  ],
  scorePeriod: [
    { required: true, message: '请输入分数区间', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑师傅等级' : '新增师傅等级')
const creditDialogTitle = computed(() => creditForm.id ? '编辑信誉分规则' : '新增信誉分规则')

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 使用API-V2调用方式
    const result = await api.technician.labelList(params)
    console.log('🏷️ 师傅等级列表数据 (API-V2):', result)

    // 处理真实API的响应格式
    if (result.code === 200 || result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      const rawList = data.list || []
      tableData.value = rawList
      total.value = data.totalCount || data.total || 0

      // 构建树形结构显示数据
      buildTreeData()

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        displayData: displayData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取师傅等级列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 构建树形结构显示数据
const buildTreeData = () => {
  const result = []

  // 处理师傅等级和信誉分规则的层级结构
  tableData.value.forEach((label, labelIndex) => {
    // 添加师傅等级（父级）
    const labelItem = {
      ...label,
      level: 0,
      hasChildren: label.creditList && label.creditList.length > 0,
      children: [],
      uniqueKey: `label_${label.id}` // 添加唯一标识
    }

    result.push(labelItem)

    // 如果展开状态且有子级，添加信誉分规则（子级）
    if (labelItem.hasChildren && expandedRows.value.has(labelItem.id)) {
      label.creditList.forEach((credit, creditIndex) => {
        result.push({
          ...credit,
          level: 1,
          hasChildren: false,
          labelId: label.id, // 标记这是信誉分规则
          // 不设置labelName，让子级使用自己的star字段显示
          uniqueKey: `credit_${credit.id}`, // 添加唯一标识
          parentLabelName: label.labelName // 父级名称，用于其他用途
        })
      })
    }
  })

  console.log('🌳 构建的树形数据:', result)
  displayData.value = result
}

// 切换展开/收起
const toggleExpand = (row) => {
  if (expandedRows.value.has(row.id)) {
    expandedRows.value.delete(row.id)
  } else {
    expandedRows.value.add(row.id)
  }
  buildTreeData()
}

// 获取行样式类名
const getRowClassName = ({ row }) => {
  if (row.level === 0) {
    return 'parent-row'
  } else {
    return 'child-row'
  }
}

// 加载师傅等级选项
const loadLabelOptions = async () => {
  try {
    const result = await api.technician.labelCoachList()
    if (result.code === '200') {
      labelOptions.value = result.data || []
    }
  } catch (error) {
    console.error('获取师傅等级选项失败:', error)
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleAddCredit = (row) => {
  resetCreditForm()
  creditForm.labelId = row.id
  loadLabelOptions()
  creditDialogVisible.value = true
}

const handleEdit = async (row) => {
  if (row.labelId) {
    // 编辑信誉分规则
    resetCreditForm()
    creditForm.id = row.id
    creditForm.labelId = row.labelId
    creditForm.star = row.star
    creditForm.scorePeriod = row.scorePeriod
    creditForm.fixedPrice = row.fixedPrice
    creditForm.comparisonOrder = row.comparisonOrder
    creditForm.quotationsNum = row.quotationsNum
    loadLabelOptions()
    creditDialogVisible.value = true
  } else {
    // 编辑师傅等级
    resetForm()
    form.id = row.id
    form.labelName = row.labelName
    form.earnestMoney = row.earnestMoney
    form.delayTime = row.delayTime
    form.proportion = row.proportion
    dialogVisible.value = true
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个${row.labelId ? '信誉分规则' : '师傅等级'}吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    let result
    if (row.labelId) {
      // 删除信誉分规则
      result = await api.technician.creditDelete({ id: row.id })
    } else {
      // 删除师傅等级
      result = await api.technician.labelDelete({ id: row.id })
    }

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑师傅等级
      result = await api.technician.labelEdit(form)
    } else {
      // 新增师傅等级
      result = await api.technician.labelAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleCreditSubmit = async () => {
  try {
    await creditFormRef.value.validate()

    creditSubmitLoading.value = true

    let result
    if (creditForm.id) {
      // 编辑信誉分规则
      result = await api.technician.creditEdit(creditForm)
    } else {
      // 新增信誉分规则
      result = await api.technician.creditAdd(creditForm)
    }

    if (result.code === '200') {
      ElMessage.success(creditForm.id ? '更新成功' : '新增成功')
      creditDialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    creditSubmitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

const handleDialogClose = () => {
  resetForm()
}

const handleCreditDialogClose = () => {
  resetCreditForm()
}

const resetForm = () => {
  form.id = null
  form.labelName = ''
  form.earnestMoney = 0
  form.delayTime = 0
  form.proportion = 0
}

const resetCreditForm = () => {
  creditForm.id = null
  creditForm.labelId = null
  creditForm.star = ''
  creditForm.scorePeriod = ''
  creditForm.fixedPrice = 0
  creditForm.comparisonOrder = 0
  creditForm.quotationsNum = 0
}

// Format date and time
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// Lifecycle hook
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* Page main styles */
.technician-level {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  margin-top: 0;
}

/* Spacing styles */
.space-lg {
  height: 16px;
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* Table styles */
.el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* Time column styles */
.time-column p {
  margin: 0;
  line-height: 1.6;
  font-size: 16px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Operation button styles */
.table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Tree structure styles */
.expandable-id {
  cursor: pointer;
  color: #409eff;
  font-weight: bold;
}

.expandable-id:hover {
  text-decoration: underline;
}

/* Row styles */
:deep(.parent-row) {
  font-weight: 500;
}

:deep(.child-row) {
  background-color: #fafbfc;
}

/* Dialog styles */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* Pagination styles */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: right;
}

/* Category name specific styles */
.category-name-wrapper {
  display: flex;
  align-items: center;
}

.arrow-icon {
  margin-right: 5px;
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
}

.arrow-icon.expanded {
  transform: rotate(90deg);
}

.child-indent {
  display: inline-block;
  width: 19px;
  height: 1px;
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}
</style>
