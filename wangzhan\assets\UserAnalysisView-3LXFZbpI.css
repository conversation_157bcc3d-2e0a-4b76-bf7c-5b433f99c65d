.user-analysis[data-v-7f0d93b2]{padding:var(--spacing-lg)}.page-title[data-v-7f0d93b2]{margin-bottom:var(--spacing-xl)}.page-title h1[data-v-7f0d93b2]{font-size:var(--font-size-xxl);color:var(--color-text-primary);margin-bottom:var(--spacing-xs)}.page-subtitle[data-v-7f0d93b2]{color:var(--color-text-secondary);font-size:var(--font-size-base)}.metrics-grid[data-v-7f0d93b2]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:var(--spacing-lg);margin-bottom:var(--spacing-xl)}.metric-card[data-v-7f0d93b2]{display:flex;align-items:center;gap:var(--spacing-lg);padding:var(--spacing-xl);background:var(--bg-color-base);border-radius:var(--border-radius-lg);box-shadow:var(--box-shadow-light);transition:var(--transition-box-shadow)}.metric-card[data-v-7f0d93b2]:hover{box-shadow:var(--box-shadow-base)}.metric-icon[data-v-7f0d93b2]{display:flex;align-items:center;justify-content:center;width:56px;height:56px;border-radius:var(--border-radius-lg);color:#fff;flex-shrink:0}.metric-content[data-v-7f0d93b2]{flex:1}.metric-value[data-v-7f0d93b2]{font-size:28px;font-weight:700;color:var(--color-text-primary);margin-bottom:var(--spacing-xs)}.metric-label[data-v-7f0d93b2]{font-size:var(--font-size-sm);color:var(--color-text-secondary);margin-bottom:var(--spacing-xs)}.metric-change[data-v-7f0d93b2]{display:flex;align-items:center;gap:var(--spacing-xs);font-size:var(--font-size-sm);font-weight:500}.metric-change.up[data-v-7f0d93b2]{color:var(--color-success)}.metric-change.down[data-v-7f0d93b2]{color:var(--color-danger)}.chart-section[data-v-7f0d93b2],.activity-section[data-v-7f0d93b2],.behavior-section[data-v-7f0d93b2]{margin-bottom:var(--spacing-xl)}.chart-row[data-v-7f0d93b2]{display:grid;grid-template-columns:1fr 1fr;gap:var(--spacing-lg)}.chart-card[data-v-7f0d93b2],.table-card[data-v-7f0d93b2]{background:var(--bg-color-base);border-radius:var(--border-radius-lg);box-shadow:var(--box-shadow-light);overflow:hidden}.chart-header[data-v-7f0d93b2],.table-header[data-v-7f0d93b2]{display:flex;align-items:center;justify-content:space-between;padding:var(--spacing-lg);border-bottom:1px solid var(--border-color-light);background-color:var(--bg-color-column)}.chart-header h3[data-v-7f0d93b2],.table-header h3[data-v-7f0d93b2]{font-size:var(--font-size-lg);color:var(--color-text-primary);margin:0}.chart-content[data-v-7f0d93b2],.table-content[data-v-7f0d93b2]{padding:var(--spacing-lg)}.mock-chart[data-v-7f0d93b2]{height:300px;display:flex;align-items:center;justify-content:center}.chart-placeholder[data-v-7f0d93b2]{text-align:center;color:var(--color-text-secondary)}.chart-placeholder p[data-v-7f0d93b2]{margin:var(--spacing-md) 0}.trend-data[data-v-7f0d93b2]{display:flex;justify-content:center;gap:var(--spacing-lg);margin-top:var(--spacing-lg)}.trend-item[data-v-7f0d93b2]{display:flex;flex-direction:column;align-items:center;gap:var(--spacing-xs)}.trend-date[data-v-7f0d93b2]{font-size:var(--font-size-xs);color:var(--color-text-placeholder)}.trend-value[data-v-7f0d93b2]{font-size:var(--font-size-sm);font-weight:500;color:var(--color-success)}.activity-stats[data-v-7f0d93b2]{display:flex;flex-direction:column;gap:var(--spacing-lg)}.activity-item[data-v-7f0d93b2]{display:flex;flex-direction:column;gap:var(--spacing-sm)}.activity-label[data-v-7f0d93b2]{display:flex;justify-content:space-between;align-items:center;font-size:var(--font-size-sm);color:var(--color-text-primary)}.activity-value[data-v-7f0d93b2]{font-weight:500;color:var(--color-text-primary)}.activity-bar[data-v-7f0d93b2]{height:8px;background-color:var(--bg-color-column);border-radius:4px;overflow:hidden}.activity-progress[data-v-7f0d93b2]{height:100%;border-radius:4px;transition:width .3s ease}.region-stats[data-v-7f0d93b2]{display:flex;flex-direction:column;gap:var(--spacing-md)}.region-item[data-v-7f0d93b2]{display:flex;justify-content:space-between;align-items:center;padding:var(--spacing-sm) 0;border-bottom:1px solid var(--border-color-light)}.region-item[data-v-7f0d93b2]:last-child{border-bottom:none}.region-info[data-v-7f0d93b2]{display:flex;flex-direction:column;gap:var(--spacing-xs)}.region-name[data-v-7f0d93b2]{font-size:var(--font-size-sm);color:var(--color-text-primary)}.region-count[data-v-7f0d93b2]{font-size:var(--font-size-xs);color:var(--color-text-secondary)}.region-percentage[data-v-7f0d93b2]{font-weight:500;color:var(--color-primary)}.trend-up[data-v-7f0d93b2]{color:var(--color-success)}.trend-down[data-v-7f0d93b2]{color:var(--color-danger)}@media (max-width: 1200px){.chart-row[data-v-7f0d93b2]{grid-template-columns:1fr}}@media (max-width: 768px){.user-analysis[data-v-7f0d93b2]{padding:var(--spacing-md)}.metrics-grid[data-v-7f0d93b2]{grid-template-columns:1fr}.metric-card[data-v-7f0d93b2]{padding:var(--spacing-lg)}.chart-content[data-v-7f0d93b2],.table-content[data-v-7f0d93b2]{padding:var(--spacing-md)}.trend-data[data-v-7f0d93b2]{flex-direction:column;gap:var(--spacing-sm)}}
