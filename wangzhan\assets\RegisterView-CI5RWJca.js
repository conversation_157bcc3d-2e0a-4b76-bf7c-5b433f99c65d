import{_ as n}from"./index-C9Xz1oqp.js";import{y as r,A as t,Q as i,I as l,al as a,z as d,M as p}from"./vendor-DmFBDimT.js";import"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const _={},f={class:"register-container"},m={class:"register-content"};function u(o,e){const s=a("el-button");return d(),r("div",f,[t("div",m,[e[2]||(e[2]=t("h1",null,"注册页面",-1)),e[3]||(e[3]=t("p",null,"注册功能开发中...",-1)),i(s,{onClick:e[0]||(e[0]=c=>o.$router.push("/login"))},{default:l(()=>e[1]||(e[1]=[p("返回登录")])),_:1,__:[1]})])])}const k=n(_,[["render",u],["__scopeId","data-v-35ff1c7e"]]);export{k as default};
