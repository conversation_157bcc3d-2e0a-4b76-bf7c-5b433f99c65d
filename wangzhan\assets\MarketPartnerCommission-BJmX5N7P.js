import{g as q,r as _,X as J,h as K,ay as Q,y as N,Q as e,A as p,I as t,al as r,J as X,ar as G,H as W,az as Y,z as x,M as n,O as i,K as Z}from"./vendor-DmFBDimT.js";import{T as ee,L as f}from"./LbButton-BtU4V_Gr.js";import{L as te}from"./LbPage-DnbiQ0Ct.js";import{_ as oe}from"./index-C9Xz1oqp.js";import{E as y}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const ae={class:"market-partner-commission"},le={class:"content-container"},se={class:"back-button-container"},ne={class:"search-form-container"},re={class:"table-container"},ie={class:"commission-amount"},de={key:0},ce={class:"commission-amount"},ue={class:"dialog-footer"},pe={__name:"MarketPartnerCommission",setup(me){const{proxy:L}=q(),R=Q(),B=Y(),D=_(),w=_(!1),k=_(!1),z=_([]),S=_(0),v=_(!1),d=_(null),l=J({userId:"",pageNum:1,pageSize:10}),g=async()=>{try{w.value=!0;const a={...l,userId:l.userId||void 0},o=await L.$api.market.partnerCommissionList(a);o.code==="200"?(z.value=o.data.list||[],S.value=o.data.totalCount||0):y.error(o.msg||"获取佣金统计列表失败")}catch(a){console.error("获取佣金统计列表失败:",a),y.error("获取佣金统计列表失败")}finally{w.value=!1}},F=()=>{l.pageNum=1,g()},P=()=>{D.value?.resetFields(),Object.assign(l,{userId:"",pageNum:1,pageSize:10}),g()},U=a=>{d.value=a,v.value=!0},$=async()=>{try{k.value=!0,console.log("📤 开始导出合伙人佣金统计");const a={};l.userId&&(a.userId=l.userId);const h=`合伙人佣金统计_${new Date().toISOString().slice(0,19).replace(/[:-]/g,"")}.xlsx`,C="http://192.168.1.29:8889/ims",I=new URLSearchParams(a),b=localStorage.getItem("token");b&&I.append("token",b);const c=`${C}/api/admin/partner/commission/export?${I.toString()}`,u=document.createElement("a");u.href=c,u.download=h,u.style.display="none",document.body.appendChild(u),u.click(),document.body.removeChild(u),y.success("导出成功"),console.log("✅ 合伙人佣金统计导出成功")}catch(a){console.error("❌ 导出失败:",a),y.error("导出失败")}finally{k.value=!1}},E=()=>{B.push("/market/partner")},M=a=>{l.pageSize=a,l.pageNum=1,g()},T=a=>{l.pageNum=a,g()};return K(()=>{const a=R.query.userId;a&&(l.userId=a,console.log("🔗 从URL参数获取userId:",a)),g()}),(a,o)=>{const V=r("el-input"),h=r("el-form-item"),C=r("el-col"),I=r("el-row"),b=r("el-form"),c=r("el-table-column"),u=r("el-tag"),H=r("el-table"),m=r("el-descriptions-item"),O=r("el-descriptions"),A=r("el-dialog"),j=G("loading");return x(),N("div",ae,[e(ee,{title:"合伙人佣金统计"}),p("div",le,[p("div",se,[e(f,{size:"default",icon:"ArrowLeft",onClick:E},{default:t(()=>o[3]||(o[3]=[n(" 返回合伙人管理 ")])),_:1,__:[3]})]),p("div",ne,[e(b,{ref_key:"searchFormRef",ref:D,model:l,inline:!0,class:"search-form"},{default:t(()=>[e(I,{gutter:20},{default:t(()=>[e(C,{span:24},{default:t(()=>[e(h,{label:"用户ID",prop:"userId"},{default:t(()=>[e(V,{size:"default",modelValue:l.userId,"onUpdate:modelValue":o[0]||(o[0]=s=>l.userId=s),placeholder:"请输入用户ID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(f,{type:"primary",onClick:F},{default:t(()=>o[4]||(o[4]=[n(" 搜索 ")])),_:1,__:[4]}),e(f,{onClick:P},{default:t(()=>o[5]||(o[5]=[n(" 重置 ")])),_:1,__:[5]}),e(f,{type:"success",onClick:$,loading:k.value},{default:t(()=>o[6]||(o[6]=[n(" 导出 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),p("div",re,[X((x(),W(H,{data:z.value,stripe:"",border:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[e(c,{prop:"id",label:"佣金ID",width:"100",align:"center"}),e(c,{prop:"userId",label:"用户ID",width:"100",align:"center"}),e(c,{prop:"type",label:"佣金类型",width:"120",align:"center"},{default:t(s=>[e(u,{type:s.row.type===1?"primary":"success"},{default:t(()=>[n(i(s.row.type===1?"一级佣金":"二级佣金"),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"price",label:"佣金金额",width:"120",align:"center"},{default:t(s=>[p("span",ie,"¥"+i(s.row.price.toFixed(2)),1)]),_:1}),e(c,{prop:"coachId",label:"师傅ID",width:"100",align:"center"},{default:t(s=>[p("span",null,i(s.row.coachId||"-"),1)]),_:1}),e(c,{prop:"agentId",label:"代理ID",width:"100",align:"center"},{default:t(s=>[p("span",null,i(s.row.agentId||"-"),1)]),_:1}),e(c,{prop:"createTime",label:"创建时间",width:"160",align:"center","show-overflow-tooltip":""}),e(c,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(s=>[e(f,{size:"small",type:"primary",onClick:_e=>U(s.row)},{default:t(()=>o[7]||(o[7]=[n(" 查看详情 ")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[j,w.value]])]),e(te,{page:l.pageNum,"page-size":l.pageSize,total:S.value,onHandleSizeChange:M,onHandleCurrentChange:T},null,8,["page","page-size","total"])]),e(A,{title:"佣金详情",modelValue:v.value,"onUpdate:modelValue":o[2]||(o[2]=s=>v.value=s),width:"600px","close-on-click-modal":!1},{footer:t(()=>[p("span",ue,[e(f,{onClick:o[1]||(o[1]=s=>v.value=!1)},{default:t(()=>o[8]||(o[8]=[n("关闭")])),_:1,__:[8]})])]),default:t(()=>[d.value?(x(),N("div",de,[e(O,{column:2,border:""},{default:t(()=>[e(m,{label:"佣金ID"},{default:t(()=>[n(i(d.value.id),1)]),_:1}),e(m,{label:"用户ID"},{default:t(()=>[n(i(d.value.userId),1)]),_:1}),e(m,{label:"佣金类型"},{default:t(()=>[e(u,{type:d.value.type===1?"primary":"success"},{default:t(()=>[n(i(d.value.type===1?"一级佣金":"二级佣金"),1)]),_:1},8,["type"])]),_:1}),e(m,{label:"佣金金额"},{default:t(()=>[p("span",ce,"¥"+i(d.value.price.toFixed(2)),1)]),_:1}),e(m,{label:"师傅ID"},{default:t(()=>[n(i(d.value.coachId||"-"),1)]),_:1}),e(m,{label:"代理ID"},{default:t(()=>[n(i(d.value.agentId||"-"),1)]),_:1}),e(m,{label:"创建时间",span:2},{default:t(()=>[n(i(d.value.createTime),1)]),_:1})]),_:1})])):Z("",!0)]),_:1},8,["modelValue"])])}}},ye=oe(pe,[["__scopeId","data-v-0abda43f"]]);export{ye as default};
