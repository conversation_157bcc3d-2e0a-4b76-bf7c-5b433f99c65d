<!--
  菜单管理页面
 /src/view/account/acountMenu/index.vue重构
-->

<template>
  <div class="lb-account-menu">
    <TopNav />
    <div class="page-main">
      <!-- 操作按钮 -->
      <el-row class="page-header">
        <LbButton type="success" @click="handleAdd">新增菜单</LbButton>
      </el-row>
      
      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never">
        <el-table 
          v-loading="loading" 
          :data="tableData" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          border
        >
          <el-table-column prop="menu_name" label="菜单名称" width="200" />
          <el-table-column prop="icon" label="图标" width="100">
            <template #default="scope">
              <i :class="scope.row.icon" v-if="scope.row.icon"></i>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="route" label="菜单路由" width="200" />
          <el-table-column prop="sort" label="排序" width="80" />
          <el-table-column prop="is_show" label="是否显示" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_show ? 'success' : 'danger'" size="small">
                {{ scope.row.is_show ? '显示' : '隐藏' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_menu" label="菜单类型" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_menu ? 'primary' : 'info'" size="small">
                {{ scope.row.is_menu ? '菜单' : '按钮' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="170">
            <template #default="scope">
              <div>{{ formatDate(scope.row.create_time, 1) }}</div>
              <div>{{ formatDate(scope.row.create_time, 2) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="success"
                  @click="handleNew(scope.row)"
                >
                  新增子菜单
                </LbButton>
                <LbButton
                  size="mini"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="getDialogTitle()" 
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="菜单名称" prop="menu_name">
          <el-input v-model="form.menu_name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="上级菜单" prop="pid" v-if="form.pid > 0">
          <el-input v-model="parentMenuName" disabled />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标类名">
            <template #prepend>
              <i :class="form.icon" v-if="form.icon"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="路由" prop="route">
          <el-input v-model="form.route" placeholder="请输入路由地址" />
        </el-form-item>
        <el-form-item label="排序值" prop="sort">
          <el-input-number v-model="form.sort" :min="0" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="是否显示" prop="is_show">
          <el-radio-group v-model="form.is_show">
            <el-radio :value="1">显示</el-radio>
            <el-radio :value="0">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单类型" prop="is_menu">
          <el-radio-group v-model="form.is_menu">
            <el-radio :value="1">菜单</el-radio>
            <el-radio :value="0">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="form.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="handleClose">取消</LbButton>
        <LbButton type="primary" @click="handleConfirm" :loading="submitLoading">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const type = ref('add')
const formRef = ref()
const submitLoading = ref(false)
const parentMenuName = ref('')

// 表单数据
const form = reactive({
  id: '',
  menu_name: '',
  pid: 0,
  icon: '',
  route: '',
  sort: 0,
  is_show: 1,
  is_menu: 1,
  remark: ''
})

// 表单验证规则
const formRules = {
  menu_name: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' }
  ],
  route: [
    { required: true, message: '请输入路由地址', trigger: 'blur' }
  ]
}

// 方法
const getTableDataList = async () => {
  loading.value = true
  
  try {
    const response = await fetch('/api/account/menu/tree')
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data || []
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const getDialogTitle = () => {
  if (type.value === 'add') {
    return form.pid > 0 ? '新增子菜单' : '新增菜单'
  } else {
    return '编辑菜单'
  }
}

const handleAdd = () => {
  type.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleNew = (row) => {
  type.value = 'add'
  resetForm()
  form.pid = row.id
  form.is_menu = 0
  parentMenuName.value = row.menu_name
  dialogVisible.value = true
}

const handleEdit = (row) => {
  type.value = 'edit'
  Object.assign(form, {
    id: row.id,
    menu_name: row.menu_name,
    pid: row.pid || 0,
    icon: row.icon || '',
    route: row.route || '',
    sort: row.sort || 0,
    is_show: row.is_show,
    is_menu: row.is_menu,
    remark: row.remark || ''
  })
  
  // 如果有父菜单，查找父菜单名称
  if (form.pid > 0) {
    const findParentMenu = (menus, pid) => {
      for (const menu of menus) {
        if (menu.id === pid) {
          return menu.menu_name
        }
        if (menu.children && menu.children.length > 0) {
          const found = findParentMenu(menu.children, pid)
          if (found) return found
        }
      }
      return ''
    }
    parentMenuName.value = findParentMenu(tableData.value, form.pid)
  }
  
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 "${row.menu_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/account/menu/delete/${row.id}`, {
      method: 'DELETE'
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const url = type.value === 'add' 
      ? '/api/account/menu/add' 
      : `/api/account/menu/update/${form.id}`
    
    const method = type.value === 'add' ? 'POST' : 'PUT'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        menu_name: form.menu_name,
        pid: form.pid,
        icon: form.icon,
        route: form.route,
        sort: form.sort,
        is_show: form.is_show,
        is_menu: form.is_menu,
        remark: form.remark
      })
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success(type.value === 'add' ? '新增成功' : '编辑成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    menu_name: '',
    pid: 0,
    icon: '',
    route: '',
    sort: 0,
    is_show: 1,
    is_menu: 1,
    remark: ''
  })
  parentMenuName.value = ''
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.lb-account-menu {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

@media (max-width: 768px) {
  .lb-account-menu {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
  }
}
</style>
