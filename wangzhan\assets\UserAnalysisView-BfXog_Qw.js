import{n as w,o as C,R as $,E as U}from"./element-fdzwdDuf.js";import{_ as I}from"./index-C9Xz1oqp.js";import{r as c,h as G,al as v,ar as H,y as i,z as n,A as e,P as m,a6 as f,C as V,Q as a,I as o,H as p,L as J,O as r,D as z,M as d,u as h,J as O}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Q={class:"user-analysis"},R={class:"metrics-grid"},j={class:"metric-content"},q={class:"metric-value"},K={class:"metric-label"},W={class:"chart-section"},X={class:"chart-card"},Y={class:"chart-header"},Z={class:"chart-content"},ee={class:"mock-chart"},te={class:"chart-placeholder"},se={class:"trend-data"},ae={class:"trend-date"},ne={class:"trend-value"},oe={class:"activity-section"},le={class:"chart-row"},re={class:"chart-card"},ie={class:"chart-content"},ce={class:"activity-stats"},de={class:"activity-label"},ue={class:"activity-value"},_e={class:"activity-bar"},ve={class:"chart-card"},pe={class:"chart-content"},he={class:"region-stats"},ge={class:"region-info"},ye={class:"region-name"},me={class:"region-count"},fe={class:"region-percentage"},be={class:"behavior-section"},we={class:"table-card"},ke={class:"table-header"},De={class:"table-content"},Ce={__name:"UserAnalysisView",setup(Ue){const b=c(!1),u=c("month"),x=c([{key:"totalUsers",label:"总用户数",value:15678,change:"+12.5%",trend:"up",color:"#409eff",icon:"User"},{key:"activeUsers",label:"活跃用户",value:8934,change:"+8.2%",trend:"up",color:"#67c23a",icon:"UserFilled"},{key:"newUsers",label:"新增用户",value:1234,change:"+15.3%",trend:"up",color:"#e6a23c",icon:"TrendCharts"},{key:"avgSessionTime",label:"平均会话时长",value:245,change:"-2.1%",trend:"down",color:"#f56c6c",icon:"Clock"}]),M=c([{date:"01-07",newUsers:123},{date:"01-08",newUsers:156},{date:"01-09",newUsers:134},{date:"01-10",newUsers:178},{date:"01-11",newUsers:198}]),B=c([{type:"daily",label:"日活跃用户",value:75,color:"#409eff"},{type:"weekly",label:"周活跃用户",value:60,color:"#67c23a"},{type:"monthly",label:"月活跃用户",value:45,color:"#e6a23c"},{type:"retention",label:"用户留存率",value:35,color:"#f56c6c"}]),T=c([{name:"北京",count:3456,percentage:22},{name:"上海",count:2890,percentage:18},{name:"广州",count:2234,percentage:14},{name:"深圳",count:1890,percentage:12},{name:"其他",count:5208,percentage:34}]),k=c([{action:"页面浏览",count:45678,percentage:35,avgDuration:"2:34",trend:"up",trendValue:"+5.2%",description:"用户浏览页面的总次数"},{action:"搜索查询",count:12345,percentage:25,avgDuration:"1:45",trend:"up",trendValue:"+8.1%",description:"用户执行搜索操作的次数"},{action:"内容互动",count:8901,percentage:20,avgDuration:"3:12",trend:"down",trendValue:"-2.3%",description:"用户与内容的互动次数"},{action:"功能使用",count:6789,percentage:15,avgDuration:"4:56",trend:"up",trendValue:"+12.7%",description:"用户使用各种功能的次数"},{action:"其他操作",count:2345,percentage:5,avgDuration:"1:23",trend:"down",trendValue:"-1.5%",description:"其他用户操作行为"}]),D=l=>l>=1e4?(l/1e4).toFixed(1)+"w":l>=1e3?(l/1e3).toFixed(1)+"k":l.toString(),E=l=>l>=30?"success":l>=20?"warning":"info",F=async()=>{b.value=!0;try{await new Promise(l=>setTimeout(l,1e3)),k.value.forEach(l=>{l.count+=Math.floor(Math.random()*100)}),U.success("数据已刷新")}catch{U.error("刷新数据失败")}finally{b.value=!1}};return G(()=>{console.log("用户分析页面已加载")}),(l,s)=>{const g=v("el-icon"),y=v("el-button"),N=v("el-button-group"),_=v("el-table-column"),P=v("el-tag"),S=v("el-table"),A=H("loading");return n(),i("div",Q,[s[12]||(s[12]=e("div",{class:"page-title"},[e("h1",null,"用户分析"),e("p",{class:"page-subtitle"},"深入分析用户行为和数据趋势")],-1)),e("div",R,[(n(!0),i(m,null,f(x.value,t=>(n(),i("div",{class:"metric-card",key:t.key},[e("div",{class:"metric-icon",style:V({backgroundColor:t.color})},[a(g,{size:28},{default:o(()=>[(n(),p(J(t.icon)))]),_:2},1024)],4),e("div",j,[e("div",q,r(D(t.value)),1),e("div",K,r(t.label),1),e("div",{class:z(["metric-change",t.trend])},[a(g,null,{default:o(()=>[t.trend==="up"?(n(),p(h(w),{key:0})):(n(),p(h(C),{key:1}))]),_:2},1024),d(" "+r(t.change),1)],2)])]))),128))]),e("div",W,[e("div",X,[e("div",Y,[s[6]||(s[6]=e("h3",null,"用户增长趋势",-1)),a(N,{size:"small"},{default:o(()=>[a(y,{type:u.value==="week"?"primary":"",onClick:s[0]||(s[0]=t=>u.value="week")},{default:o(()=>s[3]||(s[3]=[d(" 最近一周 ")])),_:1,__:[3]},8,["type"]),a(y,{type:u.value==="month"?"primary":"",onClick:s[1]||(s[1]=t=>u.value="month")},{default:o(()=>s[4]||(s[4]=[d(" 最近一月 ")])),_:1,__:[4]},8,["type"]),a(y,{type:u.value==="year"?"primary":"",onClick:s[2]||(s[2]=t=>u.value="year")},{default:o(()=>s[5]||(s[5]=[d(" 最近一年 ")])),_:1,__:[5]},8,["type"])]),_:1})]),e("div",Z,[e("div",ee,[e("div",te,[a(g,{size:64},{default:o(()=>[a(h(w))]),_:1}),s[7]||(s[7]=e("p",null,"用户增长趋势图",-1)),e("div",se,[(n(!0),i(m,null,f(M.value,(t,L)=>(n(),i("div",{class:"trend-item",key:L},[e("span",ae,r(t.date),1),e("span",ne,"+"+r(t.newUsers),1)]))),128))])])])])])]),e("div",oe,[e("div",le,[e("div",re,[s[8]||(s[8]=e("div",{class:"chart-header"},[e("h3",null,"用户活跃度")],-1)),e("div",ie,[e("div",ce,[(n(!0),i(m,null,f(B.value,t=>(n(),i("div",{class:"activity-item",key:t.type},[e("div",de,r(t.label),1),e("div",ue,r(t.value)+"%",1),e("div",_e,[e("div",{class:"activity-progress",style:V({width:t.value+"%",backgroundColor:t.color})},null,4)])]))),128))])])]),e("div",ve,[s[9]||(s[9]=e("div",{class:"chart-header"},[e("h3",null,"用户地域分布")],-1)),e("div",pe,[e("div",he,[(n(!0),i(m,null,f(T.value,t=>(n(),i("div",{class:"region-item",key:t.name},[e("div",ge,[e("span",ye,r(t.name),1),e("span",me,r(t.count)+"人",1)]),e("div",fe,r(t.percentage)+"%",1)]))),128))])])])])]),e("div",be,[e("div",we,[e("div",ke,[s[11]||(s[11]=e("h3",null,"用户行为分析",-1)),a(y,{size:"small",onClick:F},{default:o(()=>[a(g,null,{default:o(()=>[a(h($))]),_:1}),s[10]||(s[10]=d(" 刷新数据 "))]),_:1,__:[10]})]),e("div",De,[O((n(),p(S,{data:k.value,stripe:"",style:{width:"100%"}},{default:o(()=>[a(_,{prop:"action",label:"用户行为",width:"150"}),a(_,{prop:"count",label:"次数",width:"100",align:"center"},{default:o(({row:t})=>[e("strong",null,r(D(t.count)),1)]),_:1}),a(_,{prop:"percentage",label:"占比",width:"100",align:"center"},{default:o(({row:t})=>[a(P,{type:E(t.percentage),size:"small"},{default:o(()=>[d(r(t.percentage)+"% ",1)]),_:2},1032,["type"])]),_:1}),a(_,{prop:"avgDuration",label:"平均时长",width:"120",align:"center"}),a(_,{prop:"trend",label:"趋势",width:"100",align:"center"},{default:o(({row:t})=>[e("span",{class:z("trend-"+t.trend)},[a(g,null,{default:o(()=>[t.trend==="up"?(n(),p(h(w),{key:0})):(n(),p(h(C),{key:1}))]),_:2},1024),d(" "+r(t.trendValue),1)],2)]),_:1}),a(_,{prop:"description",label:"说明","min-width":"200","show-overflow-tooltip":""})]),_:1},8,["data"])),[[A,b.value]])])])])])}}},Be=I(Ce,[["__scopeId","data-v-7f0d93b2"]]);export{Be as default};
