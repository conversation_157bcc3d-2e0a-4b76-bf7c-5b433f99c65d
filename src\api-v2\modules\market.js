/**
 * 营销管理模块 - V2版本
 * 按照API封装规范文档实现营销管理相关接口
 */

import { get, post } from '../index'

export default {
  // ==================== 优惠券管理相关接口 ====================

  /**
   * 获取优惠券列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，非必填，1可用，-1不可用
   * @param {number} querys.type 类型，非必填，0满减，1无门槛
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回优惠券列表数据
   */
  couponList(querys) {
    console.log('🎫 优惠券列表API-V2请求参数:', querys)
    return get('/api/admin/coupon/list', querys)
  },

  /**
   * 获取优惠券详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 优惠券ID
   * @returns {Promise} 返回优惠券详情数据
   */
  couponInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('优惠券ID不能为空'))
    }
    console.log('🔍 获取优惠券详情API-V2请求:', querys)
    return get(`/api/admin/coupon/detail/${querys.id}`)
  },

  /**
   * 新增优惠券
   * @param {Object} querys 优惠券数据
   * @param {string} querys.title 优惠券标题
   * @param {number} querys.type 优惠券类型，0满减，1无门槛
   * @param {number} querys.full 满减条件金额（type=0时必需）
   * @param {number} querys.discount 优惠金额
   * @param {number} querys.sendType 派发方式
   * @param {number} querys.timeLimit 时间限制类型
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.day 有效天数
   * @param {number} querys.stock 库存数量
   * @param {number} querys.userLimit 用户限制
   * @param {string} querys.rule 使用规则
   * @param {string} querys.text 优惠券描述
   * @param {number} querys.top 排序值
   * @param {number} querys.status 状态，1可用，-1不可用
   * @param {Array} querys.goodsIds 限用项目ID数组
   * @returns {Promise} 返回新增结果
   */
  couponAdd(querys) {
    if (!querys || !querys.title) {
      return Promise.reject(new Error('优惠券标题不能为空'))
    }

    // 构建优惠券数据对象
    const couponData = {
      title: querys.title,
      type: querys.type || 0,
      full: querys.full || 0,
      discount: querys.discount || 0,
      sendType: querys.sendType || 0,
      timeLimit: querys.timeLimit || 0,
      startTime: querys.startTime || '',
      endTime: querys.endTime || '',
      day: querys.day || 0,
      stock: querys.stock || 100,
      userLimit: querys.userLimit || 1,
      rule: querys.rule || '',
      text: querys.text || '',
      top: querys.top || 0,
      status: querys.status || 1
    }

    // 构建API请求数据，按照用户要求的格式
    const apiData = {
      coupon: couponData,
      goodsIds: querys.goodsIds || []
    }

    console.log('➕ 新增优惠券API-V2请求数据:', apiData)
    return post('/api/admin/coupon/add', apiData)
  },

  /**
   * 编辑优惠券
   * @param {Object} querys 编辑数据（包含id）
   * @param {Array} querys.goodsIds 限用项目ID数组
   * @returns {Promise} 返回编辑结果
   */
  couponUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('优惠券ID不能为空'))
    }

    // 构建优惠券数据对象（排除goodsIds）
    const { goodsIds, ...couponData } = querys

    // 构建API请求数据，按照用户要求的格式
    const apiData = {
      coupon: couponData,
      goodsIds: goodsIds || []
    }

    console.log('✏️ 编辑优惠券API-V2请求数据:', apiData)
    return post('/api/admin/coupon/edit', apiData)
  },

  /**
   * 删除优惠券
   * @param {Object} querys 删除参数
   * @param {number} querys.id 优惠券ID
   * @returns {Promise} 返回删除结果
   */
  couponDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('优惠券ID不能为空'))
    }

    console.log('🗑️ 删除优惠券API-V2请求:', querys)
    return post(`/api/admin/coupon/delete/${querys.id}`)
  },

  /**
   * 修改优惠券状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 优惠券ID
   * @returns {Promise} 返回状态修改结果
   */
  couponStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('优惠券ID不能为空'))
    }

    console.log('🔄 修改优惠券状态API-V2请求:', querys)
    return post(`/api/admin/coupon/status/${querys.id}`)
  },

  // ==================== 公告管理相关接口 ====================

  /**
   * 获取公告列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，非必填，1启用，0禁用
   * @param {number} querys.type 类型，非必填，1用户端，2师傅端
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回公告列表数据
   */
  noticeList(querys) {
    console.log('📢 公告列表API-V2请求参数:', querys)
    return get('/api/admin/notice/list', querys)
  },

  /**
   * 获取公告详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 公告ID
   * @returns {Promise} 返回公告详情数据
   */
  noticeInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('公告ID不能为空'))
    }
    console.log('🔍 获取公告详情API-V2请求:', querys)
    return get(`/api/admin/notice/detail/${querys.id}`)
  },

  /**
   * 新增公告
   * @param {Object} querys 公告数据
   * @param {string} querys.content 公告内容
   * @param {number} querys.type 公告类型，1用户端，2师傅端
   * @param {number} querys.status 状态，1启用，0禁用
   * @returns {Promise} 返回新增结果
   */
  noticeAdd(querys) {
    if (!querys || !querys.content) {
      return Promise.reject(new Error('公告内容不能为空'))
    }

    const apiData = {
      content: querys.content,
      type: querys.type || 1,
      status: querys.status || 1
    }

    console.log('➕ 新增公告API-V2请求数据:', apiData)
    return post('/api/admin/notice/add', apiData)
  },

  /**
   * 编辑公告
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  noticeUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('公告ID不能为空'))
    }

    console.log('✏️ 编辑公告API-V2请求:', querys)
    return post('/api/admin/notice/edit', querys)
  },

  /**
   * 删除公告
   * @param {Object} querys 删除参数
   * @param {number} querys.id 公告ID
   * @returns {Promise} 返回删除结果
   */
  noticeDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('公告ID不能为空'))
    }

    console.log('🗑️ 删除公告API-V2请求:', querys)
    return post(`/api/admin/notice/delete/${querys.id}`)
  },

  /**
   * 修改公告状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 公告ID
   * @returns {Promise} 返回状态修改结果
   */
  noticeStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('公告ID不能为空'))
    }

    console.log('🔄 修改公告状态API-V2请求:', querys)
    return post(`/api/admin/notice/status/${querys.id}`)
  },

  // ==================== 合伙人管理相关接口 ====================

  /**
   * 获取合伙人ID集合
   * @returns {Promise} 返回合伙人ID集合数据
   */
  partnerIds() {
    console.log('🆔 获取合伙人ID集合API-V2请求')
    return get('/api/admin/partner/ids')
  },

  /**
   * 获取合伙人列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 合伙人对应用户id，非必填
   * @param {number} querys.level 合伙人等级，非必填
   * @param {number} querys.status 合伙人状态，非必填
   * @param {number} querys.pageNum 当前页，非必填
   * @param {number} querys.pageSize 每页数量，非必填
   * @returns {Promise} 返回合伙人列表数据
   */
  partnerList(querys) {
    console.log('🤝 合伙人列表API-V2请求参数:', querys)
    return get('/api/admin/partner/list', querys)
  },

  /**
   * 新增合伙人
   * @param {Object} data 合伙人数据
   * @param {number} data.userId 用户id
   * @param {number} data.level 等级，暂无用(默认传1即可)
   * @param {number} data.commissionRate1 一级分佣比例 1代表1%
   * @param {number} data.commissionRate2 二级分佣比例
   * @param {number} data.status 状态，1可用 0禁用
   * @returns {Promise} 返回新增结果
   */
  partnerAdd(data) {
    if (!data || !data.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    const apiData = {
      userId: data.userId,
      level: data.level || 1,
      commissionRate1: data.commissionRate1 || 0,
      commissionRate2: data.commissionRate2 || 0,
      status: data.status || 1
    }

    console.log('➕ 新增合伙人API-V2请求数据:', apiData)
    return post('/api/admin/partner/add', apiData)
  },

  /**
   * 修改合伙人状态
   * @param {Object} data 状态修改数据
   * @param {number} data.id 合伙人id
   * @returns {Promise} 返回修改结果
   */
  partnerStatus(data) {
    if (!data || !data.id) {
      return Promise.reject(new Error('合伙人ID不能为空'))
    }

    console.log('🔄 修改合伙人状态API-V2请求:', data)
    return post(`/api/admin/partner/status/${data.id}`)
  },

  /**
   * 等级/分佣比例调整
   * @param {Object} data 调整数据
   * @param {number} data.id 合伙人id
   * @param {number} data.level 等级，非必填
   * @param {number} data.commissionRate1 一级分销比例，非必填
   * @param {number} data.commissionRate2 二级分销比例，非必填
   * @returns {Promise} 返回调整结果
   */
  partnerUpdateLevelAndCommission(data) {
    if (!data || !data.id) {
      return Promise.reject(new Error('合伙人ID不能为空'))
    }

    const formData = new FormData()
    if (data.level !== undefined) formData.append('level', data.level)
    if (data.commissionRate1 !== undefined) formData.append('commissionRate1', data.commissionRate1)
    if (data.commissionRate2 !== undefined) formData.append('commissionRate2', data.commissionRate2)

    console.log('📊 等级/分佣比例调整API-V2请求:', data)
    return post(`/api/admin/partner/updateLevelAndCommission/${data.id}`, formData, 'multipart/form-data')
  },

  /**
   * 获取合伙人邀请列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.type 1用户 2师傅 其余为全部，建议做成下拉框，非必填
   * @param {number} querys.pageNum 当前页，非必填
   * @param {number} querys.pageSize 每页数量，非必填
   * @returns {Promise} 返回合伙人邀请列表数据
   */
  partnerInviteList(querys) {
    console.log('📋 合伙人邀请列表API-V2请求参数:', querys)
    return get('/api/admin/partner/inviteList', querys)
  },

  /**
   * 获取合伙人佣金统计列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.pageNum 当前页，非必填
   * @param {number} querys.pageSize 每页数量，非必填
   * @returns {Promise} 返回合伙人佣金统计列表数据
   */
  partnerCommissionList(querys) {
    console.log('💰 合伙人佣金统计列表API-V2请求参数:', querys)
    return get('/api/admin/partner/commission', querys)
  },

  /**
   * 获取合伙人推广订单列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.type 1用户，2师傅，其余全部，下拉框实现
   * @param {number} querys.pageNum 当前页，非必填
   * @param {number} querys.pageSize 每页数量，非必填
   * @returns {Promise} 返回合伙人推广订单列表数据
   */
  partnerOrdersList(querys) {
    console.log('📦 合伙人推广订单列表API-V2请求参数:', querys)
    return get('/api/admin/partner/orders', querys)
  },

  // ==================== 合伙人导出相关接口 ====================

  /**
   * 合伙人邀请列表导出
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.type 1用户 2师傅 其余为全部，非必填
   * @returns {Promise} 返回导出结果
   */
  partnerInviteListExport(querys) {
    console.log('📤 合伙人邀请列表导出API-V2请求参数:', querys)
    return get('/api/admin/partner/inviteList/export', querys)
  },

  /**
   * 合伙人佣金统计导出
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @returns {Promise} 返回导出结果
   */
  partnerCommissionExport(querys) {
    console.log('📤 合伙人佣金统计导出API-V2请求参数:', querys)
    return get('/api/admin/partner/commission/export', querys)
  },

  /**
   * 合伙人推广订单导出
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.type 1用户，2师傅，其余全部，非必填
   * @returns {Promise} 返回导出结果
   */
  partnerOrdersExport(querys) {
    console.log('📤 合伙人推广订单导出API-V2请求参数:', querys)
    return get('/api/admin/partner/orders/export', querys)
  }

}
