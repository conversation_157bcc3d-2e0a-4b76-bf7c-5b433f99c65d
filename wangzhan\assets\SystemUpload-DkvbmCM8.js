import{T as w,L as j}from"./LbButton-BtU4V_Gr.js";import{_ as C}from"./index-C9Xz1oqp.js";import{E as u}from"./element-fdzwdDuf.js";import{r as _,X as U,h as B,y as N,Q as o,A as d,I as a,al as t,z as S,M as p}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const T={class:"lb-system-upload"},k={class:"page-main"},z={__name:"SystemUpload",setup(M){const i=_(!1),f=_(),l=U({upload_type:"local",max_size:10,allowed_types:"jpg,png,gif,pdf"}),c=async()=>{try{const e=await(await fetch("/api/system/upload/config")).json();e.code===200&&Object.assign(l,e.data||{})}catch(s){console.error("获取配置失败:",s)}},g=async()=>{try{i.value=!0;const e=await(await fetch("/api/system/upload/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)})).json();e.code===200?u.success("配置保存成功"):u.error(e.message||"保存失败")}catch{u.error("保存失败")}finally{i.value=!1}};return B(()=>{c()}),(s,e)=>{const m=t("el-radio"),y=t("el-radio-group"),r=t("el-form-item"),v=t("el-input-number"),V=t("el-input"),x=t("el-form"),b=t("el-card");return S(),N("div",T,[o(w),d("div",k,[o(b,{class:"config-card",shadow:"never"},{header:a(()=>e[3]||(e[3]=[d("div",{class:"card-header"},[d("span",null,"上传配置")],-1)])),default:a(()=>[o(x,{model:l,ref_key:"configFormRef",ref:f,"label-width":"140px",class:"config-form"},{default:a(()=>[o(r,{label:"上传方式"},{default:a(()=>[o(y,{modelValue:l.upload_type,"onUpdate:modelValue":e[0]||(e[0]=n=>l.upload_type=n)},{default:a(()=>[o(m,{value:"local"},{default:a(()=>e[4]||(e[4]=[p("本地存储")])),_:1,__:[4]}),o(m,{value:"oss"},{default:a(()=>e[5]||(e[5]=[p("阿里云OSS")])),_:1,__:[5]}),o(m,{value:"qiniu"},{default:a(()=>e[6]||(e[6]=[p("七牛云")])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"最大文件大小"},{default:a(()=>[o(v,{modelValue:l.max_size,"onUpdate:modelValue":e[1]||(e[1]=n=>l.max_size=n),min:1,max:100},null,8,["modelValue"]),e[7]||(e[7]=d("span",{style:{"margin-left":"10px"}},"MB",-1))]),_:1,__:[7]}),o(r,{label:"允许的文件类型"},{default:a(()=>[o(V,{modelValue:l.allowed_types,"onUpdate:modelValue":e[2]||(e[2]=n=>l.allowed_types=n),placeholder:"如：jpg,png,gif,pdf"},null,8,["modelValue"])]),_:1}),o(r,null,{default:a(()=>[o(j,{type:"primary",onClick:g,loading:i.value},{default:a(()=>e[8]||(e[8]=[p("保存配置")])),_:1,__:[8]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},R=C(z,[["__scopeId","data-v-73a8a423"]]);export{R as default};
