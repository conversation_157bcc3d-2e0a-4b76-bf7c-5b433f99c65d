import{r as z,X as S,h as E,ay as I,y as p,Q as a,A as o,J as O,I as n,al as u,ar as P,H as R,z as d,M as k,O as r}from"./vendor-DmFBDimT.js";import{T as V,L as C}from"./LbButton-BtU4V_Gr.js";import{_ as q}from"./index-C9Xz1oqp.js";import{E as g}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const A={class:"lb-finance-detail"},H={class:"page-main"},J={class:"flex-wrap mb-lg"},Q={class:"flex-1 mr-lg pr-lg"},X={class:"stat-content"},G={class:"stat-value"},K={class:"stat-content"},W={class:"stat-value"},Y={class:"stat-content"},Z={class:"stat-value"},tt={class:"stat-content"},et={class:"stat-value"},at={key:0,style:{color:"#67c23a","font-weight":"600"}},st={key:1},ot={key:0,style:{color:"#f56c6c","font-weight":"600"}},nt={key:1},lt={key:0,style:{color:"#e6a23c","font-weight":"600"}},rt={key:1},it={style:{color:"#409eff","font-weight":"600"}},ct={class:"pagination-section"},dt={__name:"FinanceDetail",setup(_t){const D=I(),h=z(!1),y=z(!1),x=z([]),i=S({coach_id:0,start_time:"",end_time:""}),c=S({page:1,pageSize:10,total:0}),f=S({coach_name:"",total_income:0,total_refund:0,total_withdraw:0,current_balance:0}),b=async()=>{h.value=!0;try{const e=new URLSearchParams({coach_id:i.coach_id,start_time:i.start_time,end_time:i.end_time,page:c.page,pageSize:c.pageSize}),l=await(await fetch(`/api/finance/detail?${e}`)).json();l.code===200?(x.value=l.data.list||[],c.total=l.data.total||0,Object.assign(f,l.data.info||{})):g.error(l.message||"获取数据失败")}catch(e){console.error("获取财务详情失败:",e),g.error("获取数据失败")}finally{h.value=!1}},L=async()=>{y.value=!0;try{const e=new URLSearchParams({coach_id:i.coach_id,start_time:i.start_time,end_time:i.end_time}),l=await(await fetch(`/api/finance/export-detail?${e}`)).json();l.code===200?g.success("导出成功"):g.error(l.message||"导出失败")}catch(e){console.error("导出失败:",e),g.error("导出失败")}finally{y.value=!1}},M=e=>({1:"success",2:"danger",3:"warning"})[e]||"info",T=e=>({1:"订单收入",2:"订单退款",3:"提现"})[e]||"其他",m=(e,t)=>{if(!e)return"";const l=new Date(e);return t===1?l.toLocaleDateString():t===2?l.toLocaleTimeString():l.toLocaleDateString()},B=e=>{c.pageSize=e,b()},j=e=>{c.page=e,b()};return E(()=>{const{coach_id:e,start_time:t,end_time:l}=D.query;e&&(i.coach_id=e,i.start_time=t||"",i.end_time=l||"",b())}),(e,t)=>{const l=u("el-row"),w=u("el-card"),v=u("el-col"),_=u("el-table-column"),F=u("el-tag"),N=u("el-table"),U=u("el-pagination"),$=P("loading");return d(),p("div",A,[a(V,{title:"财务详情",isBack:!0}),o("div",H,[a(l,{class:"mb-lg"},{default:n(()=>[a(C,{size:"default",type:"primary",loading:y.value,onClick:L},{default:n(()=>t[3]||(t[3]=[k(" 导出 ")])),_:1,__:[3]},8,["loading"])]),_:1}),a(l,null,{default:n(()=>[o("div",J,[o("div",Q,"师傅名称："+r(f.coach_name),1),o("div",null,"账单周期："+r(m(i.start_time))+" 至 "+r(m(i.end_time)),1)])]),_:1}),a(l,{gutter:20,class:"stats-cards"},{default:n(()=>[a(v,{span:6},{default:n(()=>[a(w,{class:"stat-card"},{default:n(()=>[o("div",X,[o("div",G,"¥"+r(f.total_income||0),1),t[4]||(t[4]=o("div",{class:"stat-label"},"总收入",-1))])]),_:1})]),_:1}),a(v,{span:6},{default:n(()=>[a(w,{class:"stat-card"},{default:n(()=>[o("div",K,[o("div",W,"¥"+r(f.total_refund||0),1),t[5]||(t[5]=o("div",{class:"stat-label"},"总退款",-1))])]),_:1})]),_:1}),a(v,{span:6},{default:n(()=>[a(w,{class:"stat-card"},{default:n(()=>[o("div",Y,[o("div",Z,"¥"+r(f.total_withdraw||0),1),t[6]||(t[6]=o("div",{class:"stat-label"},"总提现",-1))])]),_:1})]),_:1}),a(v,{span:6},{default:n(()=>[a(w,{class:"stat-card"},{default:n(()=>[o("div",tt,[o("div",et,"¥"+r(f.current_balance||0),1),t[7]||(t[7]=o("div",{class:"stat-label"},"当前余额",-1))])]),_:1})]),_:1})]),_:1}),O((d(),R(N,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%","margin-top":"20px"}},{default:n(()=>[a(_,{prop:"date",label:"收支时间",width:"170"},{default:n(s=>[o("div",null,r(m(s.row.date,1)),1),o("div",null,r(m(s.row.date,2)),1)]),_:1}),a(_,{prop:"type",label:"类型",width:"120"},{default:n(s=>[a(F,{type:M(s.row.type),size:"default"},{default:n(()=>[k(r(T(s.row.type)),1)]),_:2},1032,["type"])]),_:1}),a(_,{prop:"order_price",label:"订单收入（元）",width:"150"},{default:n(s=>[s.row.order_price>0?(d(),p("span",at," +¥"+r(s.row.order_price),1)):(d(),p("span",st,"-"))]),_:1}),a(_,{prop:"refund_price",label:"订单退款（元）",width:"150"},{default:n(s=>[s.row.refund_price>0?(d(),p("span",ot," -¥"+r(s.row.refund_price),1)):(d(),p("span",nt,"-"))]),_:1}),a(_,{prop:"wallet_price",label:"提现（元）",width:"150"},{default:n(s=>[s.row.wallet_price>0?(d(),p("span",lt," -¥"+r(s.row.wallet_price),1)):(d(),p("span",rt,"-"))]),_:1}),a(_,{prop:"balance",label:"余额（元）",width:"150"},{default:n(s=>[o("span",it,"¥"+r(s.row.balance),1)]),_:1}),a(_,{prop:"remark",label:"备注","min-width":"200"})]),_:1},8,["data"])),[[$,h.value]]),o("div",ct,[a(U,{"current-page":c.page,"onUpdate:currentPage":t[0]||(t[0]=s=>c.page=s),"page-size":c.pageSize,"onUpdate:pageSize":t[1]||(t[1]=s=>c.pageSize=s),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:B,onCurrentChange:j},null,8,["current-page","page-size","total"])]),t[9]||(t[9]=o("div",{class:"space-lg mt-lg mb-lg"},null,-1)),a(C,{type:"primary",onClick:t[2]||(t[2]=s=>e.$router.go(-1))},{default:n(()=>t[8]||(t[8]=[k(" 返回 ")])),_:1,__:[8]})])])}}},wt=q(dt,[["__scopeId","data-v-d41ff600"]]);export{wt as default};
