import{T as ke,L as V}from"./LbButton-BtU4V_Gr.js";import{L as Ne}from"./LbPage-DnbiQ0Ct.js";import{_ as xe}from"./index-C9Xz1oqp.js";import{E as w}from"./element-fdzwdDuf.js";import{g as Ve,r as f,X as se,h as Se,y as N,Q as l,A as t,I as s,al as v,J as U,ar as Ie,H as E,z as b,O as d,M as c,K as O,P as ne,a6 as de}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const De={class:"page-container"},Re={class:"content-container"},ze={class:"stats-content"},Me={class:"stats-value"},$e={class:"stats-content"},Pe={class:"stats-value"},Ye={class:"stats-content"},He={class:"stats-value"},Ue={class:"stats-content"},Ee={class:"stats-value"},Oe={class:"search-form-container"},Le={class:"table-container"},Fe={class:"price-text"},je={class:"table-operate"},Be={key:0,class:"detail-content"},Ge={class:"detail-section"},Ae={class:"detail-item"},Je={class:"detail-item"},qe={class:"detail-item"},Qe={class:"detail-item"},We={key:0,class:"detail-section"},Ke={class:"detail-item"},Xe={class:"detail-item"},Ze={class:"detail-item"},el={class:"detail-item"},ll={class:"detail-item"},tl={class:"detail-section"},al={class:"detail-item"},ol={class:"detail-item"},sl={class:"detail-item"},nl={class:"detail-item"},dl={class:"price-text"},il={class:"detail-item"},rl={class:"price-text"},ul={class:"detail-item"},cl={class:"detail-item"},pl={key:1,class:"detail-section"},ml={class:"goods-info"},fl={class:"goods-image"},gl={class:"goods-details"},vl={class:"goods-name"},_l={class:"goods-quantity"},yl={class:"goods-price"},hl={key:0,class:"goods-settings"},bl={class:"setting-label"},Tl={class:"setting-value"},wl={class:"goods-total"},Cl={class:"detail-section"},kl={class:"detail-item"},Nl={class:"detail-item"},xl={class:"detail-item"},Vl={class:"detail-item"},Sl={class:"rank-container"},Il={class:"income-text"},Dl={__name:"ShopOrder",setup(Rl){const{proxy:D}=Ve(),z=f(!1),L=f(!1),I=f(!1),J=f([]),M=f(0),F=f([]),q=f([]),Q=f(0),W=f(0),K=f(0),$=f(!1),P=f(!1),i=f(null),S=f("coachIncome"),j=f([]),B=f([]),G=f([]),o=se({pageNum:1,pageSize:10,orderCode:"",goodsName:"",coachName:"",payType:null,type:null,startTime:"",endTime:"",address:""}),C=se({top:10,startTime:"",endTime:""}),X=f(null),Z=n=>({"-3":"待报价","-2":"已报价(未选择报价)","-1":"取消订单",1:"待支付",2:"已支付，师傅未接单",3:"师傅已接单待上门",4:"待预约",5:"待服务（已上门）",6:"服务中（开始服务）",7:"已完成"})[n]||"未知",ee=n=>({"-3":"info","-2":"warning","-1":"danger",1:"warning",2:"primary",3:"primary",4:"warning",5:"success",6:"success",7:"success"})[n]||"info",ie=n=>n===1||n==="1"?"微信支付":n===2||n==="2"?"支付宝":n===7||n==="7"?"余额支付":"微信支付",re=(n,e)=>{if(!n||!e)return"-";const u=new Date(n),p=new Date(e),g=h=>{const _=h.getFullYear(),m=String(h.getMonth()+1).padStart(2,"0"),T=String(h.getDate()).padStart(2,"0"),y=String(h.getHours()).padStart(2,"0"),x=String(h.getMinutes()).padStart(2,"0");return`${_}-${m}-${T} ${y}:${x}`};return`${g(u)} - ${g(p)}`},ue=()=>!i.value||!i.value.orderGoods?0:i.value.orderGoods.reduce((n,e)=>n+(e.num||0),0),ce=n=>{n&&n.length===2?(o.startTime=n[0],o.endTime=n[1]):(o.startTime="",o.endTime="")},pe=n=>{n&&n.length===2?(C.startTime=n[0],C.endTime=n[1]):(C.startTime="",C.endTime="")},me=n=>{Q.value=n.reduce((p,g)=>p+(parseFloat(g.payPrice)||0),0).toFixed(2);const e=new Date().toISOString().split("T")[0],u=n.filter(p=>p.createTime&&p.createTime.startsWith(e));W.value=u.length,K.value=u.reduce((p,g)=>p+(parseFloat(g.payPrice)||0),0).toFixed(2)},R=async()=>{try{z.value=!0,console.log("🔍 开始加载订单列表，参数:",o);const n={pageNum:o.pageNum,pageSize:o.pageSize};o.orderCode&&(n.orderCode=o.orderCode),o.goodsName&&(n.goodsName=o.goodsName),o.coachName&&(n.coachName=o.coachName),o.payType!==null&&(n.payType=o.payType),o.type!==null&&(n.type=o.type),o.startTime&&(n.startTime=o.startTime),o.endTime&&(n.endTime=o.endTime),o.address&&(n.address=o.address);const e=await D.$api.shop.orderList(n);console.log("📋 订单列表响应:",e),e.code==="200"?(J.value=e.data.list||[],M.value=e.data.totalCount||0,me(e.data.list||[]),console.log(`✅ 订单列表加载成功，共 ${M.value} 条数据`)):w.error(e.msg||"获取订单列表失败")}catch(n){console.error("❌ 加载订单列表失败:",n),w.error("获取订单列表失败")}finally{z.value=!1}},fe=()=>{o.pageNum=1,R()},ge=()=>{X.value?.resetFields(),Object.assign(o,{pageNum:1,pageSize:10,orderCode:"",goodsName:"",coachName:"",payType:null,type:null,startTime:"",endTime:"",address:""}),F.value=[],R()},ve=n=>{o.pageSize=n,o.pageNum=1,R()},_e=n=>{o.pageNum=n,R()},ye=async()=>{try{L.value=!0,console.log("📤 开始导出订单Excel...");const n={};o.orderCode!==""&&o.orderCode!==null&&o.orderCode!==void 0&&(n.orderCode=o.orderCode),o.goodsName!==""&&o.goodsName!==null&&o.goodsName!==void 0&&(n.goodsName=o.goodsName),o.coachName!==""&&o.coachName!==null&&o.coachName!==void 0&&(n.coachName=o.coachName),o.payType!==""&&o.payType!==null&&o.payType!==void 0&&(n.payType=parseInt(o.payType)),o.type!==""&&o.type!==null&&o.type!==void 0&&(n.type=parseInt(o.type)),o.address!==""&&o.address!==null&&o.address!==void 0&&(n.address=o.address),o.startTime!==""&&o.startTime!==null&&o.startTime!==void 0&&(n.startTime=o.startTime),o.endTime!==""&&o.endTime!==null&&o.endTime!==void 0&&(n.endTime=o.endTime),n.pageNum=1,n.pageSize=10,console.log("📤 导出参数:",n);const e=sessionStorage.getItem("minitk"),u=await fetch("/api/admin/order/export",{method:"POST",headers:{"Content-Type":"application/json",...e&&{Authorization:`Bearer ${e}`}},body:JSON.stringify(n)});if(u.ok){const p=u.headers.get("Content-Type");if(p&&p.includes("application/json")){const y=await u.json();if(console.error("❌ 导出返回错误:",y),y.code==="-1"||y.code===-1){const x=y.msg||"导出失败";w.error(`导出失败: ${x}`),(x.includes("coachMobile")||x.includes("ResultMapException"))&&w.warning("后端数据库字段映射异常，请联系技术人员修复")}else w.error(y.msg||"导出失败");return}const g=u.headers.get("Content-Disposition");let h=`订单导出_${new Date().toISOString().slice(0,10)}.xlsx`;if(g){const y=g.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);y&&y[1]&&(h=y[1].replace(/['"]/g,""))}const _=await u.blob(),m=window.URL.createObjectURL(_),T=document.createElement("a");T.href=m,T.download=h,T.style.display="none",document.body.appendChild(T),T.click(),document.body.removeChild(T),window.URL.revokeObjectURL(m),w.success("导出成功，请查看浏览器下载"),console.log("✅ 导出订单Excel成功")}else try{const p=await u.text();console.error("❌ 导出HTTP错误:",u.status,u.statusText,p);try{const g=JSON.parse(p);if(g.msg)w.error(`导出失败: ${g.msg}`);else throw new Error(`HTTP ${u.status}: ${u.statusText}`)}catch{throw new Error(`导出失败: HTTP ${u.status} ${u.statusText}`)}}catch{throw new Error(`导出失败: HTTP ${u.status} ${u.statusText}`)}}catch(n){console.error("❌ 导出订单Excel异常:",n),w.error("导出失败，请稍后重试")}finally{L.value=!1}},he=async n=>{try{console.log("📄 查看订单详情:",n.id);const e=await D.$api.shop.orderDetail(n.id);console.log("📄 订单详情响应:",e),e.code==="200"?(i.value=e.data,$.value=!0):w.error(e.msg||"获取订单详情失败")}catch(e){console.error("❌ 获取订单详情失败:",e),w.error("获取订单详情失败")}},le=async()=>{try{I.value=!0,console.log("📊 加载排行榜数据，当前标签:",S.value);const n={top:C.top,startTime:C.startTime,endTime:C.endTime};if(S.value==="coachIncome"){const e=await D.$api.shop.coachIncomeRank(n);e.code==="200"&&(j.value=e.data||[],console.log("💰 师傅收入排行榜加载成功:",j.value.length))}else if(S.value==="coachCancel"){const e=await D.$api.shop.coachCancelRank(n);e.code==="200"&&(B.value=e.data||[],console.log("🏃 师傅跑单排行榜加载成功:",B.value.length))}else if(S.value==="userCancel"){const e=await D.$api.shop.userCancelRank(n);e.code==="200"&&(G.value=e.data||[],console.log("👤 用户跑单排行榜加载成功:",G.value.length))}}catch(n){console.error("❌ 加载排行榜数据失败:",n),w.error("加载排行榜数据失败")}finally{I.value=!1}},be=n=>{console.log("🔄 切换排行榜标签:",n.props.name),S.value=n.props.name,le()};return Se(()=>{console.log("🚀 订单管理页面初始化"),R()}),(n,e)=>{const u=v("el-card"),p=v("el-col"),g=v("el-row"),h=v("el-input"),_=v("el-form-item"),m=v("el-option"),T=v("el-select"),y=v("el-date-picker"),x=v("el-form"),r=v("el-table-column"),k=v("el-tag"),Y=v("el-table"),Te=v("el-image"),te=v("el-dialog"),A=v("el-tab-pane"),we=v("el-tabs"),H=Ie("loading");return b(),N("div",De,[l(ke,{title:"订单管理"}),t("div",Re,[l(g,{gutter:20,class:"stats-cards"},{default:s(()=>[l(p,{span:6},{default:s(()=>[l(u,{class:"stats-card"},{default:s(()=>[t("div",ze,[t("div",Me,d(M.value||0),1),e[15]||(e[15]=t("div",{class:"stats-label"},"订单总数",-1))])]),_:1})]),_:1}),l(p,{span:6},{default:s(()=>[l(u,{class:"stats-card"},{default:s(()=>[t("div",$e,[t("div",Pe,"¥"+d(Q.value||"0.00"),1),e[16]||(e[16]=t("div",{class:"stats-label"},"订单总金额",-1))])]),_:1})]),_:1}),l(p,{span:6},{default:s(()=>[l(u,{class:"stats-card"},{default:s(()=>[t("div",Ye,[t("div",He,d(W.value||0),1),e[17]||(e[17]=t("div",{class:"stats-label"},"今日订单",-1))])]),_:1})]),_:1}),l(p,{span:6},{default:s(()=>[l(u,{class:"stats-card"},{default:s(()=>[t("div",Ue,[t("div",Ee,"¥"+d(K.value||"0.00"),1),e[18]||(e[18]=t("div",{class:"stats-label"},"今日金额",-1))])]),_:1})]),_:1})]),_:1}),t("div",Oe,[l(x,{ref_key:"searchFormRef",ref:X,model:o,inline:!0,class:"search-form"},{default:s(()=>[l(g,{gutter:20},{default:s(()=>[l(p,{span:24},{default:s(()=>[l(_,{label:"订单号",prop:"orderCode"},{default:s(()=>[l(h,{size:"default",modelValue:o.orderCode,"onUpdate:modelValue":e[0]||(e[0]=a=>o.orderCode=a),placeholder:"请输入订单号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(_,{label:"商品名称",prop:"goodsName"},{default:s(()=>[l(h,{size:"default",modelValue:o.goodsName,"onUpdate:modelValue":e[1]||(e[1]=a=>o.goodsName=a),placeholder:"请输入商品名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(_,{label:"师傅姓名",prop:"coachName"},{default:s(()=>[l(h,{size:"default",modelValue:o.coachName,"onUpdate:modelValue":e[2]||(e[2]=a=>o.coachName=a),placeholder:"请输入师傅姓名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(_,{label:"订单状态",prop:"payType"},{default:s(()=>[l(T,{size:"default",modelValue:o.payType,"onUpdate:modelValue":e[3]||(e[3]=a=>o.payType=a),placeholder:"请选择订单状态",clearable:"",style:{width:"180px"}},{default:s(()=>[l(m,{label:"待报价",value:-3}),l(m,{label:"已报价(未选择报价)",value:-2}),l(m,{label:"取消订单",value:-1}),l(m,{label:"待支付",value:1}),l(m,{label:"已支付，师傅未接单",value:2}),l(m,{label:"师傅已接单待上门",value:3}),l(m,{label:"待预约",value:4}),l(m,{label:"待服务（已上门）",value:5}),l(m,{label:"服务中（开始服务）",value:6}),l(m,{label:"已完成",value:7})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(g,{gutter:20},{default:s(()=>[l(p,{span:24},{default:s(()=>[l(_,{label:"订单类型",prop:"type"},{default:s(()=>[l(T,{size:"default",modelValue:o.type,"onUpdate:modelValue":e[4]||(e[4]=a=>o.type=a),placeholder:"请选择订单类型",clearable:"",style:{width:"150px"}},{default:s(()=>[l(m,{label:"一口价模式",value:0}),l(m,{label:"报价模式",value:1})]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"地址",prop:"address"},{default:s(()=>[l(h,{size:"default",modelValue:o.address,"onUpdate:modelValue":e[5]||(e[5]=a=>o.address=a),placeholder:"请输入地址",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(_,{label:"时间范围",prop:"timeRange"},{default:s(()=>[l(y,{size:"default",modelValue:F.value,"onUpdate:modelValue":e[6]||(e[6]=a=>F.value=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:ce},null,8,["modelValue"])]),_:1}),l(_,null,{default:s(()=>[l(V,{size:"default",type:"primary",icon:"Search",onClick:fe,loading:z.value},{default:s(()=>e[19]||(e[19]=[c(" 搜索 ")])),_:1,__:[19]},8,["loading"]),l(V,{size:"default",icon:"RefreshLeft",onClick:ge},{default:s(()=>e[20]||(e[20]=[c(" 重置 ")])),_:1,__:[20]}),l(V,{size:"default",type:"success",icon:"Download",onClick:ye,loading:L.value},{default:s(()=>e[21]||(e[21]=[c(" 导出 ")])),_:1,__:[21]},8,["loading"]),l(V,{size:"default",type:"warning",icon:"TrendCharts",onClick:e[7]||(e[7]=a=>P.value=!0)},{default:s(()=>e[22]||(e[22]=[c(" 排行榜 ")])),_:1,__:[22]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),t("div",Le,[U((b(),E(Y,{data:J.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:s(()=>[l(r,{prop:"id",label:"ID",width:"80",align:"center"}),l(r,{prop:"orderCode",label:"订单号","min-width":"200"}),l(r,{prop:"goodsName",label:"商品名称","min-width":"150"}),l(r,{prop:"num",label:"数量",width:"80",align:"center"}),l(r,{prop:"payPrice",label:"支付金额",width:"100",align:"center"},{default:s(({row:a})=>[t("span",Fe,"¥"+d(a.payPrice),1)]),_:1}),l(r,{prop:"nickName",label:"用户昵称",width:"120"}),l(r,{prop:"phone",label:"用户手机",width:"120"}),l(r,{prop:"coachName",label:"师傅姓名",width:"120"}),l(r,{prop:"coachMobile",label:"师傅手机",width:"120"}),l(r,{prop:"payType",label:"订单状态",width:"150",align:"center"},{default:s(({row:a})=>[l(k,{type:ee(a.payType)},{default:s(()=>[c(d(Z(a.payType)),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"type",label:"订单类型",width:"100",align:"center"},{default:s(({row:a})=>[l(k,{type:a.type===0?"warning":"primary"},{default:s(()=>[c(d(a.type===0?"一口价模式":"报价模式"),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"createTime",label:"创建时间",width:"160"}),l(r,{label:"操作",width:"120",align:"center",fixed:"right"},{default:s(a=>[t("div",je,[l(V,{size:"default",type:"primary",onClick:ae=>he(a.row)},{default:s(()=>e[23]||(e[23]=[c(" 详情 ")])),_:2,__:[23]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[H,z.value]])]),l(Ne,{page:o.pageNum,"page-size":o.pageSize,total:M.value,onHandleSizeChange:ve,onHandleCurrentChange:_e},null,8,["page","page-size","total"])]),l(te,{modelValue:$.value,"onUpdate:modelValue":e[9]||(e[9]=a=>$.value=a),title:"订单详情",width:"900px","close-on-click-modal":!1},{footer:s(()=>[l(V,{onClick:e[8]||(e[8]=a=>$.value=!1)},{default:s(()=>e[49]||(e[49]=[c("关闭")])),_:1,__:[49]})]),default:s(()=>[i.value?(b(),N("div",Be,[t("div",Ge,[e[28]||(e[28]=t("h4",null,"用户信息",-1)),t("div",Ae,[e[24]||(e[24]=t("label",null,"用户ID：",-1)),t("span",null,d(i.value.userId||"-"),1)]),t("div",Je,[e[25]||(e[25]=t("label",null,"下单人：",-1)),t("span",null,d(i.value.addressInfo?.userName||"-"),1)]),t("div",qe,[e[26]||(e[26]=t("label",null,"联系方式：",-1)),t("span",null,d(i.value.addressInfo?.mobile||"-"),1)]),t("div",Qe,[e[27]||(e[27]=t("label",null,"项目地址：",-1)),t("span",null,d(i.value.addressInfo?.addressInfo||"-"),1)])]),i.value.coachInfo?(b(),N("div",We,[e[34]||(e[34]=t("h4",null,"师傅信息",-1)),t("div",Ke,[e[29]||(e[29]=t("label",null,"师傅ID：",-1)),t("span",null,d(i.value.coachInfo.id||"-"),1)]),t("div",Xe,[e[30]||(e[30]=t("label",null,"师傅姓名：",-1)),t("span",null,d(i.value.coachInfo.coachName||"-"),1)]),t("div",Ze,[e[31]||(e[31]=t("label",null,"联系方式：",-1)),t("span",null,d(i.value.coachInfo.mobileEncry||"-"),1)]),t("div",el,[e[32]||(e[32]=t("label",null,"师傅等级：",-1)),t("span",null,d(i.value.coachInfo.labelName||"-"),1)]),t("div",ll,[e[33]||(e[33]=t("label",null,"师傅地址：",-1)),t("span",null,d(i.value.coachInfo.address||"-"),1)])])):O("",!0),t("div",tl,[e[42]||(e[42]=t("h4",null,"订单信息",-1)),t("div",al,[e[35]||(e[35]=t("label",null,"系统订单号：",-1)),t("span",null,d(i.value.orderCode||"-"),1)]),t("div",ol,[e[36]||(e[36]=t("label",null,"下单时间：",-1)),t("span",null,d(i.value.createTime||"-"),1)]),t("div",sl,[e[37]||(e[37]=t("label",null,"项目时间：",-1)),t("span",null,d(re(i.value.startTime,i.value.endTime)),1)]),t("div",nl,[e[38]||(e[38]=t("label",null,"项目费用：",-1)),t("span",dl,"¥"+d(i.value.servicePrice||"0.00"),1)]),t("div",il,[e[39]||(e[39]=t("label",null,"订单金额：",-1)),t("span",rl,"¥"+d(i.value.payPrice||"0.00"),1)]),t("div",ul,[e[40]||(e[40]=t("label",null,"支付方式：",-1)),t("span",null,d(ie(i.value.payType)),1)]),t("div",cl,[e[41]||(e[41]=t("label",null,"订单状态：",-1)),l(k,{type:ee(i.value.payType)},{default:s(()=>[c(d(Z(i.value.payType)),1)]),_:1},8,["type"])])]),i.value.orderGoods&&i.value.orderGoods.length>0?(b(),N("div",pl,[e[43]||(e[43]=t("h4",null,"项目内容",-1)),(b(!0),N(ne,null,de(i.value.orderGoods,(a,ae)=>(b(),N("div",{key:ae,class:"goods-item"},[t("div",ml,[t("div",fl,[l(Te,{src:a.goodsCover,style:{width:"60px",height:"60px","border-radius":"4px"},fit:"cover","preview-src-list":[a.goodsCover]},null,8,["src","preview-src-list"])]),t("div",gl,[t("div",vl,d(a.goodsName),1),t("div",_l,"x"+d(a.num),1),t("div",yl,"¥"+d(a.price),1)])]),a.priceSetting&&a.priceSetting.length>0?(b(),N("div",hl,[(b(!0),N(ne,null,de(a.priceSetting,(oe,Ce)=>(b(),N("div",{key:Ce,class:"setting-item"},[t("span",bl,d(oe.problemDesc)+"：",1),t("span",Tl,d(oe.val),1)]))),128))])):O("",!0)]))),128)),t("div",wl,[t("span",null,"合计数量："+d(ue()),1)])])):O("",!0),t("div",Cl,[e[48]||(e[48]=t("h4",null,"其他信息",-1)),t("div",kl,[e[44]||(e[44]=t("label",null,"微信交易号：",-1)),t("span",null,d(i.value.transactionId||"-"),1)]),t("div",Nl,[e[45]||(e[45]=t("label",null,"支付时间：",-1)),t("span",null,d(i.value.payTime||"-"),1)]),t("div",xl,[e[46]||(e[46]=t("label",null,"订单类型：",-1)),l(k,{type:i.value.type===0?"warning":"primary"},{default:s(()=>[c(d(i.value.type===0?"一口价模式":"报价模式"),1)]),_:1},8,["type"])]),t("div",Vl,[e[47]||(e[47]=t("label",null,"退款状态：",-1)),l(k,{type:i.value.refundStatus===0?"success":"warning"},{default:s(()=>[c(d(i.value.refundStatus===0?"未退款":"已退款"),1)]),_:1},8,["type"])])])])):O("",!0)]),_:1},8,["modelValue"]),l(te,{modelValue:P.value,"onUpdate:modelValue":e[14]||(e[14]=a=>P.value=a),title:"排行榜统计",width:"1000px","close-on-click-modal":!1},{footer:s(()=>[l(V,{onClick:e[13]||(e[13]=a=>P.value=!1)},{default:s(()=>e[51]||(e[51]=[c("关闭")])),_:1,__:[51]})]),default:s(()=>[t("div",Sl,[l(x,{model:C,inline:!0,class:"rank-form"},{default:s(()=>[l(_,{label:"排行数量"},{default:s(()=>[l(T,{modelValue:C.top,"onUpdate:modelValue":e[10]||(e[10]=a=>C.top=a),placeholder:"选择排行数量",style:{width:"120px"}},{default:s(()=>[l(m,{label:"前5名",value:5}),l(m,{label:"前10名",value:10}),l(m,{label:"前20名",value:20})]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"时间范围"},{default:s(()=>[l(y,{modelValue:q.value,"onUpdate:modelValue":e[11]||(e[11]=a=>q.value=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:pe},null,8,["modelValue"])]),_:1}),l(_,null,{default:s(()=>[l(V,{type:"primary",onClick:le,loading:I.value},{default:s(()=>e[50]||(e[50]=[c(" 查询排行榜 ")])),_:1,__:[50]},8,["loading"])]),_:1})]),_:1},8,["model"]),l(we,{modelValue:S.value,"onUpdate:modelValue":e[12]||(e[12]=a=>S.value=a),onTabClick:be},{default:s(()=>[l(A,{label:"师傅收入排行榜",name:"coachIncome"},{default:s(()=>[U((b(),E(Y,{data:j.value,style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:s(()=>[l(r,{type:"index",label:"排名",width:"80",align:"center"},{default:s(({$index:a})=>[l(k,{type:a<3?"danger":"info",effect:"dark"},{default:s(()=>[c(d(a+1),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"coachId",label:"师傅ID",width:"100",align:"center"}),l(r,{prop:"coachName",label:"师傅姓名",width:"120"},{default:s(({row:a})=>[c(d(a.coachName||"-"),1)]),_:1}),l(r,{prop:"coachMobile",label:"师傅手机",width:"130"},{default:s(({row:a})=>[c(d(a.coachMobile||"-"),1)]),_:1}),l(r,{prop:"totalIncome",label:"总收入",width:"120",align:"center"},{default:s(({row:a})=>[t("span",Il,"¥"+d(a.totalIncome),1)]),_:1}),l(r,{prop:"orderCount",label:"订单数量",width:"100",align:"center"})]),_:1},8,["data"])),[[H,I.value]])]),_:1}),l(A,{label:"师傅跑单排行榜",name:"coachCancel"},{default:s(()=>[U((b(),E(Y,{data:B.value,style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:s(()=>[l(r,{type:"index",label:"排名",width:"80",align:"center"},{default:s(({$index:a})=>[l(k,{type:a<3?"danger":"info",effect:"dark"},{default:s(()=>[c(d(a+1),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"coachId",label:"师傅ID",width:"100",align:"center"}),l(r,{prop:"coachName",label:"师傅姓名",width:"120"},{default:s(({row:a})=>[c(d(a.coachName||"-"),1)]),_:1}),l(r,{prop:"coachMobile",label:"师傅手机",width:"130"},{default:s(({row:a})=>[c(d(a.coachMobile||"-"),1)]),_:1}),l(r,{prop:"cancelCount",label:"跑单次数",width:"100",align:"center"}),l(r,{prop:"cancelRate",label:"跑单率",width:"100",align:"center"},{default:s(({row:a})=>[l(k,{type:a.cancelRate>30?"danger":a.cancelRate>15?"warning":"success"},{default:s(()=>[c(d(a.cancelRate)+"% ",1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[H,I.value]])]),_:1}),l(A,{label:"用户跑单排行榜",name:"userCancel"},{default:s(()=>[U((b(),E(Y,{data:G.value,style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:s(()=>[l(r,{type:"index",label:"排名",width:"80",align:"center"},{default:s(({$index:a})=>[l(k,{type:a<3?"danger":"info",effect:"dark"},{default:s(()=>[c(d(a+1),1)]),_:2},1032,["type"])]),_:1}),l(r,{prop:"userId",label:"用户ID",width:"100",align:"center"}),l(r,{prop:"nickName",label:"用户昵称",width:"120"},{default:s(({row:a})=>[c(d(a.nickName||"-"),1)]),_:1}),l(r,{prop:"phone",label:"用户手机",width:"130"},{default:s(({row:a})=>[c(d(a.phone||"-"),1)]),_:1}),l(r,{prop:"cancelCount",label:"跑单次数",width:"100",align:"center"}),l(r,{prop:"cancelRate",label:"跑单率",width:"100",align:"center"},{default:s(({row:a})=>[l(k,{type:a.cancelRate>30?"danger":a.cancelRate>15?"warning":"success"},{default:s(()=>[c(d(a.cancelRate)+"% ",1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[H,I.value]])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])])}}},Ul=xe(Dl,[["__scopeId","data-v-78059bc9"]]);export{Ul as default};
