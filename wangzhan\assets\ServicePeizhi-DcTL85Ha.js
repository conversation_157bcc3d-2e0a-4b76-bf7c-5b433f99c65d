import{E as p,y as Ie,q as xe}from"./element-fdzwdDuf.js";import{T as Re,L as v}from"./LbButton-BtU4V_Gr.js";import{L as De}from"./LbPage-DnbiQ0Ct.js";import{_ as Se}from"./index-C9Xz1oqp.js";import{g as qe,r as m,X as Q,h as $e,y as h,Q as l,A as r,I as t,al as f,J as Ue,ar as Ae,H as X,z as b,M as i,O as V,K as G,P as W,a6 as Y,u as Le}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Ne={class:"service-peizhi"},Pe={class:"content-container"},Me={class:"search-form-container"},Fe={class:"table-container"},Be={class:"content-cell"},Ee={key:0,class:"options-cell"},Oe={key:1,class:"text-muted"},Je={class:"options-config"},He={class:"dialog-footer"},je={class:"import-content"},Ke={class:"import-tips"},Qe={class:"upload-area",style:{"margin-top":"20px"}},Xe={class:"dialog-footer"},Ge={class:"fail-data-content"},We={class:"import-summary"},Ye={class:"fail-data-table",style:{"margin-top":"20px"}},Ze={key:0,class:"fail-reasons",style:{"margin-top":"20px"}},el={class:"reason-list"},ll={class:"dialog-footer"},tl={__name:"ServicePeizhi",setup(ol){const{proxy:Z}=qe(),y=Z.$api,U=m(!1),A=m(!1),k=m(!1),R=m(!1),I=m(!1),L=m(!1),_=m(null),ee=m(null),S=m(!1),z=m({successCount:0,failCount:0,failList:[],failReasons:[]}),D=m([]),q=m(0),u=Q({serviceId:"",name:"",type:null,pageNum:1,pageSize:10}),s=Q({id:"",type:1,serviceId:"",serviceTitle:"",problemDesc:"",problemContent:"",isRequired:1,inputType:1,val:"",options:"[]"}),g=m([]),le={serviceId:[{required:!0,message:"请输入关联项目id",trigger:"blur"}],problemDesc:[{required:!0,message:"请输入配置名称",trigger:"blur"}],problemContent:[{required:!0,message:"请输入配置描述",trigger:"blur"}],isRequired:[{required:!0,message:"请选择是否必填",trigger:"change"}],type:[{required:!0,message:"请选择报价类型",trigger:"change"}],inputType:[{required:!0,message:"请选择配置类型",trigger:"change"}]},N=m(null),E=m(null),O=a=>({1:"输入框",2:"上传图片",3:"单选",4:"多选"})[a]||"未知",J=a=>({1:"primary",2:"success",3:"warning",4:"danger"})[a]||"info",te=a=>({1:"一口价",2:"报价模式"})[a]||"未知",oe=a=>({1:"success",2:"warning"})[a]||"info",ae=a=>{try{return JSON.parse(a||"[]").join(", ")}catch{return a||"-"}},w=async()=>{console.log("🚀 开始获取服务配置列表数据..."),U.value=!0;try{const a={pageNum:u.pageNum,pageSize:u.pageSize};u.serviceId&&(a.serviceId=u.serviceId),u.name&&(a.name=u.name),u.type!==null&&u.type!==""&&(a.type=u.type),console.log("📤 API请求参数:",a),console.log("🔗 API实例:",y),console.log("🔗 service模块:",y.service);const e=await y.service.priceSettingList(a);if(console.log("📋 服务配置列表数据 (API-V2):",e),console.log("📋 result类型:",typeof e),console.log("📋 result.data:",e?.data),console.log("📋 result.data类型:",typeof e?.data),e.code===200||e.code==="200"){const n=e.data;console.log("📊 处理API响应数据:",n),n.list&&Array.isArray(n.list)?(D.value=n.list,q.value=n.totalCount||0):(D.value=Array.isArray(n)?n:[],q.value=Array.isArray(n)?n.length:0),console.log("📊 处理后的数据:",{list:D.value,total:q.value,count:D.value.length})}else console.error("❌ API响应错误:",e),p.error(e.message||e.msg||"获取数据失败")}catch(a){console.error("获取服务配置列表失败:",a),p.error("获取数据失败")}finally{U.value=!1}},se=()=>{u.pageNum=1,w()},ne=()=>{E.value?.resetFields(),u.pageNum=1,w()},ie=a=>{u.pageSize=a,u.pageNum=1,w()},re=a=>{u.pageNum=a,w()},ue=()=>{R.value=!1,ve(),k.value=!0},de=async a=>{R.value=!0;try{const e=await y.service.priceSettingInfo({id:a.id});if(console.log("📋 服务配置详情数据:",e),e.code===200||e.code==="200"){const n=e.data;if(s.id=n.id||"",s.type=n.type||1,s.serviceId=n.serviceId||"",s.serviceTitle=n.serviceTitle||"",s.problemDesc=n.problemDesc||"",s.problemContent=n.problemContent||"",s.isRequired=n.isRequired!==void 0?n.isRequired:1,s.inputType=n.inputType||1,s.val=n.val||"",s.options=n.options||"[]",s.inputType===3||s.inputType===4)try{g.value=JSON.parse(s.options||"[]")}catch{g.value=[]}k.value=!0}else p.error(e.message||e.msg||"获取详情失败")}catch(e){console.error("获取服务配置详情失败:",e),p.error("获取详情失败")}},pe=a=>{xe.confirm(`确定要删除配置"${a.problemDesc}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await y.service.priceSettingDelete({id:a.id});console.log("🗑️ 删除服务配置结果:",e),e.code===200||e.code==="200"?(p.success("删除成功"),w()):p.error(e.message||e.msg||"删除失败")}catch(e){console.error("删除服务配置失败:",e),p.error("删除失败")}}).catch(()=>{})},ce=a=>{a===3||a===4?g.value.length===0&&(g.value=[""]):(g.value=[],s.options="[]")},fe=()=>{g.value.push("")},me=a=>{g.value.length>1?g.value.splice(a,1):p.warning("至少保留一个可选项")},ve=()=>{Object.assign(s,{id:"",type:1,serviceId:"",serviceTitle:"",problemDesc:"",problemContent:"",isRequired:1,inputType:1,val:"",options:"[]"}),g.value=[],N.value?.clearValidate()},ge=async()=>{try{if(await N.value?.validate(),A.value=!0,s.inputType===3||s.inputType===4){const n=g.value.filter(d=>d.trim());if(n.length===0){p.error("单选/多选类型至少需要一个有效可选项");return}s.options=JSON.stringify(n)}else s.options="[]";const a={id:s.id,type:s.type,serviceId:s.serviceId,serviceTitle:s.serviceTitle,problemDesc:s.problemDesc,problemContent:s.problemContent,isRequired:s.isRequired,inputType:s.inputType,val:s.val,options:s.options};let e;R.value?(e=await y.service.priceSettingEdit(a),console.log("✏️ 编辑服务配置结果:",e)):(e=await y.service.priceSettingAdd(a),console.log("➕ 新增服务配置结果:",e)),e.code===200||e.code==="200"?(p.success(R.value?"编辑成功":"新增成功"),k.value=!1,w()):p.error(e.message||e.msg||"操作失败")}catch(a){console.error("提交服务配置失败:",a),p.error("操作失败")}finally{A.value=!1}},ye=async()=>{try{console.log("📥 开始下载服务配置模板...");const a="http://192.168.1.29:8889/ims/api/admin/priceSetting/template",e=document.createElement("a");e.href=a,e.download="服务价格配置导入模板.xlsx",e.target="_blank";const n=sessionStorage.getItem("minitk");if(n){const d=a.includes("?")?"&":"?";e.href=`${a}${d}token=${encodeURIComponent(n)}`}document.body.appendChild(e),e.click(),document.body.removeChild(e),p.success("模板下载开始，请查看浏览器下载")}catch(a){console.error("下载服务配置模板失败:",a),p.error("下载模板失败")}},_e=()=>{I.value=!0,_.value=null},be=a=>{console.log("📁 文件选择变化:",a),_.value=a},we=()=>{console.log("🗑️ 文件被移除"),_.value=null},Ce=async()=>{if(!_.value){p.error("请选择要导入的文件");return}try{L.value=!0,console.log("📤 开始批量导入服务配置...");const a=new FormData;a.append("file",_.value.raw),console.log("📤 导入文件信息:",{name:_.value.name,size:_.value.size,type:_.value.raw.type});const e=await y.service.priceSettingImport(a);if(console.log("📤 批量导入API响应:",e),e.code===200||e.code==="200")p.success("批量导入成功"),I.value=!1,_.value=null,w();else if(e.code==="-1"){const{successCount:n=0,failCount:d=0,failList:$=[],failReasons:P=[]}=e.data||{};z.value={successCount:n,failCount:d,failList:$,failReasons:P},p.warning(e.msg||`成功${n}条，失败${d}条`),I.value=!1,_.value=null,S.value=!0,w()}else p.error(e.message||e.msg||"批量导入失败")}catch(a){console.error("批量导入服务配置失败:",a),p.error("批量导入失败")}finally{L.value=!1}};return $e(()=>{console.log("🎯 页面初始化开始..."),console.log("🔍 检查API实例:",y),console.log("🔍 检查service模块:",y?.service),console.log("🔍 检查priceSettingList方法:",y?.service?.priceSettingList),setTimeout(()=>{console.log("⏰ 延迟后开始获取数据..."),w()},100)}),(a,e)=>{const n=f("el-input"),d=f("el-form-item"),$=f("el-option"),P=f("el-select"),Te=f("el-col"),he=f("el-row"),H=f("el-form"),c=f("el-table-column"),x=f("el-tag"),j=f("el-table"),T=f("el-radio"),M=f("el-radio-group"),F=f("el-dialog"),B=f("el-alert"),Ve=f("el-icon"),ze=f("el-upload"),ke=Ae("loading");return b(),h("div",Ne,[l(Re,{title:"服务配置管理"}),r("div",Pe,[r("div",Me,[l(H,{ref_key:"searchFormRef",ref:E,model:u,inline:!0,class:"search-form"},{default:t(()=>[l(he,{gutter:20},{default:t(()=>[l(Te,{span:24},{default:t(()=>[l(d,{label:"项目名称",prop:"name"},{default:t(()=>[l(n,{size:"default",modelValue:u.name,"onUpdate:modelValue":e[0]||(e[0]=o=>u.name=o),placeholder:"请输入项目名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(d,{label:"报价类型",prop:"type"},{default:t(()=>[l(P,{size:"default",modelValue:u.type,"onUpdate:modelValue":e[1]||(e[1]=o=>u.type=o),placeholder:"请选择报价类型",clearable:"",style:{width:"150px"}},{default:t(()=>[l($,{label:"一口价",value:1}),l($,{label:"报价模式",value:2})]),_:1},8,["modelValue"])]),_:1}),l(d,null,{default:t(()=>[l(v,{size:"default",type:"primary",icon:"Search",onClick:se},{default:t(()=>e[15]||(e[15]=[i(" 搜索 ")])),_:1,__:[15]}),l(v,{size:"default",icon:"RefreshLeft",onClick:ne},{default:t(()=>e[16]||(e[16]=[i(" 重置 ")])),_:1,__:[16]}),l(v,{size:"default",type:"primary",icon:"Plus",onClick:ue},{default:t(()=>e[17]||(e[17]=[i(" 新增配置 ")])),_:1,__:[17]}),l(v,{size:"default",type:"success",icon:"Download",onClick:ye},{default:t(()=>e[18]||(e[18]=[i(" 下载服务配置模板 ")])),_:1,__:[18]}),l(v,{size:"default",type:"warning",icon:"Upload",onClick:_e},{default:t(()=>e[19]||(e[19]=[i(" 批量导入服务配置 ")])),_:1,__:[19]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),r("div",Fe,[Ue((b(),X(j,{data:D.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},"cell-style":{padding:"15px 8px"},"row-style":{height:"auto",backgroundColor:"#ffffff"},style:{width:"100%"}},{default:t(()=>[l(c,{prop:"id",label:"ID",width:"80",fixed:"left"}),l(c,{prop:"serviceId",label:"关联项目ID",width:"120"}),l(c,{prop:"serviceTitle",label:"项目名称",width:"180","show-overflow-tooltip":""}),l(c,{prop:"problemDesc",label:"配置名称",width:"160","show-overflow-tooltip":""}),l(c,{prop:"problemContent",label:"配置描述","min-width":"250"},{default:t(o=>[r("div",Be,V(o.row.problemContent),1)]),_:1}),l(c,{prop:"inputType",label:"配置类型",width:"100",align:"center"},{default:t(o=>[l(x,{type:J(o.row.inputType),size:"small"},{default:t(()=>[i(V(O(o.row.inputType)),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"isRequired",label:"是否必填",width:"100",align:"center"},{default:t(o=>[l(x,{type:o.row.isRequired===1?"danger":"info",size:"small"},{default:t(()=>[i(V(o.row.isRequired===1?"必填":"非必填"),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"type",label:"报价类型",width:"100",align:"center"},{default:t(o=>[l(x,{type:oe(o.row.type),size:"small"},{default:t(()=>[i(V(te(o.row.type)),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"options",label:"可选项","min-width":"200"},{default:t(o=>[o.row.inputType===3||o.row.inputType===4?(b(),h("div",Ee,V(ae(o.row.options)),1)):(b(),h("span",Oe,"-"))]),_:1}),l(c,{prop:"createTime",label:"创建时间",width:"180"}),l(c,{label:"操作",width:"180",fixed:"right"},{default:t(o=>[l(v,{size:"default",type:"primary",onClick:C=>de(o.row)},{default:t(()=>e[20]||(e[20]=[i(" 编辑 ")])),_:2,__:[20]},1032,["onClick"]),l(v,{size:"default",type:"danger",onClick:C=>pe(o.row)},{default:t(()=>e[21]||(e[21]=[i(" 删除 ")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,U.value]])]),l(De,{page:u.pageNum,"page-size":u.pageSize,total:q.value,onHandleSizeChange:ie,onHandleCurrentChange:re},null,8,["page","page-size","total"])]),l(F,{modelValue:k.value,"onUpdate:modelValue":e[10]||(e[10]=o=>k.value=o),title:R.value?"编辑配置":"新增配置",width:"600px","close-on-click-modal":!1},{footer:t(()=>[r("span",He,[l(v,{size:"default",onClick:e[9]||(e[9]=o=>k.value=!1)},{default:t(()=>e[32]||(e[32]=[i("取消")])),_:1,__:[32]}),l(v,{size:"default",type:"primary",loading:A.value,onClick:ge},{default:t(()=>e[33]||(e[33]=[i(" 确定 ")])),_:1,__:[33]},8,["loading"])])]),default:t(()=>[l(H,{ref_key:"formRef",ref:N,model:s,rules:le,"label-width":"120px"},{default:t(()=>[l(d,{label:"关联项目ID",prop:"serviceId"},{default:t(()=>[l(n,{size:"default",modelValue:s.serviceId,"onUpdate:modelValue":e[2]||(e[2]=o=>s.serviceId=o),placeholder:"请输入关联项目ID",type:"number"},null,8,["modelValue"])]),_:1}),l(d,{label:"项目名称",prop:"serviceTitle"},{default:t(()=>[l(n,{size:"default",modelValue:s.serviceTitle,"onUpdate:modelValue":e[3]||(e[3]=o=>s.serviceTitle=o),placeholder:"请输入项目名称（非必填）"},null,8,["modelValue"])]),_:1}),l(d,{label:"配置名称",prop:"problemDesc"},{default:t(()=>[l(n,{size:"default",modelValue:s.problemDesc,"onUpdate:modelValue":e[4]||(e[4]=o=>s.problemDesc=o),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),l(d,{label:"配置描述",prop:"problemContent"},{default:t(()=>[l(n,{size:"default",modelValue:s.problemContent,"onUpdate:modelValue":e[5]||(e[5]=o=>s.problemContent=o),type:"textarea",rows:3,placeholder:"请输入配置描述"},null,8,["modelValue"])]),_:1}),l(d,{label:"报价类型",prop:"type"},{default:t(()=>[l(M,{modelValue:s.type,"onUpdate:modelValue":e[6]||(e[6]=o=>s.type=o)},{default:t(()=>[l(T,{value:1},{default:t(()=>e[22]||(e[22]=[i("一口价")])),_:1,__:[22]}),l(T,{value:2},{default:t(()=>e[23]||(e[23]=[i("报价模式")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"配置类型",prop:"inputType"},{default:t(()=>[l(M,{modelValue:s.inputType,"onUpdate:modelValue":e[7]||(e[7]=o=>s.inputType=o),onChange:ce},{default:t(()=>[l(T,{value:1},{default:t(()=>e[24]||(e[24]=[i("输入框")])),_:1,__:[24]}),l(T,{value:2},{default:t(()=>e[25]||(e[25]=[i("上传图片")])),_:1,__:[25]}),l(T,{value:3},{default:t(()=>e[26]||(e[26]=[i("单选")])),_:1,__:[26]}),l(T,{value:4},{default:t(()=>e[27]||(e[27]=[i("多选")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),s.inputType===3||s.inputType===4?(b(),X(d,{key:0,label:"可选项配置",prop:"options"},{default:t(()=>[r("div",Je,[(b(!0),h(W,null,Y(g.value,(o,C)=>(b(),h("div",{key:C,class:"option-item"},[l(n,{size:"default",modelValue:g.value[C],"onUpdate:modelValue":K=>g.value[C]=K,placeholder:"请输入可选项内容",style:{width:"300px"}},null,8,["modelValue","onUpdate:modelValue"]),l(v,{size:"default",type:"danger",icon:"Delete",onClick:K=>me(C),style:{"margin-left":"10px"}},{default:t(()=>e[28]||(e[28]=[i(" 删除 ")])),_:2,__:[28]},1032,["onClick"])]))),128)),l(v,{size:"default",type:"primary",icon:"Plus",onClick:fe,style:{"margin-top":"10px"}},{default:t(()=>e[29]||(e[29]=[i(" 添加可选项 ")])),_:1,__:[29]})])]),_:1})):G("",!0),l(d,{label:"是否必填",prop:"isRequired"},{default:t(()=>[l(M,{modelValue:s.isRequired,"onUpdate:modelValue":e[8]||(e[8]=o=>s.isRequired=o)},{default:t(()=>[l(T,{value:1},{default:t(()=>e[30]||(e[30]=[i("必填")])),_:1,__:[30]}),l(T,{value:0},{default:t(()=>e[31]||(e[31]=[i("非必填")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(F,{modelValue:I.value,"onUpdate:modelValue":e[12]||(e[12]=o=>I.value=o),title:"批量导入服务配置",width:"500px","close-on-click-modal":!1},{footer:t(()=>[r("span",Xe,[l(v,{size:"default",onClick:e[11]||(e[11]=o=>I.value=!1)},{default:t(()=>e[37]||(e[37]=[i("取消")])),_:1,__:[37]}),l(v,{size:"default",type:"primary",loading:L.value,disabled:!_.value,onClick:Ce},{default:t(()=>e[38]||(e[38]=[i(" 确认导入 ")])),_:1,__:[38]},8,["loading","disabled"])])]),default:t(()=>[r("div",je,[r("div",Ke,[l(B,{title:"导入说明",type:"info",closable:!1,"show-icon":""},{default:t(()=>e[34]||(e[34]=[r("p",null,"1. 请先下载服务配置模板文件",-1),r("p",null,"2. 按照模板格式填写数据",-1),r("p",null,"3. 上传填写完成的Excel文件",-1),r("p",null,"4. 支持的文件格式：.xlsx",-1)])),_:1})]),r("div",Qe,[l(ze,{ref_key:"uploadRef",ref:ee,"auto-upload":!1,"show-file-list":!0,limit:1,accept:".xlsx,.xls","on-change":be,"on-remove":we,drag:""},{tip:t(()=>e[35]||(e[35]=[r("div",{class:"el-upload__tip"}," 只能上传 xlsx/xls 文件 ",-1)])),default:t(()=>[l(Ve,{class:"el-icon--upload"},{default:t(()=>[l(Le(Ie))]),_:1}),e[36]||(e[36]=r("div",{class:"el-upload__text"},[i(" 将文件拖到此处，或"),r("em",null,"点击上传")],-1))]),_:1,__:[36]},512)])])]),_:1},8,["modelValue"]),l(F,{modelValue:S.value,"onUpdate:modelValue":e[14]||(e[14]=o=>S.value=o),title:"导入失败数据详情",width:"50%","close-on-click-modal":!1,top:"5vh"},{footer:t(()=>[r("span",ll,[l(v,{size:"default",onClick:e[13]||(e[13]=o=>S.value=!1)},{default:t(()=>e[41]||(e[41]=[i("关闭")])),_:1,__:[41]})])]),default:t(()=>[r("div",Ge,[r("div",We,[l(B,{title:`导入结果：成功 ${z.value.successCount||0} 条，失败 ${z.value.failCount||0} 条`,type:"warning",closable:!1,"show-icon":""},null,8,["title"])]),r("div",Ye,[e[39]||(e[39]=r("h4",null,"失败数据列表：",-1)),l(j,{data:z.value.failList||[],"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%","margin-top":"10px"},"max-height":"400"},{default:t(()=>[l(c,{prop:"serviceId",label:"服务ID",width:"100"}),l(c,{prop:"problemDesc",label:"配置名称",width:"150","show-overflow-tooltip":""}),l(c,{prop:"problemContent",label:"配置描述","min-width":"200","show-overflow-tooltip":""}),l(c,{prop:"type",label:"报价类型",width:"100",align:"center"},{default:t(o=>[l(x,{type:o.row.type===1?"success":"warning",size:"small"},{default:t(()=>[i(V(o.row.type===1?"一口价":"报价模式"),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"isRequired",label:"是否必填",width:"100",align:"center"},{default:t(o=>[l(x,{type:o.row.isRequired===1?"danger":"info",size:"small"},{default:t(()=>[i(V(o.row.isRequired===1?"必填":"非必填"),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"inputType",label:"配置类型",width:"100",align:"center"},{default:t(o=>[l(x,{type:J(o.row.inputType),size:"small"},{default:t(()=>[i(V(O(o.row.inputType)),1)]),_:2},1032,["type"])]),_:1}),l(c,{prop:"options",label:"可选项","min-width":"150","show-overflow-tooltip":""})]),_:1},8,["data"])]),z.value.failReasons&&z.value.failReasons.length>0?(b(),h("div",Ze,[e[40]||(e[40]=r("h4",null,"失败原因：",-1)),r("div",el,[(b(!0),h(W,null,Y(z.value.failReasons,(o,C)=>(b(),h("div",{key:C,class:"reason-item"},[l(B,{title:`第 ${C+1} 条数据：${o}`,type:"error",closable:!1,"show-icon":"",style:{"margin-bottom":"10px"}},null,8,["title"])]))),128))])])):G("",!0)])]),_:1},8,["modelValue"])])}}},dl=Se(tl,[["__scopeId","data-v-669fd528"]]);export{dl as default};
