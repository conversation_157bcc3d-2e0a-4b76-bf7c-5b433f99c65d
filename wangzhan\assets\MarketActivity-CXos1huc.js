import{r as i,X as re,h as se,y as _,Q as l,A as C,I as t,V as ue,al as d,z as s,K as v,M as r,P as x,a6 as F,H as y,O as L}from"./vendor-DmFBDimT.js";import{T as ie,L as c}from"./LbButton-BtU4V_Gr.js";import{L as M}from"./LbToolTips-Bbf_ZQSC.js";import{L as de}from"./LbCover-Ctxlz8o1.js";import{_ as pe}from"./index-C9Xz1oqp.js";import{E as B}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const me={class:"lb-edit-card"},_e={class:"page-main"},ve={key:0,style:{"margin-top":"8px"}},fe={key:0,style:{"margin-top":"8px"}},ce={__name:"MarketActivity",setup(ye){const R=i(),A=i(),O=i(),S=i(!1),k=i(!1),V=i(!1),T=i([]),p=i([]),m=i([]),N=i([]),$=i([]),g=i([]),Y=i([{title:"推荐人",is_check:!1,tips:"邀请好友成功后获得奖励"},{title:"被推荐人",is_check:!1,tips:"被邀请并成功注册后获得奖励"}]),a=re({status:0,share_img:"",time:[],inv_user_num:1,inv_time:24,atv_num:1,inv_user:[],inv_reward_type:1,inv_points:0,inv_coupon_ids:[],rec_reward_type:1,rec_points:0,rec_coupon_ids:[]}),z={time:[{required:!0,message:"请选择活动时间",trigger:"change"}],inv_user_num:[{required:!0,message:"请输入邀请好友数",trigger:"blur"}],inv_time:[{required:!0,message:"请输入邀请有效期",trigger:"blur"}],atv_num:[{required:!0,message:"请输入发起活动次数",trigger:"blur"}]},E=n=>n.getTime()<Date.now()-864e5,H=n=>{n&&n.length>0&&(a.share_img=n[0].url)},P=n=>{a.inv_user=n},J=async()=>{try{const e=await(await fetch("/api/market/coupon/list")).json();e.code===200&&(T.value=e.data.list||[])}catch(n){console.error("获取卡券列表失败:",n)}},K=n=>{N.value=n},Q=()=>{p.value=[...N.value],a.inv_coupon_ids=p.value.map(n=>n.id),k.value=!1},X=n=>{p.value=p.value.filter(e=>e.id!==n),a.inv_coupon_ids=p.value.map(e=>e.id)},G=n=>{$.value=n},W=()=>{m.value=[...$.value],a.rec_coupon_ids=m.value.map(n=>n.id),V.value=!1},Z=n=>{m.value=m.value.filter(e=>e.id!==n),a.rec_coupon_ids=m.value.map(e=>e.id)},h=async()=>{try{const e=await(await fetch("/api/market/activity/info")).json();e.code===200&&(Object.assign(a,e.data),g.value=e.data.inv_user||[],p.value=e.data.inv_coupons||[],m.value=e.data.rec_coupons||[])}catch(n){console.error("获取活动信息失败:",n)}},ee=async()=>{try{await R.value.validate(),S.value=!0;const e=await(await fetch("/api/market/activity/save",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).json();e.code===200?B.success("保存成功"):B.error(e.message||"保存失败")}catch(n){console.error("保存失败:",n),B.error("保存失败")}finally{S.value=!1}},le=()=>{R.value.resetFields(),g.value=[],p.value=[],m.value=[]};return se(()=>{J(),h()}),(n,e)=>{const b=d("el-radio"),U=d("el-radio-group"),u=d("el-form-item"),te=d("el-date-picker"),w=d("el-input-number"),oe=d("el-checkbox"),ae=d("el-checkbox-group"),j=d("el-tag"),ne=d("el-form"),f=d("el-table-column"),I=d("el-table"),q=d("el-dialog");return s(),_("div",me,[l(ie),C("div",_e,[l(M,null,{default:t(()=>e[17]||(e[17]=[C("div",null,"推荐人：邀请一定数量的好友授权用户信息及手机号后才可获得相应奖励",-1),C("div",{style:{"margin-top":"8px"}},"被推荐人：被推荐人授权用户信息及手机号后即可获得相应奖励",-1)])),_:1,__:[17]}),l(ne,{onSubmit:e[12]||(e[12]=ue(()=>{},["prevent"])),model:a,rules:z,ref_key:"subFormRef",ref:R,"label-width":"140px",class:"basic-form"},{default:t(()=>[l(u,{label:"开启活动",prop:"status"},{default:t(()=>[l(U,{modelValue:a.status,"onUpdate:modelValue":e[0]||(e[0]=o=>a.status=o)},{default:t(()=>[l(b,{value:1},{default:t(()=>e[18]||(e[18]=[r("开启")])),_:1,__:[18]}),l(b,{value:0},{default:t(()=>e[19]||(e[19]=[r("关闭")])),_:1,__:[19]})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"分享图",prop:"share_img"},{default:t(()=>[l(de,{fileList:a.share_img,onSelectedFiles:H},null,8,["fileList"]),l(M,null,{default:t(()=>e[20]||(e[20]=[r("图片建议尺寸: 652 * 652")])),_:1,__:[20]})]),_:1}),l(u,{label:"活动时间",prop:"time"},{default:t(()=>[l(te,{modelValue:a.time,"onUpdate:modelValue":e[1]||(e[1]=o=>a.time=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":E},null,8,["modelValue"])]),_:1}),l(u,{label:"邀请好友数",prop:"inv_user_num"},{default:t(()=>[l(w,{modelValue:a.inv_user_num,"onUpdate:modelValue":e[2]||(e[2]=o=>a.inv_user_num=o),controls:!1,min:1,precision:0,placeholder:"请输入邀请好友数",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(u,{label:"邀请有效期",prop:"inv_time"},{default:t(()=>[l(w,{modelValue:a.inv_time,"onUpdate:modelValue":e[3]||(e[3]=o=>a.inv_time=o),controls:!1,min:1,precision:0,placeholder:"请输入邀请有效期",style:{width:"200px"}},null,8,["modelValue"]),e[21]||(e[21]=C("span",{style:{"margin-left":"8px"}},"小时",-1))]),_:1,__:[21]}),l(u,{label:"发起活动次数",prop:"atv_num"},{default:t(()=>[l(w,{modelValue:a.atv_num,"onUpdate:modelValue":e[4]||(e[4]=o=>a.atv_num=o),controls:!1,min:1,precision:0,placeholder:"请输入发起活动次数",style:{width:"200px"}},null,8,["modelValue"]),e[22]||(e[22]=C("span",{style:{"margin-left":"8px"}},"次/人",-1))]),_:1,__:[22]}),l(u,{label:"获得奖励人",prop:"inv_user"},{default:t(()=>[l(ae,{onChange:P,modelValue:g.value,"onUpdate:modelValue":e[5]||(e[5]=o=>g.value=o)},{default:t(()=>[(s(!0),_(x,null,F(Y.value,(o,D)=>(s(),_("div",{key:D,style:{display:"inline-block","margin-right":"15px"}},[l(oe,{disabled:!!o.is_check,label:o.title},null,8,["disabled","label"]),o.tips?(s(),y(M,{key:0},{default:t(()=>[r(L(o.tips),1)]),_:2},1024)):v("",!0)]))),128))]),_:1},8,["modelValue"])]),_:1}),g.value.includes("推荐人")?(s(),_(x,{key:0},[l(u,{label:"推荐人奖励类型",prop:"inv_reward_type"},{default:t(()=>[l(U,{modelValue:a.inv_reward_type,"onUpdate:modelValue":e[6]||(e[6]=o=>a.inv_reward_type=o)},{default:t(()=>[l(b,{value:1},{default:t(()=>e[23]||(e[23]=[r("卡券")])),_:1,__:[23]}),l(b,{value:2},{default:t(()=>e[24]||(e[24]=[r("积分")])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),a.inv_reward_type===1?(s(),y(u,{key:0,label:"推荐人奖励"},{default:t(()=>[l(c,{type:"primary",onClick:e[7]||(e[7]=o=>k.value=!0)},{default:t(()=>e[25]||(e[25]=[r(" 选择卡券 ")])),_:1,__:[25]}),p.value.length>0?(s(),_("div",ve,[(s(!0),_(x,null,F(p.value,o=>(s(),y(j,{key:o.id,closable:"",onClose:D=>X(o.id),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:t(()=>[r(L(o.title),1)]),_:2},1032,["onClose"]))),128))])):v("",!0)]),_:1})):v("",!0),a.inv_reward_type===2?(s(),y(u,{key:1,label:"推荐人积分"},{default:t(()=>[l(w,{modelValue:a.inv_points,"onUpdate:modelValue":e[8]||(e[8]=o=>a.inv_points=o),controls:!1,min:1,placeholder:"请输入积分数量",style:{width:"200px"}},null,8,["modelValue"])]),_:1})):v("",!0)],64)):v("",!0),g.value.includes("被推荐人")?(s(),_(x,{key:1},[l(u,{label:"被推荐人奖励类型",prop:"rec_reward_type"},{default:t(()=>[l(U,{modelValue:a.rec_reward_type,"onUpdate:modelValue":e[9]||(e[9]=o=>a.rec_reward_type=o)},{default:t(()=>[l(b,{value:1},{default:t(()=>e[26]||(e[26]=[r("卡券")])),_:1,__:[26]}),l(b,{value:2},{default:t(()=>e[27]||(e[27]=[r("积分")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),a.rec_reward_type===1?(s(),y(u,{key:0,label:"被推荐人奖励"},{default:t(()=>[l(c,{type:"primary",onClick:e[10]||(e[10]=o=>V.value=!0)},{default:t(()=>e[28]||(e[28]=[r(" 选择卡券 ")])),_:1,__:[28]}),m.value.length>0?(s(),_("div",fe,[(s(!0),_(x,null,F(m.value,o=>(s(),y(j,{key:o.id,closable:"",onClose:D=>Z(o.id),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:t(()=>[r(L(o.title),1)]),_:2},1032,["onClose"]))),128))])):v("",!0)]),_:1})):v("",!0),a.rec_reward_type===2?(s(),y(u,{key:1,label:"被推荐人积分"},{default:t(()=>[l(w,{modelValue:a.rec_points,"onUpdate:modelValue":e[11]||(e[11]=o=>a.rec_points=o),controls:!1,min:1,placeholder:"请输入积分数量",style:{width:"200px"}},null,8,["modelValue"])]),_:1})):v("",!0)],64)):v("",!0),l(u,null,{default:t(()=>[l(c,{type:"primary",onClick:ee,loading:S.value,size:"default"},{default:t(()=>e[29]||(e[29]=[r(" 保存活动设置 ")])),_:1,__:[29]},8,["loading"]),l(c,{onClick:le,style:{"margin-left":"12px"},size:"default"},{default:t(()=>e[30]||(e[30]=[r(" 重置 ")])),_:1,__:[30]})]),_:1})]),_:1},8,["model"])]),l(q,{modelValue:k.value,"onUpdate:modelValue":e[14]||(e[14]=o=>k.value=o),title:"选择推荐人奖励卡券",width:"60%"},{footer:t(()=>[l(c,{onClick:e[13]||(e[13]=o=>k.value=!1)},{default:t(()=>e[31]||(e[31]=[r("取消")])),_:1,__:[31]}),l(c,{type:"primary",onClick:Q},{default:t(()=>e[32]||(e[32]=[r("确定")])),_:1,__:[32]})]),default:t(()=>[l(I,{data:T.value,onSelectionChange:K,ref_key:"couponTableRef",ref:A},{default:t(()=>[l(f,{type:"selection",width:"55"}),l(f,{prop:"id",label:"卡券ID",width:"80"}),l(f,{prop:"title",label:"卡券名称"}),l(f,{prop:"discount",label:"优惠金额",width:"120"},{default:t(o=>[r(" ¥"+L(o.row.discount),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),l(q,{modelValue:V.value,"onUpdate:modelValue":e[16]||(e[16]=o=>V.value=o),title:"选择被推荐人奖励卡券",width:"60%"},{footer:t(()=>[l(c,{onClick:e[15]||(e[15]=o=>V.value=!1)},{default:t(()=>e[33]||(e[33]=[r("取消")])),_:1,__:[33]}),l(c,{type:"primary",onClick:W},{default:t(()=>e[34]||(e[34]=[r("确定")])),_:1,__:[34]})]),default:t(()=>[l(I,{data:T.value,onSelectionChange:G,ref_key:"recCouponTableRef",ref:O},{default:t(()=>[l(f,{type:"selection",width:"55"}),l(f,{prop:"id",label:"卡券ID",width:"80"}),l(f,{prop:"title",label:"卡券名称"}),l(f,{prop:"discount",label:"优惠金额",width:"120"},{default:t(o=>[r(" ¥"+L(o.row.discount),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}},Le=pe(ce,[["__scopeId","data-v-2ec66e8a"]]);export{Le as default};
