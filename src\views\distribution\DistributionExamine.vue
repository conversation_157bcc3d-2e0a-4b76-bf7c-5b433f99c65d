<!--
  分销审核页面
 /src/view/distribution/examine.vue重构
-->

<template>
  <div class="lb-goods-list">
    <TopNav />
    <div class="page-main">
      <!-- 状态筛选按钮 -->
      <el-row class="page-top-operate">
        <el-button
          @click="toChange(0)"
          :type="searchForm.status === 0 ? 'primary' : ''"
          size="default"
        >
          全部（{{ count.all || 0 }}）
        </el-button>
        <el-button
          @click="toChange(4)"
          :type="searchForm.status === 4 ? 'primary' : ''"
          size="default"
        >
          未授权（{{ count.nopass || 0 }}）
        </el-button>
        <el-button
          @click="toChange(1)"
          :type="searchForm.status === 1 ? 'primary' : ''"
          size="default"
        >
          申请中（{{ count.ing || 0 }}）
        </el-button>
        <el-button
          @click="toChange(2)"
          :type="searchForm.status === 2 ? 'primary' : ''"
          size="default"
        >
          已授权（{{ count.pass || 0 }}）
        </el-button>
      </el-row>
      
      <!-- 搜索表单 -->
      <el-row class="page-search-form">
        <el-form
          @submit.prevent
          :inline="true"
          :model="searchForm"
          ref="searchFormRef"
        >
          <el-form-item label="输入查询" prop="name">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入姓名/手机号"
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="申请时间" prop="start_time">
            <el-date-picker
              @change="getTableDataList(1)"
              v-model="range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <LbButton
              size="default"
              type="primary"
              style="margin-right: 5px"
              @click="getTableDataList(1)"
            >
              搜索
            </LbButton>
            <LbButton
              size="default"
              style="margin-right: 5px"
              @click="resetForm"
            >
              重置
            </LbButton>
          </el-form-item>
        </el-form>
      </el-row>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" fixed />
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="avatarUrl" label="头像" width="80">
          <template #default="scope">
            <img :src="scope.row.avatarUrl" alt="头像" style="width: 40px; height: 40px; border-radius: 50%;" />
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="昵称" min-width="120" />
        <el-table-column prop="user_name" label="姓名" min-width="120" />
        <el-table-column prop="mobile" label="手机号" width="130" />
        <el-table-column prop="id_code" label="身份证号" width="180" />
        <el-table-column prop="create_time" label="申请时间" width="170">
          <template #default="scope">
            <div>{{ formatDate(scope.row.create_time, 1) }}</div>
            <div>{{ formatDate(scope.row.create_time, 2) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="commission_total" label="累计佣金" width="120">
          <template #default="scope">
            <span style="color: #e6a23c; font-weight: 600;">¥{{ scope.row.commission_total || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invite_count" label="邀请人数" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="mini"
                type="primary"
                @click="viewDetail(scope.row)"
              >
                查看详情
              </LbButton>
              <LbButton
                v-if="scope.row.status === 1"
                size="mini"
                type="success"
                @click="approveDistribution(scope.row)"
              >
                通过审核
              </LbButton>
              <LbButton
                v-if="scope.row.status === 1"
                size="mini"
                type="danger"
                @click="rejectDistribution(scope.row)"
              >
                拒绝审核
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="分销商详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ distributorDetail.user_id }}</el-descriptions-item>
        <el-descriptions-item label="昵称">{{ distributorDetail.nickName }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ distributorDetail.user_name }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ distributorDetail.mobile }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ distributorDetail.id_code }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ distributorDetail.create_time }}</el-descriptions-item>
        <el-descriptions-item label="累计佣金">¥{{ distributorDetail.commission_total || 0 }}</el-descriptions-item>
        <el-descriptions-item label="邀请人数">{{ distributorDetail.invite_count || 0 }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="distributorDetail.id_card_images && distributorDetail.id_card_images.length > 0" style="margin-top: 20px;">
        <h4>身份证照片</h4>
        <div class="id-card-images">
          <img 
            v-for="(img, index) in distributorDetail.id_card_images" 
            :key="index"
            :src="img" 
            alt="身份证照片"
            style="width: 150px; height: 100px; margin-right: 8px; border-radius: 4px;"
          />
        </div>
      </div>
      
      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditVisible" title="分销商审核" width="50%" :close-on-click-modal="false">
      <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="100px">
        <el-form-item label="申请人">
          <span>{{ auditForm.user_name }}</span>
        </el-form-item>
        <el-form-item label="手机号">
          <span>{{ auditForm.mobile }}</span>
        </el-form-item>
        <el-form-item label="身份证号">
          <span>{{ auditForm.id_code }}</span>
        </el-form-item>
        <el-form-item label="申请时间">
          <span>{{ auditForm.create_time }}</span>
        </el-form-item>
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :value="2">通过审核</el-radio>
            <el-radio :value="4">拒绝申请</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核说明" prop="sh_text">
          <el-input
            v-model="auditForm.sh_text"
            type="textarea"
            :rows="4"
            placeholder="请输入审核说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <LbButton @click="auditVisible = false">取消</LbButton>
        <LbButton type="primary" @click="submitAudit" :loading="auditLoading">确定审核</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchFormRef = ref()
const range = ref([])
const detailVisible = ref(false)
const auditVisible = ref(false)
const auditFormRef = ref()
const auditLoading = ref(false)

// 统计数据
const count = reactive({
  all: 0,
  nopass: 0,
  ing: 0,
  pass: 0
})

// 搜索表单
const searchForm = reactive({
  name: '',
  status: 0,
  start_time: '',
  end_time: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 分销商详情
const distributorDetail = reactive({
  user_id: '',
  nickName: '',
  user_name: '',
  mobile: '',
  id_code: '',
  create_time: '',
  commission_total: 0,
  invite_count: 0,
  id_card_images: []
})

// 审核表单
const auditForm = reactive({
  id: '',
  user_name: '',
  mobile: '',
  id_code: '',
  create_time: '',
  status: 2,
  sh_text: ''
})

// 审核表单验证规则
const auditRules = {
  status: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  sh_text: [
    { required: true, message: '请输入审核说明', trigger: 'blur' }
  ]
}

// 方法
const getTableDataList = async (page = 1) => {
  loading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      status: searchForm.status,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time
    })
    
    const response = await fetch(`/api/distribution/examine/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
      // 更新统计数据
      Object.assign(count, result.data.count || {})
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取分销审核列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const toChange = (status) => {
  searchForm.status = status
  getTableDataList(1)
}

const resetForm = () => {
  searchForm.name = ''
  searchForm.status = 0
  searchForm.start_time = ''
  searchForm.end_time = ''
  range.value = []
  getTableDataList(1)
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 申请中
    2: 'success',  // 已授权
    3: 'danger',   // 已拒绝
    4: 'info'      // 未授权
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '申请中',
    2: '已授权',
    3: '已拒绝',
    4: '未授权'
  }
  return statusMap[status] || '未知'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const viewDetail = async (row) => {
  try {
    const response = await fetch(`/api/distribution/examine/detail/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(distributorDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取分销商详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const approveDistribution = (row) => {
  // 打开审核对话框
  Object.assign(auditForm, {
    id: row.id,
    user_name: row.user_name,
    mobile: row.mobile,
    id_code: row.id_code,
    create_time: row.create_time,
    status: 2,
    sh_text: ''
  })
  auditVisible.value = true
}

const rejectDistribution = (row) => {
  // 打开审核对话框
  Object.assign(auditForm, {
    id: row.id,
    user_name: row.user_name,
    mobile: row.mobile,
    id_code: row.id_code,
    create_time: row.create_time,
    status: 4,
    sh_text: ''
  })
  auditVisible.value = true
}

const submitAudit = async () => {
  try {
    await auditFormRef.value.validate()

    auditLoading.value = true

    const response = await fetch(`/api/distribution/examine/audit/${auditForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: auditForm.status,
        sh_text: auditForm.sh_text
      })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('审核成功')
      auditVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '审核失败')
    }
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  } finally {
    auditLoading.value = false
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getTableDataList(1)
}

const handleCurrentChange = (page) => {
  getTableDataList(page)
}

// 监听日期范围变化
const watchRange = () => {
  if (range.value && range.value.length === 2) {
    searchForm.start_time = range.value[0]
    searchForm.end_time = range.value[1]
  } else {
    searchForm.start_time = ''
    searchForm.end_time = ''
  }
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.lb-goods-list {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-top-operate {
  margin-bottom: 16px;
}

.page-top-operate .el-button {
  margin-right: 8px;
}

.page-search-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.id-card-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .lb-goods-list {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
