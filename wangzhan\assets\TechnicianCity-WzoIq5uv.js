import{E as s,z as he,A as Ce,q as J}from"./element-fdzwdDuf.js";import{T as Ie,L as m}from"./LbButton-BtU4V_Gr.js";import{_ as we,a as x}from"./index-C9Xz1oqp.js";import{r as y,c as be,X as K,h as ke,y as b,Q as l,A as d,I as o,al as h,J as Se,ar as xe,H as B,z as g,M as c,O as C,D as Ne,u as Q,K as W}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Ve={class:"technician-city"},Be={class:"content-container"},ze={class:"search-form-container"},De={class:"table-container"},Te={class:"city-name-cell"},$e=["onClick"],Pe={class:"city-count"},Re={key:1,class:"city-name"},Ee={key:0},Le={key:1,class:"text-muted"},Ae={key:0,class:"table-operate"},Oe={key:1,class:"text-muted"},Fe={class:"city-info"},Me={class:"dialog-footer"},Ue={class:"batch-add-container"},qe={class:"batch-header"},He={key:0,class:"batch-list"},Je={class:"batch-selector"},Ke={class:"dialog-footer"},Qe={__name:"TechnicianCity",setup(We){const z=y(!1),D=y(!1),T=y(!1),$=y([]),k=y([]),I=y([]),X=y(),E=y(),N=y(!1),V=y(!1),_=y([]),u=y([]),w=y(new Set),L=be(()=>{const t=[];return $.value.forEach(e=>{t.push({...e,isProvince:!0,isExpanded:w.value.has(e.cityId)}),w.value.has(e.cityId)&&e.children&&e.children.length>0&&e.children.forEach(n=>{t.push({...n,provinceName:e.name,isProvince:!1})})}),t}),j=K({}),a=K({selectedCity:[],cityId:"",cityStr:"",provinceName:"",cityName:""}),A={value:"cityId",label:"name",children:"children",leaf:"leaf",checkStrictly:!1,emitPath:!0},G={selectedCity:[{required:!0,message:"请选择城市",trigger:"change"}]},Y=t=>{w.value.has(t.cityId)?w.value.delete(t.cityId):w.value.add(t.cityId),console.log("🔄 切换省份展开状态:",t.name,"展开:",w.value.has(t.cityId))},Z=t=>t.children?t.children.length:0,ee=()=>!0,P=y(),te=t=>{console.log("🔄 选择变化:",t),I.value=t.filter(n=>n.leaf&&n.id),t.filter(n=>!n.leaf).forEach(n=>{w.value.has(n.cityId)||w.value.add(n.cityId),setTimeout(()=>{($.value.find(p=>p.cityId===n.cityId)?.children||[]).forEach(p=>{const r=L.value.find(f=>f.cityId===p.cityId&&f.leaf);r&&P.value&&P.value.toggleRowSelection(r,!0)})},100)})},le=async()=>{try{const t=await x.technician.cityTree();console.log("🏙️ 城市树形数据 (API-V2):",t),t.code===200||t.code==="200"?k.value=t.data||[]:(console.error("❌ 获取城市树形数据失败:",t),s.error(t.message||t.msg||"获取城市数据失败"))}catch(t){console.error("获取城市树形数据失败:",t),s.error("获取城市数据失败")}},S=async()=>{z.value=!0;try{const t=await x.technician.openCityList();console.log("📋 开放城市列表数据 (API-V2):",t),t.code===200||t.code==="200"?$.value=t.data||[]:(console.error("❌ 获取开放城市列表失败:",t),s.error(t.message||t.msg||"获取开放城市列表失败"))}catch(t){console.error("获取开放城市列表失败:",t),s.error("获取开放城市列表失败")}finally{z.value=!1}},oe=()=>{O(),N.value=!0},ae=()=>{u.value=[],_.value=[],V.value=!0},ne=()=>{S()},se=t=>{if(console.log("🔄 级联选择器值变化:",t),console.log("🏙️ 当前城市树数据:",k.value),t&&t.length===2){const[e,n]=t;console.log("📍 选中的省份cityId:",e,"城市cityId:",n);const v=k.value.find(r=>(console.log("🔍 比较省份:",r.cityId,"===",e,"结果:",r.cityId===e),r.cityId===e));console.log("🏛️ 找到的省份:",v);const p=v?.children?.find(r=>(console.log("🔍 比较城市:",r.cityId,"===",n,"结果:",r.cityId===n),r.cityId===n));console.log("🏙️ 找到的城市:",p),v&&p&&n!==null&&n!==void 0?(a.cityId=String(n),a.cityStr=`${e},${n}`,a.provinceName=v.name,a.cityName=p.name,console.log("✅ 表单数据已更新:"),console.log("   cityId:",a.cityId),console.log("   cityStr:",a.cityStr),console.log("   provinceName:",a.provinceName),console.log("   cityName:",a.cityName)):(console.log("❌ 未找到对应的省份或城市，清空表单"),a.cityId="",a.cityStr="",a.provinceName="",a.cityName="")}else console.log("❌ 选择值无效，清空表单"),a.cityId="",a.cityStr="",a.provinceName="",a.cityName=""},ie=()=>{},ce=()=>{if(!_.value||_.value.length!==2){s.warning("请先选择省份和城市");return}const[t,e]=_.value;if(e==null){s.warning("选择的城市数据无效");return}if(u.value.some(r=>r.cityId===String(e))){s.warning("该城市已在列表中");return}const v=k.value.find(r=>r.cityId===t),p=v?.children?.find(r=>r.cityId===e);v&&p?(u.value.push({cityId:String(e),cityStr:`${t},${e}`,provinceName:v.name,cityName:p.name}),_.value=[],s.success("城市已添加到列表")):s.error("找不到对应的省份或城市信息")},re=()=>{s.info("请使用下方的城市选择器添加城市")},de=t=>{u.value.splice(t,1),s.success("已从列表中移除")},ue=()=>{u.value=[],s.success("列表已清空")},fe=async()=>{try{if(await E.value.validate(),console.log("提交表单数据:",a.cityId,a.cityStr),!a.cityId||!a.cityStr){s.error("请选择有效的城市");return}D.value=!0;const t=await x.technician.addOpenCity({cityId:String(a.cityId),cityStr:String(a.cityStr)});t.code==="200"?(s.success("添加开放城市成功"),N.value=!1,S()):s.error(t.message||"添加失败")}catch(t){console.error("添加开放城市失败:",t),s.error("添加失败")}finally{D.value=!1}},ye=async()=>{if(u.value.length===0){s.warning("请先添加要开放的城市");return}try{T.value=!0;const t=await x.technician.batchAddOpenCity(u.value);t.code==="200"?(s.success(`批量添加 ${u.value.length} 个城市成功`),V.value=!1,S()):s.error(t.message||"批量添加失败")}catch(t){console.error("批量添加开放城市失败:",t),s.error("批量添加失败")}finally{T.value=!1}},me=async t=>{try{await J.confirm(`确定要移除开放城市"${t.name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await x.technician.deleteOpenCity({ids:[t.id]});e.code==="200"?(s.success("删除成功"),S()):s.error(e.msg||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除开放城市失败:",e),s.error("删除失败"))}},pe=async()=>{if(I.value.length===0){s.warning("请先选择要删除的城市");return}try{await J.confirm(`确定要删除选中的 ${I.value.length} 个开放城市吗？`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const t=I.value.map(n=>n.id),e=await x.technician.deleteOpenCity({ids:t});e.code==="200"?(s.success(`批量删除 ${I.value.length} 个城市成功`),I.value=[],S()):s.error(e.msg||"批量删除失败")}catch(t){t!=="cancel"&&(console.error("批量删除开放城市失败:",t),s.error("批量删除失败"))}},ge=()=>{O()},ve=()=>{u.value=[],_.value=[]},O=()=>{a.selectedCity=[],a.cityId="",a.cityStr="",a.provinceName="",a.cityName=""};return ke(()=>{le(),S()}),(t,e)=>{const n=h("el-form-item"),v=h("el-col"),p=h("el-row"),r=h("el-form"),f=h("el-table-column"),F=h("el-icon"),R=h("el-tag"),M=h("el-table"),U=h("el-cascader"),q=h("el-dialog"),_e=xe("loading");return g(),b("div",Ve,[l(Ie,{title:"城市管理"}),d("div",Be,[d("div",ze,[l(r,{ref_key:"searchFormRef",ref:X,model:j,inline:!0,class:"search-form"},{default:o(()=>[l(p,{gutter:20},{default:o(()=>[l(v,{span:24},{default:o(()=>[l(n,null,{default:o(()=>[l(m,{size:"default",type:"primary",icon:"Plus",onClick:oe},{default:o(()=>e[6]||(e[6]=[c(" 添加开放城市 ")])),_:1,__:[6]}),l(m,{size:"default",type:"success",icon:"Plus",onClick:ae},{default:o(()=>e[7]||(e[7]=[c(" 批量添加城市 ")])),_:1,__:[7]}),l(m,{size:"default",type:"danger",icon:"Delete",onClick:pe,disabled:I.value.length===0},{default:o(()=>[c(" 批量删除 ("+C(I.value.length)+") ",1)]),_:1},8,["disabled"]),l(m,{size:"default",type:"info",icon:"Refresh",onClick:ne},{default:o(()=>e[8]||(e[8]=[c(" 刷新列表 ")])),_:1,__:[8]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),d("div",De,[Se((g(),B(M,{ref_key:"tableRef",ref:P,data:L.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"},onSelectionChange:te},{default:o(()=>[l(f,{type:"selection",width:"55",selectable:ee}),l(f,{prop:"id",label:"ID",width:"80",align:"center"}),l(f,{prop:"name",label:"省份/城市","min-width":"200"},{default:o(i=>[d("div",Te,[i.row.leaf?(g(),b("span",Re,[e[9]||(e[9]=d("span",{class:"city-indent"},"└─",-1)),l(R,{type:"success",size:"default"},{default:o(()=>[c(C(i.row.name),1)]),_:2},1024)])):(g(),b("span",{key:0,class:"province-name",onClick:H=>Y(i.row)},[l(F,{class:Ne(["expand-icon",{expanded:i.row.isExpanded}])},{default:o(()=>[l(Q(he))]),_:2},1032,["class"]),l(F,null,{default:o(()=>[l(Q(Ce))]),_:1}),c(" "+C(i.row.name)+" ",1),d("span",Pe,"("+C(Z(i.row))+"个城市)",1)],8,$e))])]),_:1}),l(f,{prop:"cityId",label:"城市ID",width:"100",align:"center"},{default:o(i=>[i.row.leaf?(g(),b("span",Ee,C(i.row.cityId),1)):(g(),b("span",Le,"—"))]),_:1}),l(f,{prop:"leaf",label:"类型",width:"100",align:"center"},{default:o(i=>[i.row.leaf?(g(),B(R,{key:0,type:"success",size:"default"},{default:o(()=>e[10]||(e[10]=[c("城市")])),_:1,__:[10]})):(g(),B(R,{key:1,type:"primary",size:"default"},{default:o(()=>e[11]||(e[11]=[c("省份")])),_:1,__:[11]}))]),_:1}),l(f,{label:"操作",width:"120",fixed:"right"},{default:o(i=>[i.row.leaf?(g(),b("div",Ae,[l(m,{size:"default",type:"danger",onClick:H=>me(i.row)},{default:o(()=>e[12]||(e[12]=[c(" 移除 ")])),_:2,__:[12]},1032,["onClick"])])):(g(),b("span",Oe,"—"))]),_:1})]),_:1},8,["data"])),[[_e,z.value]])])]),l(q,{title:"添加开放城市",modelValue:N.value,"onUpdate:modelValue":e[2]||(e[2]=i=>N.value=i),width:"600px",onClose:ge},{footer:o(()=>[d("span",Me,[l(m,{onClick:e[1]||(e[1]=i=>N.value=!1)},{default:o(()=>e[17]||(e[17]=[c("取消")])),_:1,__:[17]}),l(m,{type:"primary",onClick:fe,loading:D.value},{default:o(()=>e[18]||(e[18]=[c(" 确定 ")])),_:1,__:[18]},8,["loading"])])]),default:o(()=>[l(r,{ref_key:"formRef",ref:E,model:a,rules:G,"label-width":"100px"},{default:o(()=>[l(n,{label:"选择城市",prop:"selectedCity"},{default:o(()=>[l(U,{modelValue:a.selectedCity,"onUpdate:modelValue":e[0]||(e[0]=i=>a.selectedCity=i),options:k.value,props:A,placeholder:"请选择省份和城市",style:{width:"100%"},onChange:se},null,8,["modelValue","options"])]),_:1}),a.cityId?(g(),B(n,{key:0,label:"城市信息"},{default:o(()=>[d("div",Fe,[d("p",null,[e[13]||(e[13]=d("strong",null,"省份：",-1)),c(C(a.provinceName),1)]),d("p",null,[e[14]||(e[14]=d("strong",null,"城市：",-1)),c(C(a.cityName),1)]),d("p",null,[e[15]||(e[15]=d("strong",null,"城市ID：",-1)),c(C(a.cityId),1)]),d("p",null,[e[16]||(e[16]=d("strong",null,"城市字符串：",-1)),c(C(a.cityStr),1)])])]),_:1})):W("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(q,{title:"批量添加开放城市",modelValue:V.value,"onUpdate:modelValue":e[5]||(e[5]=i=>V.value=i),width:"800px",onClose:ve},{footer:o(()=>[d("span",Ke,[l(m,{onClick:e[4]||(e[4]=i=>V.value=!1)},{default:o(()=>e[23]||(e[23]=[c("取消")])),_:1,__:[23]}),l(m,{type:"primary",onClick:ye,loading:T.value,disabled:u.value.length===0},{default:o(()=>[c(" 批量添加 ("+C(u.value.length)+") ",1)]),_:1},8,["loading","disabled"])])]),default:o(()=>[d("div",Ue,[d("div",qe,[l(m,{size:"default",type:"primary",icon:"Plus",onClick:re},{default:o(()=>e[19]||(e[19]=[c(" 添加城市 ")])),_:1,__:[19]}),l(m,{size:"default",type:"danger",icon:"Delete",onClick:ue,disabled:u.value.length===0},{default:o(()=>e[20]||(e[20]=[c(" 清空列表 ")])),_:1,__:[20]},8,["disabled"])]),u.value.length>0?(g(),b("div",He,[l(M,{data:u.value,style:{width:"100%"},"max-height":"300"},{default:o(()=>[l(f,{prop:"provinceName",label:"省份",width:"120"}),l(f,{prop:"cityName",label:"城市",width:"120"}),l(f,{prop:"cityId",label:"城市ID",width:"100"}),l(f,{prop:"cityStr",label:"城市字符串",width:"120"}),l(f,{label:"操作",width:"80"},{default:o(i=>[l(m,{size:"default",type:"danger",onClick:H=>de(i.$index)},{default:o(()=>e[21]||(e[21]=[c(" 删除 ")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):W("",!0),d("div",Je,[l(r,{inline:!0},{default:o(()=>[l(n,{label:"选择城市"},{default:o(()=>[l(U,{modelValue:_.value,"onUpdate:modelValue":e[3]||(e[3]=i=>_.value=i),options:k.value,props:A,placeholder:"请选择省份和城市",style:{width:"300px"},onChange:ie},null,8,["modelValue","options"])]),_:1}),l(n,null,{default:o(()=>[l(m,{size:"default",type:"success",onClick:ce,disabled:!_.value||_.value.length!==2},{default:o(()=>e[22]||(e[22]=[c(" 添加到列表 ")])),_:1,__:[22]},8,["disabled"])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])])}}},et=we(Qe,[["__scopeId","data-v-1fe02d23"]]);export{et as default};
