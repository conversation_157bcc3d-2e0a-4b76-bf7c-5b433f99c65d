import{E as r,x as ve,q as ye}from"./element-fdzwdDuf.js";import{T as be,L as v}from"./LbButton-BtU4V_Gr.js";import{L as he}from"./LbImage-CnNh5Udj.js";import{L as ke}from"./LbPage-DnbiQ0Ct.js";import{_ as Ve,a as h}from"./index-C9Xz1oqp.js";import{r as d,X as E,c as we,h as Ce,y as B,Q as t,A as y,I as a,al as u,J as Se,ar as ze,H as M,z as V,M as c,O as F,K as xe,u as Le}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Ie={class:"service-banner"},De={class:"content-container"},Ne={class:"search-form-container"},Ue={class:"table-container"},Be={key:1,class:"no-data"},Fe={class:"table-operate"},Re={key:0,class:"upload-progress"},Te={class:"dialog-footer"},$e={__name:"ServiceBanner",setup(Ae){const S=d(!1),z=d(!1),x=d([]),L=d(0),R=d(),T=d(),b=d(!1),k=d([]),g=d(0),I=d(!1),D=d(!1);d(!1);const N=d(!1),H=d(!1),i=E({pageNum:1,pageSize:10,status:null,type:null}),o=E({id:null,img:"",link:"",top:0,type:1,status:1}),q={img:[{required:!0,message:"请上传轮播图片",trigger:"change"}]},O=we(()=>o.id?"编辑轮播图":"新增轮播图"),f=async l=>{if(D.value){console.log("⚠️ API正在调用中，跳过重复请求");return}l&&(i.pageNum=1),D.value=!0,S.value=!0;try{const e={pageNum:i.pageNum,pageSize:i.pageSize};i.status!==null&&i.status!==""&&(e.status=i.status),i.type!==null&&i.type!==""&&(e.type=i.type),console.log("🔍 轮播图查询参数:",e);const n=await h.service.bannerList(e);if(console.log("📋 轮播图列表数据:",n),n.code==="200"){const m=n.data;x.value=(m.list||[]).map(p=>({...p,originalStatus:p.status,status:p.status===0?1:p.status,statusLoading:!1})),L.value=m.totalCount||m.total||0,console.log("📊 轮播图处理后的数据:",{list:x.value,total:L.value,pageNum:m.pageNum,pageSize:m.pageSize}),setTimeout(()=>{H.value=!0,console.log("✅ 页面初始化完成，状态切换功能已启用")},100)}else console.error("❌ 轮播图API响应错误:",n),r.error(n.message||n.msg||"获取数据失败")}catch(e){console.error("获取轮播图列表失败:",e),r.error("获取数据失败")}finally{S.value=!1,D.value=!1}},j=()=>{f(1)},J=()=>{i.status=null,i.type=null,R.value?.resetFields(),f(1)},K=()=>{U(),b.value=!0},Q=l=>{U(),o.id=l.id,o.img=l.img||"",o.link=l.link||"",o.top=l.top||0,o.type=l.type||1,o.status=l.originalStatus!==void 0?l.originalStatus:l.status,l.img&&(k.value=[{name:"image",url:l.img}]),console.log("📝 编辑轮播图，使用列表数据:",{id:o.id,img:o.img,link:o.link,top:o.top,type:o.type,status:o.status}),b.value=!0},W=async l=>{try{await ye.confirm("确定要删除这个轮播图吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await h.service.bannerDelete({id:l.id});e.code==="200"?(r.success("删除成功"),f()):r.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除轮播图失败:",e),r.error("删除失败"))}},X=async l=>{if(l.statusLoading)return;const e=l.status===1?-1:1;try{l.statusLoading=!0,console.log(`🔄 切换轮播图状态: ID=${l.id}, 当前状态=${l.status}`);const n=await h.service.bannerStatus({id:l.id,status:l.status});console.log("📊 状态切换API响应:",n),n.code==="200"?(r.success("状态修改成功"),console.log(`✅ 状态切换成功: ID=${l.id}, 新状态=${l.status}`),await f()):(l.status=e,console.error("❌ 状态切换失败:",n),r.error(n.message||n.msg||"状态修改失败"))}catch(n){l.status=e,console.error("❌ 状态切换异常:",n),r.error("状态修改失败")}finally{l.statusLoading=!1}},G=async()=>{if(N.value){console.log("⚠️ 表单正在提交中，跳过重复请求");return}try{await T.value.validate(),z.value=!0,N.value=!0;let l;o.id?(l=await h.service.bannerUpdate(o),console.log("✏️ 编辑轮播图提交:",o)):(l=await h.service.bannerAdd(o),console.log("➕ 新增轮播图提交:",o)),l.code==="200"?(r.success(o.id?"更新成功":"新增成功"),b.value=!1,f()):r.error(l.message||"操作失败")}catch(l){console.error("提交失败:",l),r.error("操作失败")}finally{z.value=!1,N.value=!1}},Y=l=>{i.pageSize=l,$(1)},$=l=>{i.pageNum=l,f()},Z=l=>(console.log("📋 图片上传前验证:",l),l.type.indexOf("image/")===0?(console.log("✅ 图片验证通过"),!0):(r.error("只能上传图片文件!"),!1)),ee=async(l,e)=>{console.log("🖼️ 图片文件变更:",l,e),l.status==="ready"&&await te(l)},le=(l,e)=>{console.log("🗑️ 移除图片:",l),o.img="",g.value=0},te=async l=>{console.log("📤 开始上传图片:",l);try{I.value=!0,g.value=0;const e=new FormData;e.append("multipartFile",l.raw),console.log("📦 FormData创建完成:",e);const n=await h.upload.uploadFile(e,m=>{g.value=Math.round(m.loaded*100/m.total),console.log("📊 上传进度:",g.value+"%")});if(console.log("✅ 图片上传成功:",n),n.code===200||n.code==="200")o.img=n.data.url||n.data.fileUrl||n.data,r.success("图片上传成功"),k.value=[{name:l.name,url:o.img,status:"success"}],console.log("💾 图片URL已保存到表单:",o.img);else throw new Error(n.message||n.msg||"上传失败")}catch(e){console.error("❌ 图片上传失败:",e),r.error("图片上传失败: "+(e.message||"未知错误")),k.value=[],o.img=""}finally{I.value=!1,g.value=0}},ae=()=>{U()},U=()=>{o.id=null,o.img="",o.link="",o.top=0,o.type=1,o.status=1,k.value=[],g.value=0,I.value=!1};return Ce(()=>{f(1)}),(l,e)=>{const n=u("el-option"),m=u("el-select"),p=u("el-form-item"),oe=u("el-col"),se=u("el-row"),A=u("el-form"),_=u("el-table-column"),ne=u("el-link"),ie=u("el-tag"),ue=u("el-switch"),re=u("el-table"),de=u("el-icon"),pe=u("el-upload"),ce=u("el-progress"),me=u("el-input"),ge=u("el-input-number"),w=u("el-radio"),P=u("el-radio-group"),fe=u("el-dialog"),_e=ze("loading");return V(),B("div",Ie,[t(be,{title:"轮播图管理"}),y("div",De,[y("div",Ne,[t(A,{ref_key:"searchFormRef",ref:R,model:i,inline:!0,class:"search-form"},{default:a(()=>[t(se,{gutter:20},{default:a(()=>[t(oe,{span:24},{default:a(()=>[t(p,{label:"状态",prop:"status"},{default:a(()=>[t(m,{size:"default",modelValue:i.status,"onUpdate:modelValue":e[0]||(e[0]=s=>i.status=s),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[t(n,{label:"可用",value:1}),t(n,{label:"不可用",value:-1})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"类型",prop:"type"},{default:a(()=>[t(m,{size:"default",modelValue:i.type,"onUpdate:modelValue":e[1]||(e[1]=s=>i.type=s),placeholder:"请选择类型",clearable:"",style:{width:"120px"}},{default:a(()=>[t(n,{label:"用户端",value:1}),t(n,{label:"师傅端",value:2})]),_:1},8,["modelValue"])]),_:1}),t(p,null,{default:a(()=>[t(v,{size:"default",type:"primary",icon:"Search",onClick:j},{default:a(()=>e[8]||(e[8]=[c(" 搜索 ")])),_:1,__:[8]}),t(v,{size:"default",icon:"RefreshLeft",onClick:J},{default:a(()=>e[9]||(e[9]=[c(" 重置 ")])),_:1,__:[9]}),t(v,{size:"default",type:"primary",icon:"Plus",onClick:K},{default:a(()=>e[10]||(e[10]=[c(" 新增轮播图 ")])),_:1,__:[10]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),y("div",Ue,[Se((V(),M(re,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[t(_,{prop:"id",label:"ID",width:"60"}),t(_,{prop:"img",label:"轮播图",width:"300"},{default:a(s=>[t(he,{src:s.row.img,width:"120",height:"50"},null,8,["src"])]),_:1}),t(_,{prop:"link",label:"链接地址","min-width":"200"},{default:a(s=>[s.row.link?(V(),M(ne,{key:0,href:s.row.link,target:"_blank",type:"primary"},{default:a(()=>[c(F(s.row.link),1)]),_:2},1032,["href"])):(V(),B("span",Be,"无链接"))]),_:1}),t(_,{prop:"top",label:"排序",width:"80",align:"center"}),t(_,{prop:"type",label:"类型",width:"100",align:"center"},{default:a(s=>[t(ie,{type:s.row.type===1?"primary":"success",size:"default"},{default:a(()=>[c(F(s.row.type===1?"用户端":"师傅端"),1)]),_:2},1032,["type"])]),_:1}),t(_,{label:"状态",width:"120",align:"center"},{default:a(s=>[t(ue,{modelValue:s.row.status,"onUpdate:modelValue":C=>s.row.status=C,"active-value":1,"inactive-value":-1,loading:s.row.statusLoading,onChange:C=>X(s.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),t(_,{label:"操作",width:"200",fixed:"right"},{default:a(s=>[y("div",Fe,[t(v,{size:"default",type:"primary",onClick:C=>Q(s.row)},{default:a(()=>e[11]||(e[11]=[c(" 编辑 ")])),_:2,__:[11]},1032,["onClick"]),t(v,{size:"default",type:"danger",onClick:C=>W(s.row)},{default:a(()=>e[12]||(e[12]=[c(" 删除 ")])),_:2,__:[12]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[_e,S.value]])]),t(ke,{page:i.pageNum,"page-size":i.pageSize,total:L.value,onHandleSizeChange:Y,onHandleCurrentChange:$},null,8,["page","page-size","total"])]),t(fe,{title:O.value,modelValue:b.value,"onUpdate:modelValue":e[7]||(e[7]=s=>b.value=s),width:"600px",onClose:ae},{footer:a(()=>[y("span",Te,[t(v,{onClick:e[6]||(e[6]=s=>b.value=!1)},{default:a(()=>e[18]||(e[18]=[c("取消")])),_:1,__:[18]}),t(v,{type:"primary",onClick:G,loading:z.value},{default:a(()=>e[19]||(e[19]=[c(" 确定 ")])),_:1,__:[19]},8,["loading"])])]),default:a(()=>[t(A,{ref_key:"formRef",ref:T,model:o,rules:q,"label-width":"100px"},{default:a(()=>[t(p,{label:"轮播图片",prop:"img"},{default:a(()=>[t(pe,{class:"image-upload",action:"#","auto-upload":!1,"on-change":ee,"on-remove":le,"before-upload":Z,"file-list":k.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:a(()=>e[13]||(e[13]=[y("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:a(()=>[t(de,null,{default:a(()=>[t(Le(ve))]),_:1})]),_:1},8,["file-list"]),g.value>0&&g.value<100?(V(),B("div",Re,[t(ce,{percentage:g.value,"show-text":!0},null,8,["percentage"]),y("p",null,"上传中... "+F(g.value)+"%",1)])):xe("",!0)]),_:1}),t(p,{label:"链接地址",prop:"link"},{default:a(()=>[t(me,{modelValue:o.link,"onUpdate:modelValue":e[2]||(e[2]=s=>o.link=s),placeholder:"请输入链接地址（可选）"},null,8,["modelValue"])]),_:1}),t(p,{label:"排序值",prop:"top"},{default:a(()=>[t(ge,{modelValue:o.top,"onUpdate:modelValue":e[3]||(e[3]=s=>o.top=s),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(p,{label:"类型",prop:"type"},{default:a(()=>[t(P,{modelValue:o.type,"onUpdate:modelValue":e[4]||(e[4]=s=>o.type=s)},{default:a(()=>[t(w,{value:1},{default:a(()=>e[14]||(e[14]=[c("用户端")])),_:1,__:[14]}),t(w,{value:2},{default:a(()=>e[15]||(e[15]=[c("师傅端")])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"状态",prop:"status"},{default:a(()=>[t(P,{modelValue:o.status,"onUpdate:modelValue":e[5]||(e[5]=s=>o.status=s)},{default:a(()=>[t(w,{value:1},{default:a(()=>e[16]||(e[16]=[c("可用")])),_:1,__:[16]}),t(w,{value:-1},{default:a(()=>e[17]||(e[17]=[c("不可用")])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},Je=Ve($e,[["__scopeId","data-v-156d7dd8"]]);export{Je as default};
