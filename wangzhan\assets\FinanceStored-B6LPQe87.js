import{r as y,X as C,h as oe,y as $,Q as t,A as o,J as se,I as a,al as d,ar as ne,H as re,az as ie,z as j,O as n,V as de,M as r}from"./vendor-DmFBDimT.js";import{T as ue,L as v}from"./LbButton-BtU4V_Gr.js";import{_ as _e}from"./index-C9Xz1oqp.js";import{E as f}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const me={class:"lb-finance-stored"},ce={class:"page-main"},pe={class:"stat-content"},fe={class:"stat-value"},ge={class:"stat-content"},be={class:"stat-value"},ve={class:"stat-content"},we={class:"stat-value"},ye={class:"stat-content"},ke={class:"stat-value"},Ve=["src"],he={style:{color:"#67c23a","font-weight":"600"}},Ce={style:{color:"#e6a23c","font-weight":"600"}},ze={style:{color:"#409eff","font-weight":"600"}},Ne={key:0},je={key:1},Se={class:"table-operate"},xe={class:"pagination-section"},De={__name:"FinanceStored",setup(Ue){const I=ie(),S=y(!1),F=y([]),P=y(),x=y([]),z=y(!1),V=y(!1),R=y(),D=y(!1),h=C({total_users:0,total_amount:0,used_amount:0,balance_amount:0}),i=C({nickName:"",mobile:"",start_time:"",end_time:""}),c=C({page:1,pageSize:10,total:0}),g=C({id:"",nickName:"",mobile:"",create_time:"",total_recharge:0,total_consume:0,current_balance:0,recharge_count:0}),_=C({id:"",nickName:"",current_balance:0,type:1,amount:0,reason:""}),Y={type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"}],reason:[{required:!0,message:"请输入调整原因",trigger:"blur"}]},k=async(s=1)=>{S.value=!0,c.page=s;try{const e=new URLSearchParams({page:c.page,pageSize:c.pageSize,nickName:i.nickName,mobile:i.mobile,start_time:i.start_time,end_time:i.end_time}),p=await(await fetch(`/api/finance/stored/list?${e}`)).json();p.code===200?(F.value=p.data.list||[],c.total=p.data.total||0,Object.assign(h,p.data.stats||{})):f.error(p.message||"获取数据失败")}catch(e){console.error("获取储值列表失败:",e),f.error("获取数据失败")}finally{S.value=!1}},q=()=>{i.nickName="",i.mobile="",i.start_time="",i.end_time="",x.value=[],k(1)},N=(s,e)=>{if(!s)return"";const u=new Date(s);return e===1?u.toLocaleDateString():u.toLocaleTimeString()},E=async s=>{try{const u=await(await fetch(`/api/finance/stored/detail/${s.id}`)).json();u.code===200?(Object.assign(g,u.data),z.value=!0):f.error(u.message||"获取用户详情失败")}catch(e){console.error("获取用户详情失败:",e),f.error("获取用户详情失败")}},A=s=>{I.push(`/finance/stored/records?user_id=${s.id}`)},J=s=>{Object.assign(_,{id:s.id,nickName:s.nickName,current_balance:s.current_balance,type:1,amount:0,reason:""}),V.value=!0},H=async()=>{try{await R.value.validate(),D.value=!0;const e=await(await fetch(`/api/finance/stored/adjust/${_.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:_.type,amount:_.amount,reason:_.reason})})).json();e.code===200?(f.success("余额调整成功"),V.value=!1,k()):f.error(e.message||"调整失败")}catch(s){console.error("余额调整失败:",s),f.error("调整失败")}finally{D.value=!1}},Q=async()=>{try{const s=new URLSearchParams({nickName:i.nickName,mobile:i.mobile,start_time:i.start_time,end_time:i.end_time}),u=await(await fetch(`/api/finance/stored/export?${s}`)).json();u.code===200?f.success("导出成功"):f.error(u.message||"导出失败")}catch(s){console.error("导出数据失败:",s),f.error("导出失败")}},X=s=>{c.pageSize=s,k(1)},G=s=>{k(s)};return oe(()=>{k()}),(s,e)=>{const u=d("el-card"),p=d("el-col"),L=d("el-row"),U=d("el-input"),b=d("el-form-item"),K=d("el-date-picker"),T=d("el-form"),m=d("el-table-column"),W=d("el-table"),Z=d("el-pagination"),w=d("el-descriptions-item"),ee=d("el-descriptions"),B=d("el-dialog"),M=d("el-radio"),te=d("el-radio-group"),ae=d("el-input-number"),le=ne("loading");return j(),$("div",me,[t(ue),o("div",ce,[t(L,{gutter:20,class:"stats-cards"},{default:a(()=>[t(p,{span:6},{default:a(()=>[t(u,{class:"stat-card"},{default:a(()=>[o("div",pe,[o("div",fe,n(h.total_users||0),1),e[15]||(e[15]=o("div",{class:"stat-label"},"储值用户",-1))]),e[16]||(e[16]=o("div",{class:"stats-icon users"},[o("i",{class:"el-icon-user"})],-1))]),_:1,__:[16]})]),_:1}),t(p,{span:6},{default:a(()=>[t(u,{class:"stat-card"},{default:a(()=>[o("div",ge,[o("div",be,"¥"+n(h.total_amount||0),1),e[17]||(e[17]=o("div",{class:"stat-label"},"储值总额",-1))]),e[18]||(e[18]=o("div",{class:"stats-icon amount"},[o("i",{class:"el-icon-money"})],-1))]),_:1,__:[18]})]),_:1}),t(p,{span:6},{default:a(()=>[t(u,{class:"stat-card"},{default:a(()=>[o("div",ve,[o("div",we,"¥"+n(h.used_amount||0),1),e[19]||(e[19]=o("div",{class:"stat-label"},"已消费",-1))]),e[20]||(e[20]=o("div",{class:"stats-icon used"},[o("i",{class:"el-icon-shopping-cart-2"})],-1))]),_:1,__:[20]})]),_:1}),t(p,{span:6},{default:a(()=>[t(u,{class:"stat-card"},{default:a(()=>[o("div",ye,[o("div",ke,"¥"+n(h.balance_amount||0),1),e[21]||(e[21]=o("div",{class:"stat-label"},"余额",-1))]),e[22]||(e[22]=o("div",{class:"stats-icon balance"},[o("i",{class:"el-icon-wallet"})],-1))]),_:1,__:[22]})]),_:1})]),_:1}),t(L,{class:"page-search-form"},{default:a(()=>[t(T,{onSubmit:e[5]||(e[5]=de(()=>{},["prevent"])),inline:!0,model:i,ref_key:"searchFormRef",ref:P},{default:a(()=>[t(b,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(U,{modelValue:i.nickName,"onUpdate:modelValue":e[0]||(e[0]=l=>i.nickName=l),placeholder:"请输入用户昵称",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(b,{label:"手机号",prop:"mobile"},{default:a(()=>[t(U,{modelValue:i.mobile,"onUpdate:modelValue":e[1]||(e[1]=l=>i.mobile=l),placeholder:"请输入手机号",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(b,{label:"储值时间",prop:"date_range"},{default:a(()=>[t(K,{modelValue:x.value,"onUpdate:modelValue":e[2]||(e[2]=l=>x.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:e[3]||(e[3]=l=>k(1))},null,8,["modelValue"])]),_:1}),t(b,null,{default:a(()=>[t(v,{size:"default",type:"primary",style:{"margin-right":"5px"},onClick:e[4]||(e[4]=l=>k(1))},{default:a(()=>e[23]||(e[23]=[r(" 搜索 ")])),_:1,__:[23]}),t(v,{size:"default",style:{"margin-right":"5px"},onClick:q},{default:a(()=>e[24]||(e[24]=[r(" 重置 ")])),_:1,__:[24]}),t(v,{size:"default",type:"success",onClick:Q},{default:a(()=>e[25]||(e[25]=[r(" 导出数据 ")])),_:1,__:[25]})]),_:1})]),_:1},8,["model"])]),_:1}),se((j(),re(W,{data:F.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:a(()=>[t(m,{prop:"id",label:"ID",width:"80",fixed:""}),t(m,{prop:"avatarUrl",label:"头像",width:"80"},{default:a(l=>[o("img",{src:l.row.avatarUrl,alt:"头像",style:{width:"40px",height:"40px","border-radius":"50%"}},null,8,Ve)]),_:1}),t(m,{prop:"nickName",label:"用户昵称",width:"150"}),t(m,{prop:"mobile",label:"手机号",width:"130"}),t(m,{prop:"total_recharge",label:"累计储值",width:"120"},{default:a(l=>[o("span",he,"¥"+n(l.row.total_recharge||0),1)]),_:1}),t(m,{prop:"total_consume",label:"累计消费",width:"120"},{default:a(l=>[o("span",Ce,"¥"+n(l.row.total_consume||0),1)]),_:1}),t(m,{prop:"current_balance",label:"当前余额",width:"120"},{default:a(l=>[o("span",ze,"¥"+n(l.row.current_balance||0),1)]),_:1}),t(m,{prop:"recharge_count",label:"储值次数",width:"100"}),t(m,{prop:"last_recharge_time",label:"最后储值",width:"170"},{default:a(l=>[l.row.last_recharge_time?(j(),$("div",Ne,[o("div",null,n(N(l.row.last_recharge_time,1)),1),o("div",null,n(N(l.row.last_recharge_time,2)),1)])):(j(),$("span",je,"-"))]),_:1}),t(m,{prop:"create_time",label:"注册时间",width:"170"},{default:a(l=>[o("div",null,n(N(l.row.create_time,1)),1),o("div",null,n(N(l.row.create_time,2)),1)]),_:1}),t(m,{label:"操作",width:"200",fixed:"right"},{default:a(l=>[o("div",Se,[t(v,{size:"mini",type:"primary",onClick:O=>E(l.row)},{default:a(()=>e[26]||(e[26]=[r(" 查看详情 ")])),_:2,__:[26]},1032,["onClick"]),t(v,{size:"mini",type:"success",onClick:O=>A(l.row)},{default:a(()=>e[27]||(e[27]=[r(" 储值记录 ")])),_:2,__:[27]},1032,["onClick"]),t(v,{size:"mini",type:"warning",onClick:O=>J(l.row)},{default:a(()=>e[28]||(e[28]=[r(" 余额调整 ")])),_:2,__:[28]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[le,S.value]]),o("div",xe,[t(Z,{"current-page":c.page,"onUpdate:currentPage":e[6]||(e[6]=l=>c.page=l),"page-size":c.pageSize,"onUpdate:pageSize":e[7]||(e[7]=l=>c.pageSize=l),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),t(B,{modelValue:z.value,"onUpdate:modelValue":e[9]||(e[9]=l=>z.value=l),title:"用户储值详情",width:"60%"},{footer:a(()=>[t(v,{onClick:e[8]||(e[8]=l=>z.value=!1)},{default:a(()=>e[29]||(e[29]=[r("关闭")])),_:1,__:[29]})]),default:a(()=>[t(ee,{column:2,border:""},{default:a(()=>[t(w,{label:"用户ID"},{default:a(()=>[r(n(g.id),1)]),_:1}),t(w,{label:"用户昵称"},{default:a(()=>[r(n(g.nickName),1)]),_:1}),t(w,{label:"手机号"},{default:a(()=>[r(n(g.mobile),1)]),_:1}),t(w,{label:"注册时间"},{default:a(()=>[r(n(g.create_time),1)]),_:1}),t(w,{label:"累计储值"},{default:a(()=>[r("¥"+n(g.total_recharge||0),1)]),_:1}),t(w,{label:"累计消费"},{default:a(()=>[r("¥"+n(g.total_consume||0),1)]),_:1}),t(w,{label:"当前余额"},{default:a(()=>[r("¥"+n(g.current_balance||0),1)]),_:1}),t(w,{label:"储值次数"},{default:a(()=>[r(n(g.recharge_count||0),1)]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(B,{modelValue:V.value,"onUpdate:modelValue":e[14]||(e[14]=l=>V.value=l),title:"余额调整",width:"40%"},{footer:a(()=>[t(v,{onClick:e[13]||(e[13]=l=>V.value=!1)},{default:a(()=>e[32]||(e[32]=[r("取消")])),_:1,__:[32]}),t(v,{type:"primary",onClick:H,loading:D.value},{default:a(()=>e[33]||(e[33]=[r("确定调整")])),_:1,__:[33]},8,["loading"])]),default:a(()=>[t(T,{model:_,rules:Y,ref_key:"adjustFormRef",ref:R},{default:a(()=>[t(b,{label:"用户昵称"},{default:a(()=>[o("span",null,n(_.nickName),1)]),_:1}),t(b,{label:"当前余额"},{default:a(()=>[o("span",null,"¥"+n(_.current_balance),1)]),_:1}),t(b,{label:"调整类型",prop:"type"},{default:a(()=>[t(te,{modelValue:_.type,"onUpdate:modelValue":e[10]||(e[10]=l=>_.type=l)},{default:a(()=>[t(M,{value:1},{default:a(()=>e[30]||(e[30]=[r("增加余额")])),_:1,__:[30]}),t(M,{value:2},{default:a(()=>e[31]||(e[31]=[r("减少余额")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1}),t(b,{label:"调整金额",prop:"amount"},{default:a(()=>[t(ae,{modelValue:_.amount,"onUpdate:modelValue":e[11]||(e[11]=l=>_.amount=l),min:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(b,{label:"调整原因",prop:"reason"},{default:a(()=>[t(U,{modelValue:_.reason,"onUpdate:modelValue":e[12]||(e[12]=l=>_.reason=l),type:"textarea",rows:3,placeholder:"请输入调整原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Be=_e(De,[["__scopeId","data-v-25a5b526"]]);export{Be as default};
