<!--
  服务点设置页面
  基于轮播图页面模式重构，添加完整的搜索和分页功能
  API: /api/admin/agent/list
-->
<template>
  <div class="service-daili">
    <!-- 顶部导航 -->
    <TopNav title="服务点管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="服务点名称" prop="name">
                <el-input
                  size="default"
                  v-model="searchForm.name"
                  placeholder="请输入服务点名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="isGrounding">
                <el-select
                  size="default"
                  v-model="searchForm.isGrounding"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="上架" :value="1" />
                  <el-option label="下架" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增服务点
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
        <el-table-column prop="id" label="ID" width="60" />

        <el-table-column prop="img" label="服务点图片" width="120">
          <template #default="scope">
            <LbImage :src="scope.row.img" width="80" height="50" />
          </template>
        </el-table-column>

        <el-table-column prop="name" label="服务点名称" min-width="150" />

        <el-table-column prop="address" label="地址" min-width="200" />

        <el-table-column prop="tel" label="联系电话" width="120" />

        <el-table-column prop="serviceCate" label="主营业务" min-width="150">
          <template #default="scope">
            <el-tag v-if="scope.row.serviceCate" size="default">
              {{ scope.row.serviceCate }}
            </el-tag>
            <span v-else class="no-data">暂无业务</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isGrounding"
              :active-value="1"
              :inactive-value="-1"
              :loading="scope.row.statusLoading"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" min-width="120">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="default"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="default"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="服务点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入服务点名称" />
        </el-form-item>

        <el-form-item label="服务点地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入服务点地址" :disabled="form.id" />
        </el-form-item>

        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="经度" :disabled="true" />
        </el-form-item>

        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="纬度" :disabled="true" />
        </el-form-item>

        <el-form-item label="联系电话" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="主营业务" prop="serviceCate">
          <el-input v-model="form.serviceCate" placeholder="请输入主营业务" />
        </el-form-item>

        <el-form-item label="服务点图片" prop="img">
          <el-upload
            class="image-upload"
            action="#"
            :auto-upload="false"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :before-upload="beforeImageUpload"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png等图片文件
              </div>
            </template>
          </el-upload>

          <!-- 上传进度显示 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" :show-text="true" />
            <p>上传中... {{ uploadProgress }}%</p>
          </div>
        </el-form-item>

        <!-- <el-form-item label="经度" prop="longitude">
          <el-input-number
            v-model="form.longitude"
            :precision="6"
            placeholder="请输入经度"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="纬度" prop="latitude">
          <el-input-number
            v-model="form.latitude"
            :precision="6"
            placeholder="请输入纬度"
            style="width: 100%"
          />
        </el-form-item> -->

        <el-form-item label="状态" prop="isGrounding">
          <el-radio-group v-model="form.isGrounding">
            <el-radio :value="1">上架</el-radio>
            <el-radio :value="-1">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)
const fileList = ref([])
const uploadProgress = ref(0)
const uploading = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  address: '',
  tel: '',
  serviceCate: '',
  isGrounding: null
})

// 编辑表单
const form = reactive({
  id: null,
  name: '',
  address: '',
  tel: '',
  serviceCate: '',
  img: '',
  longitude: null,
  latitude: null,
  isGrounding: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入服务点名称', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入服务点地址', trigger: 'blur' }
  ],
  tel: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑服务点' : '新增服务点')

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.name) params.name = searchForm.name
    if (searchForm.address) params.address = searchForm.address
    if (searchForm.tel) params.tel = searchForm.tel
    if (searchForm.serviceCate) params.serviceCate = searchForm.serviceCate
    if (searchForm.isGrounding !== null && searchForm.isGrounding !== '') params.isGrounding = searchForm.isGrounding

    console.log('🔍 查询参数:', params)

    // 使用整合后的API-V2调用方式
    const result = await api.service.agentList(params)
    console.log('📋 服务点列表数据:', result)

    // 处理真实API的响应格式
    if (result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      // 为每个服务点项添加statusLoading属性
      tableData.value = (data.list || []).map(item => ({
        ...item,
        statusLoading: false
      }))
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取服务点列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.address = ''
  searchForm.tel = ''
  searchForm.serviceCate = ''
  searchForm.isGrounding = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  form.id = row.id
  form.name = row.name || ''
  form.address = row.address || ''
  form.tel = row.tel || ''
  form.serviceCate = row.serviceCate || ''
  form.img = row.img || ''
  form.longitude = row.longitude || null
  form.latitude = row.latitude || null
  form.isGrounding = row.isGrounding || 1

  // 如果有图片，设置文件列表用于显示
  if (row.img) {
    fileList.value = [{
      name: 'image',
      url: row.img
    }]
  }

  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个服务点吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 使用整合后的API-V2调用方式
    const result = await api.service.agentDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除服务点失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换方法
const handleStatusChange = async (row) => {
  // 保存原始状态，以便在失败时恢复
  const originalStatus = row.isGrounding === 1 ? -1 : 1

  // 设置加载状态
  row.statusLoading = true

  try {
    const result = await api.service.agentStatus({
      id: row.id,
      status: row.isGrounding
    })

    if (result.code === '200') {
      ElMessage.success('状态更新成功')
      // 状态更新成功后，重新获取列表数据以确保状态一致
      await getTableDataList()
    } else {
      ElMessage.error(result.message || result.msg || '状态更新失败')
      // 恢复原状态
      row.isGrounding = originalStatus
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.isGrounding = originalStatus
  } finally {
    // 清除加载状态
    row.statusLoading = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑服务点，使用整合后的API-V2调用方式
      result = await api.service.agentUpdate(form)
    } else {
      // 新增服务点，使用整合后的API-V2调用方式
      result = await api.service.agentAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  console.log('📋 图片上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  console.log('✅ 图片验证通过')
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fileList) => {
  console.log('🖼️ 图片文件变更:', file, fileList)

  if (file.status === 'ready') {
    // 文件准备上传，开始上传流程
    await uploadImage(file)
  }
}

// 图片移除处理
const handleImageRemove = (file, fileList) => {
  console.log('🗑️ 移除图片:', file)
  form.img = ''
  uploadProgress.value = 0
}

// 执行图片上传
const uploadImage = async (file) => {
  console.log('📤 开始上传图片:', file)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      // 更新上传进度
      uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', uploadProgress.value + '%')
    })

    console.log('✅ 图片上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // 上传成功，保存文件URL到表单
      form.img = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('图片上传成功')

      // 更新文件列表显示
      fileList.value = [{
        name: file.name,
        url: form.img,
        status: 'success'
      }]

      console.log('💾 图片URL已保存到表单:', form.img)
    } else {
      throw new Error(result.message || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))

    // 清理失败的文件
    fileList.value = []
    form.img = ''
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.name = ''
  form.address = ''
  form.tel = ''
  form.serviceCate = ''
  form.img = ''
  form.longitude = null
  form.latitude = null
  form.isGrounding = 1
  fileList.value = []
  uploadProgress.value = 0
  uploading.value = false
}

// 格式化日期时间
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 生命周期
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* 页面主体样式 */
.lb-examine-goods {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  margin-top: 0;
}

/* 间距样式 */
.space-lg {
  height: 16px;
}

/* 搜索表单样式 */
.page-search-form {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* 表格样式 */
.el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 时间列样式 */
.time-column p {
  margin: 0;
  line-height: 1.4;
  font-size: 18px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
}
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 上传组件样式 */
.image-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.image-upload :deep(.el-upload__tip) {
  font-size: 18px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 18px;
  color: #606266;
  text-align: center;
}

.no-data {
  color: #999;
  font-size: 18px;
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lb-examine-goods {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .table-operate {
    flex-direction: column;
    gap: 2px;
  }
}
</style>