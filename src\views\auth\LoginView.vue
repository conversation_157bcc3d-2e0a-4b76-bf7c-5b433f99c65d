
<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <img src="/logo.jpg" alt="Logo" />
        </div>
        <h1 class="title">今师傅</h1>
        <p class="subtitle">今师傅后台管理系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      </div>

    <div class="login-bg">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  Lock,
  Platform,
  ChatDotRound,
  Message
} from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()
const route = useRoute()

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: 'admin',
  password: 'admin1118',
  rememberMe: false
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()

    loading.value = true

    // 执行登录
    await store.dispatch('auth/login', loginForm)

    ElMessage.success('登录成功')

    // 获取重定向地址，默认跳转到服务项目列表页
    const redirect = route.query.redirect || '/service/list'

    console.log('🚀 登录成功，准备跳转到:', redirect)

    // 跳转到目标页面
    await router.push(redirect)

    console.log('✅ 页面跳转完成')

  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

const handleForgotPassword = () => {
  router.push('/forgot-password')
}

const handleRegister = () => {
  router.push('/register')
}

const handleSocialLogin = (type) => {
  ElMessage.info(`${type} 登录功能开发中...`)
}

// 生命周期
onMounted(() => {
  // 如果已经登录，直接跳转
  if (store.getters['auth/isLoggedIn']) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.login-box {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: var(--spacing-xl);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  /* Added for alignment */
  display: flex;
  flex-direction: column;
  align-items: center; /* Horizontally center content */
  justify-content: center; /* Vertically center content if there's available space */
}

.logo img {
  width: 64px;
  height: 64px;
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-md);
}

.title {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.login-form {
  margin-bottom: var(--spacing-lg);
}

.login-form .el-form-item {
  margin-bottom: var(--spacing-lg);
}

.login-form .el-form-item:last-child {
  margin-bottom: 0;
}

.forgot-password {
  margin-left: auto;
  padding: 0;
  font-size: var(--font-size-sm);
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: var(--font-size-md);
  font-weight: 500;
}

.other-login {
  margin-bottom: var(--spacing-lg);
}

.divider {
  position: relative;
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color-light);
}

.divider span {
  background: var(--bg-color-base);
  padding: 0 var(--spacing-md);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.social-login {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.social-login .el-button {
  width: 40px;
  height: 40px;
}

.register-link {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.register-link .el-button {
  padding: 0;
  margin-left: var(--spacing-xs);
}

/* 背景装饰 */
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式适配 */
@media (max-width: 480px) {
  .login-box {
    margin: var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .title {
    font-size: var(--font-size-xl);
  }

  .social-login {
    gap: var(--spacing-sm);
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .login-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .login-box {
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid var(--border-color-dark);
}

[data-theme="dark"] .bg-shape {
  background: rgba(255, 255, 255, 0.05);
}
</style>
```