<template>
  <div class="security-view">
    <!-- 顶部导航 -->
    <TopNav title="安全设置" />

    <div class="content-container">
      <!-- 修改密码卡片 -->
      <el-card class="password-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>修改密码</h3>
            <p>为了您的账户安全，建议定期修改密码</p>
          </div>
        </template>

        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="120px"
          class="password-form"
        >
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              clearable
              style="width: 400px"
            />
            <div class="password-tips">
              <p>密码要求：</p>
              <ul>
                <li>长度6-20个字符</li>
                <li>建议包含字母、数字和特殊字符</li>
                <li>不要使用过于简单的密码</li>
              </ul>
            </div>
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              clearable
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item>
            <LbButton
              type="primary"
              @click="handleChangePassword"
              :loading="loading"
            >
              修改密码
            </LbButton>
            <LbButton @click="handleReset">
              重置
            </LbButton>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 安全信息卡片 -->
      <el-card class="security-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>安全信息</h3>
          </div>
        </template>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="当前用户">
            {{ userInfo.username || '管理员' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ lastLoginTime || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后修改密码时间">
            {{ lastPasswordChangeTime || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag type="success">正常</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 安全建议卡片 -->
      <el-card class="security-tips-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>安全建议</h3>
          </div>
        </template>

        <div class="tips-content">
          <el-alert
            title="安全提醒"
            type="info"
            :closable="false"
            show-icon
          >
            <ul>
              <li>定期修改密码，建议每3个月更换一次</li>
              <li>不要在多个平台使用相同密码</li>
              <li>不要将密码告诉他人或写在容易被发现的地方</li>
              <li>如发现账户异常，请立即修改密码并联系管理员</li>
            </ul>
          </el-alert>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import { md5 } from '@/utils/crypto'
import { api } from '@/api-v2'

const store = useStore()

// 响应式数据
const loading = ref(false)
const passwordFormRef = ref()

// 用户信息
const userInfo = computed(() => store.state.auth.userInfo)
const lastLoginTime = ref('')
const lastPasswordChangeTime = ref('')

// 密码表单
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
      message: '密码必须包含字母和数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate()

    loading.value = true

    // MD5加密密码
    const encryptedNewPassword = md5(passwordForm.newPassword)
    const encryptedConfirmPassword = md5(passwordForm.confirmPassword)

    const result = await api.base.changePassword({
      newPassword: encryptedNewPassword,
      confirmPassword: encryptedConfirmPassword
    })

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('密码修改成功，请重新登录')

      // 清空表单
      handleReset()

      // 更新最后修改密码时间
      lastPasswordChangeTime.value = new Date().toLocaleString()

      // 延迟后退出登录
      setTimeout(() => {
        store.dispatch('auth/logout')
        window.location.href = '/login'
      }, 2000)
    } else {
      ElMessage.error(result.msg || '密码修改失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '密码修改失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 初始化数据
onMounted(() => {
  // 获取最后登录时间（可以从store或API获取）
  lastLoginTime.value = new Date().toLocaleString()
})
</script>

<style scoped>
.security-view {
  padding: 0;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.password-card,
.security-info-card,
.security-tips-card {
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.card-header p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.password-form {
  max-width: 600px;
}

.password-tips {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.password-tips p {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.password-tips li {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 4px;
}

.tips-content ul {
  margin: 0;
  padding-left: 20px;
}

.tips-content li {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 8px;
  color: #666;
}

.el-button + .el-button {
  margin-left: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 12px;
  }

  .password-form .el-input {
    width: 100% !important;
  }
}
</style>
