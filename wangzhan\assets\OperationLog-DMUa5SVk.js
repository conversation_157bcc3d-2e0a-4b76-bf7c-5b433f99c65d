import{T as K,L as D}from"./LbButton-BtU4V_Gr.js";import{L as Y}from"./LbPage-DnbiQ0Ct.js";import{_ as Z,b as ee}from"./index-C9Xz1oqp.js";import{E as m}from"./element-fdzwdDuf.js";import{r as u,X as le,h as te,y as E,Q as e,A as d,I as t,al as s,J as ae,ar as oe,H as ne,z as T,M as r,O as V}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const se={class:"operation-log"},re={class:"content-container"},ie={class:"search-form-container"},de={class:"table-container"},ue={key:0},pe={key:1,class:"text-success"},ce={class:"params-content"},me={class:"dialog-footer"},fe={class:"exception-content"},_e={class:"dialog-footer"},ge={__name:"OperationLog",setup(ve){const v=u(!1),y=u([]),b=u(0),f=u(!1),_=u(!1),h=u(""),C=u(""),n=le({adminUserId:"",method:"",uri:"",clientIp:"",resultCode:"",pageNum:1,pageSize:10}),I=u(),S=o=>({GET:"success",POST:"primary",PUT:"warning",DELETE:"danger"})[o]||"info",U=o=>o==="200"?"success":o.startsWith("4")?"warning":o.startsWith("5")?"danger":"info",P=o=>{h.value=o,f.value=!0},N=o=>{C.value=o,_.value=!0},L=o=>{if(!o)return"";try{const l=JSON.parse(o);return JSON.stringify(l,null,2)}catch{return o}},M=async()=>{try{await navigator.clipboard.writeText(h.value),m.success("参数已复制到剪贴板")}catch{m.error("复制失败")}},O=async()=>{try{await navigator.clipboard.writeText(C.value),m.success("异常信息已复制到剪贴板")}catch{m.error("复制失败")}},G=()=>{f.value=!1,h.value=""},B=()=>{_.value=!1,C.value=""},F=()=>{n.pageNum=1,g()},$=()=>{I.value?.resetFields(),n.pageNum=1,g()},H=o=>{n.pageSize=o,n.pageNum=1,g()},J=o=>{n.pageNum=o,g()},g=async()=>{v.value=!0;try{const o=await ee.log.operationLog.list(n);o.code==="200"?(y.value=o.data.list||[],b.value=o.data.totalCount||0):(m.error(o.msg||"获取数据失败"),y.value=[],b.value=0)}catch(o){console.error("加载数据失败:",o),m.error("加载数据失败");const l={data:{totalCount:17,pageNum:n.pageNum,pageSize:n.pageSize,list:[{adminUserId:1,uri:"/api/admin/partner/inviteList",method:"GET",clientIp:"0:0:0:0:0:0:0:1",params:"[3, 0, 1, 10]",resultCode:"200",durationMs:247,ex:null,createTime:"2025-07-24 15:15:56"},{adminUserId:1,uri:"/api/admin/operationLog/list",method:"GET",clientIp:"0:0:0:0:0:0:0:1",params:"[1, GET, null, null, null, 1, 10]",resultCode:"200",durationMs:83,ex:null,createTime:"2025-07-24 14:45:47"}]}};y.value=l.data.list,b.value=l.data.totalCount}finally{v.value=!1}};return te(()=>{g()}),(o,l)=>{const w=s("el-input"),p=s("el-form-item"),x=s("el-option"),R=s("el-select"),W=s("el-col"),A=s("el-row"),Q=s("el-form"),i=s("el-table-column"),k=s("el-tag"),c=s("el-button"),X=s("el-table"),z=s("el-dialog"),j=oe("loading");return T(),E("div",se,[e(K,{title:"操作日志"}),d("div",re,[d("div",ie,[e(Q,{ref_key:"searchFormRef",ref:I,model:n,inline:!0,class:"search-form"},{default:t(()=>[e(A,{gutter:20},{default:t(()=>[e(W,{span:24},{default:t(()=>[e(p,{label:"操作人ID",prop:"adminUserId"},{default:t(()=>[e(w,{size:"default",modelValue:n.adminUserId,"onUpdate:modelValue":l[0]||(l[0]=a=>n.adminUserId=a),placeholder:"请输入操作人ID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(p,{label:"请求类型",prop:"method"},{default:t(()=>[e(R,{modelValue:n.method,"onUpdate:modelValue":l[1]||(l[1]=a=>n.method=a),placeholder:"请选择请求类型",clearable:"",style:{width:"150px"}},{default:t(()=>[e(x,{label:"GET",value:"GET"}),e(x,{label:"POST",value:"POST"}),e(x,{label:"PUT",value:"PUT"}),e(x,{label:"DELETE",value:"DELETE"})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"请求路径",prop:"uri"},{default:t(()=>[e(w,{size:"default",modelValue:n.uri,"onUpdate:modelValue":l[2]||(l[2]=a=>n.uri=a),placeholder:"请输入请求路径",clearable:"",style:{width:"250px"}},null,8,["modelValue"])]),_:1}),e(p,{label:"请求IP",prop:"clientIp"},{default:t(()=>[e(w,{size:"default",modelValue:n.clientIp,"onUpdate:modelValue":l[3]||(l[3]=a=>n.clientIp=a),placeholder:"请输入请求IP",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(p,{label:"状态码",prop:"resultCode"},{default:t(()=>[e(w,{size:"default",modelValue:n.resultCode,"onUpdate:modelValue":l[4]||(l[4]=a=>n.resultCode=a),placeholder:"请输入状态码",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(p,null,{default:t(()=>[e(D,{type:"primary",onClick:F,loading:v.value},{default:t(()=>l[9]||(l[9]=[r(" 搜索 ")])),_:1,__:[9]},8,["loading"]),e(D,{onClick:$},{default:t(()=>l[10]||(l[10]=[r("重置")])),_:1,__:[10]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),d("div",de,[ae((T(),ne(X,{data:y.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:t(()=>[e(i,{prop:"adminUserId",label:"操作人ID",width:"100",align:"center"}),e(i,{prop:"uri",label:"请求路径","min-width":"200","show-overflow-tooltip":""}),e(i,{prop:"method",label:"请求类型",width:"100",align:"center"},{default:t(({row:a})=>[e(k,{type:S(a.method),size:"small"},{default:t(()=>[r(V(a.method),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"clientIp",label:"请求IP",width:"150","show-overflow-tooltip":""}),e(i,{prop:"params",label:"请求参数",width:"150"},{default:t(({row:a})=>[e(c,{type:"primary",link:"",size:"small",onClick:q=>P(a.params)},{default:t(()=>l[11]||(l[11]=[r(" 查看参数 ")])),_:2,__:[11]},1032,["onClick"])]),_:1}),e(i,{prop:"resultCode",label:"状态码",width:"100",align:"center"},{default:t(({row:a})=>[e(k,{type:U(a.resultCode),size:"small"},{default:t(()=>[r(V(a.resultCode),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"durationMs",label:"耗时(ms)",width:"100",align:"center"}),e(i,{prop:"ex",label:"异常信息",width:"120"},{default:t(({row:a})=>[a.ex?(T(),E("span",ue,[e(c,{type:"danger",link:"",size:"small",onClick:q=>N(a.ex)},{default:t(()=>l[12]||(l[12]=[r(" 查看异常 ")])),_:2,__:[12]},1032,["onClick"])])):(T(),E("span",pe,"正常"))]),_:1}),e(i,{prop:"createTime",label:"访问时间",width:"180",align:"center"})]),_:1},8,["data"])),[[j,v.value]])]),e(Y,{page:n.pageNum,"page-size":n.pageSize,total:b.value,onHandleSizeChange:H,onHandleCurrentChange:J},null,8,["page","page-size","total"])]),e(z,{modelValue:f.value,"onUpdate:modelValue":l[6]||(l[6]=a=>f.value=a),title:"请求参数详情",width:"60%","before-close":G},{footer:t(()=>[d("span",me,[e(c,{onClick:l[5]||(l[5]=a=>f.value=!1)},{default:t(()=>l[13]||(l[13]=[r("关闭")])),_:1,__:[13]}),e(c,{type:"primary",onClick:M},{default:t(()=>l[14]||(l[14]=[r("复制参数")])),_:1,__:[14]})])]),default:t(()=>[d("div",ce,[d("pre",null,V(L(h.value)),1)])]),_:1},8,["modelValue"]),e(z,{modelValue:_.value,"onUpdate:modelValue":l[8]||(l[8]=a=>_.value=a),title:"异常信息详情",width:"70%","before-close":B},{footer:t(()=>[d("span",_e,[e(c,{onClick:l[7]||(l[7]=a=>_.value=!1)},{default:t(()=>l[15]||(l[15]=[r("关闭")])),_:1,__:[15]}),e(c,{type:"primary",onClick:O},{default:t(()=>l[16]||(l[16]=[r("复制异常")])),_:1,__:[16]})])]),default:t(()=>[d("div",fe,[d("pre",null,V(C.value),1)])]),_:1},8,["modelValue"])])}}},Te=Z(ge,[["__scopeId","data-v-137af5d9"]]);export{Te as default};
