<!--
  售后管理页面
 /src/view/shop/AfterSale/AfterSale.vue重构
-->

<template>
  <div class="page">
    <TopNav />
    <div class="page-main">
      <el-card>
        <!-- 操作栏 -->
        <div class="operation-bar">
          <el-input
            v-model="searchTitle"
            placeholder="按名称查找"
            style="width: 300px; margin-right: 20px;"
          />
          <LbButton type="success" @click="getAfterSaleList">查找</LbButton>
          <LbButton type="primary" @click="openServiceModal">客服电话设置</LbButton>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          style="width: 100%; margin-top: 20px;"
          v-loading="loading"
        >
          <el-table-column label="ID" prop="id" width="120" show-overflow-tooltip align="center" />
          <el-table-column label="品牌" prop="brand" width="200" show-overflow-tooltip align="center" />
          <el-table-column label="名称" prop="title" width="200" show-overflow-tooltip align="center" />
          <el-table-column label="时间" prop="time" width="200" show-overflow-tooltip align="center" />
          <el-table-column label="联系人" prop="name" width="200" show-overflow-tooltip align="center" />
          <el-table-column label="联系方式" prop="tel" width="200" show-overflow-tooltip align="center" />
          <el-table-column label="备注" prop="mark" min-width="240" show-overflow-tooltip align="center" />
          <el-table-column label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="viewDetail(scope.row)"
                >
                  查看详情
                </LbButton>
                <LbButton
                  v-if="scope.row.status === 1"
                  size="mini"
                  type="success"
                  @click="handleAfterSale(scope.row)"
                >
                  处理
                </LbButton>
                <LbButton
                  size="mini"
                  type="danger"
                  @click="deleteAfterSale(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 客服电话设置对话框 -->
    <el-dialog v-model="serviceDialogVisible" width="450px">
      <template #header>
        <div class="dialog-header">
          设置客服电话
        </div>
      </template>
      <div>
        <el-input
          v-model="serviceMobile"
          placeholder="请输入客服电话"
          style="width: 100%;"
        />
      </div>
      <template #footer>
        <div>
          <LbButton type="primary" @click="saveServiceMobile" :loading="saveLoading">保存</LbButton>
          <LbButton @click="serviceDialogVisible = false">取消</LbButton>
        </div>
      </template>
    </el-dialog>

    <!-- 售后详情对话框 -->
    <el-dialog v-model="detailVisible" title="售后详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="售后ID">{{ afterSaleDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ afterSaleDetail.brand }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ afterSaleDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ afterSaleDetail.time }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ afterSaleDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ afterSaleDetail.tel }}</el-descriptions-item>
        <el-descriptions-item label="问题描述" span="2">{{ afterSaleDetail.mark }}</el-descriptions-item>
      </el-descriptions>

      <div v-if="afterSaleDetail.images && afterSaleDetail.images.length > 0" style="margin-top: 20px;">
        <h4>问题图片</h4>
        <div class="detail-images">
          <img
            v-for="(img, index) in afterSaleDetail.images"
            :key="index"
            :src="img"
            alt="问题图片"
            style="width: 100px; height: 100px; margin-right: 8px; border-radius: 4px;"
          />
        </div>
      </div>

      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 处理售后对话框 -->
    <el-dialog v-model="handleVisible" title="处理售后" width="50%">
      <el-form :model="handleForm" :rules="handleRules" ref="handleFormRef">
        <el-form-item label="处理结果" prop="result">
          <el-radio-group v-model="handleForm.result">
            <el-radio :value="1">已解决</el-radio>
            <el-radio :value="2">需要上门</el-radio>
            <el-radio :value="3">无法解决</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理说明" prop="remark">
          <el-input
            v-model="handleForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <LbButton @click="handleVisible = false">取消</LbButton>
        <LbButton type="primary" @click="submitHandle" :loading="handleLoading">确定处理</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchTitle = ref('')
const serviceDialogVisible = ref(false)
const serviceMobile = ref('')
const saveLoading = ref(false)
const detailVisible = ref(false)
const handleVisible = ref(false)
const handleFormRef = ref()
const handleLoading = ref(false)

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 售后详情
const afterSaleDetail = reactive({
  id: '',
  brand: '',
  title: '',
  time: '',
  name: '',
  tel: '',
  mark: '',
  images: []
})

// 处理表单
const handleForm = reactive({
  id: '',
  result: 1,
  remark: ''
})

// 处理表单验证规则
const handleRules = {
  result: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入处理说明', trigger: 'blur' }
  ]
}

// 方法
const getAfterSaleList = async (page = 1) => {
  loading.value = true
  pagination.page = page

  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize,
      title: searchTitle.value
    })

    const response = await fetch(`/api/shop/aftersale/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取售后列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 待处理
    2: 'success',  // 已解决
    3: 'primary',  // 需要上门
    4: 'danger'    // 无法解决
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '待处理',
    2: '已解决',
    3: '需要上门',
    4: '无法解决'
  }
  return statusMap[status] || '未知'
}

const openServiceModal = async () => {
  try {
    const response = await fetch('/api/system/config')
    const result = await response.json()

    if (result.code === 200) {
      serviceMobile.value = result.data.service_mobile || ''
    }
  } catch (error) {
    console.error('获取客服电话失败:', error)
  }

  serviceDialogVisible.value = true
}

const saveServiceMobile = async () => {
  if (!serviceMobile.value) {
    ElMessage.error('请填写客服电话')
    return
  }

  saveLoading.value = true

  try {
    const response = await fetch('/api/system/config', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ service_mobile: serviceMobile.value })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('保存成功')
      serviceDialogVisible.value = false
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存客服电话失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const viewDetail = async (row) => {
  try {
    const response = await fetch(`/api/shop/aftersale/detail/${row.id}`)
    const result = await response.json()

    if (result.code === 200) {
      Object.assign(afterSaleDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.message || '获取售后详情失败')
    }
  } catch (error) {
    console.error('获取售后详情失败:', error)
    ElMessage.error('获取售后详情失败')
  }
}

const handleAfterSale = (row) => {
  handleForm.id = row.id
  handleForm.result = 1
  handleForm.remark = ''
  handleVisible.value = true
}

const submitHandle = async () => {
  try {
    await handleFormRef.value.validate()

    handleLoading.value = true

    const response = await fetch(`/api/shop/aftersale/handle/${handleForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        result: handleForm.result,
        remark: handleForm.remark
      })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('处理成功')
      handleVisible.value = false
      getAfterSaleList()
    } else {
      ElMessage.error(result.message || '处理失败')
    }
  } catch (error) {
    console.error('处理售后失败:', error)
    ElMessage.error('处理失败')
  } finally {
    handleLoading.value = false
  }
}

const deleteAfterSale = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条售后记录吗？`,
      '删除售后确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/shop/aftersale/delete/${row.id}`, {
      method: 'DELETE'
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('删除成功')
      getAfterSaleList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除售后失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getAfterSaleList(1)
}

const handleCurrentChange = (page) => {
  getAfterSaleList(page)
}

// 生命周期
onMounted(() => {
  getAfterSaleList()
})
</script>

<style scoped>
.page {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 1200px;
  margin: 0 auto;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.dialog-header {
  height: 40px;
  background-color: #0081ff;
  color: #fff;
  line-height: 40px;
  padding: 0 20px;
  margin: -20px -20px 20px -20px;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .page {
    padding: 10px;
  }

  .operation-bar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
