import{T as le,L as m}from"./LbButton-BtU4V_Gr.js";import{L as ae}from"./LbPage-DnbiQ0Ct.js";import{_ as oe,a as w}from"./index-C9Xz1oqp.js";import{E as d,q as ne}from"./element-fdzwdDuf.js";import{r as f,X as I,c as se,h as re,y as ie,Q as t,A as p,I as l,al as i,J as ue,ar as de,H as ce,z as M,M as c,O as h}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const pe={class:"market-notice"},me={class:"content-container"},fe={class:"search-form-container"},_e={class:"table-container"},ge={class:"content-column"},ye={class:"time-column"},ve={class:"table-operate"},we={class:"dialog-footer"},be={__name:"MarketNotice",setup(Ve){const C=f(!1),k=f(!1),z=f([]),x=f(0),N=f(),D=f(),_=f(!1),n=I({pageNum:1,pageSize:10,content:"",status:null,type:null}),s=I({id:null,content:"",type:1,status:1}),R={content:[{required:!0,message:"请输入公告内容",trigger:"blur"}],type:[{required:!0,message:"请选择公告类型",trigger:"change"}]},A=se(()=>s.id?"编辑公告":"新增公告"),g=async a=>{a&&(n.pageNum=1),C.value=!0;try{const e={pageNum:n.pageNum,pageSize:n.pageSize};n.content&&(e.content=n.content),n.status!==null&&n.status!==""&&(e.status=n.status),n.type!==null&&n.type!==""&&(e.type=n.type);const r=await w.market.noticeList(e);if(console.log("📢 公告列表数据 (API-V2):",r),r.code===200||r.code==="200"){const u=r.data,y=u.list||[];z.value=y,x.value=u.totalCount||u.total||0,console.log("📊 处理后的数据:",{list:z.value,total:x.value,pageNum:u.pageNum,pageSize:u.pageSize})}else console.error("❌ API响应错误:",r),d.error(r.message||r.msg||"获取数据失败")}catch(e){console.error("获取公告列表失败:",e),d.error("获取数据失败")}finally{C.value=!1}},E=()=>{g(1)},F=()=>{n.content="",n.status=null,n.type=null,N.value?.resetFields(),g(1)},P=()=>{S(),_.value=!0},$=async a=>{S();try{const e=await w.market.noticeInfo({id:a.id});if(e.code==="200"){const r=e.data;s.id=r.id,s.content=r.content||"",s.type=r.type||1,s.status=r.status||1}else{d.error(e.message||"获取公告详情失败");return}}catch(e){console.error("获取公告详情失败:",e),d.error("获取公告详情失败");return}_.value=!0},q=async a=>{try{await ne.confirm("确定要删除这条公告吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await w.market.noticeDelete({id:a.id});e.code==="200"?(d.success("删除成功"),g()):d.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除公告失败:",e),d.error("删除失败"))}},H=async a=>{try{const e=await w.market.noticeStatus({id:a.id});e.code==="200"?d.success("状态修改成功"):(a.status=a.status===1?0:1,d.error(e.message||"状态修改失败"))}catch(e){a.status=a.status===1?0:1,console.error("修改状态失败:",e),d.error("状态修改失败")}},J=async()=>{try{await D.value.validate(),k.value=!0;let a;s.id?a=await w.market.noticeUpdate(s):a=await w.market.noticeAdd(s),a.code==="200"?(d.success(s.id?"更新成功":"新增成功"),_.value=!1,g()):d.error(a.message||"操作失败")}catch(a){console.error("提交失败:",a),d.error("操作失败")}finally{k.value=!1}},O=a=>{n.pageSize=a,T(1)},T=a=>{n.pageNum=a,g()},Q=()=>{S()},S=()=>{s.id=null,s.content="",s.type=1,s.status=1},W=a=>a?new Date(a).toLocaleDateString("zh-CN"):"",X=a=>a?new Date(a).toLocaleTimeString("zh-CN",{hour12:!1}):"";return re(()=>{g(1)}),(a,e)=>{const r=i("el-input"),u=i("el-form-item"),y=i("el-option"),L=i("el-select"),j=i("el-col"),G=i("el-row"),U=i("el-form"),v=i("el-table-column"),K=i("el-tag"),Y=i("el-switch"),Z=i("el-table"),b=i("el-radio"),B=i("el-radio-group"),ee=i("el-dialog"),te=de("loading");return M(),ie("div",pe,[t(le,{title:"公告管理"}),p("div",me,[p("div",fe,[t(U,{ref_key:"searchFormRef",ref:N,model:n,inline:!0,class:"search-form"},{default:l(()=>[t(G,{gutter:20},{default:l(()=>[t(j,{span:24},{default:l(()=>[t(u,{label:"公告内容",prop:"content"},{default:l(()=>[t(r,{size:"default",modelValue:n.content,"onUpdate:modelValue":e[0]||(e[0]=o=>n.content=o),placeholder:"请输入公告内容",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:l(()=>[t(L,{size:"default",modelValue:n.status,"onUpdate:modelValue":e[1]||(e[1]=o=>n.status=o),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:l(()=>[t(y,{label:"启用",value:1}),t(y,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"类型",prop:"type"},{default:l(()=>[t(L,{size:"default",modelValue:n.type,"onUpdate:modelValue":e[2]||(e[2]=o=>n.type=o),placeholder:"请选择类型",clearable:"",style:{width:"120px"}},{default:l(()=>[t(y,{label:"用户端",value:1}),t(y,{label:"师傅端",value:2})]),_:1},8,["modelValue"])]),_:1}),t(u,null,{default:l(()=>[t(m,{size:"default",type:"primary",icon:"Search",onClick:E},{default:l(()=>e[8]||(e[8]=[c(" 搜索 ")])),_:1,__:[8]}),t(m,{size:"default",icon:"RefreshLeft",onClick:F},{default:l(()=>e[9]||(e[9]=[c(" 重置 ")])),_:1,__:[9]}),t(m,{size:"default",type:"primary",icon:"Plus",onClick:P},{default:l(()=>e[10]||(e[10]=[c(" 新增公告 ")])),_:1,__:[10]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),p("div",_e,[ue((M(),ce(Z,{data:z.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:l(()=>[t(v,{prop:"id",label:"ID",width:"100",align:"center"}),t(v,{prop:"content",label:"公告内容","min-width":"300"},{default:l(o=>[p("div",ge,[p("p",null,h(o.row.content),1)])]),_:1}),t(v,{prop:"type",label:"类型",width:"100"},{default:l(o=>[t(K,{type:o.row.type===1?"primary":"warning"},{default:l(()=>[c(h(o.row.type===1?"用户端":"师傅端"),1)]),_:2},1032,["type"])]),_:1}),t(v,{prop:"status",label:"状态",width:"100"},{default:l(o=>[t(Y,{modelValue:o.row.status,"onUpdate:modelValue":V=>o.row.status=V,"active-value":1,"inactive-value":0,onChange:V=>H(o.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(v,{prop:"createTime",label:"创建时间","min-width":"120"},{default:l(o=>[p("div",ye,[p("p",null,h(W(o.row.createTime)),1),p("p",null,h(X(o.row.createTime)),1)])]),_:1}),t(v,{label:"操作",width:"200",fixed:"right"},{default:l(o=>[p("div",ve,[t(m,{size:"default",type:"primary",onClick:V=>$(o.row)},{default:l(()=>e[11]||(e[11]=[c(" 编辑 ")])),_:2,__:[11]},1032,["onClick"]),t(m,{size:"default",type:"danger",onClick:V=>q(o.row)},{default:l(()=>e[12]||(e[12]=[c(" 删除 ")])),_:2,__:[12]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[te,C.value]])]),t(ae,{page:n.pageNum,"page-size":n.pageSize,total:x.value,onHandleSizeChange:O,onHandleCurrentChange:T},null,8,["page","page-size","total"])]),t(ee,{title:A.value,modelValue:_.value,"onUpdate:modelValue":e[7]||(e[7]=o=>_.value=o),width:"600px",onClose:Q},{footer:l(()=>[p("span",we,[t(m,{onClick:e[6]||(e[6]=o=>_.value=!1)},{default:l(()=>e[17]||(e[17]=[c("取消")])),_:1,__:[17]}),t(m,{type:"primary",onClick:J,loading:k.value},{default:l(()=>e[18]||(e[18]=[c(" 确定 ")])),_:1,__:[18]},8,["loading"])])]),default:l(()=>[t(U,{ref_key:"formRef",ref:D,model:s,rules:R,"label-width":"100px"},{default:l(()=>[t(u,{label:"公告内容",prop:"content"},{default:l(()=>[t(r,{modelValue:s.content,"onUpdate:modelValue":e[3]||(e[3]=o=>s.content=o),type:"textarea",rows:6,placeholder:"请输入公告内容"},null,8,["modelValue"])]),_:1}),t(u,{label:"公告类型",prop:"type"},{default:l(()=>[t(B,{modelValue:s.type,"onUpdate:modelValue":e[4]||(e[4]=o=>s.type=o)},{default:l(()=>[t(b,{value:1},{default:l(()=>e[13]||(e[13]=[c("用户端")])),_:1,__:[13]}),t(b,{value:2},{default:l(()=>e[14]||(e[14]=[c("师傅端")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:l(()=>[t(B,{modelValue:s.status,"onUpdate:modelValue":e[5]||(e[5]=o=>s.status=o)},{default:l(()=>[t(b,{value:1},{default:l(()=>e[15]||(e[15]=[c("启用")])),_:1,__:[15]}),t(b,{value:0},{default:l(()=>e[16]||(e[16]=[c("禁用")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},Ne=oe(be,[["__scopeId","data-v-9963cca6"]]);export{Ne as default};
