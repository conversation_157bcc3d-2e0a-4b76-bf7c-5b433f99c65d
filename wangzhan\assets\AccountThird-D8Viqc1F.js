import{T as we,L as g}from"./LbButton-BtU4V_Gr.js";import{_ as Ve}from"./index-C9Xz1oqp.js";import{E as s,q as Q}from"./element-fdzwdDuf.js";import{r as m,X as A,h as ke,y as I,Q as t,A as v,I as o,al as u,ar as Ce,z as V,M as p,J as he,H as L,O as C,P as X,a6 as G}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const xe={class:"lb-account-third"},Pe={class:"page-main"},Te={class:"table-operate"},Se={class:"pagination-section"},Ue={__name:"AccountThird",setup(ze){const T=m(!1),D=m([]),b=m(!1),k=m(!1),w=m("add"),h=m(),S=m(),U=m(!1),z=m(!1),$=m(!1),B=m([]),x=m([]),f=A({page:1,pageSize:10,total:0}),r=A({id:"",name:"",tel:"",province:"",city:"",address:"",service_type:[],remark:""}),i=A({id:"",api_url:"",api_key:"",api_secret:"",timeout:30,retry_count:3,enabled:1}),K={name:[{required:!0,message:"请输入第三方名称",trigger:"blur"}],tel:[{required:!0,message:"请输入联系电话",trigger:"blur"}],province:[{required:!0,message:"请选择省份",trigger:"change"}],city:[{required:!0,message:"请选择城市",trigger:"change"}]},W={api_url:[{required:!0,message:"请输入API地址",trigger:"blur"}],api_key:[{required:!0,message:"请输入API密钥",trigger:"blur"}],api_secret:[{required:!0,message:"请输入API秘钥",trigger:"blur"}]},_=async(l=1)=>{T.value=!0,f.page=l;try{const e=new URLSearchParams({page:f.page,pageSize:f.pageSize}),n=await(await fetch(`/api/account/third/list?${e}`)).json();n.code===200?(D.value=n.data.list||[],f.total=n.data.total||0):s.error(n.message||"获取数据失败")}catch(e){console.error("获取第三方列表失败:",e),s.error("获取数据失败")}finally{T.value=!1}},Y=async()=>{try{const e=await(await fetch("/api/common/province")).json();e.code===200&&(B.value=e.data||[])}catch(l){console.error("获取省份列表失败:",l)}},O=async l=>{try{const d=await(await fetch(`/api/common/city?province_id=${l}`)).json();d.code===200&&(x.value=d.data||[])}catch(e){console.error("获取城市列表失败:",e)}},Z=l=>l===1?"success":"danger",ee=l=>l===1?"启用":"禁用",q=(l,e)=>{if(!l)return"";const d=new Date(l);return e===1?d.toLocaleDateString():d.toLocaleTimeString()},te=l=>{r.city="",x.value=[],l&&O(l)},ae=()=>{w.value="add",E(),b.value=!0},le=l=>{w.value="edit",Object.assign(r,{id:l.id,name:l.name,tel:l.tel,province:l.province,city:l.city,address:l.address||"",service_type:l.service_type||[],remark:l.remark||""}),r.province&&O(r.province),b.value=!0},oe=async l=>{try{const d=await(await fetch(`/api/account/third/config/${l.id}`)).json();d.code===200?(Object.assign(i,{id:l.id,...d.data}),k.value=!0):s.error(d.message||"获取配置失败")}catch(e){console.error("获取API配置失败:",e),s.error("获取配置失败")}},re=async l=>{try{await Q.confirm(`确定要删除第三方 "${l.name}" 吗？`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const d=await(await fetch(`/api/account/third/delete/${l.id}`,{method:"DELETE"})).json();d.code===200?(s.success("删除成功"),_()):s.error(d.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除第三方失败:",e),s.error("删除失败"))}},ne=async l=>{try{const e=l.status===1?"禁用":"启用";await Q.confirm(`确定要${e}第三方 "${l.name}" 吗？`,`${e}确认`,{confirmButtonText:`确定${e}`,cancelButtonText:"取消",type:"warning"});const n=await(await fetch(`/api/account/third/status/${l.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:l.status===1?0:1})})).json();n.code===200?(s.success(`${e}成功`),_()):s.error(n.message||"操作失败")}catch(e){e!=="cancel"&&(console.error("修改第三方状态失败:",e),s.error("操作失败"))}},se=async()=>{try{await S.value.validate(),$.value=!0;const e=await(await fetch("/api/account/third/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).json();e.code===200?s.success("连接测试成功"):s.error(e.message||"连接测试失败")}catch(l){console.error("连接测试失败:",l),s.error("连接测试失败")}finally{$.value=!1}},ie=async()=>{try{await S.value.validate(),z.value=!0;const e=await(await fetch(`/api/account/third/config/${i.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).json();e.code===200?(s.success("配置保存成功"),k.value=!1,_()):s.error(e.message||"保存失败")}catch(l){console.error("保存配置失败:",l),s.error("保存失败")}finally{z.value=!1}},de=async()=>{try{await h.value.validate(),U.value=!0;const l=w.value==="add"?"/api/account/third/add":`/api/account/third/update/${r.id}`,e=w.value==="add"?"POST":"PUT",n=await(await fetch(l,{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).json();n.code===200?(s.success(w.value==="add"?"新增成功":"编辑成功"),b.value=!1,_()):s.error(n.message||"操作失败")}catch(l){console.error("提交失败:",l),s.error("操作失败")}finally{U.value=!1}},ue=()=>{b.value=!1,E()},E=()=>{Object.assign(r,{id:"",name:"",tel:"",province:"",city:"",address:"",service_type:[],remark:""}),x.value=[],h.value&&h.value.clearValidate()},pe=l=>{f.pageSize=l,_(1)},ce=l=>{_(l)};return ke(()=>{_(),Y()}),(l,e)=>{const d=u("el-row"),n=u("el-table-column"),me=u("el-tag"),fe=u("el-table"),ge=u("el-card"),_e=u("el-pagination"),y=u("el-input"),c=u("el-form-item"),N=u("el-option"),R=u("el-select"),P=u("el-checkbox"),ye=u("el-checkbox-group"),F=u("el-form"),J=u("el-dialog"),M=u("el-input-number"),H=u("el-radio"),ve=u("el-radio-group"),be=Ce("loading");return V(),I("div",xe,[t(we),v("div",Pe,[t(d,{class:"page-header"},{default:o(()=>[t(g,{type:"success",onClick:ae},{default:o(()=>e[18]||(e[18]=[p("新增第三方")])),_:1,__:[18]})]),_:1}),t(ge,{class:"table-card",shadow:"never"},{default:o(()=>[he((V(),L(fe,{data:D.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"},border:""},{default:o(()=>[t(n,{prop:"id",label:"ID",width:"80"}),t(n,{prop:"name",label:"第三方名称",width:"200"}),t(n,{prop:"city_name",label:"服务地址",width:"200"}),t(n,{prop:"tel",label:"联系电话",width:"150"}),t(n,{prop:"api_key",label:"API密钥",width:"200"},{default:o(a=>[v("span",null,C(a.row.api_key?"已配置":"未配置"),1)]),_:1}),t(n,{prop:"status",label:"状态",width:"100"},{default:o(a=>[t(me,{type:Z(a.row.status),size:"small"},{default:o(()=>[p(C(ee(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(n,{prop:"create_time",label:"创建时间",width:"170"},{default:o(a=>[v("div",null,C(q(a.row.create_time,1)),1),v("div",null,C(q(a.row.create_time,2)),1)]),_:1}),t(n,{label:"操作",width:"250",fixed:"right"},{default:o(a=>[v("div",Te,[t(g,{size:"mini",type:"primary",onClick:j=>le(a.row)},{default:o(()=>e[19]||(e[19]=[p(" 编辑 ")])),_:2,__:[19]},1032,["onClick"]),t(g,{size:"mini",type:"warning",onClick:j=>oe(a.row)},{default:o(()=>e[20]||(e[20]=[p(" API配置 ")])),_:2,__:[20]},1032,["onClick"]),t(g,{size:"mini",type:a.row.status===1?"danger":"success",onClick:j=>ne(a.row)},{default:o(()=>[p(C(a.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(g,{size:"mini",type:"danger",onClick:j=>re(a.row)},{default:o(()=>e[21]||(e[21]=[p(" 删除 ")])),_:2,__:[21]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[be,T.value]])]),_:1}),v("div",Se,[t(_e,{"current-page":f.page,"onUpdate:currentPage":e[0]||(e[0]=a=>f.page=a),"page-size":f.pageSize,"onUpdate:pageSize":e[1]||(e[1]=a=>f.pageSize=a),"page-sizes":[10,20,50,100],total:f.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:ce},null,8,["current-page","page-size","total"])])]),t(J,{modelValue:b.value,"onUpdate:modelValue":e[9]||(e[9]=a=>b.value=a),title:w.value==="add"?"新增第三方":"编辑第三方",width:"50%","close-on-click-modal":!1},{footer:o(()=>[t(g,{onClick:ue},{default:o(()=>e[26]||(e[26]=[p("取消")])),_:1,__:[26]}),t(g,{type:"primary",onClick:de,loading:U.value},{default:o(()=>e[27]||(e[27]=[p("确定")])),_:1,__:[27]},8,["loading"])]),default:o(()=>[t(F,{model:r,rules:K,ref_key:"formRef",ref:h,"label-width":"120px"},{default:o(()=>[t(c,{label:"第三方名称",prop:"name"},{default:o(()=>[t(y,{modelValue:r.name,"onUpdate:modelValue":e[2]||(e[2]=a=>r.name=a),placeholder:"请输入第三方名称"},null,8,["modelValue"])]),_:1}),t(c,{label:"联系电话",prop:"tel"},{default:o(()=>[t(y,{modelValue:r.tel,"onUpdate:modelValue":e[3]||(e[3]=a=>r.tel=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),t(c,{label:"省份",prop:"province"},{default:o(()=>[t(R,{modelValue:r.province,"onUpdate:modelValue":e[4]||(e[4]=a=>r.province=a),placeholder:"请选择省份",style:{width:"100%"},onChange:te},{default:o(()=>[(V(!0),I(X,null,G(B.value,a=>(V(),L(N,{key:a.id,label:a.title,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"城市",prop:"city"},{default:o(()=>[t(R,{modelValue:r.city,"onUpdate:modelValue":e[5]||(e[5]=a=>r.city=a),placeholder:"请选择城市",style:{width:"100%"}},{default:o(()=>[(V(!0),I(X,null,G(x.value,a=>(V(),L(N,{key:a.id,label:a.title,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"详细地址",prop:"address"},{default:o(()=>[t(y,{modelValue:r.address,"onUpdate:modelValue":e[6]||(e[6]=a=>r.address=a),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),t(c,{label:"服务类型",prop:"service_type"},{default:o(()=>[t(ye,{modelValue:r.service_type,"onUpdate:modelValue":e[7]||(e[7]=a=>r.service_type=a)},{default:o(()=>[t(P,{label:"维修服务"},{default:o(()=>e[22]||(e[22]=[p("维修服务")])),_:1,__:[22]}),t(P,{label:"清洁服务"},{default:o(()=>e[23]||(e[23]=[p("清洁服务")])),_:1,__:[23]}),t(P,{label:"搬家服务"},{default:o(()=>e[24]||(e[24]=[p("搬家服务")])),_:1,__:[24]}),t(P,{label:"装修服务"},{default:o(()=>e[25]||(e[25]=[p("装修服务")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"备注",prop:"remark"},{default:o(()=>[t(y,{modelValue:r.remark,"onUpdate:modelValue":e[8]||(e[8]=a=>r.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(J,{modelValue:k.value,"onUpdate:modelValue":e[17]||(e[17]=a=>k.value=a),title:"API配置",width:"60%"},{footer:o(()=>[t(g,{onClick:e[16]||(e[16]=a=>k.value=!1)},{default:o(()=>e[31]||(e[31]=[p("取消")])),_:1,__:[31]}),t(g,{type:"warning",onClick:se,loading:$.value},{default:o(()=>e[32]||(e[32]=[p("测试连接")])),_:1,__:[32]},8,["loading"]),t(g,{type:"primary",onClick:ie,loading:z.value},{default:o(()=>e[33]||(e[33]=[p("保存配置")])),_:1,__:[33]},8,["loading"])]),default:o(()=>[t(F,{model:i,rules:W,ref_key:"configFormRef",ref:S,"label-width":"120px"},{default:o(()=>[t(c,{label:"API地址",prop:"api_url"},{default:o(()=>[t(y,{modelValue:i.api_url,"onUpdate:modelValue":e[10]||(e[10]=a=>i.api_url=a),placeholder:"请输入API地址"},null,8,["modelValue"])]),_:1}),t(c,{label:"API密钥",prop:"api_key"},{default:o(()=>[t(y,{modelValue:i.api_key,"onUpdate:modelValue":e[11]||(e[11]=a=>i.api_key=a),placeholder:"请输入API密钥","show-password":""},null,8,["modelValue"])]),_:1}),t(c,{label:"API秘钥",prop:"api_secret"},{default:o(()=>[t(y,{modelValue:i.api_secret,"onUpdate:modelValue":e[12]||(e[12]=a=>i.api_secret=a),placeholder:"请输入API秘钥","show-password":""},null,8,["modelValue"])]),_:1}),t(c,{label:"超时时间",prop:"timeout"},{default:o(()=>[t(M,{modelValue:i.timeout,"onUpdate:modelValue":e[13]||(e[13]=a=>i.timeout=a),min:1,max:60},null,8,["modelValue"]),e[28]||(e[28]=v("span",{style:{"margin-left":"10px"}},"秒",-1))]),_:1,__:[28]}),t(c,{label:"重试次数",prop:"retry_count"},{default:o(()=>[t(M,{modelValue:i.retry_count,"onUpdate:modelValue":e[14]||(e[14]=a=>i.retry_count=a),min:0,max:10},null,8,["modelValue"])]),_:1}),t(c,{label:"启用状态",prop:"enabled"},{default:o(()=>[t(ve,{modelValue:i.enabled,"onUpdate:modelValue":e[15]||(e[15]=a=>i.enabled=a)},{default:o(()=>[t(H,{value:1},{default:o(()=>e[29]||(e[29]=[p("启用")])),_:1,__:[29]}),t(H,{value:0},{default:o(()=>e[30]||(e[30]=[p("禁用")])),_:1,__:[30]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},De=Ve(Ue,[["__scopeId","data-v-005a06ac"]]);export{De as default};
