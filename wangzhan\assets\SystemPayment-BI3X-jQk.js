import{T as R,L as _}from"./LbButton-BtU4V_Gr.js";import{_ as I}from"./index-C9Xz1oqp.js";import{E as r}from"./element-fdzwdDuf.js";import{r as m,X as C,h as q,y as B,Q as a,A as g,I as t,al as p,z as J,M as i}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const L={class:"lb-system-payment"},A={class:"page-main"},D={__name:"SystemPayment",setup(E){const b=m("wechat"),f=m(!1),y=m(!1),v=m(),w=m(),o=C({mch_id:"",mch_key:"",cert_path:"",status:1}),s=C({app_id:"",private_key:"",public_key:"",status:1}),T={mch_id:[{required:!0,message:"请输入商户号",trigger:"blur"}],mch_key:[{required:!0,message:"请输入商户密钥",trigger:"blur"}]},j={app_id:[{required:!0,message:"请输入应用ID",trigger:"blur"}],private_key:[{required:!0,message:"请输入私钥",trigger:"blur"}]},x=async()=>{try{const e=await(await fetch("/api/system/payment/config")).json();e.code===200&&(Object.assign(o,e.data.wechat||{}),Object.assign(s,e.data.alipay||{}))}catch(n){console.error("获取配置失败:",n)}},U=async()=>{try{await v.value.validate(),f.value=!0;const e=await(await fetch("/api/system/payment/wechat",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?r.success("微信支付配置保存成功"):r.error(e.message||"保存失败")}catch{r.error("保存失败")}finally{f.value=!1}},N=async()=>{try{await w.value.validate(),y.value=!0;const e=await(await fetch("/api/system/payment/alipay",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();e.code===200?r.success("支付宝配置保存成功"):r.error(e.message||"保存失败")}catch{r.error("保存失败")}finally{y.value=!1}},O=async()=>{try{const e=await(await fetch("/api/system/payment/wechat/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?r.success("微信支付连接测试成功"):r.error(e.message||"连接测试失败")}catch{r.error("连接测试失败")}},S=async()=>{try{const e=await(await fetch("/api/system/payment/alipay/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();e.code===200?r.success("支付宝连接测试成功"):r.error(e.message||"连接测试失败")}catch{r.error("连接测试失败")}};return q(()=>{x()}),(n,e)=>{const u=p("el-input"),d=p("el-form-item"),c=p("el-radio"),V=p("el-radio-group"),h=p("el-form"),k=p("el-tab-pane"),F=p("el-tabs"),P=p("el-card");return J(),B("div",L,[a(R),g("div",A,[a(P,{class:"config-card",shadow:"never"},{header:t(()=>e[9]||(e[9]=[g("div",{class:"card-header"},[g("span",null,"支付配置")],-1)])),default:t(()=>[a(F,{modelValue:b.value,"onUpdate:modelValue":e[8]||(e[8]=l=>b.value=l),type:"border-card"},{default:t(()=>[a(k,{label:"微信支付",name:"wechat"},{default:t(()=>[a(h,{model:o,rules:T,ref_key:"wechatFormRef",ref:v,"label-width":"140px",class:"config-form"},{default:t(()=>[a(d,{label:"商户号",prop:"mch_id"},{default:t(()=>[a(u,{modelValue:o.mch_id,"onUpdate:modelValue":e[0]||(e[0]=l=>o.mch_id=l),placeholder:"请输入微信支付商户号"},null,8,["modelValue"])]),_:1}),a(d,{label:"商户密钥",prop:"mch_key"},{default:t(()=>[a(u,{modelValue:o.mch_key,"onUpdate:modelValue":e[1]||(e[1]=l=>o.mch_key=l),placeholder:"请输入商户密钥",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(d,{label:"证书路径",prop:"cert_path"},{default:t(()=>[a(u,{modelValue:o.cert_path,"onUpdate:modelValue":e[2]||(e[2]=l=>o.cert_path=l),placeholder:"请输入证书文件路径"},null,8,["modelValue"])]),_:1}),a(d,{label:"启用状态",prop:"status"},{default:t(()=>[a(V,{modelValue:o.status,"onUpdate:modelValue":e[3]||(e[3]=l=>o.status=l)},{default:t(()=>[a(c,{value:1},{default:t(()=>e[10]||(e[10]=[i("启用")])),_:1,__:[10]}),a(c,{value:0},{default:t(()=>e[11]||(e[11]=[i("禁用")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:t(()=>[a(_,{type:"primary",onClick:U,loading:f.value},{default:t(()=>e[12]||(e[12]=[i("保存配置")])),_:1,__:[12]},8,["loading"]),a(_,{onClick:O,style:{"margin-left":"10px"}},{default:t(()=>e[13]||(e[13]=[i("测试连接")])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1}),a(k,{label:"支付宝",name:"alipay"},{default:t(()=>[a(h,{model:s,rules:j,ref_key:"alipayFormRef",ref:w,"label-width":"140px",class:"config-form"},{default:t(()=>[a(d,{label:"应用ID",prop:"app_id"},{default:t(()=>[a(u,{modelValue:s.app_id,"onUpdate:modelValue":e[4]||(e[4]=l=>s.app_id=l),placeholder:"请输入支付宝应用ID"},null,8,["modelValue"])]),_:1}),a(d,{label:"私钥",prop:"private_key"},{default:t(()=>[a(u,{modelValue:s.private_key,"onUpdate:modelValue":e[5]||(e[5]=l=>s.private_key=l),type:"textarea",rows:4,placeholder:"请输入应用私钥"},null,8,["modelValue"])]),_:1}),a(d,{label:"公钥",prop:"public_key"},{default:t(()=>[a(u,{modelValue:s.public_key,"onUpdate:modelValue":e[6]||(e[6]=l=>s.public_key=l),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1}),a(d,{label:"启用状态",prop:"status"},{default:t(()=>[a(V,{modelValue:s.status,"onUpdate:modelValue":e[7]||(e[7]=l=>s.status=l)},{default:t(()=>[a(c,{value:1},{default:t(()=>e[14]||(e[14]=[i("启用")])),_:1,__:[14]}),a(c,{value:0},{default:t(()=>e[15]||(e[15]=[i("禁用")])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:t(()=>[a(_,{type:"primary",onClick:N,loading:y.value},{default:t(()=>e[16]||(e[16]=[i("保存配置")])),_:1,__:[16]},8,["loading"]),a(_,{onClick:S,style:{"margin-left":"10px"}},{default:t(()=>e[17]||(e[17]=[i("测试连接")])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1})])])}}},G=I(D,[["__scopeId","data-v-473386c5"]]);export{G as default};
