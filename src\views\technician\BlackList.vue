<!--
  黑名单管理页面
  根据API接口文档开发的黑名单管理功能
  支持查看黑名单列表、权限处理等功能
-->

<template>
  <div class="blacklist-page">
    <!-- 顶部导航 -->
    <TopNav title="黑名单管理" />

    <div class="content-container">
      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="selfImg" label="师傅头像" width="80" align="center">
            <template #default="scope">
              <el-avatar
                v-if="scope.row.selfImg"
                :src="getFirstImage(scope.row.selfImg)"
                :size="50"
                shape="circle"
              />
              <el-avatar v-else :size="50" shape="circle">
                <el-icon><User /></el-icon>
              </el-avatar>
            </template>
          </el-table-column>

          <el-table-column prop="coachName" label="师傅姓名" width="120" />

          <el-table-column prop="sex" label="性别" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.sex === 0 ? 'primary' : 'success'" size="small">
                {{ scope.row.sex === 0 ? '男' : scope.row.sex === 1 ? '女' : '未知' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="mobile" label="手机号" width="150" />

          <el-table-column prop="idCode" label="身份证号" width="180" />

          <el-table-column prop="address" label="详细地址" min-width="150" show-overflow-tooltip />

          <el-table-column prop="createTime" label="加入黑名单时间" width="180" />

          <el-table-column label="权限状态" width="200" align="center">
            <template #default="scope">
              <div class="permission-status">
                <el-tag v-if="scope.row.banTx" type="danger" size="small">限制提现</el-tag>
                <el-tag v-if="scope.row.banTk" type="danger" size="small">限制退款</el-tag>
                <el-tag v-if="scope.row.banDl" type="danger" size="small">限制登录</el-tag>
                <el-tag v-if="!scope.row.banTx && !scope.row.banTk && !scope.row.banDl" type="success" size="small">无限制</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <LbButton
                size="small"
                type="primary"
                @click="handlePermissionSetting(scope.row)"
              >
                权限设置
              </LbButton>
              <LbButton
                size="small"
                type="success"
                @click="handleRemoveFromBlacklist(scope.row)"
              >
                移除黑名单
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 权限设置对话框 -->
    <el-dialog v-model="permissionDialogVisible" title="权限设置" width="500px">
      <el-form :model="permissionForm" label-width="100px">
        <el-form-item label="师傅姓名">
          <span>{{ currentCoach.coachName }}</span>
        </el-form-item>
        <el-form-item label="限制提现">
          <el-switch
            v-model="permissionForm.banTx"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="限制退款">
          <el-switch
            v-model="permissionForm.banTk"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="限制登录">
          <el-switch
            v-model="permissionForm.banDl"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="permissionDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmPermissionSetting" :loading="permissionLoading">
          确认设置
        </LbButton>
      </template>
    </el-dialog>

    <!-- 移除黑名单确认对话框 -->
    <el-dialog v-model="removeDialogVisible" title="移除黑名单" width="500px">
      <el-form :model="removeForm" label-width="100px">
        <el-form-item label="师傅姓名">
          <span>{{ currentCoach.coachName }}</span>
        </el-form-item>
        <el-form-item label="移除原因" required>
          <el-input
            v-model="removeForm.text"
            type="textarea"
            :rows="4"
            placeholder="请输入移除原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="removeDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmRemoveFromBlacklist" :loading="removeLoading">
          确认移除
        </LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const permissionDialogVisible = ref(false)
const removeDialogVisible = ref(false)
const permissionLoading = ref(false)
const removeLoading = ref(false)
const currentCoach = ref({})

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10
})

// 权限设置表单
const permissionForm = reactive({
  coachId: '',
  banTx: 0,
  banTk: 0,
  banDl: 0
})

// 移除黑名单表单
const removeForm = reactive({
  coachId: '',
  text: ''
})

// 方法
const getFirstImage = (images) => {
  if (typeof images === 'string') {
    const imageArray = images.split(',')
    return imageArray[0]
  }
  return images
}

const handlePermissionSetting = (row) => {
  currentCoach.value = row
  permissionForm.coachId = row.id
  permissionForm.banTx = row.banTx || 0
  permissionForm.banTk = row.banTk || 0
  permissionForm.banDl = row.banDl || 0
  permissionDialogVisible.value = true
}

const confirmPermissionSetting = async () => {
  try {
    permissionLoading.value = true
    const result = await proxy.$api.technician.blackPermission({
      coachId: permissionForm.coachId,
      banTx: permissionForm.banTx,
      banTk: permissionForm.banTk,
      banDl: permissionForm.banDl
    })

    if (result.code === '200') {
      ElMessage.success('权限设置成功')
      permissionDialogVisible.value = false
      getBlackList()
    } else {
      ElMessage.error(result.msg || '权限设置失败')
    }
  } catch (error) {
    console.error('权限设置失败:', error)
    ElMessage.error('权限设置失败')
  } finally {
    permissionLoading.value = false
  }
}

const handleRemoveFromBlacklist = (row) => {
  currentCoach.value = row
  removeForm.coachId = row.id
  removeForm.text = ''
  removeDialogVisible.value = true
}

const confirmRemoveFromBlacklist = async () => {
  if (!removeForm.text.trim()) {
    ElMessage.warning('请输入移除原因')
    return
  }

  try {
    removeLoading.value = true
    const result = await proxy.$api.technician.addBlack({
      coachId: removeForm.coachId,
      text: removeForm.text,
      status: 0 // 0表示移除黑名单
    })

    if (result.code === '200') {
      ElMessage.success('移除黑名单成功')
      removeDialogVisible.value = false
      getBlackList()
    } else {
      ElMessage.error(result.msg || '移除黑名单失败')
    }
  } catch (error) {
    console.error('移除黑名单失败:', error)
    ElMessage.error('移除黑名单失败')
  } finally {
    removeLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getBlackList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getBlackList()
}

// API调用方法
const getBlackList = async () => {
  try {
    loading.value = true
    const result = await proxy.$api.technician.blackList(searchForm)

    if (result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0
    } else {
      ElMessage.error(result.msg || '获取黑名单列表失败')
    }
  } catch (error) {
    console.error('获取黑名单列表失败:', error)
    ElMessage.error('获取黑名单列表失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getBlackList()
})
</script>

<style scoped>
.blacklist-page {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.permission-status {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border: none;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa !important;
}

:deep(.el-table th) {
  border-bottom: 2px solid #e4e7ed;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  background: #f5f7fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .content-container {
    margin: 5px;
    padding: 10px;
  }
}
</style>
