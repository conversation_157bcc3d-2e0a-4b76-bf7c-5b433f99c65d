import{T as V,L as k}from"./LbButton-BtU4V_Gr.js";import{_ as C}from"./index-C9Xz1oqp.js";import{E as f}from"./element-fdzwdDuf.js";import{r as u,X as w,h as j,y as z,Q as o,A as m,I as t,al as l,z as N,M as n}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const T={class:"lb-system-city"},B={class:"page-main"},E={__name:"SystemCity",setup(F){const c=u(!1),p=u(),a=w({default_city:"",open_cities:[]}),g=async()=>{try{const e=await(await fetch("/api/system/city/config")).json();e.code===200&&Object.assign(a,e.data||{})}catch(s){console.error("获取配置失败:",s)}},y=async()=>{try{c.value=!0;const e=await(await fetch("/api/system/city/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).json();e.code===200?f.success("配置保存成功"):f.error(e.message||"保存失败")}catch{f.error("保存失败")}finally{c.value=!1}};return j(()=>{g()}),(s,e)=>{const r=l("el-option"),b=l("el-select"),d=l("el-form-item"),i=l("el-checkbox"),v=l("el-checkbox-group"),h=l("el-form"),x=l("el-card");return N(),z("div",T,[o(V),m("div",B,[o(x,{class:"config-card",shadow:"never"},{header:t(()=>e[2]||(e[2]=[m("div",{class:"card-header"},[m("span",null,"城市设置")],-1)])),default:t(()=>[o(h,{model:a,ref_key:"configFormRef",ref:p,"label-width":"140px",class:"config-form"},{default:t(()=>[o(d,{label:"默认城市"},{default:t(()=>[o(b,{modelValue:a.default_city,"onUpdate:modelValue":e[0]||(e[0]=_=>a.default_city=_),placeholder:"请选择默认城市"},{default:t(()=>[o(r,{label:"北京",value:"beijing"}),o(r,{label:"上海",value:"shanghai"}),o(r,{label:"广州",value:"guangzhou"}),o(r,{label:"深圳",value:"shenzhen"})]),_:1},8,["modelValue"])]),_:1}),o(d,{label:"开放城市"},{default:t(()=>[o(v,{modelValue:a.open_cities,"onUpdate:modelValue":e[1]||(e[1]=_=>a.open_cities=_)},{default:t(()=>[o(i,{label:"beijing"},{default:t(()=>e[3]||(e[3]=[n("北京")])),_:1,__:[3]}),o(i,{label:"shanghai"},{default:t(()=>e[4]||(e[4]=[n("上海")])),_:1,__:[4]}),o(i,{label:"guangzhou"},{default:t(()=>e[5]||(e[5]=[n("广州")])),_:1,__:[5]}),o(i,{label:"shenzhen"},{default:t(()=>e[6]||(e[6]=[n("深圳")])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),o(d,null,{default:t(()=>[o(k,{type:"primary",onClick:y,loading:c.value},{default:t(()=>e[7]||(e[7]=[n("保存配置")])),_:1,__:[7]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},O=C(E,[["__scopeId","data-v-84c08d7c"]]);export{O as default};
