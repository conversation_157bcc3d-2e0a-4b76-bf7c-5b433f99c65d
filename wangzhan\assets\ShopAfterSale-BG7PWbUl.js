import{T as ae,L as f}from"./LbButton-BtU4V_Gr.js";import{_ as oe}from"./index-C9Xz1oqp.js";import{E as n,q as se}from"./element-fdzwdDuf.js";import{r as g,X as D,h as re,y as C,Q as t,A as c,I as l,al as i,ar as ne,z as y,J as ie,M as s,H as E,O as _,K as N,P as de,a6 as ue}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const pe={class:"page"},me={class:"page-main"},fe={class:"operation-bar"},ge={class:"table-operate"},ce={class:"pagination-section"},_e={key:0,style:{"margin-top":"20px"}},ve={class:"detail-images"},we=["src"],ye={__name:"ShopAfterSale",setup(be){const x=g(!1),$=g([]),z=g(""),b=g(!1),h=g(""),T=g(!1),V=g(!1),k=g(!1),B=g(),U=g(!1),m=D({page:1,pageSize:10,total:0}),d=D({id:"",brand:"",title:"",time:"",name:"",tel:"",mark:"",images:[]}),u=D({id:"",result:1,remark:""}),A={result:[{required:!0,message:"请选择处理结果",trigger:"change"}],remark:[{required:!0,message:"请输入处理说明",trigger:"blur"}]},w=async(o=1)=>{x.value=!0,m.page=o;try{const e=new URLSearchParams({page:m.page,pageSize:m.pageSize,title:z.value}),r=await(await fetch(`/api/shop/aftersale/list?${e}`)).json();r.code===200?($.value=r.data.list||[],m.total=r.data.total||0):n.error(r.message||"获取数据失败")}catch(e){console.error("获取售后列表失败:",e),n.error("获取数据失败")}finally{x.value=!1}},P=o=>({1:"warning",2:"success",3:"primary",4:"danger"})[o]||"info",F=o=>({1:"待处理",2:"已解决",3:"需要上门",4:"无法解决"})[o]||"未知",I=async()=>{try{const e=await(await fetch("/api/system/config")).json();e.code===200&&(h.value=e.data.service_mobile||"")}catch(o){console.error("获取客服电话失败:",o)}b.value=!0},O=async()=>{if(!h.value){n.error("请填写客服电话");return}T.value=!0;try{const e=await(await fetch("/api/system/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({service_mobile:h.value})})).json();e.code===200?(n.success("保存成功"),b.value=!1):n.error(e.message||"保存失败")}catch(o){console.error("保存客服电话失败:",o),n.error("保存失败")}finally{T.value=!1}},R=async o=>{try{const p=await(await fetch(`/api/shop/aftersale/detail/${o.id}`)).json();p.code===200?(Object.assign(d,p.data),V.value=!0):n.error(p.message||"获取售后详情失败")}catch(e){console.error("获取售后详情失败:",e),n.error("获取售后详情失败")}},q=o=>{u.id=o.id,u.result=1,u.remark="",k.value=!0},J=async()=>{try{await B.value.validate(),U.value=!0;const e=await(await fetch(`/api/shop/aftersale/handle/${u.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({result:u.result,remark:u.remark})})).json();e.code===200?(n.success("处理成功"),k.value=!1,w()):n.error(e.message||"处理失败")}catch(o){console.error("处理售后失败:",o),n.error("处理失败")}finally{U.value=!1}},H=async o=>{try{await se.confirm("确定要删除这条售后记录吗？","删除售后确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const p=await(await fetch(`/api/shop/aftersale/delete/${o.id}`,{method:"DELETE"})).json();p.code===200?(n.success("删除成功"),w()):n.error(p.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除售后失败:",e),n.error("删除失败"))}},K=o=>{m.pageSize=o,w(1)},Q=o=>{w(o)};return re(()=>{w()}),(o,e)=>{const p=i("el-input"),r=i("el-table-column"),X=i("el-tag"),G=i("el-table"),W=i("el-pagination"),Y=i("el-card"),M=i("el-dialog"),v=i("el-descriptions-item"),Z=i("el-descriptions"),j=i("el-radio"),ee=i("el-radio-group"),L=i("el-form-item"),te=i("el-form"),le=ne("loading");return y(),C("div",pe,[t(ae),c("div",me,[t(Y,null,{default:l(()=>[c("div",fe,[t(p,{modelValue:z.value,"onUpdate:modelValue":e[0]||(e[0]=a=>z.value=a),placeholder:"按名称查找",style:{width:"300px","margin-right":"20px"}},null,8,["modelValue"]),t(f,{type:"success",onClick:w},{default:l(()=>e[12]||(e[12]=[s("查找")])),_:1,__:[12]}),t(f,{type:"primary",onClick:I},{default:l(()=>e[13]||(e[13]=[s("客服电话设置")])),_:1,__:[13]})]),ie((y(),E(G,{data:$.value,style:{width:"100%","margin-top":"20px"}},{default:l(()=>[t(r,{label:"ID",prop:"id",width:"120","show-overflow-tooltip":""}),t(r,{label:"品牌",prop:"brand",width:"200","show-overflow-tooltip":""}),t(r,{label:"名称",prop:"title",width:"200","show-overflow-tooltip":""}),t(r,{label:"时间",prop:"time",width:"200","show-overflow-tooltip":""}),t(r,{label:"联系人",prop:"name",width:"200","show-overflow-tooltip":""}),t(r,{label:"联系方式",prop:"tel",width:"200","show-overflow-tooltip":""}),t(r,{label:"备注",prop:"mark","min-width":"240","show-overflow-tooltip":""}),t(r,{label:"状态",width:"100"},{default:l(a=>[t(X,{type:P(a.row.status),size:"small"},{default:l(()=>[s(_(F(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(r,{label:"操作",width:"200",fixed:"right"},{default:l(a=>[c("div",ge,[t(f,{size:"mini",type:"primary",onClick:S=>R(a.row)},{default:l(()=>e[14]||(e[14]=[s(" 查看详情 ")])),_:2,__:[14]},1032,["onClick"]),a.row.status===1?(y(),E(f,{key:0,size:"mini",type:"success",onClick:S=>q(a.row)},{default:l(()=>e[15]||(e[15]=[s(" 处理 ")])),_:2,__:[15]},1032,["onClick"])):N("",!0),t(f,{size:"mini",type:"danger",onClick:S=>H(a.row)},{default:l(()=>e[16]||(e[16]=[s(" 删除 ")])),_:2,__:[16]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[le,x.value]]),c("div",ce,[t(W,{"current-page":m.page,"onUpdate:currentPage":e[1]||(e[1]=a=>m.page=a),"page-size":m.pageSize,"onUpdate:pageSize":e[2]||(e[2]=a=>m.pageSize=a),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1})]),t(M,{modelValue:b.value,"onUpdate:modelValue":e[5]||(e[5]=a=>b.value=a),width:"450px"},{header:l(()=>e[17]||(e[17]=[c("div",{class:"dialog-header"}," 设置客服电话 ",-1)])),footer:l(()=>[c("div",null,[t(f,{type:"primary",onClick:O,loading:T.value},{default:l(()=>e[18]||(e[18]=[s("保存")])),_:1,__:[18]},8,["loading"]),t(f,{onClick:e[4]||(e[4]=a=>b.value=!1)},{default:l(()=>e[19]||(e[19]=[s("取消")])),_:1,__:[19]})])]),default:l(()=>[c("div",null,[t(p,{modelValue:h.value,"onUpdate:modelValue":e[3]||(e[3]=a=>h.value=a),placeholder:"请输入客服电话",style:{width:"100%"}},null,8,["modelValue"])])]),_:1},8,["modelValue"]),t(M,{modelValue:V.value,"onUpdate:modelValue":e[7]||(e[7]=a=>V.value=a),title:"售后详情",width:"60%"},{footer:l(()=>[t(f,{onClick:e[6]||(e[6]=a=>V.value=!1)},{default:l(()=>e[21]||(e[21]=[s("关闭")])),_:1,__:[21]})]),default:l(()=>[t(Z,{column:2,border:""},{default:l(()=>[t(v,{label:"售后ID"},{default:l(()=>[s(_(d.id),1)]),_:1}),t(v,{label:"品牌"},{default:l(()=>[s(_(d.brand),1)]),_:1}),t(v,{label:"产品名称"},{default:l(()=>[s(_(d.title),1)]),_:1}),t(v,{label:"申请时间"},{default:l(()=>[s(_(d.time),1)]),_:1}),t(v,{label:"联系人"},{default:l(()=>[s(_(d.name),1)]),_:1}),t(v,{label:"联系方式"},{default:l(()=>[s(_(d.tel),1)]),_:1}),t(v,{label:"问题描述",span:"2"},{default:l(()=>[s(_(d.mark),1)]),_:1})]),_:1}),d.images&&d.images.length>0?(y(),C("div",_e,[e[20]||(e[20]=c("h4",null,"问题图片",-1)),c("div",ve,[(y(!0),C(de,null,ue(d.images,(a,S)=>(y(),C("img",{key:S,src:a,alt:"问题图片",style:{width:"100px",height:"100px","margin-right":"8px","border-radius":"4px"}},null,8,we))),128))])])):N("",!0)]),_:1},8,["modelValue"]),t(M,{modelValue:k.value,"onUpdate:modelValue":e[11]||(e[11]=a=>k.value=a),title:"处理售后",width:"50%"},{footer:l(()=>[t(f,{onClick:e[10]||(e[10]=a=>k.value=!1)},{default:l(()=>e[25]||(e[25]=[s("取消")])),_:1,__:[25]}),t(f,{type:"primary",onClick:J,loading:U.value},{default:l(()=>e[26]||(e[26]=[s("确定处理")])),_:1,__:[26]},8,["loading"])]),default:l(()=>[t(te,{model:u,rules:A,ref_key:"handleFormRef",ref:B},{default:l(()=>[t(L,{label:"处理结果",prop:"result"},{default:l(()=>[t(ee,{modelValue:u.result,"onUpdate:modelValue":e[8]||(e[8]=a=>u.result=a)},{default:l(()=>[t(j,{value:1},{default:l(()=>e[22]||(e[22]=[s("已解决")])),_:1,__:[22]}),t(j,{value:2},{default:l(()=>e[23]||(e[23]=[s("需要上门")])),_:1,__:[23]}),t(j,{value:3},{default:l(()=>e[24]||(e[24]=[s("无法解决")])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),t(L,{label:"处理说明",prop:"remark"},{default:l(()=>[t(p,{modelValue:u.remark,"onUpdate:modelValue":e[9]||(e[9]=a=>u.remark=a),type:"textarea",rows:4,placeholder:"请输入处理说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},xe=oe(ye,[["__scopeId","data-v-3974f491"]]);export{xe as default};
