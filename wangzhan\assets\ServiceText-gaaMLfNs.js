import{E as d,x as me,q as fe}from"./element-fdzwdDuf.js";import{T as ge,L as _}from"./LbButton-BtU4V_Gr.js";import{L as _e}from"./LbImage-CnNh5Udj.js";import{L as ve}from"./LbPage-DnbiQ0Ct.js";import{_ as he,a as w}from"./index-C9Xz1oqp.js";import{r as p,X as A,h as we,y as E,Q as l,A as g,I as a,al as i,J as ye,ar as be,H as Ve,z as L,M as m,O as V,K as ke,u as Ce}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const xe={class:"service-jingang"},ze={class:"content-container"},Se={class:"search-form-container"},Ie={class:"table-container"},Le={class:"time-column"},Ne={key:0,class:"upload-progress"},Ue={class:"dialog-footer"},De={__name:"ServiceText",setup(Te){const k=p(!1),C=p(!1),v=p(!1),x=p([]),z=p(0),y=p([]),c=p(0),S=p(!1),r=A({pageNum:1,pageSize:5,title:"",status:null}),o=A({id:null,title:"",img:"",link:"",top:0,status:1}),M={title:[{required:!0,message:"请输入金刚区标题",trigger:"blur"}],img:[{required:!0,message:"请上传金刚区图标",trigger:"change"}]},N=p(),U=p(),h=async t=>{t&&(r.pageNum=1),k.value=!0;try{const e={pageNum:r.pageNum,pageSize:r.pageSize};r.title&&(e.title=r.title),r.status!==null&&r.status!==""&&(e.status=r.status);const s=await w.service.navList(e);if(console.log("📋 金刚区列表数据 (API-V2):",s),s.code==="200"){const u=s.data;x.value=u.list||[],z.value=u.totalCount||u.total||0,console.log("📊 处理后的数据:",{list:x.value,total:z.value,pageNum:u.pageNum,pageSize:u.pageSize})}else console.error("❌ API响应错误:",s),d.error(s.message||s.msg||"获取数据失败")}catch(e){console.error("获取金刚区列表失败:",e),d.error("获取数据失败")}finally{k.value=!1}},P=()=>{h(1)},q=()=>{r.title="",r.status=null,N.value?.resetFields(),h(1)},H=()=>{I(),v.value=!0},$=async t=>{I();try{const e=await w.service.navInfo({id:t.id});if(e.code==="200"){const s=e.data;o.id=s.id,o.title=s.title||"",o.img=s.img||"",o.link=s.link||"",o.top=s.top||0,o.status=s.status||1,s.img&&(y.value=[{name:"image",url:s.img,status:"success"}])}else{d.error(e.message||"获取金刚区详情失败");return}}catch(e){console.error("获取金刚区详情失败:",e),d.error("获取金刚区详情失败");return}v.value=!0},j=async t=>{try{await fe.confirm("确定要删除这个金刚区吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await w.service.navDelete({id:t.id});e.code==="200"?(d.success("删除成功"),h()):d.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除金刚区失败:",e),d.error("删除失败"))}},O=async t=>{try{d.success("状态修改成功")}catch(e){t.status=t.status===1?0:1,console.error("修改状态失败:",e),d.error("状态修改失败")}},J=async()=>{try{if(await U.value.validate(),!o.img){d.error("请先上传图片");return}C.value=!0;let t;o.id?t=await w.service.navUpdate(o):t=await w.service.navAdd(o),t.code==="200"?(d.success(o.id?"更新成功":"新增成功"),v.value=!1,h()):d.error(t.message||"操作失败")}catch(t){console.error("提交失败:",t),d.error("操作失败")}finally{C.value=!1}},K=t=>{r.pageSize=t,D(1)},D=t=>{r.pageNum=t,h()},Q=t=>(console.log("📋 图片上传前验证:",t),t.type.indexOf("image/")===0?(console.log("✅ 图片验证通过"),!0):(d.error("只能上传图片文件!"),!1)),W=async(t,e)=>{console.log("🖼️ 图片文件变更:",t,e),t.status==="ready"&&await G(t)},X=(t,e)=>{console.log("🗑️ 移除图片:",t),o.img="",c.value=0},G=async t=>{console.log("📤 开始上传图片:",t);try{S.value=!0,c.value=0;const e=new FormData;e.append("multipartFile",t.raw),console.log("📦 FormData创建完成:",e);const s=await w.upload.uploadFile(e,u=>{c.value=Math.round(u.loaded*100/u.total),console.log("📊 上传进度:",c.value+"%")});if(console.log("✅ 图片上传成功:",s),s.code===200||s.code==="200")o.img=s.data.url||s.data.fileUrl||s.data,d.success("图片上传成功"),y.value=[{name:t.name,url:o.img,status:"success"}],console.log("💾 图片URL已保存到表单:",o.img);else throw new Error(s.message||s.msg||"上传失败")}catch(e){console.error("❌ 图片上传失败:",e),d.error("图片上传失败: "+(e.message||"未知错误")),y.value=[],o.img=""}finally{S.value=!1,c.value=0}},T=()=>{v.value=!1,I()},I=()=>{o.id=null,o.title="",o.img="",o.link="",o.top=0,o.status=1,y.value=[],c.value=0,S.value=!1},Y=t=>t?t.split(" ")[0]:"-",Z=t=>t?t.split(" ")[1]:"-";return we(()=>{h()}),(t,e)=>{const s=i("el-input"),u=i("el-form-item"),F=i("el-option"),ee=i("el-select"),le=i("el-col"),te=i("el-row"),B=i("el-form"),f=i("el-table-column"),ae=i("el-link"),oe=i("el-switch"),se=i("el-table"),ne=i("el-icon"),re=i("el-upload"),ie=i("el-progress"),ue=i("el-input-number"),R=i("el-radio"),de=i("el-radio-group"),ce=i("el-dialog"),pe=be("loading");return L(),E("div",xe,[l(ge,{title:"金刚区管理"}),g("div",ze,[g("div",Se,[l(B,{ref_key:"searchFormRef",ref:N,model:r,inline:!0,class:"search-form"},{default:a(()=>[l(te,{gutter:20},{default:a(()=>[l(le,{span:24},{default:a(()=>[l(u,{label:"标题查询",prop:"title"},{default:a(()=>[l(s,{size:"default",modelValue:r.title,"onUpdate:modelValue":e[0]||(e[0]=n=>r.title=n),placeholder:"请输入金刚区标题",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(u,{label:"状态",prop:"status"},{default:a(()=>[l(ee,{size:"default",modelValue:r.status,"onUpdate:modelValue":e[1]||(e[1]=n=>r.status=n),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[l(F,{label:"启用",value:1}),l(F,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),l(u,null,{default:a(()=>[l(_,{size:"default",type:"primary",icon:"Search",onClick:P},{default:a(()=>e[7]||(e[7]=[m(" 搜索 ")])),_:1,__:[7]}),l(_,{size:"default",icon:"RefreshLeft",onClick:q},{default:a(()=>e[8]||(e[8]=[m(" 重置 ")])),_:1,__:[8]}),l(_,{size:"default",type:"primary",icon:"Plus",onClick:H},{default:a(()=>e[9]||(e[9]=[m(" 新增金刚区 ")])),_:1,__:[9]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),g("div",Ie,[ye((L(),Ve(se,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[l(f,{type:"selection",width:"55"}),l(f,{prop:"id",label:"ID",width:"80",align:"center"}),l(f,{prop:"img",label:"图标",width:"120"},{default:a(n=>[l(_e,{src:n.row.img,width:"80",height:"50"},null,8,["src"])]),_:1}),l(f,{prop:"title",label:"标题","min-width":"150"}),l(f,{prop:"link",label:"链接地址","min-width":"200"},{default:a(n=>[l(ae,{href:n.row.link,target:"_blank",type:"primary"},{default:a(()=>[m(V(n.row.link||"无链接"),1)]),_:2},1032,["href"])]),_:1}),l(f,{prop:"top",label:"排序",width:"80",align:"center"}),l(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(n=>[l(oe,{modelValue:n.row.status,"onUpdate:modelValue":b=>n.row.status=b,"active-value":1,"inactive-value":0,onChange:b=>O(n.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(f,{label:"时间",width:"160"},{default:a(n=>[g("div",Le,[g("p",null,V(Y(n.row.createTime)),1),g("p",null,V(Z(n.row.createTime)),1)])]),_:1}),l(f,{label:"操作",width:"200",fixed:"right"},{default:a(n=>[l(_,{size:"default",type:"primary",onClick:b=>$(n.row)},{default:a(()=>e[10]||(e[10]=[m(" 编辑 ")])),_:2,__:[10]},1032,["onClick"]),l(_,{size:"default",type:"danger",onClick:b=>j(n.row)},{default:a(()=>e[11]||(e[11]=[m(" 删除 ")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,k.value]])]),l(ve,{page:r.pageNum,"page-size":r.pageSize,total:z.value,onHandleSizeChange:K,onHandleCurrentChange:D},null,8,["page","page-size","total"])]),l(ce,{modelValue:v.value,"onUpdate:modelValue":e[6]||(e[6]=n=>v.value=n),title:o.id?"编辑金刚区":"新增金刚区",width:"600px","before-close":T},{footer:a(()=>[g("span",Ue,[l(_,{onClick:T},{default:a(()=>e[15]||(e[15]=[m("取消")])),_:1,__:[15]}),l(_,{type:"primary",loading:C.value,onClick:J},{default:a(()=>e[16]||(e[16]=[m(" 确定 ")])),_:1,__:[16]},8,["loading"])])]),default:a(()=>[l(B,{ref_key:"formRef",ref:U,model:o,rules:M,"label-width":"100px"},{default:a(()=>[l(u,{label:"标题",prop:"title"},{default:a(()=>[l(s,{modelValue:o.title,"onUpdate:modelValue":e[2]||(e[2]=n=>o.title=n),placeholder:"请输入金刚区标题"},null,8,["modelValue"])]),_:1}),l(u,{label:"图标",prop:"img"},{default:a(()=>[l(re,{class:"image-upload",action:"#","auto-upload":!1,"on-change":W,"on-remove":X,"before-upload":Q,"file-list":y.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:a(()=>e[12]||(e[12]=[g("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:a(()=>[l(ne,null,{default:a(()=>[l(Ce(me))]),_:1})]),_:1},8,["file-list"]),c.value>0&&c.value<100?(L(),E("div",Ne,[l(ie,{percentage:c.value,"show-text":!0},null,8,["percentage"]),g("p",null,"上传中... "+V(c.value)+"%",1)])):ke("",!0)]),_:1}),l(u,{label:"链接地址",prop:"link"},{default:a(()=>[l(s,{modelValue:o.link,"onUpdate:modelValue":e[3]||(e[3]=n=>o.link=n),placeholder:"请输入链接地址（可选）"},null,8,["modelValue"])]),_:1}),l(u,{label:"排序",prop:"top"},{default:a(()=>[l(ue,{modelValue:o.top,"onUpdate:modelValue":e[4]||(e[4]=n=>o.top=n),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(u,{label:"状态",prop:"status"},{default:a(()=>[l(de,{modelValue:o.status,"onUpdate:modelValue":e[5]||(e[5]=n=>o.status=n)},{default:a(()=>[l(R,{value:1},{default:a(()=>e[13]||(e[13]=[m("启用")])),_:1,__:[13]}),l(R,{value:0},{default:a(()=>e[14]||(e[14]=[m("禁用")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},qe=he(De,[["__scopeId","data-v-d8113f11"]]);export{qe as default};
