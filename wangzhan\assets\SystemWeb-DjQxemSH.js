import{T as x,L as c}from"./LbButton-BtU4V_Gr.js";import{_ as T}from"./index-C9Xz1oqp.js";import{E as m}from"./element-fdzwdDuf.js";import{r as g,X as A,h as U,y as j,Q as o,A as f,I as t,al as r,z as I,M as d}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const N={class:"lb-system-web"},S={class:"page-main"},B={__name:"SystemWeb",setup(D){const i=g(!1),u=g(),a=A({name:"",app_id:"",app_secret:"",token:"",status:1}),b={name:[{required:!0,message:"请输入公众号名称",trigger:"blur"}],app_id:[{required:!0,message:"请输入AppID",trigger:"blur"}]},y=async()=>{try{const e=await(await fetch("/api/system/web/config")).json();e.code===200&&Object.assign(a,e.data||{})}catch(n){console.error("获取配置失败:",n)}},V=async()=>{try{await u.value.validate(),i.value=!0;const e=await(await fetch("/api/system/web/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).json();e.code===200?m.success("配置保存成功"):m.error(e.message||"保存失败")}catch{m.error("保存失败")}finally{i.value=!1}},v=()=>{Object.assign(a,{name:"",app_id:"",app_secret:"",token:"",status:1}),u.value?.clearValidate()};return U(()=>{y()}),(n,e)=>{const p=r("el-input"),s=r("el-form-item"),_=r("el-radio"),w=r("el-radio-group"),k=r("el-form"),C=r("el-card");return I(),j("div",N,[o(x),f("div",S,[o(C,{class:"config-card",shadow:"never"},{header:t(()=>e[5]||(e[5]=[f("div",{class:"card-header"},[f("span",null,"公众号设置")],-1)])),default:t(()=>[o(k,{model:a,rules:b,ref_key:"configFormRef",ref:u,"label-width":"140px",class:"config-form"},{default:t(()=>[o(s,{label:"公众号名称",prop:"name"},{default:t(()=>[o(p,{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=l=>a.name=l),placeholder:"请输入公众号名称"},null,8,["modelValue"])]),_:1}),o(s,{label:"AppID",prop:"app_id"},{default:t(()=>[o(p,{modelValue:a.app_id,"onUpdate:modelValue":e[1]||(e[1]=l=>a.app_id=l),placeholder:"请输入公众号AppID"},null,8,["modelValue"])]),_:1}),o(s,{label:"AppSecret",prop:"app_secret"},{default:t(()=>[o(p,{modelValue:a.app_secret,"onUpdate:modelValue":e[2]||(e[2]=l=>a.app_secret=l),placeholder:"请输入公众号AppSecret",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(s,{label:"Token",prop:"token"},{default:t(()=>[o(p,{modelValue:a.token,"onUpdate:modelValue":e[3]||(e[3]=l=>a.token=l),placeholder:"请输入Token"},null,8,["modelValue"])]),_:1}),o(s,{label:"启用状态",prop:"status"},{default:t(()=>[o(w,{modelValue:a.status,"onUpdate:modelValue":e[4]||(e[4]=l=>a.status=l)},{default:t(()=>[o(_,{value:1},{default:t(()=>e[6]||(e[6]=[d("启用")])),_:1,__:[6]}),o(_,{value:0},{default:t(()=>e[7]||(e[7]=[d("禁用")])),_:1,__:[7]})]),_:1},8,["modelValue"])]),_:1}),o(s,null,{default:t(()=>[o(c,{type:"primary",onClick:V,loading:i.value},{default:t(()=>e[8]||(e[8]=[d("保存配置")])),_:1,__:[8]},8,["loading"]),o(c,{onClick:v,style:{"margin-left":"10px"}},{default:t(()=>e[9]||(e[9]=[d("重置")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1})])])}}},R=T(B,[["__scopeId","data-v-1a87ebe1"]]);export{R as default};
