import{r as d,X as _e,c as H,ay as he,h as Ie,y as x,Q as a,A as u,I as o,u as K,az as be,al as c,z as y,a5 as Ce,P as Q,a6 as X,H as $,O as g,M as k,K as R}from"./vendor-DmFBDimT.js";import{v as we,E as i,x as Ve}from"./element-fdzwdDuf.js";import{T as xe,L as G}from"./LbButton-BtU4V_Gr.js";import{L as Ne}from"./LbUeditor-Dh_pKaDh.js";import{_ as Se,a as I}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const ke={class:"service-edit"},Te={class:"page-main"},Le={class:"category-selector-container"},Ue={class:"category-option"},Pe={class:"city-selector-container"},Ae={key:0,style:{color:"#999","font-size":"12px"}},De={key:0,class:"selected-cities-preview"},Ee={class:"city-count"},ze={class:"city-names"},Be={key:0,class:"upload-progress"},Oe={class:"editor-container"},Me={__name:"ServiceEdit",setup(Fe){const P=he(),W=be(),A=d(!1),b=d([]),p=d(0),T=d(!1),L=d([]),D=d(!1),E=d([]),z=d(!1),C=d([]),B=d(!1),q=d(""),t=_e({id:null,title:"",subTitle:"",cover:"",price:0,serviceCateId:null,serviceCateName:"",servicePriceType:0,top:0,status:1,introduce:"",explain:"",notice:"",cityId:[],cityStr:"",agentIds:[]}),Y={title:[{required:!0,message:"请输入服务名称",trigger:"blur"}],serviceCateId:[{required:!0,message:"请选择服务分类",trigger:"change"}],servicePriceType:[{required:!0,message:"请选择报价类型",trigger:"change"}]},j=d(),Z={value:"cityId",label:"name",children:"children",leaf:"leaf",checkStrictly:!1,emitPath:!0,multiple:!0},O=H(()=>P.query.type==="edit"&&!!P.query.id),w=H(()=>{const l=[];return(r=>!r||!Array.isArray(r)||r.length===0?[]:(r.forEach(n=>{if(Array.isArray(n)&&n.length>0){const N=((v,_)=>{if(!v||!_||_.length===0)return null;let f=v,V=null;for(let h=0;h<_.length;h++){const M=_[h],S=f.find(F=>F.cityId===M);if(S)h===_.length-1&&(V=S.name),f=S.children||[];else break}return V})(E.value,n);N&&l.push(N)}}),l))(t.cityId)}),ee=()=>{t.cityId=[],t.cityStr="",i({message:"已清空所有已选城市",type:"info",duration:2e3})},J=()=>{W.push({name:"ServiceList"})},te=async()=>{try{await j.value.validate(),A.value=!0;const l={...t,cityId:t.cityStr||""};console.log("📤 提交服务数据:",l);let e;t.id?e=await I.service.serviceUpdate(l):e=await I.service.serviceAdd(l),e.code==="200"?(i.success(t.id?"更新成功":"新增成功"),J()):i.error(e.message||"操作失败")}catch(l){console.error("提交失败:",l),i.error("操作失败")}finally{A.value=!1}},le=async()=>{try{D.value=!0;const l=await I.service.serviceCateList({status:1,pageNum:1,pageSize:100});if(console.log("📂 服务分类列表数据:",l),l.code==="200"||l.code===200){const e=l.data.list||l.data||[];console.log("📂 服务分类原始数据:",l),console.log("📂 处理后的分类数据:",e);const r=e.map(n=>({id:n.id,name:n.name||n.categoryName||n.title,...n}));L.value=r,console.log("📂 格式化后的分类数据:",r),r.length>0&&(console.log("📂 第一个分类项:",r[0]),console.log("📂 分类选项总数:",r.length)),r.length===0&&(console.warn("⚠️ 服务分类数据为空，添加测试数据"),L.value=[{id:1,name:"测试分类1"},{id:2,name:"测试分类2"},{id:3,name:"测试分类3"}])}else console.error("❌ 获取服务分类失败:",l),i.error(l.message||l.msg||"获取服务分类失败")}catch(l){console.error("加载分类选项失败:",l),i.error("加载分类选项失败")}finally{D.value=!1}},ae=async()=>{try{z.value=!0;const l=await I.technician.cityTree();console.log("🏙️ 城市树形数据:",l),l.code===200||l.code==="200"?E.value=l.data||[]:(console.error("❌ 获取城市数据失败:",l),i.error(l.message||l.msg||"获取城市数据失败"))}catch(l){console.error("加载城市数据失败:",l),i.error("加载城市数据失败")}finally{z.value=!1}},oe=l=>{if(console.log("📂 服务分类选择变化:",l),l){const e=L.value.find(r=>r.id===l);console.log("📂 选中的分类:",e),e&&(t.serviceCateName=e.name,console.log("📂 已保存分类名称:",t.serviceCateName),i({message:`已选择服务分类: ${e.name}`,type:"success",duration:2e3}))}else t.serviceCateName=""},se=l=>{if(console.log("🔄 城市选择变化:",l),l&&Array.isArray(l)){console.log("🏙️ 选中的城市路径:",l);const e=JSON.stringify(l);console.log("🏙️ 城市字符串:",e),t.cityStr=e,console.log("🏙️ 已选择城市:",w.value)}else t.cityStr=""},re=async l=>{if(!l){C.value=[];return}try{B.value=!0;const e=await I.service.agentList({name:l,pageNum:1,pageSize:20});console.log("🔍 服务点搜索结果:",e),e.code==="200"?C.value=e.data.list||e.data||[]:(console.error("❌ 搜索服务点失败:",e),C.value=[])}catch(e){console.error("搜索服务点失败:",e),C.value=[]}finally{B.value=!1}},ne=l=>(console.log("📋 图片上传前验证:",l),l.type.indexOf("image/")===0?l.size/1024/1024<2?(console.log("✅ 图片验证通过"),!0):(i.error("上传图片大小不能超过 2MB!"),!1):(i.error("只能上传图片文件!"),!1)),ce=async(l,e)=>{console.log("🖼️ 图片文件变更:",l,e),l.status==="ready"&&!T.value&&l.raw&&(e.length>1&&e.splice(0,e.length-1),await de(l))},ie=l=>{console.log("🗑️ 移除图片:",l),t.cover="",b.value=[],p.value=0},de=async l=>{console.log("📤 开始上传图片:",l);try{T.value=!0,p.value=0;const e=new FormData;e.append("multipartFile",l.raw),console.log("📦 FormData创建完成:",e);const r=await I.upload.uploadFile(e,n=>{p.value=Math.round(n.loaded*100/n.total),console.log("📊 上传进度:",p.value+"%")});if(console.log("✅ 图片上传成功:",r),r.code===200||r.code==="200")t.cover=r.data.url||r.data.fileUrl||r.data,i.success("图片上传成功"),b.value=[{name:l.name,url:t.cover,status:"success"}],console.log("💾 图片URL已保存到表单:",t.cover);else throw new Error(r.message||r.msg||"上传失败")}catch(e){console.error("❌ 图片上传失败:",e),i.error("图片上传失败: "+(e.message||"未知错误")),b.value=[],t.cover=""}finally{T.value=!1,p.value=0}},ue=async()=>{try{const l=await I.service.serviceInfo({id:P.query.id});if(l.code==="200"){const e=l.data;if(t.id=e.id,t.title=e.title||"",t.subTitle=e.subTitle||"",t.cover=e.cover||"",t.price=e.price||0,t.serviceCateId=e.serviceCateId||null,t.serviceCateName=e.serviceCateName||"",t.servicePriceType=e.servicePriceType||0,t.top=e.top||0,t.status=e.status||1,t.introduce=e.introduce||"",t.explain=e.explain||"",t.notice=e.notice||"",e.cityId&&typeof e.cityId=="string")try{t.cityId=JSON.parse(e.cityId),t.cityStr=e.cityId}catch{console.warn("Invalid cityId string format, resetting cityId:",e.cityId),t.cityId=[],t.cityStr=""}else Array.isArray(e.cityId)?(t.cityId=e.cityId,t.cityStr=JSON.stringify(e.cityId)):(t.cityId=[],t.cityStr="");t.agentIds=e.agentIds||[],e.cover&&(b.value=[{name:"cover",url:e.cover,status:"success"}])}else{i.error(l.message||"获取服务详情失败");return}}catch(l){console.error("获取服务详情失败:",l),i.error("获取服务详情失败");return}},pe=()=>{t.id=null,t.title="",t.subTitle="",t.cover="",t.price=0,t.serviceCateId=null,t.serviceCateName="",t.servicePriceType=0,t.top=0,t.status=1,t.introduce="",t.explain="",t.notice="",t.cityId=[],t.cityStr="",t.agentIds=[],b.value=[],p.value=0,T.value=!1,C.value=[]};return Ie(()=>{q.value=O.value?"编辑服务项目":"新增服务项目",le(),ae(),O.value?ue():pe()}),(l,e)=>{const r=c("el-input"),n=c("el-form-item"),m=c("el-col"),N=c("el-input-number"),v=c("el-row"),_=c("el-tag"),f=c("el-option"),V=c("el-select"),h=c("el-radio"),M=c("el-radio-group"),S=c("el-cascader"),F=c("el-tooltip"),me=c("el-button"),ge=c("el-icon"),ve=c("el-upload"),fe=c("el-progress"),ye=c("el-form");return y(),x("div",ke,[a(xe,{title:q.value,isBack:!0},null,8,["title"]),u("div",Te,[a(K(we),{class:"service-form-card",shadow:l.never},{default:o(()=>[a(ye,{ref_key:"formRef",ref:j,model:t,rules:Y,"label-width":"100px",class:"service-form"},{default:o(()=>[a(v,{gutter:20},{default:o(()=>[a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"项目名称",prop:"title"},{default:o(()=>[a(r,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=s=>t.title=s),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1})]),_:1}),a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"服务价格",prop:"price"},{default:o(()=>[a(N,{modelValue:t.price,"onUpdate:modelValue":e[1]||(e[1]=s=>t.price=s),min:0,precision:2,placeholder:"请输入服务价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(v,{gutter:20},{default:o(()=>[a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"排序值",prop:"top"},{default:o(()=>[a(N,{modelValue:t.top,"onUpdate:modelValue":e[2]||(e[2]=s=>t.top=s),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"服务分类",prop:"serviceCateId"},{default:o(()=>[u("div",Le,[a(V,{modelValue:t.serviceCateId,"onUpdate:modelValue":e[3]||(e[3]=s=>t.serviceCateId=s),placeholder:"请选择服务分类",style:{width:"100%"},filterable:"",loading:D.value,clearable:"",onChange:oe,"popper-class":"category-select-dropdown"},Ce({default:o(()=>[(y(!0),x(Q,null,X(L.value,s=>(y(),$(f,{key:s.id,label:s.name,value:s.id},{default:o(()=>[u("div",Ue,[u("span",null,g(s.name),1)])]),_:2},1032,["label","value"]))),128))]),_:2},[t.serviceCateName?{name:"prefix",fn:o(()=>[a(_,{size:"small",type:"success",class:"selected-category-tag"},{default:o(()=>[k(g(t.serviceCateName),1)]),_:1})]),key:"0"}:void 0]),1032,["modelValue","loading"])])]),_:1})]),_:1})]),_:1}),a(v,{gutter:20},{default:o(()=>[a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"报价类型",prop:"servicePriceType"},{default:o(()=>[a(V,{modelValue:t.servicePriceType,"onUpdate:modelValue":e[4]||(e[4]=s=>t.servicePriceType=s),placeholder:"请选择报价类型",style:{width:"100%"}},{default:o(()=>[a(f,{label:"一口价模式",value:0}),a(f,{label:"报价模式",value:1}),a(f,{label:"两者都有",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(m,{xs:24,sm:12,md:12,lg:12,xl:12},{default:o(()=>[a(n,{label:"状态",prop:"status"},{default:o(()=>[a(M,{modelValue:t.status,"onUpdate:modelValue":e[5]||(e[5]=s=>t.status=s)},{default:o(()=>[a(h,{value:1},{default:o(()=>e[9]||(e[9]=[k("可用")])),_:1,__:[9]}),a(h,{value:-1},{default:o(()=>e[10]||(e[10]=[k("禁用")])),_:1,__:[10]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(v,{gutter:20},{default:o(()=>[a(m,{span:24},{default:o(()=>[a(n,{label:"城市",prop:"cityId"},{default:o(()=>[u("div",Pe,[a(S,{modelValue:t.cityId,"onUpdate:modelValue":e[6]||(e[6]=s=>t.cityId=s),options:E.value,props:Z,placeholder:"请选择省份和城市",style:{width:"100%"},class:"city-cascader",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",loading:z.value,onChange:se,"popper-class":"city-cascader-popper"},{default:o(({node:s,data:U})=>[u("span",null,g(U.name),1),U.children&&U.children.length?(y(),x("span",Ae," ("+g(U.children.length)+"个城市) ",1)):R("",!0)]),_:1},8,["modelValue","options","loading"]),w.value.length?(y(),x("div",De,[u("span",Ee,"已选择 "+g(w.value.length)+" 个城市",1),a(F,{placement:"top",content:w.value.join("、")},{default:o(()=>[u("span",ze,g(w.value.slice(0,3).join("、"))+g(w.value.length>3?"...":""),1)]),_:1},8,["content"]),a(me,{type:"danger",size:"small",plain:"",icon:"Delete",circle:"",onClick:ee,class:"clear-cities-btn"})])):R("",!0)])]),_:1})]),_:1})]),_:1}),a(v,{gutter:20},{default:o(()=>[a(m,{span:24},{default:o(()=>[a(n,{label:"服务点",prop:"agentIds"},{default:o(()=>[a(V,{modelValue:t.agentIds,"onUpdate:modelValue":e[7]||(e[7]=s=>t.agentIds=s),placeholder:"请输入关键词搜索",style:{width:"100%"},multiple:"",filterable:"",remote:"","reserve-keyword":"","remote-method":re,loading:B.value,"value-key":"id"},{default:o(()=>[(y(!0),x(Q,null,X(C.value,s=>(y(),$(f,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1})]),_:1}),a(n,{label:"封面图片",prop:"cover"},{default:o(()=>[a(ve,{class:"image-upload",action:"#","auto-upload":!1,"on-change":ce,"on-remove":ie,"before-upload":ne,"file-list":b.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:o(()=>e[11]||(e[11]=[u("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件，建议尺寸 750x400px ",-1)])),default:o(()=>[a(ge,null,{default:o(()=>[a(K(Ve))]),_:1})]),_:1},8,["file-list"]),p.value>0&&p.value<100?(y(),x("div",Be,[a(fe,{percentage:p.value,"show-text":!0},null,8,["percentage"]),u("p",null,"上传中... "+g(p.value)+"%",1)])):R("",!0)]),_:1}),a(n,{label:"项目介绍",prop:"introduce"},{default:o(()=>[u("div",Oe,[a(Ne,{modelValue:t.introduce,"onUpdate:modelValue":e[8]||(e[8]=s=>t.introduce=s),height:"280px",placeholder:"请输入项目介绍..."},null,8,["modelValue"])])]),_:1}),a(n,{class:"form-actions"},{default:o(()=>[a(G,{type:"primary",loading:A.value,onClick:te},{default:o(()=>[k(g(O.value?"更新":"新增"),1)]),_:1},8,["loading"]),a(G,{onClick:J},{default:o(()=>e[12]||(e[12]=[k(" 返回 ")])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),_:1},8,["shadow"])])])}}},Qe=Se(Me,[["__scopeId","data-v-912c276f"]]);export{Qe as default};
