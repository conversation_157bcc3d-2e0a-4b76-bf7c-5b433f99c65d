import{T as y,L as x}from"./LbButton-BtU4V_Gr.js";import{_ as V}from"./index-C9Xz1oqp.js";import{E as c}from"./element-fdzwdDuf.js";import{r as f,X as b,h as w,y as T,Q as a,A as s,I as t,al as m,z as C,M as N}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const j={class:"lb-system-travel"},k={class:"page-main"},B={__name:"SystemTravel",setup(U){const i=f(!1),p=f(),o=b({max_distance:10,advance_time:2,cancel_time:15}),_=async()=>{try{const e=await(await fetch("/api/system/travel/config")).json();e.code===200&&Object.assign(o,e.data||{})}catch(n){console.error("获取配置失败:",n)}},u=async()=>{try{i.value=!0;const e=await(await fetch("/api/system/travel/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?c.success("配置保存成功"):c.error(e.message||"保存失败")}catch{c.error("保存失败")}finally{i.value=!1}};return w(()=>{_()}),(n,e)=>{const d=m("el-input-number"),r=m("el-form-item"),g=m("el-form"),v=m("el-card");return C(),T("div",j,[a(y),s("div",k,[a(v,{class:"config-card",shadow:"never"},{header:t(()=>e[3]||(e[3]=[s("div",{class:"card-header"},[s("span",null,"出行设置")],-1)])),default:t(()=>[a(g,{model:o,ref_key:"configFormRef",ref:p,"label-width":"140px",class:"config-form"},{default:t(()=>[a(r,{label:"最大接单距离"},{default:t(()=>[a(d,{modelValue:o.max_distance,"onUpdate:modelValue":e[0]||(e[0]=l=>o.max_distance=l),min:1,max:100},null,8,["modelValue"]),e[4]||(e[4]=s("span",{style:{"margin-left":"10px"}},"公里",-1))]),_:1,__:[4]}),a(r,{label:"预约提前时间"},{default:t(()=>[a(d,{modelValue:o.advance_time,"onUpdate:modelValue":e[1]||(e[1]=l=>o.advance_time=l),min:1,max:24},null,8,["modelValue"]),e[5]||(e[5]=s("span",{style:{"margin-left":"10px"}},"小时",-1))]),_:1,__:[5]}),a(r,{label:"取消订单时限"},{default:t(()=>[a(d,{modelValue:o.cancel_time,"onUpdate:modelValue":e[2]||(e[2]=l=>o.cancel_time=l),min:1,max:60},null,8,["modelValue"]),e[6]||(e[6]=s("span",{style:{"margin-left":"10px"}},"分钟",-1))]),_:1,__:[6]}),a(r,null,{default:t(()=>[a(x,{type:"primary",onClick:u,loading:i.value},{default:t(()=>e[7]||(e[7]=[N("保存配置")])),_:1,__:[7]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},I=V(B,[["__scopeId","data-v-e43eceb4"]]);export{I as default};
