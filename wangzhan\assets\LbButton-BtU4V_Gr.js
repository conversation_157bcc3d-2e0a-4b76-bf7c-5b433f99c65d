import{c as y,ay as v,y as h,Q as g,I as u,P as _,a6 as N,H as m,K as S,M as k,al as d,L as B,O as z,ax as T,z as c,B as C,D as L}from"./vendor-DmFBDimT.js";import{_ as b}from"./index-C9Xz1oqp.js";const x=[{name:"服务项目",path:"/service",icon:"Service",menuName:"Service"},{name:"师傅管理",path:"/technician",icon:"User",menuName:"Technician"},{name:"营销管理",path:"/market",icon:"Promotion",menuName:"Market"},{name:"订单管理",path:"/shop",icon:"Document",menuName:"Shop"},{name:"分销管理",path:"/distribution",icon:"Share",menuName:"Distribution"},{name:"财务管理",path:"/finance",icon:"Money",menuName:"Finance"},{name:"用户管理",path:"/user",icon:"UserFilled",menuName:"User"},{name:"账号设置",path:"/account",icon:"Setting",menuName:"Account"},{name:"系统设置",path:"/sys",icon:"Tools",menuName:"System"},{name:"日志管理",path:"/log",icon:"Document",menuName:"Log"}],M={class:"top-nav"},P={__name:"TopNav",setup(e){const i=v();T();const n=y(()=>{const t=[];t.push({title:"首页",path:"/",icon:"House"});const s=i.path,a=x.find(l=>s.startsWith(l.path));if(a&&t.push({title:a.name,path:a.path,icon:a.icon}),i.meta&&i.meta.title)t.push({title:i.meta.title,path:s});else if(s.split("/").filter(Boolean).length>1){const o=p(s);o&&t.push({title:o,path:s})}return t}),p=t=>({"/service/list":"服务项目","/service/banner":"轮播图设置","/service/jingang":"金刚区设置","/service/fenlei":"分类设置","/service/daili":"服务点设置","/service/peizhi":"项目配置","/service/edit":"编辑服务项目","/service/add":"新增服务项目","/technician/list":"师傅管理","/technician/level":"师傅等级","/technician/deposit":"师傅押金","/technician/distance":"接单范围","/market/list":"卡券管理","/market/notice":"公告设置","/market/partner":"合伙人管理","/market/partner/invite":"合伙人邀请列表","/shop/order":"订单管理","/shop/refund":"退款管理","/shop/evaluate/list":"评价管理","/shop/commission/distribution":"分销佣金","/shop/AfterSale":"售后管理","/distribution/examine":"分销商审核","/distribution/set":"分销设置","/finance/list":"财务管理","/finance/record":"提现申请","/custom/list":"用户管理","/account/franchisee":"代理商管理","/account/acountRole":"角色管理","/account/acountAdmin":"管理员","/account/acountThree":"第三方管理","/sys/upgrade":"系统升级","/sys/examine":"上传微信审核","/sys/wechat":"小程序设置","/sys/web":"公众号设置","/sys/app":"APP设置","/sys/info":"隐私协议","/sys/payment":"支付配置","/sys/upload":"上传配置","/sys/transaction":"交易设置","/sys/notice":"万能通知","/sys/message":"短信通知","/sys/information":"备案信息","/sys/print":"打印机设置","/sys/car_fee":"车费设置","/sys/city":"城市设置","/sys/travel":"出行设置","/sys/other":"其他设置","/sys/version":"版本管理"})[t]||"";return(t,s)=>{const a=d("el-icon"),l=d("el-breadcrumb-item"),o=d("el-breadcrumb");return c(),h("div",M,[g(o,{separator:"/"},{default:u(()=>[(c(!0),h(_,null,N(n.value,(r,f)=>(c(),m(l,{key:f,to:r.path&&f<n.value.length-1?{path:r.path}:null},{default:u(()=>[r.icon&&f===0?(c(),m(a,{key:0},{default:u(()=>[(c(),m(B(r.icon)))]),_:2},1024)):S("",!0),k(" "+z(r.title),1)]),_:2},1032,["to"]))),128))]),_:1})])}}},$=b(P,[["__scopeId","data-v-a2629bb3"]]),D={__name:"LbButton",props:{size:{type:String,default:"default",validator:e=>["large","default","small","mini"].includes(e)},type:{type:String,default:"default",validator:e=>["primary","success","warning","danger","info","text","default"].includes(e)},plain:{type:Boolean,default:!1},round:{type:Boolean,default:!1},circle:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},icon:{type:String,default:""},autofocus:{type:Boolean,default:!1},nativeType:{type:String,default:"button",validator:e=>["button","submit","reset"].includes(e)}},emits:["click"],setup(e,{emit:i}){const n=e,p=i,t=y(()=>({"lb-button":!0,[`lb-button--${n.size}`]:n.size!=="default",[`lb-button--${n.type}`]:n.type!=="default"})),s=a=>{!n.disabled&&!n.loading&&p("click",a)};return(a,l)=>{const o=d("el-button");return c(),m(o,{size:e.size,type:e.type,plain:e.plain,round:e.round,circle:e.circle,loading:e.loading,disabled:e.disabled,icon:e.icon,autofocus:e.autofocus,"native-type":e.nativeType,onClick:s,class:L(t.value)},{default:u(()=>[C(a.$slots,"default",{},void 0,!0)]),_:3},8,["size","type","plain","round","circle","loading","disabled","icon","autofocus","native-type","class"])}}},F=b(D,[["__scopeId","data-v-0b2b5f09"]]);export{F as L,$ as T};
