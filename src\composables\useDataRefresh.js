/**
 * 数据刷新 Composable
 * 
 * 功能：
 * - 统一管理列表数据的刷新逻辑
 * - 自动监听相关事件
 * - 提供便捷的刷新方法
 * - 自动清理事件监听器
 */

import { ref, onMounted, onUnmounted } from 'vue'
import eventBus, { EVENT_TYPES, refreshUtils } from '@/utils/eventBus'

/**
 * 列表数据刷新 Composable
 * @param {Object} options 配置选项
 * @returns {Object} 刷新相关的方法和状态
 */
export function useListRefresh(options = {}) {
  const {
    module = '', // 模块名称，如 'technician', 'service' 等
    loadDataFn = null, // 加载数据的函数
    autoRefresh = true, // 是否自动监听刷新事件
    refreshEvents = [], // 额外监听的刷新事件
    onRefresh = null // 刷新时的回调函数
  } = options

  // 刷新状态
  const isRefreshing = ref(false)
  const lastRefreshTime = ref(null)

  // 事件监听器清理函数
  const cleanupFunctions = []

  /**
   * 执行数据刷新
   * @param {*} data 刷新时传递的数据
   */
  const refresh = async (data = null) => {
    if (isRefreshing.value) {
      console.log('⏳ 正在刷新中，跳过重复刷新')
      return
    }

    try {
      isRefreshing.value = true
      console.log(`🔄 开始刷新${module}数据`, data)

      // 执行自定义刷新回调
      if (onRefresh) {
        await onRefresh(data)
      }

      // 执行数据加载函数
      if (loadDataFn && typeof loadDataFn === 'function') {
        await loadDataFn()
      }

      lastRefreshTime.value = new Date()
      console.log(`✅ ${module}数据刷新完成`)
    } catch (error) {
      console.error(`❌ ${module}数据刷新失败:`, error)
      throw error
    } finally {
      isRefreshing.value = false
    }
  }

  /**
   * 设置事件监听器
   */
  const setupEventListeners = () => {
    if (!autoRefresh) return

    // 监听通用刷新事件
    const unsubscribeGeneral = eventBus.on(EVENT_TYPES.REFRESH_LIST, (eventData) => {
      if (!eventData || !eventData.module || eventData.module === module) {
        refresh(eventData?.data)
      }
    })
    cleanupFunctions.push(unsubscribeGeneral)

    // 监听全局刷新事件
    const unsubscribeGlobal = eventBus.on(EVENT_TYPES.GLOBAL_REFRESH, (data) => {
      refresh(data)
    })
    cleanupFunctions.push(unsubscribeGlobal)

    // 监听模块特定的刷新事件
    if (module) {
      const moduleEventType = `refresh_${module}_list`
      if (EVENT_TYPES[moduleEventType.toUpperCase()]) {
        const unsubscribeModule = eventBus.on(EVENT_TYPES[moduleEventType.toUpperCase()], refresh)
        cleanupFunctions.push(unsubscribeModule)
      }
    }

    // 监听操作成功事件
    const unsubscribeOperation = eventBus.on(EVENT_TYPES.OPERATION_SUCCESS, (eventData) => {
      if (!eventData || !eventData.module || eventData.module === module) {
        refresh(eventData?.data)
      }
    })
    cleanupFunctions.push(unsubscribeOperation)

    // 监听额外的刷新事件
    refreshEvents.forEach(eventType => {
      if (EVENT_TYPES[eventType]) {
        const unsubscribe = eventBus.on(EVENT_TYPES[eventType], refresh)
        cleanupFunctions.push(unsubscribe)
      }
    })

    console.log(`📡 ${module}模块事件监听器已设置`)
  }

  /**
   * 清理事件监听器
   */
  const cleanup = () => {
    cleanupFunctions.forEach(fn => fn())
    cleanupFunctions.length = 0
    console.log(`🧹 ${module}模块事件监听器已清理`)
  }

  // 生命周期钩子
  onMounted(() => {
    setupEventListeners()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    isRefreshing,
    lastRefreshTime,
    refresh,
    cleanup
  }
}

/**
 * 编辑页面操作 Composable
 * @param {Object} options 配置选项
 * @returns {Object} 操作相关的方法
 */
export function useEditOperations(options = {}) {
  const {
    module = '', // 模块名称
    router = null, // 路由实例
    redirectPath = '', // 操作成功后的重定向路径
    showMessage = true // 是否显示成功消息
  } = options

  /**
   * 处理新增成功
   * @param {*} data 新增的数据
   * @param {string} message 成功消息
   */
  const handleAddSuccess = (data = null, message = '新增成功') => {
    if (showMessage && message) {
      // 这里可以使用 ElMessage 或其他消息组件
      console.log(`✅ ${message}`)
    }

    // 发送新增成功事件
    if (module) {
      refreshUtils[`refresh${module.charAt(0).toUpperCase() + module.slice(1)}List`]?.(data)
    }

    // 重定向
    if (router && redirectPath) {
      router.push(redirectPath)
    }
  }

  /**
   * 处理编辑成功
   * @param {*} data 编辑的数据
   * @param {string} message 成功消息
   */
  const handleEditSuccess = (data = null, message = '更新成功') => {
    if (showMessage && message) {
      console.log(`✅ ${message}`)
    }

    // 发送编辑成功事件
    if (module) {
      refreshUtils[`refresh${module.charAt(0).toUpperCase() + module.slice(1)}List`]?.(data)
    }

    // 重定向
    if (router && redirectPath) {
      router.push(redirectPath)
    }
  }

  /**
   * 处理删除成功
   * @param {*} data 删除的数据
   * @param {string} message 成功消息
   */
  const handleDeleteSuccess = (data = null, message = '删除成功') => {
    if (showMessage && message) {
      console.log(`✅ ${message}`)
    }

    // 发送删除成功事件
    if (module) {
      refreshUtils[`refresh${module.charAt(0).toUpperCase() + module.slice(1)}List`]?.(data)
    }
  }

  /**
   * 处理状态变更成功
   * @param {*} data 变更的数据
   * @param {string} message 成功消息
   */
  const handleStatusChangeSuccess = (data = null, message = '状态更新成功') => {
    if (showMessage && message) {
      console.log(`✅ ${message}`)
    }

    // 发送状态变更成功事件
    if (module) {
      refreshUtils[`refresh${module.charAt(0).toUpperCase() + module.slice(1)}List`]?.(data)
    }
  }

  return {
    handleAddSuccess,
    handleEditSuccess,
    handleDeleteSuccess,
    handleStatusChangeSuccess
  }
}

/**
 * 路由返回时刷新 Composable
 * @param {Object} options 配置选项
 * @returns {Object} 相关方法
 */
export function useRouteBackRefresh(options = {}) {
  const {
    loadDataFn = null, // 加载数据的函数
    delay = 100 // 延迟刷新时间（毫秒）
  } = options

  /**
   * 处理路由返回时的刷新
   */
  const handleRouteBack = () => {
    if (loadDataFn && typeof loadDataFn === 'function') {
      // 延迟执行，确保页面已经完全加载
      setTimeout(() => {
        console.log('🔄 路由返回，刷新数据')
        loadDataFn()
      }, delay)
    }
  }

  return {
    handleRouteBack
  }
}
