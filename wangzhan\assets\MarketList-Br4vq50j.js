import{r as g,X as F,c as ue,h as de,y as N,Q as e,A as _,I as l,al as d,J as re,ar as ie,H as D,az as pe,z as w,M as c,O as h,K as I}from"./vendor-DmFBDimT.js";import{T as me,L as y}from"./LbButton-BtU4V_Gr.js";import{L as fe}from"./LbPage-DnbiQ0Ct.js";import{_ as ce,a as k}from"./index-C9Xz1oqp.js";import{E as m,q as _e}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const ge={class:"market-list"},ye={class:"content-container"},Ve={class:"search-form-container"},be={class:"table-container"},ve={key:0},we={key:1},he={class:"time-column"},ke={class:"table-operate"},xe={class:"dialog-footer"},Ce={__name:"MarketList",setup(ze){const B=pe(),T=g(!1),S=g(!1),L=g([]),U=g(0),M=g(),R=g(),x=g(!1),n=F({pageNum:1,pageSize:10,title:"",status:null,type:null}),o=F({id:null,title:"",type:0,full:0,discount:0,sendType:0,timeLimit:0,startTime:"",endTime:"",day:0,stock:100,userLimit:1,rule:"",text:"",top:0,status:1}),q={title:[{required:!0,message:"请输入优惠券名称",trigger:"blur"}],type:[{required:!0,message:"请选择优惠券类型",trigger:"change"}],discount:[{required:!0,message:"请输入优惠金额",trigger:"blur"}]},P=ue(()=>o.id?"编辑优惠券":"新增优惠券"),V=async s=>{s&&(n.pageNum=1),T.value=!0;try{const t={pageNum:n.pageNum,pageSize:n.pageSize};n.title&&(t.title=n.title),n.status!==null&&n.status!==""&&(t.status=n.status),n.type!==null&&n.type!==""&&(t.type=n.type);const r=await k.market.couponList(t);if(console.log("🎫 优惠券列表数据 (API-V2):",r),r.code===200||r.code==="200"){const u=r.data,i=u.list||[];L.value=i,U.value=u.totalCount||u.total||0,console.log("📊 处理后的数据:",{list:L.value,total:U.value,pageNum:u.pageNum,pageSize:u.pageSize})}else console.error("❌ API响应错误:",r),m.error(r.message||r.msg||"获取数据失败")}catch(t){console.error("获取优惠券列表失败:",t),m.error("获取数据失败")}finally{T.value=!1}},H=()=>{V(1)},J=()=>{n.title="",n.status=null,n.type=null,M.value?.resetFields(),V(1)},K=()=>{B.push("/market/edit")},O=s=>({0:"活动派发",1:"平台定向派发",2:"用户领取"})[s]||"未知",Q=s=>{B.push(`/market/edit?id=${s.id}`)},W=async s=>{try{await _e.confirm(`确定要删除优惠券 "${s.title}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await k.market.couponDelete({id:s.id});t.code==="200"?(m.success("删除成功"),V()):m.error(t.message||"删除失败")}catch(t){t!=="cancel"&&(console.error("删除优惠券失败:",t),m.error("删除失败"))}},X=async s=>{try{const t=await k.market.couponStatus({id:s.id});t.code==="200"?m.success("状态修改成功"):(s.status=s.status===1?-1:1,m.error(t.message||"状态修改失败"))}catch(t){s.status=s.status===1?-1:1,console.error("修改状态失败:",t),m.error("状态修改失败")}},j=async()=>{try{await R.value.validate(),S.value=!0;let s;o.id?s=await k.market.couponUpdate(o):s=await k.market.couponAdd(o),s.code==="200"?(m.success(o.id?"更新成功":"新增成功"),x.value=!1,V()):m.error(s.message||"操作失败")}catch(s){console.error("提交失败:",s),m.error("操作失败")}finally{S.value=!1}},G=s=>{n.pageSize=s,$(1)},$=s=>{n.pageNum=s,V()},Y=()=>{Z()},Z=()=>{o.id=null,o.title="",o.type=0,o.full=0,o.discount=0,o.sendType=0,o.timeLimit=0,o.startTime="",o.endTime="",o.day=0,o.stock=100,o.userLimit=1,o.rule="",o.text="",o.top=0,o.status=1},ee=s=>s?new Date(s).toLocaleDateString("zh-CN"):"",te=s=>s?new Date(s).toLocaleTimeString("zh-CN",{hour12:!1}):"";return de(()=>{V(1)}),(s,t)=>{const r=d("el-input"),u=d("el-form-item"),i=d("el-option"),C=d("el-select"),p=d("el-col"),b=d("el-row"),A=d("el-form"),f=d("el-table-column"),le=d("el-switch"),ae=d("el-table"),v=d("el-input-number"),E=d("el-radio"),oe=d("el-radio-group"),se=d("el-dialog"),ne=ie("loading");return w(),N("div",ge,[e(me,{title:"优惠券管理"}),_("div",ye,[_("div",Ve,[e(A,{ref_key:"searchFormRef",ref:M,model:n,inline:!0,class:"search-form"},{default:l(()=>[e(b,{gutter:20},{default:l(()=>[e(p,{span:24},{default:l(()=>[e(u,{label:"优惠券名称",prop:"title"},{default:l(()=>[e(r,{size:"default",modelValue:n.title,"onUpdate:modelValue":t[0]||(t[0]=a=>n.title=a),placeholder:"请输入优惠券名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(u,{label:"状态",prop:"status"},{default:l(()=>[e(C,{size:"default",modelValue:n.status,"onUpdate:modelValue":t[1]||(t[1]=a=>n.status=a),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:l(()=>[e(i,{label:"可用",value:1}),e(i,{label:"不可用",value:-1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"类型",prop:"type"},{default:l(()=>[e(C,{size:"default",modelValue:n.type,"onUpdate:modelValue":t[2]||(t[2]=a=>n.type=a),placeholder:"请选择类型",clearable:"",style:{width:"140px"}},{default:l(()=>[e(i,{label:"满减券",value:0}),e(i,{label:"无门槛券",value:1})]),_:1},8,["modelValue"])]),_:1}),e(u,null,{default:l(()=>[e(y,{size:"default",type:"primary",icon:"Search",onClick:H},{default:l(()=>t[17]||(t[17]=[c(" 搜索 ")])),_:1,__:[17]}),e(y,{size:"default",icon:"RefreshLeft",onClick:J},{default:l(()=>t[18]||(t[18]=[c(" 重置 ")])),_:1,__:[18]}),e(y,{size:"default",type:"primary",icon:"Plus",onClick:K},{default:l(()=>t[19]||(t[19]=[c(" 新增优惠券 ")])),_:1,__:[19]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_("div",be,[re((w(),D(ae,{data:L.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:l(()=>[e(f,{prop:"id",label:"ID",width:"100",align:"center"}),e(f,{prop:"title",label:"优惠券名称","min-width":"150"}),e(f,{prop:"type",label:"使用条件","min-width":"180"},{default:l(a=>[a.row.type===0?(w(),N("span",ve," 消费满¥"+h(a.row.full)+"减¥"+h(a.row.discount),1)):(w(),N("span",we," 立减¥"+h(a.row.discount),1))]),_:1}),e(f,{prop:"sendType",label:"派发方式","min-width":"120"},{default:l(a=>[c(h(O(a.row.sendType)),1)]),_:1}),e(f,{prop:"stock",label:"库存数量",width:"100",align:"center"}),e(f,{prop:"haveSend",label:"已发放",width:"100",align:"center"}),e(f,{prop:"top",label:"排序值",width:"100",align:"center"}),e(f,{prop:"createTime",label:"创建时间","min-width":"120"},{default:l(a=>[_("div",he,[_("p",null,h(ee(a.row.createTime)),1),_("p",null,h(te(a.row.createTime)),1)])]),_:1}),e(f,{prop:"status",label:"状态",width:"100"},{default:l(a=>[e(le,{modelValue:a.row.status,"onUpdate:modelValue":z=>a.row.status=z,"active-value":1,"inactive-value":-1,onChange:z=>X(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(f,{label:"操作",width:"200",fixed:"right"},{default:l(a=>[_("div",ke,[e(y,{size:"default",type:"primary",onClick:z=>Q(a.row)},{default:l(()=>t[20]||(t[20]=[c(" 编辑 ")])),_:2,__:[20]},1032,["onClick"]),e(y,{size:"default",type:"danger",onClick:z=>W(a.row)},{default:l(()=>t[21]||(t[21]=[c(" 删除 ")])),_:2,__:[21]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ne,T.value]])]),e(fe,{page:n.pageNum,"page-size":n.pageSize,total:U.value,onHandleSizeChange:G,onHandleCurrentChange:$},null,8,["page","page-size","total"])]),e(se,{title:P.value,modelValue:x.value,"onUpdate:modelValue":t[16]||(t[16]=a=>x.value=a),width:"800px",onClose:Y},{footer:l(()=>[_("span",xe,[e(y,{onClick:t[15]||(t[15]=a=>x.value=!1)},{default:l(()=>t[24]||(t[24]=[c("取消")])),_:1,__:[24]}),e(y,{type:"primary",onClick:j,loading:S.value},{default:l(()=>t[25]||(t[25]=[c(" 确定 ")])),_:1,__:[25]},8,["loading"])])]),default:l(()=>[e(A,{ref_key:"formRef",ref:R,model:o,rules:q,"label-width":"120px"},{default:l(()=>[e(b,{gutter:20},{default:l(()=>[e(p,{span:12},{default:l(()=>[e(u,{label:"优惠券名称",prop:"title"},{default:l(()=>[e(r,{modelValue:o.title,"onUpdate:modelValue":t[3]||(t[3]=a=>o.title=a),placeholder:"请输入优惠券名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:l(()=>[e(u,{label:"优惠券类型",prop:"type"},{default:l(()=>[e(C,{modelValue:o.type,"onUpdate:modelValue":t[4]||(t[4]=a=>o.type=a),placeholder:"请选择类型",style:{width:"100%"}},{default:l(()=>[e(i,{label:"满减券",value:0}),e(i,{label:"无门槛券",value:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o.type===0?(w(),D(b,{key:0,gutter:20},{default:l(()=>[e(p,{span:12},{default:l(()=>[e(u,{label:"满减条件",prop:"full"},{default:l(()=>[e(v,{modelValue:o.full,"onUpdate:modelValue":t[5]||(t[5]=a=>o.full=a),min:0,precision:2,placeholder:"请输入满减条件金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:l(()=>[e(u,{label:"优惠金额",prop:"discount"},{default:l(()=>[e(v,{modelValue:o.discount,"onUpdate:modelValue":t[6]||(t[6]=a=>o.discount=a),min:0,precision:2,placeholder:"请输入优惠金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):I("",!0),o.type===1?(w(),D(b,{key:1,gutter:20},{default:l(()=>[e(p,{span:12},{default:l(()=>[e(u,{label:"优惠金额",prop:"discount"},{default:l(()=>[e(v,{modelValue:o.discount,"onUpdate:modelValue":t[7]||(t[7]=a=>o.discount=a),min:0,precision:2,placeholder:"请输入优惠金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:l(()=>[e(u,{label:"库存数量",prop:"stock"},{default:l(()=>[e(v,{modelValue:o.stock,"onUpdate:modelValue":t[8]||(t[8]=a=>o.stock=a),min:1,placeholder:"请输入库存数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):I("",!0),e(b,{gutter:20},{default:l(()=>[e(p,{span:12},{default:l(()=>[e(u,{label:"派发方式",prop:"sendType"},{default:l(()=>[e(C,{modelValue:o.sendType,"onUpdate:modelValue":t[9]||(t[9]=a=>o.sendType=a),placeholder:"请选择派发方式",style:{width:"100%"}},{default:l(()=>[e(i,{label:"活动派发",value:0}),e(i,{label:"平台定向派发",value:1}),e(i,{label:"用户领取",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:l(()=>[e(u,{label:"排序值",prop:"top"},{default:l(()=>[e(v,{modelValue:o.top,"onUpdate:modelValue":t[10]||(t[10]=a=>o.top=a),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,{gutter:20},{default:l(()=>[e(p,{span:12},{default:l(()=>[e(u,{label:"用户限制",prop:"userLimit"},{default:l(()=>[e(v,{modelValue:o.userLimit,"onUpdate:modelValue":t[11]||(t[11]=a=>o.userLimit=a),min:1,placeholder:"每用户可领取数量",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{span:12},{default:l(()=>[e(u,{label:"状态",prop:"status"},{default:l(()=>[e(oe,{modelValue:o.status,"onUpdate:modelValue":t[12]||(t[12]=a=>o.status=a)},{default:l(()=>[e(E,{value:1},{default:l(()=>t[22]||(t[22]=[c("可用")])),_:1,__:[22]}),e(E,{value:-1},{default:l(()=>t[23]||(t[23]=[c("不可用")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{label:"使用规则",prop:"rule"},{default:l(()=>[e(r,{modelValue:o.rule,"onUpdate:modelValue":t[13]||(t[13]=a=>o.rule=a),type:"textarea",placeholder:"请输入使用规则（可选）"},null,8,["modelValue"])]),_:1}),e(u,{label:"优惠券描述",prop:"text"},{default:l(()=>[e(r,{modelValue:o.text,"onUpdate:modelValue":t[14]||(t[14]=a=>o.text=a),type:"textarea",placeholder:"请输入优惠券描述（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},Be=ce(Ce,[["__scopeId","data-v-82d149c1"]]);export{Be as default};
