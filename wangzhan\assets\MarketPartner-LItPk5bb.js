import{g as le,r as p,X as $,h as te,y as ae,Q as l,A as I,I as a,al as r,J as oe,ar as se,H as ne,az as re,z as L,M as i,O as q}from"./vendor-DmFBDimT.js";import{T as ie,L as m}from"./LbButton-BtU4V_Gr.js";import{L as ue}from"./LbPage-DnbiQ0Ct.js";import{_ as de}from"./index-C9Xz1oqp.js";import{E as c}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const me={class:"market-partner"},pe={class:"content-container"},ce={class:"search-form-container"},ge={class:"table-container"},fe={class:"dialog-footer"},_e={__name:"MarketPartner",setup(ve){const{proxy:V}=le(),k=re(),z=p(),R=p(),h=p(!1),x=p([]),S=p(0),g=p(!1),C=p(""),y=p(!1),s=$({pageNum:1,pageSize:10,userId:"",level:null,status:null}),n=$({id:null,userId:"",level:1,commissionRate1:0,commissionRate2:0,status:1}),T={userId:[{required:!0,message:"请输入用户ID",trigger:"blur"},{pattern:/^\d+$/,message:"用户ID必须为数字",trigger:"blur"}],level:[{required:!0,message:"请选择等级",trigger:"change"}],commissionRate1:[{required:!0,message:"请输入一级分佣比例",trigger:"blur"}],commissionRate2:[{required:!0,message:"请输入二级分佣比例",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},f=async()=>{try{h.value=!0,console.log("📋 获取合伙人列表:",s);const t={pageNum:s.pageNum,pageSize:s.pageSize};s.userId&&(t.userId=parseInt(s.userId)),s.level!==null&&s.level!==""&&(t.level=s.level),s.status!==null&&s.status!==""&&(t.status=s.status);const e=await V.$api.market.partnerList(t);if(e.code==="200"||e.code===200){const _=e.data;x.value=_.list||[],S.value=_.totalCount||0,console.log("✅ 合伙人列表获取成功:",_)}else c.error(e.msg||"获取列表失败")}catch(t){console.error("❌ 获取合伙人列表失败:",t),c.error("获取列表失败")}finally{h.value=!1}},B=()=>{console.log("🔍 执行搜索"),s.pageNum=1,f()},E=()=>{console.log("🔄 重置搜索条件"),z.value?.resetFields(),Object.assign(s,{pageNum:1,pageSize:10,userId:"",level:null,status:null}),f()},M=()=>{console.log("➕ 新增合伙人"),C.value="新增合伙人",y.value=!1,g.value=!0,Object.assign(n,{id:null,userId:"",level:1,commissionRate1:0,commissionRate2:0,status:1}),setTimeout(()=>{R.value?.clearValidate()},100)},O=t=>{console.log("✏️ 编辑合伙人:",t),C.value="编辑合伙人",y.value=!0,g.value=!0,Object.assign(n,{id:t.id,userId:t.userId,level:t.level,commissionRate1:t.commissionRate1,commissionRate2:t.commissionRate2,status:t.status}),setTimeout(()=>{R.value?.clearValidate()},100)},A=async t=>{try{console.log("🔄 切换合伙人状态:",t);const e=await V.$api.market.partnerStatus({id:t.id});e.code==="200"||e.code===200?(c.success("状态修改成功"),console.log("✅ 状态修改成功")):(t.status=t.status===1?0:1,c.error(e.msg||"状态修改失败"))}catch(e){t.status=t.status===1?0:1,console.error("❌ 状态修改失败:",e),c.error("状态修改失败")}},F=async()=>{try{await R.value?.validate(),console.log("💾 提交表单数据:",n);let t;y.value?t=await V.$api.market.partnerUpdateLevelAndCommission({id:n.id,level:n.level,commissionRate1:n.commissionRate1,commissionRate2:n.commissionRate2}):t=await V.$api.market.partnerAdd({userId:parseInt(n.userId),level:n.level,commissionRate1:n.commissionRate1,commissionRate2:n.commissionRate2,status:n.status}),t.code==="200"||t.code===200?(c.success(y.value?"编辑成功":"新增成功"),g.value=!1,f(),console.log("✅ 操作成功")):c.error(t.msg||"操作失败")}catch(t){if(t.message&&t.message.includes("validation")){console.log("⚠️ 表单验证失败");return}console.error("❌ 操作失败:",t),c.error("操作失败")}},P=t=>{console.log("📋 查看邀请列表:",t),k.push({path:"/market/partner/invite",query:{userId:t.userId}})},j=t=>{console.log("💰 查看佣金统计:",t),k.push({path:"/market/partner/commission",query:{userId:t.userId}})},H=t=>{console.log("📦 查看推广订单:",t),k.push({path:"/market/partner/orders",query:{userId:t.userId}})},J=t=>{console.log("📄 分页大小改变:",t),s.pageSize=t,s.pageNum=1,f()},Q=t=>{console.log("📄 当前页改变:",t),s.pageNum=t,f()};return te(()=>{console.log("🚀 合伙人管理页面初始化"),f()}),(t,e)=>{const _=r("el-input"),u=r("el-form-item"),v=r("el-option"),w=r("el-select"),W=r("el-col"),X=r("el-row"),D=r("el-form"),d=r("el-table-column"),G=r("el-switch"),K=r("el-table"),N=r("el-input-number"),U=r("el-radio"),Y=r("el-radio-group"),Z=r("el-dialog"),ee=se("loading");return L(),ae("div",me,[l(ie,{title:"合伙人管理"}),I("div",pe,[I("div",ce,[l(D,{ref_key:"searchFormRef",ref:z,model:s,inline:!0,class:"search-form"},{default:a(()=>[l(X,{gutter:20},{default:a(()=>[l(W,{span:24},{default:a(()=>[l(u,{label:"用户ID",prop:"userId"},{default:a(()=>[l(_,{size:"default",modelValue:s.userId,"onUpdate:modelValue":e[0]||(e[0]=o=>s.userId=o),placeholder:"请输入用户ID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(u,{label:"等级",prop:"level"},{default:a(()=>[l(w,{size:"default",modelValue:s.level,"onUpdate:modelValue":e[1]||(e[1]=o=>s.level=o),placeholder:"请选择等级",clearable:"",style:{width:"120px"}},{default:a(()=>[l(v,{label:"等级1",value:1}),l(v,{label:"等级2",value:2})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"状态",prop:"status"},{default:a(()=>[l(w,{size:"default",modelValue:s.status,"onUpdate:modelValue":e[2]||(e[2]=o=>s.status=o),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[l(v,{label:"可用",value:1}),l(v,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),l(u,null,{default:a(()=>[l(m,{size:"default",type:"primary",icon:"Search",onClick:B},{default:a(()=>e[10]||(e[10]=[i(" 搜索 ")])),_:1,__:[10]}),l(m,{size:"default",icon:"RefreshLeft",onClick:E},{default:a(()=>e[11]||(e[11]=[i(" 重置 ")])),_:1,__:[11]}),l(m,{size:"default",type:"primary",icon:"Plus",onClick:M},{default:a(()=>e[12]||(e[12]=[i(" 新增合伙人 ")])),_:1,__:[12]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),I("div",ge,[oe((L(),ne(K,{data:x.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[l(d,{prop:"id",label:"ID",width:"80",align:"center"}),l(d,{prop:"userId",label:"用户ID",width:"100",align:"center"}),l(d,{prop:"level",label:"等级",width:"80",align:"center"}),l(d,{prop:"commissionRate1",label:"一级分佣比例(%)",width:"140",align:"center"},{default:a(o=>[i(q(o.row.commissionRate1)+"% ",1)]),_:1}),l(d,{prop:"commissionRate2",label:"二级分佣比例(%)",width:"140",align:"center"},{default:a(o=>[i(q(o.row.commissionRate2)+"% ",1)]),_:1}),l(d,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(o=>[l(G,{modelValue:o.row.status,"onUpdate:modelValue":b=>o.row.status=b,"active-value":1,"inactive-value":0,onChange:b=>A(o.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(d,{prop:"createTime",label:"创建时间",width:"180",align:"center"}),l(d,{prop:"updateTime",label:"更新时间",width:"180",align:"center"}),l(d,{label:"操作","min-width":"400",align:"center",fixed:"right"},{default:a(o=>[l(m,{size:"small",type:"primary",onClick:b=>O(o.row)},{default:a(()=>e[13]||(e[13]=[i(" 编辑 ")])),_:2,__:[13]},1032,["onClick"]),l(m,{size:"small",type:"success",onClick:b=>P(o.row)},{default:a(()=>e[14]||(e[14]=[i(" 邀请列表 ")])),_:2,__:[14]},1032,["onClick"]),l(m,{size:"small",type:"warning",onClick:b=>j(o.row)},{default:a(()=>e[15]||(e[15]=[i(" 佣金统计 ")])),_:2,__:[15]},1032,["onClick"]),l(m,{size:"small",type:"info",onClick:b=>H(o.row)},{default:a(()=>e[16]||(e[16]=[i(" 推广订单 ")])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,h.value]])]),l(ue,{page:s.pageNum,"page-size":s.pageSize,total:S.value,onHandleSizeChange:J,onHandleCurrentChange:Q},null,8,["page","page-size","total"])]),l(Z,{title:C.value,modelValue:g.value,"onUpdate:modelValue":e[9]||(e[9]=o=>g.value=o),width:"600px","close-on-click-modal":!1},{footer:a(()=>[I("span",fe,[l(m,{onClick:e[8]||(e[8]=o=>g.value=!1)},{default:a(()=>e[21]||(e[21]=[i("取消")])),_:1,__:[21]}),l(m,{type:"primary",onClick:F},{default:a(()=>e[22]||(e[22]=[i("确定")])),_:1,__:[22]})])]),default:a(()=>[l(D,{ref_key:"formRef",ref:R,model:n,rules:T,"label-width":"120px"},{default:a(()=>[l(u,{label:"用户ID",prop:"userId"},{default:a(()=>[l(_,{modelValue:n.userId,"onUpdate:modelValue":e[3]||(e[3]=o=>n.userId=o),placeholder:"请输入用户ID",disabled:y.value},null,8,["modelValue","disabled"])]),_:1}),l(u,{label:"等级",prop:"level"},{default:a(()=>[l(w,{modelValue:n.level,"onUpdate:modelValue":e[4]||(e[4]=o=>n.level=o),placeholder:"请选择等级",style:{width:"100%"}},{default:a(()=>[l(v,{label:"等级1",value:1}),l(v,{label:"等级2",value:2})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"一级分佣比例",prop:"commissionRate1"},{default:a(()=>[l(N,{modelValue:n.commissionRate1,"onUpdate:modelValue":e[5]||(e[5]=o=>n.commissionRate1=o),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),e[17]||(e[17]=I("span",{style:{"margin-left":"8px",color:"#999"}},"%",-1))]),_:1,__:[17]}),l(u,{label:"二级分佣比例",prop:"commissionRate2"},{default:a(()=>[l(N,{modelValue:n.commissionRate2,"onUpdate:modelValue":e[6]||(e[6]=o=>n.commissionRate2=o),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),e[18]||(e[18]=I("span",{style:{"margin-left":"8px",color:"#999"}},"%",-1))]),_:1,__:[18]}),l(u,{label:"状态",prop:"status"},{default:a(()=>[l(Y,{modelValue:n.status,"onUpdate:modelValue":e[7]||(e[7]=o=>n.status=o)},{default:a(()=>[l(U,{label:1},{default:a(()=>e[19]||(e[19]=[i("可用")])),_:1,__:[19]}),l(U,{label:0},{default:a(()=>e[20]||(e[20]=[i("禁用")])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},he=de(_e,[["__scopeId","data-v-f2f8d7e5"]]);export{he as default};
