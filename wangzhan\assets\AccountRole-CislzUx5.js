import{T as oe,L as m}from"./LbButton-BtU4V_Gr.js";import{_ as re,a as y}from"./index-C9Xz1oqp.js";import{E as u,q as se}from"./element-fdzwdDuf.js";import{r as p,X as I,h as ne,y as C,Q as l,A as R,I as o,al as s,ar as ie,z as g,M as i,J as de,H as T,K as ue,P,a6 as U,O as k}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ce={class:"lb-account-role"},me={class:"page-main"},pe={key:0,class:"text-muted"},ge={class:"table-operate"},_e={class:"pagination-section"},fe={__name:"AccountRole",setup(ve){const x=p(!1),D=p([]),_=p(!1),f=p("add"),N=p(),V=p(!1),z=p([]),v=I({roleName:""}),n=I({pageNum:1,pageSize:10,totalCount:0,totalPage:0}),d=I({id:"",roleName:"",ruleIds:[]}),E={roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"}],ruleIds:[{required:!0,message:"请选择角色权限",trigger:"change"}]},c=async(a=1)=>{x.value=!0,n.pageNum=a;try{const e={pageNum:n.pageNum,pageSize:n.pageSize};v.roleName&&(e.roleName=v.roleName);const r=await y.role.getRoleList(e);r.code==="200"?(D.value=r.data.list||[],n.totalCount=r.data.totalCount||0,n.totalPage=r.data.totalPage||0):u.error(r.msg||"获取数据失败")}catch(e){console.error("获取角色列表失败:",e),u.error("获取数据失败")}finally{x.value=!1}},q=async()=>{try{const a=await y.role.getMenuList();a.code==="200"&&(z.value=a.data||[])}catch(a){console.error("获取菜单列表失败:",a)}},A=a=>{if(!a||a==="")return[];const e=a.split(",").map(r=>parseInt(r.trim()));return z.value.filter(r=>e.includes(r.id))},L=a=>a?a.replace("T"," ").substring(0,19):"",j=()=>{c(1)},F=()=>{v.roleName="",c(1)},O=()=>{f.value="add",B(),_.value=!0},$=async a=>{f.value="edit";try{const e=await y.role.getRoleDetail(a.id);e.code==="200"?(Object.assign(d,{id:e.data.id,roleName:e.data.roleName,ruleIds:e.data.ruleIds||[]}),_.value=!0):u.error(e.msg||"获取角色详情失败")}catch(e){console.error("获取角色详情失败:",e),u.error("获取角色详情失败")}},H=async a=>{try{await se.confirm(`确定要删除角色 "${a.roleName}" 吗？`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const e=await y.role.deleteRole(a.id);e.code==="200"?(u.success("删除成功"),c()):u.error(e.msg||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除角色失败:",e),u.error("删除失败"))}},J=async()=>{try{await N.value.validate(),V.value=!0;const a={roleName:d.roleName,ruleIds:d.ruleIds};f.value==="edit"&&(a.id=d.id);const e=f.value==="add"?await y.role.addRole(a):await y.role.editRole(a);e.code==="200"?(u.success(f.value==="add"?"新增成功":"编辑成功"),_.value=!1,c()):u.error(e.msg||"操作失败")}catch(a){console.error("提交失败:",a),u.error("操作失败")}finally{V.value=!1}},K=()=>{_.value=!1,B()},B=()=>{Object.assign(d,{id:"",roleName:"",ruleIds:[]}),N.value&&N.value.clearValidate()},Q=a=>{n.pageSize=a,c(1)},X=a=>{c(a)};return ne(()=>{c(),q()}),(a,e)=>{const r=s("el-input"),w=s("el-form-item"),S=s("el-form"),M=s("el-card"),G=s("el-row"),b=s("el-table-column"),W=s("el-tag"),Y=s("el-table"),Z=s("el-pagination"),ee=s("el-checkbox"),ae=s("el-checkbox-group"),le=s("el-dialog"),te=ie("loading");return g(),C("div",ce,[l(oe),R("div",me,[l(M,{class:"search-card",shadow:"never"},{default:o(()=>[l(S,{model:v,inline:""},{default:o(()=>[l(w,{label:"角色名称"},{default:o(()=>[l(r,{modelValue:v.roleName,"onUpdate:modelValue":e[0]||(e[0]=t=>v.roleName=t),placeholder:"请输入角色名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(w,null,{default:o(()=>[l(m,{type:"primary",onClick:j},{default:o(()=>e[6]||(e[6]=[i("搜索")])),_:1,__:[6]}),l(m,{onClick:F},{default:o(()=>e[7]||(e[7]=[i("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1}),l(G,{class:"page-header"},{default:o(()=>[l(m,{type:"primary",onClick:O},{default:o(()=>e[8]||(e[8]=[i("新增角色")])),_:1,__:[8]})]),_:1}),l(M,{class:"table-card",shadow:"never"},{default:o(()=>[de((g(),T(Y,{data:D.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"},border:""},{default:o(()=>[l(b,{prop:"id",label:"ID",width:"80"}),l(b,{prop:"roleName",label:"角色名称",width:"150"}),l(b,{prop:"rules",label:"角色权限","min-width":"300"},{default:o(t=>[(g(!0),C(P,null,U(A(t.row.rules),h=>(g(),T(W,{key:h.id,size:"small",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:o(()=>[i(k(h.menuName),1)]),_:2},1024))),128)),!t.row.rules||t.row.rules===""?(g(),C("span",pe," 暂无权限 ")):ue("",!0)]),_:1}),l(b,{prop:"createTime",label:"创建时间",width:"170"},{default:o(t=>[i(k(L(t.row.createTime)),1)]),_:1}),l(b,{prop:"updateTime",label:"修改时间",width:"170"},{default:o(t=>[i(k(L(t.row.updateTime)),1)]),_:1}),l(b,{label:"操作",width:"200",fixed:"right"},{default:o(t=>[R("div",ge,[l(m,{size:"mini",type:"primary",onClick:h=>$(t.row)},{default:o(()=>e[9]||(e[9]=[i(" 编辑 ")])),_:2,__:[9]},1032,["onClick"]),l(m,{size:"mini",type:"danger",onClick:h=>H(t.row)},{default:o(()=>e[10]||(e[10]=[i(" 删除 ")])),_:2,__:[10]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[te,x.value]])]),_:1}),R("div",_e,[l(Z,{"current-page":n.pageNum,"onUpdate:currentPage":e[1]||(e[1]=t=>n.pageNum=t),"page-size":n.pageSize,"onUpdate:pageSize":e[2]||(e[2]=t=>n.pageSize=t),"page-sizes":[10,20,50,100],total:n.totalCount,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:X},null,8,["current-page","page-size","total"])])]),l(le,{modelValue:_.value,"onUpdate:modelValue":e[5]||(e[5]=t=>_.value=t),title:f.value==="add"?"新增角色":"编辑角色",width:"50%","close-on-click-modal":!1},{footer:o(()=>[l(m,{onClick:K},{default:o(()=>e[11]||(e[11]=[i("取消")])),_:1,__:[11]}),l(m,{type:"primary",onClick:J,loading:V.value},{default:o(()=>e[12]||(e[12]=[i("确定")])),_:1,__:[12]},8,["loading"])]),default:o(()=>[l(S,{model:d,rules:E,ref_key:"formRef",ref:N,"label-width":"100px"},{default:o(()=>[l(w,{label:"角色名称",prop:"roleName"},{default:o(()=>[l(r,{modelValue:d.roleName,"onUpdate:modelValue":e[3]||(e[3]=t=>d.roleName=t),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),l(w,{label:"选择权限",prop:"ruleIds"},{default:o(()=>[l(ae,{modelValue:d.ruleIds,"onUpdate:modelValue":e[4]||(e[4]=t=>d.ruleIds=t),class:"permission-group"},{default:o(()=>[(g(!0),C(P,null,U(z.value,t=>(g(),T(ee,{key:t.id,value:t.id,class:"permission-item"},{default:o(()=>[i(k(t.menuName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ce=re(fe,[["__scopeId","data-v-69bcb6b9"]]);export{Ce as default};
