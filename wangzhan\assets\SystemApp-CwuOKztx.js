import{T as x,L as c}from"./LbButton-BtU4V_Gr.js";import{_ as C}from"./index-C9Xz1oqp.js";import{E as m}from"./element-fdzwdDuf.js";import{r as g,X as U,h as j,y as k,Q as o,A as _,I as l,al as r,z as N,M as p}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const T={class:"lb-system-app"},B={class:"page-main"},E={__name:"SystemApp",setup(F){const u=g(!1),i=g(),a=U({app_name:"",version:"",download_url:"",update_desc:"",force_update:0}),v={app_name:[{required:!0,message:"请输入APP名称",trigger:"blur"}],version:[{required:!0,message:"请输入版本号",trigger:"blur"}]},y=async()=>{try{const e=await(await fetch("/api/system/app/config")).json();e.code===200&&Object.assign(a,e.data||{})}catch(n){console.error("获取配置失败:",n)}},V=async()=>{try{await i.value.validate(),u.value=!0;const e=await(await fetch("/api/system/app/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).json();e.code===200?m.success("配置保存成功"):m.error(e.message||"保存失败")}catch{m.error("保存失败")}finally{u.value=!1}},P=()=>{Object.assign(a,{app_name:"",version:"",download_url:"",update_desc:"",force_update:0}),i.value?.clearValidate()};return j(()=>{y()}),(n,e)=>{const d=r("el-input"),s=r("el-form-item"),f=r("el-radio"),w=r("el-radio-group"),b=r("el-form"),A=r("el-card");return N(),k("div",T,[o(x),_("div",B,[o(A,{class:"config-card",shadow:"never"},{header:l(()=>e[5]||(e[5]=[_("div",{class:"card-header"},[_("span",null,"APP设置")],-1)])),default:l(()=>[o(b,{model:a,rules:v,ref_key:"configFormRef",ref:i,"label-width":"140px",class:"config-form"},{default:l(()=>[o(s,{label:"APP名称",prop:"app_name"},{default:l(()=>[o(d,{modelValue:a.app_name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.app_name=t),placeholder:"请输入APP名称"},null,8,["modelValue"])]),_:1}),o(s,{label:"APP版本",prop:"version"},{default:l(()=>[o(d,{modelValue:a.version,"onUpdate:modelValue":e[1]||(e[1]=t=>a.version=t),placeholder:"请输入APP版本号"},null,8,["modelValue"])]),_:1}),o(s,{label:"下载地址",prop:"download_url"},{default:l(()=>[o(d,{modelValue:a.download_url,"onUpdate:modelValue":e[2]||(e[2]=t=>a.download_url=t),placeholder:"请输入APP下载地址"},null,8,["modelValue"])]),_:1}),o(s,{label:"更新说明",prop:"update_desc"},{default:l(()=>[o(d,{modelValue:a.update_desc,"onUpdate:modelValue":e[3]||(e[3]=t=>a.update_desc=t),type:"textarea",rows:4,placeholder:"请输入更新说明"},null,8,["modelValue"])]),_:1}),o(s,{label:"强制更新",prop:"force_update"},{default:l(()=>[o(w,{modelValue:a.force_update,"onUpdate:modelValue":e[4]||(e[4]=t=>a.force_update=t)},{default:l(()=>[o(f,{value:1},{default:l(()=>e[6]||(e[6]=[p("是")])),_:1,__:[6]}),o(f,{value:0},{default:l(()=>e[7]||(e[7]=[p("否")])),_:1,__:[7]})]),_:1},8,["modelValue"])]),_:1}),o(s,null,{default:l(()=>[o(c,{type:"primary",onClick:V,loading:u.value},{default:l(()=>e[8]||(e[8]=[p("保存配置")])),_:1,__:[8]},8,["loading"]),o(c,{onClick:P,style:{"margin-left":"10px"}},{default:l(()=>e[9]||(e[9]=[p("重置")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1})])])}}},q=C(E,[["__scopeId","data-v-a2fa37fe"]]);export{q as default};
