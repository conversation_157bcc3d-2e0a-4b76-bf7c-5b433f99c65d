import{E as d,x as me,q as ge}from"./element-fdzwdDuf.js";import{T as fe,L as v}from"./LbButton-BtU4V_Gr.js";import{L as _e}from"./LbImage-CnNh5Udj.js";import{L as ve}from"./LbPage-DnbiQ0Ct.js";import{_ as he,a as h}from"./index-C9Xz1oqp.js";import{r as p,X as A,h as we,y as E,Q as t,A as g,I as a,al as i,J as ye,ar as be,H as Ve,z as I,M as m,O as V,K as ke,u as Ce}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ze={class:"service-jingang"},xe={class:"content-container"},Se={class:"search-form-container"},Le={class:"table-container"},Ie={class:"time-column"},Ne={key:0,class:"upload-progress"},Ue={class:"dialog-footer"},De={__name:"ServiceJingang",setup(Fe){const k=p(!1),C=p(!1),w=p(!1),z=p([]),x=p(0),y=p([]),c=p(0),S=p(!1),r=A({pageNum:1,pageSize:10,title:"",status:null}),s=A({id:null,title:"",img:"",link:"",top:0,status:1}),M={title:[{required:!0,message:"请输入金刚区标题",trigger:"blur"}],img:[{required:!0,message:"请上传金刚区图标",trigger:"change"}]},N=p(),U=p(),_=async l=>{l&&(r.pageNum=1),k.value=!0;try{const e={pageNum:r.pageNum,pageSize:r.pageSize};r.title&&(e.title=r.title),r.status!==null&&r.status!==""&&(e.status=r.status);const o=await h.service.navList(e);if(console.log("📋 金刚区列表数据 (API-V2):",o),o.code==="200"){const u=o.data;z.value=u.list||[],x.value=u.totalCount||u.total||0,console.log("📊 处理后的数据:",{list:z.value,total:x.value,pageNum:u.pageNum,pageSize:u.pageSize})}else console.error("❌ API响应错误:",o),d.error(o.message||o.msg||"获取数据失败")}catch(e){console.error("获取金刚区列表失败:",e),d.error("获取数据失败")}finally{k.value=!1}},P=()=>{_(1)},q=()=>{r.title="",r.status=null,N.value?.resetFields(),_(1)},H=()=>{L(),w.value=!0},J=async l=>{L();try{const e=await h.service.navInfo({id:l.id});if(e.code==="200"){const o=e.data;s.id=o.id,s.title=o.title||"",s.img=o.img||"",s.link=o.link||"",s.top=o.top||0,s.status=o.status||1,o.img&&(y.value=[{name:"image",url:o.img,status:"success"}])}else{d.error(e.message||"获取金刚区详情失败");return}}catch(e){console.error("获取金刚区详情失败:",e),d.error("获取金刚区详情失败");return}w.value=!0},$=async l=>{try{await ge.confirm("确定要删除这个金刚区吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await h.service.navDelete({id:l.id});e.code==="200"?(d.success("删除成功"),_()):d.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除金刚区失败:",e),d.error("删除失败"))}},j=async l=>{if(l.statusLoading)return;const e=l.status===1?-1:1;try{l.statusLoading=!0;const o=await h.service.navStatus({id:l.id});o.code==="200"?(d.success("状态修改成功"),await _()):(l.status=e,d.error(o.message||"状态修改失败"))}catch(o){l.status=e,console.error("修改状态失败:",o),d.error("状态修改失败")}finally{l.statusLoading=!1}},O=async()=>{try{if(await U.value.validate(),!s.img){d.error("请先上传图片");return}C.value=!0;let l;s.id?l=await h.service.navUpdate(s):l=await h.service.navAdd(s),l.code==="200"?(d.success(s.id?"更新成功":"新增成功"),w.value=!1,_()):d.error(l.message||"操作失败")}catch(l){console.error("提交失败:",l),d.error("操作失败")}finally{C.value=!1}},K=l=>{r.pageSize=l,D(1)},D=l=>{r.pageNum=l,_()},Q=l=>(console.log("📋 图片上传前验证:",l),l.type.indexOf("image/")===0?(console.log("✅ 图片验证通过"),!0):(d.error("只能上传图片文件!"),!1)),W=async(l,e)=>{console.log("🖼️ 图片文件变更:",l,e),l.status==="ready"&&await G(l)},X=(l,e)=>{console.log("🗑️ 移除图片:",l),s.img="",c.value=0},G=async l=>{console.log("📤 开始上传图片:",l);try{S.value=!0,c.value=0;const e=new FormData;e.append("multipartFile",l.raw),console.log("📦 FormData创建完成:",e);const o=await h.upload.uploadFile(e,u=>{c.value=Math.round(u.loaded*100/u.total),console.log("📊 上传进度:",c.value+"%")});if(console.log("✅ 图片上传成功:",o),o.code===200||o.code==="200")s.img=o.data.url||o.data.fileUrl||o.data,d.success("图片上传成功"),y.value=[{name:l.name,url:s.img,status:"success"}],console.log("💾 图片URL已保存到表单:",s.img);else throw new Error(o.message||o.msg||"上传失败")}catch(e){console.error("❌ 图片上传失败:",e),d.error("图片上传失败: "+(e.message||"未知错误")),y.value=[],s.img=""}finally{S.value=!1,c.value=0}},F=()=>{w.value=!1,L()},L=()=>{s.id=null,s.title="",s.img="",s.link="",s.top=0,s.status=1,y.value=[],c.value=0,S.value=!1},Y=l=>l?l.split(" ")[0]:"-",Z=l=>l?l.split(" ")[1]:"-";return we(()=>{_()}),(l,e)=>{const o=i("el-input"),u=i("el-form-item"),T=i("el-option"),ee=i("el-select"),te=i("el-col"),le=i("el-row"),B=i("el-form"),f=i("el-table-column"),ae=i("el-link"),oe=i("el-switch"),se=i("el-table"),ne=i("el-icon"),re=i("el-upload"),ie=i("el-progress"),ue=i("el-input-number"),R=i("el-radio"),de=i("el-radio-group"),ce=i("el-dialog"),pe=be("loading");return I(),E("div",ze,[t(fe,{title:"金刚区管理"}),g("div",xe,[g("div",Se,[t(B,{ref_key:"searchFormRef",ref:N,model:r,inline:!0,class:"search-form"},{default:a(()=>[t(le,{gutter:20},{default:a(()=>[t(te,{span:24},{default:a(()=>[t(u,{label:"标题查询",prop:"title"},{default:a(()=>[t(o,{size:"default",modelValue:r.title,"onUpdate:modelValue":e[0]||(e[0]=n=>r.title=n),placeholder:"请输入金刚区标题",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:a(()=>[t(ee,{size:"default",modelValue:r.status,"onUpdate:modelValue":e[1]||(e[1]=n=>r.status=n),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[t(T,{label:"可用",value:1}),t(T,{label:"不可用",value:-1})]),_:1},8,["modelValue"])]),_:1}),t(u,null,{default:a(()=>[t(v,{size:"default",type:"primary",icon:"Search",onClick:P},{default:a(()=>e[7]||(e[7]=[m(" 搜索 ")])),_:1,__:[7]}),t(v,{size:"default",icon:"RefreshLeft",onClick:q},{default:a(()=>e[8]||(e[8]=[m(" 重置 ")])),_:1,__:[8]}),t(v,{size:"default",type:"primary",icon:"Plus",onClick:H},{default:a(()=>e[9]||(e[9]=[m(" 新增金刚区 ")])),_:1,__:[9]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),g("div",Le,[ye((I(),Ve(se,{data:z.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[t(f,{prop:"id",label:"ID",width:"80",align:"center"}),t(f,{prop:"img",label:"图标",width:"120"},{default:a(n=>[t(_e,{src:n.row.img,width:"80",height:"50"},null,8,["src"])]),_:1}),t(f,{prop:"title",label:"标题","min-width":"150"}),t(f,{prop:"link",label:"链接地址","min-width":"200"},{default:a(n=>[t(ae,{href:n.row.link,target:"_blank",type:"primary"},{default:a(()=>[m(V(n.row.link||"无链接"),1)]),_:2},1032,["href"])]),_:1}),t(f,{prop:"top",label:"排序",width:"80",align:"center"}),t(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:a(n=>[t(oe,{modelValue:n.row.status,"onUpdate:modelValue":b=>n.row.status=b,"active-value":1,"inactive-value":-1,loading:n.row.statusLoading,onChange:b=>j(n.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),t(f,{label:"时间",width:"160"},{default:a(n=>[g("div",Ie,[g("p",null,V(Y(n.row.createTime)),1),g("p",null,V(Z(n.row.createTime)),1)])]),_:1}),t(f,{label:"操作",width:"200",fixed:"right"},{default:a(n=>[t(v,{size:"default",type:"primary",onClick:b=>J(n.row)},{default:a(()=>e[10]||(e[10]=[m(" 编辑 ")])),_:2,__:[10]},1032,["onClick"]),t(v,{size:"default",type:"danger",onClick:b=>$(n.row)},{default:a(()=>e[11]||(e[11]=[m(" 删除 ")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,k.value]])]),t(ve,{page:r.pageNum,"page-size":r.pageSize,total:x.value,onHandleSizeChange:K,onHandleCurrentChange:D},null,8,["page","page-size","total"])]),t(ce,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=n=>w.value=n),title:s.id?"编辑金刚区":"新增金刚区",width:"600px","before-close":F},{footer:a(()=>[g("span",Ue,[t(v,{onClick:F},{default:a(()=>e[15]||(e[15]=[m("取消")])),_:1,__:[15]}),t(v,{type:"primary",loading:C.value,onClick:O},{default:a(()=>e[16]||(e[16]=[m(" 确定 ")])),_:1,__:[16]},8,["loading"])])]),default:a(()=>[t(B,{ref_key:"formRef",ref:U,model:s,rules:M,"label-width":"100px"},{default:a(()=>[t(u,{label:"标题",prop:"title"},{default:a(()=>[t(o,{modelValue:s.title,"onUpdate:modelValue":e[2]||(e[2]=n=>s.title=n),placeholder:"请输入金刚区标题"},null,8,["modelValue"])]),_:1}),t(u,{label:"图标",prop:"img"},{default:a(()=>[t(re,{class:"image-upload",action:"#","auto-upload":!1,"on-change":W,"on-remove":X,"before-upload":Q,"file-list":y.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:a(()=>e[12]||(e[12]=[g("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件 ",-1)])),default:a(()=>[t(ne,null,{default:a(()=>[t(Ce(me))]),_:1})]),_:1},8,["file-list"]),c.value>0&&c.value<100?(I(),E("div",Ne,[t(ie,{percentage:c.value,"show-text":!0},null,8,["percentage"]),g("p",null,"上传中... "+V(c.value)+"%",1)])):ke("",!0)]),_:1}),t(u,{label:"链接地址",prop:"link"},{default:a(()=>[t(o,{modelValue:s.link,"onUpdate:modelValue":e[3]||(e[3]=n=>s.link=n),placeholder:"请输入链接地址（可选）"},null,8,["modelValue"])]),_:1}),t(u,{label:"排序",prop:"top"},{default:a(()=>[t(ue,{modelValue:s.top,"onUpdate:modelValue":e[4]||(e[4]=n=>s.top=n),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:a(()=>[t(de,{modelValue:s.status,"onUpdate:modelValue":e[5]||(e[5]=n=>s.status=n)},{default:a(()=>[t(R,{value:1},{default:a(()=>e[13]||(e[13]=[m("可用")])),_:1,__:[13]}),t(R,{value:-1},{default:a(()=>e[14]||(e[14]=[m("不可用")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},qe=he(De,[["__scopeId","data-v-d81dad69"]]);export{qe as default};
