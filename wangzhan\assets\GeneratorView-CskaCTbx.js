import{x as Y,O as ee,T as le,U as te,V as ae,q as oe,E as g}from"./element-fdzwdDuf.js";import{_ as ne}from"./index-C9Xz1oqp.js";import{r as N,X as se,al as r,y as R,z as D,A as i,Q as e,I as t,M as p,u as w,P as ie,a6 as re,H as de,O as L}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ue={class:"generator-view"},me={class:"generator-config"},pe={class:"card-header"},ce={class:"fields-config"},fe={class:"card-header"},_e={class:"generator-actions"},ge={class:"actions-content"},ve={class:"action-buttons"},$e={class:"generation-info"},be={class:"code-preview"},Ve={class:"code-content"},ye={class:"code-header"},Ne={class:"file-path"},we={class:"code-block"},Ce={__name:"GeneratorView",setup(he){const b=N(),C=N(!1),h=N(!1),k=N(""),o=se({moduleName:"",moduleDescription:"",tableName:"",classPrefix:"",generateTypes:["controller","service","entity","vue"],packagePath:"com.example.system",author:"Admin System",fields:[{fieldName:"id",fieldType:"Long",fieldComment:"主键ID",isRequired:!0,isQuery:!1,isList:!0,isEdit:!1},{fieldName:"name",fieldType:"String",fieldComment:"名称",isRequired:!0,isQuery:!0,isList:!0,isEdit:!0}]}),x=N([]),S={moduleName:[{required:!0,message:"请输入模块名称",trigger:"blur"},{pattern:/^[a-zA-Z][a-zA-Z0-9_]*$/,message:"模块名称只能包含字母、数字和下划线，且以字母开头",trigger:"blur"}],moduleDescription:[{required:!0,message:"请输入模块描述",trigger:"blur"}],tableName:[{required:!0,message:"请输入表名",trigger:"blur"}],classPrefix:[{required:!0,message:"请输入类名前缀",trigger:"blur"},{pattern:/^[A-Z][a-zA-Z0-9]*$/,message:"类名前缀必须以大写字母开头",trigger:"blur"}],generateTypes:[{required:!0,message:"请选择生成类型",trigger:"change"}],packagePath:[{required:!0,message:"请输入包路径",trigger:"blur"}],author:[{required:!0,message:"请输入作者信息",trigger:"blur"}]},T=()=>{o.fields.push({fieldName:"",fieldType:"String",fieldComment:"",isRequired:!1,isQuery:!1,isList:!0,isEdit:!0})},q=n=>{if(o.fields.length<=1){g.warning("至少需要保留一个字段");return}o.fields.splice(n,1)},B=()=>{oe.confirm("确定要重置所有配置吗？","重置确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object.assign(o,{moduleName:"",moduleDescription:"",tableName:"",classPrefix:"",generateTypes:["controller","service","entity","vue"],packagePath:"com.example.system",author:"Admin System",fields:[{fieldName:"id",fieldType:"Long",fieldComment:"主键ID",isRequired:!0,isQuery:!1,isList:!0,isEdit:!1}]}),g.success("配置已重置")}).catch(()=>{})},z=async()=>{if(b.value)try{await b.value.validate(),C.value=!0,await new Promise(n=>setTimeout(n,2e3)),g.success("代码生成成功！"),U()}catch(n){n!==!1&&g.error("代码生成失败")}finally{C.value=!1}},E=async()=>{if(b.value)try{await b.value.validate(),U(),h.value=!0,k.value=x.value[0]?.name||""}catch(n){n!==!1&&g.error("请先完善配置信息")}},I=()=>{g.info("下载功能开发中...")},j=async n=>{try{await navigator.clipboard.writeText(n),g.success("代码已复制到剪贴板")}catch{g.error("复制失败")}},U=()=>{const{moduleName:n,classPrefix:l,packagePath:d}=o;x.value=[{name:`${l}Controller.java`,path:`src/main/java/${d.replace(/\./g,"/")}/controller/${l}Controller.java`,content:A()},{name:`${l}Service.java`,path:`src/main/java/${d.replace(/\./g,"/")}/service/${l}Service.java`,content:M()},{name:`${l}.java`,path:`src/main/java/${d.replace(/\./g,"/")}/entity/${l}.java`,content:Q()},{name:`${n}.vue`,path:`src/views/${n}/${n}.vue`,content:F()}]},A=()=>{const{classPrefix:n,packagePath:l,author:d,moduleDescription:s}=o;return`package ${l}.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ${l}.entity.${n};
import ${l}.service.${n}Service;

/**
 * ${s}控制器
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
@RestController
@RequestMapping("/${o.moduleName}")
public class ${n}Controller {

    @Autowired
    private ${n}Service ${o.moduleName}Service;

    /**
     * 查询${s}列表
     */
    @GetMapping("/list")
    public Result list(${n} ${o.moduleName}) {
        return Result.success(${o.moduleName}Service.selectList(${o.moduleName}));
    }

    /**
     * 获取${s}详细信息
     */
    @GetMapping("/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(${o.moduleName}Service.selectById(id));
    }

    /**
     * 新增${s}
     */
    @PostMapping
    public Result add(@RequestBody ${n} ${o.moduleName}) {
        return Result.success(${o.moduleName}Service.insert(${o.moduleName}));
    }

    /**
     * 修改${s}
     */
    @PutMapping
    public Result edit(@RequestBody ${n} ${o.moduleName}) {
        return Result.success(${o.moduleName}Service.update(${o.moduleName}));
    }

    /**
     * 删除${s}
     */
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return Result.success(${o.moduleName}Service.deleteByIds(ids));
    }
}`},M=()=>{const{classPrefix:n,packagePath:l,author:d,moduleDescription:s}=o;return`package ${l}.service;

import java.util.List;
import ${l}.entity.${n};

/**
 * ${s}服务接口
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
public interface ${n}Service {

    /**
     * 查询${s}列表
     */
    List<${n}> selectList(${n} ${o.moduleName});

    /**
     * 根据ID查询${s}
     */
    ${n} selectById(Long id);

    /**
     * 新增${s}
     */
    int insert(${n} ${o.moduleName});

    /**
     * 修改${s}
     */
    int update(${n} ${o.moduleName});

    /**
     * 批量删除${s}
     */
    int deleteByIds(Long[] ids);
}`},Q=()=>{const{classPrefix:n,packagePath:l,author:d,moduleDescription:s,tableName:f,fields:v}=o;let V="";return v.forEach(u=>{V+=`
    /**
     * ${u.fieldComment}
     */
    private ${u.fieldType} ${u.fieldName};
`}),`package ${l}.entity;

import java.util.Date;

/**
 * ${s}实体类
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
public class ${n} {
${V}
    // getter and setter methods...
}`},F=()=>`// Vue组件代码生成功能暂时简化
// 完整的代码生成功能需要更复杂的模板处理
const vueTemplate = "Vue组件模板代码"
return vueTemplate`;return(n,l)=>{const d=r("el-button"),s=r("el-input"),f=r("el-form-item"),v=r("el-col"),V=r("el-row"),u=r("el-checkbox"),G=r("el-checkbox-group"),Z=r("el-form"),P=r("el-card"),y=r("el-icon"),_=r("el-table-column"),$=r("el-option"),O=r("el-select"),H=r("el-table"),X=r("el-alert"),J=r("el-tab-pane"),K=r("el-tabs"),W=r("el-dialog");return D(),R("div",ue,[l[25]||(l[25]=i("div",{class:"page-title"},[i("h1",null,"代码生成器"),i("p",{class:"page-subtitle"},"快速生成CRUD代码和模板文件")],-1)),i("div",me,[e(P,null,{header:t(()=>[i("div",pe,[l[10]||(l[10]=i("span",null,"生成配置",-1)),e(d,{onClick:B},{default:t(()=>l[9]||(l[9]=[p("重置配置")])),_:1,__:[9]})])]),default:t(()=>[e(Z,{ref_key:"configFormRef",ref:b,model:o,rules:S,"label-width":"120px",size:"default"},{default:t(()=>[e(V,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(f,{label:"模块名称",prop:"moduleName"},{default:t(()=>[e(s,{modelValue:o.moduleName,"onUpdate:modelValue":l[0]||(l[0]=a=>o.moduleName=a),placeholder:"请输入模块名称，如：user"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(f,{label:"模块描述",prop:"moduleDescription"},{default:t(()=>[e(s,{modelValue:o.moduleDescription,"onUpdate:modelValue":l[1]||(l[1]=a=>o.moduleDescription=a),placeholder:"请输入模块描述，如：用户管理"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:t(()=>[e(v,{span:12},{default:t(()=>[e(f,{label:"表名",prop:"tableName"},{default:t(()=>[e(s,{modelValue:o.tableName,"onUpdate:modelValue":l[2]||(l[2]=a=>o.tableName=a),placeholder:"请输入数据库表名，如：sys_user"},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:12},{default:t(()=>[e(f,{label:"类名前缀",prop:"classPrefix"},{default:t(()=>[e(s,{modelValue:o.classPrefix,"onUpdate:modelValue":l[3]||(l[3]=a=>o.classPrefix=a),placeholder:"请输入类名前缀，如：User"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{label:"生成类型",prop:"generateTypes"},{default:t(()=>[e(G,{modelValue:o.generateTypes,"onUpdate:modelValue":l[4]||(l[4]=a=>o.generateTypes=a)},{default:t(()=>[e(u,{label:"controller"},{default:t(()=>l[11]||(l[11]=[p("Controller 控制器")])),_:1,__:[11]}),e(u,{label:"service"},{default:t(()=>l[12]||(l[12]=[p("Service 服务层")])),_:1,__:[12]}),e(u,{label:"mapper"},{default:t(()=>l[13]||(l[13]=[p("Mapper 数据层")])),_:1,__:[13]}),e(u,{label:"entity"},{default:t(()=>l[14]||(l[14]=[p("Entity 实体类")])),_:1,__:[14]}),e(u,{label:"vue"},{default:t(()=>l[15]||(l[15]=[p("Vue 前端页面")])),_:1,__:[15]}),e(u,{label:"api"},{default:t(()=>l[16]||(l[16]=[p("API 接口文件")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"包路径",prop:"packagePath"},{default:t(()=>[e(s,{modelValue:o.packagePath,"onUpdate:modelValue":l[5]||(l[5]=a=>o.packagePath=a),placeholder:"请输入包路径，如：com.example.system"},null,8,["modelValue"])]),_:1}),e(f,{label:"作者信息",prop:"author"},{default:t(()=>[e(s,{modelValue:o.author,"onUpdate:modelValue":l[6]||(l[6]=a=>o.author=a),placeholder:"请输入作者信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),i("div",ce,[e(P,null,{header:t(()=>[i("div",fe,[l[18]||(l[18]=i("span",null,"字段配置",-1)),e(d,{type:"primary",size:"small",onClick:T},{default:t(()=>[e(y,null,{default:t(()=>[e(w(Y))]),_:1}),l[17]||(l[17]=p(" 添加字段 "))]),_:1,__:[17]})])]),default:t(()=>[e(H,{data:o.fields,border:"",style:{width:"100%"}},{default:t(()=>[e(_,{prop:"fieldName",label:"字段名",width:"150"},{default:t(({row:a,$index:c})=>[e(s,{modelValue:a.fieldName,"onUpdate:modelValue":m=>a.fieldName=m,placeholder:"字段名",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"fieldType",label:"字段类型",width:"120"},{default:t(({row:a,$index:c})=>[e(O,{modelValue:a.fieldType,"onUpdate:modelValue":m=>a.fieldType=m,placeholder:"类型",size:"small"},{default:t(()=>[e($,{label:"String",value:"String"}),e($,{label:"Integer",value:"Integer"}),e($,{label:"Long",value:"Long"}),e($,{label:"Date",value:"Date"}),e($,{label:"Boolean",value:"Boolean"}),e($,{label:"BigDecimal",value:"BigDecimal"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"fieldComment",label:"字段注释",width:"150"},{default:t(({row:a,$index:c})=>[e(s,{modelValue:a.fieldComment,"onUpdate:modelValue":m=>a.fieldComment=m,placeholder:"注释",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"isRequired",label:"必填",width:"80",align:"center"},{default:t(({row:a,$index:c})=>[e(u,{modelValue:a.isRequired,"onUpdate:modelValue":m=>a.isRequired=m},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"isQuery",label:"查询",width:"80",align:"center"},{default:t(({row:a,$index:c})=>[e(u,{modelValue:a.isQuery,"onUpdate:modelValue":m=>a.isQuery=m},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"isList",label:"列表",width:"80",align:"center"},{default:t(({row:a,$index:c})=>[e(u,{modelValue:a.isList,"onUpdate:modelValue":m=>a.isList=m},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"isEdit",label:"编辑",width:"80",align:"center"},{default:t(({row:a,$index:c})=>[e(u,{modelValue:a.isEdit,"onUpdate:modelValue":m=>a.isEdit=m},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{label:"操作",width:"100",align:"center"},{default:t(({row:a,$index:c})=>[e(d,{type:"danger",size:"small",onClick:m=>q(c)},{default:t(()=>l[19]||(l[19]=[p(" 删除 ")])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),i("div",_e,[e(P,null,{default:t(()=>[i("div",ge,[i("div",ve,[e(d,{type:"primary",size:"large",loading:C.value,onClick:z},{default:t(()=>[e(y,null,{default:t(()=>[e(w(ee))]),_:1}),l[20]||(l[20]=p(" 生成代码 "))]),_:1,__:[20]},8,["loading"]),e(d,{type:"success",size:"large",onClick:E},{default:t(()=>[e(y,null,{default:t(()=>[e(w(le))]),_:1}),l[21]||(l[21]=p(" 预览代码 "))]),_:1,__:[21]}),e(d,{type:"info",size:"large",onClick:I},{default:t(()=>[e(y,null,{default:t(()=>[e(w(te))]),_:1}),l[22]||(l[22]=p(" 下载代码 "))]),_:1,__:[22]})]),i("div",$e,[e(X,{title:"代码生成说明",type:"info",closable:!1,"show-icon":""},{default:t(()=>l[23]||(l[23]=[i("p",null,"1. 请先配置模块基本信息和字段信息",-1),i("p",null,"2. 选择需要生成的代码类型",-1),i("p",null,"3. 点击生成代码按钮开始生成",-1),i("p",null,"4. 生成的代码将包含完整的CRUD功能",-1)])),_:1})])])]),_:1})]),e(W,{modelValue:h.value,"onUpdate:modelValue":l[8]||(l[8]=a=>h.value=a),title:"代码预览",width:"80%","close-on-click-modal":!1},{default:t(()=>[i("div",be,[e(K,{modelValue:k.value,"onUpdate:modelValue":l[7]||(l[7]=a=>k.value=a)},{default:t(()=>[(D(!0),R(ie,null,re(x.value,a=>(D(),de(J,{key:a.name,label:a.name,name:a.name},{default:t(()=>[i("div",Ve,[i("div",ye,[i("span",Ne,L(a.path),1),e(d,{size:"small",onClick:c=>j(a.content)},{default:t(()=>[e(y,null,{default:t(()=>[e(w(ae))]),_:1}),l[24]||(l[24]=p(" 复制代码 "))]),_:2,__:[24]},1032,["onClick"])]),i("pre",we,[i("code",null,L(a.content),1)])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])])}}},Ue=ne(Ce,[["__scopeId","data-v-e1717a36"]]);export{Ue as default};
