/**
 * API配置文件
 * 配置基础URL和请求相关设置
 */

// 获取全局配置（生产环境从 window.APP_CONFIG 读取）
function getGlobalConfig() {
  if (typeof window !== 'undefined' && window.APP_CONFIG) {
    return window.APP_CONFIG.api
  }
  return null
}

// 开发环境配置
const DEV_CONFIG = {
  // Mock服务器（本地开发）
  MOCK_BASE_URL: 'http://localhost:3000',

  // 真实服务器（通过Vite代理）
  REAL_BASE_URL: '', // 使用相对路径，通过Vite代理转发

  // 当前使用的模式：'mock' | 'real'
  API_MODE: 'real', // 改为 'real' 使用真实API，'mock' 使用Mock数据

  // 请求超时时间
  TIMEOUT: 10000,

  // 是否启用请求日志
  ENABLE_LOG: true
}

// 生产环境配置
const PROD_CONFIG = {
  REAL_BASE_URL: 'http://************:8889/ims',
  API_MODE: 'real',
  TIMEOUT: 10000,
  ENABLE_LOG: false
}

// 根据环境选择配置，生产环境优先使用全局配置
function getConfig() {
  const globalConfig = getGlobalConfig()

  if (process.env.NODE_ENV === 'production' && globalConfig) {
    return {
      REAL_BASE_URL: globalConfig.baseURL || PROD_CONFIG.REAL_BASE_URL,
      API_MODE: 'real',
      TIMEOUT: globalConfig.timeout || PROD_CONFIG.TIMEOUT,
      ENABLE_LOG: globalConfig.enableLog || PROD_CONFIG.ENABLE_LOG
    }
  }

  return process.env.NODE_ENV === 'production' ? PROD_CONFIG : DEV_CONFIG
}

const config = getConfig()

// 获取当前基础URL
export function getBaseURL() {
  return config.API_MODE === 'mock' ? config.MOCK_BASE_URL : config.REAL_BASE_URL
}

// 获取完整的API URL
export function getApiURL(path) {
  const baseURL = getBaseURL()
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${baseURL}${normalizedPath}`
}

// 导出配置
export default config

// API路径常量
export const API_PATHS = {
  // 轮播图相关
  BANNER: {
    LIST: '/api/admin/banner/list',
    DETAIL: '/api/admin/banner/detail',
    ADD: '/api/admin/banner/add',
    EDIT: '/api/admin/banner/edit',
    DELETE: '/api/admin/banner/delete',
    STATUS: '/api/admin/banner/status'
  },

  // 金刚区相关
  NAV: {
    LIST: '/api/admin/nav/list',
    DETAIL: '/api/admin/nav/detail',
    ADD: '/api/admin/nav/add',
    EDIT: '/api/admin/nav/edit',
    DELETE: '/api/admin/nav/delete'
  },

  // 服务管理相关
  SERVICE: {
    LIST: '/api/admin/service/list',
    DETAIL: '/api/admin/service/info',
    ADD: '/api/admin/service/add',
    UPDATE: '/api/admin/service/update',
    DELETE: '/api/admin/service/delete',
    STATUS: '/api/admin/service/status'
  },

  // 服务点相关
  AGENT: {
    LIST: '/api/admin/agent/list',
    DETAIL: '/api/admin/agent/detail',
    ADD: '/api/admin/agent/add',
    EDIT: '/api/admin/agent/edit',
    DELETE: '/api/admin/agent/delete',
    STATUS: '/api/admin/agent/status'
  },
  
  // 认证相关
  AUTH: {
    LOGIN: '/api/admin/auth/login',
    LOGIN_BY_PASS: '/api/admin/login/loginByPass', // 新的密码登录接口
    LOGOUT: '/api/admin/auth/logout',
    REFRESH: '/api/admin/auth/refresh',
    USER_INFO: '/api/admin/auth/user'
  },
  
  // 服务项目相关
  SERVICE: {
    LIST: '/api/admin/service/list',
    DETAIL: '/api/admin/service/detail',
    ADD: '/api/admin/service/add',
    UPDATE: '/api/admin/service/update',
    DELETE: '/api/admin/service/delete'
  },

  // 操作日志相关
  OPERATION_LOG: {
    LIST: '/api/admin/operationLog/list'
  }
}

// 请求方法常量
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
}

// 响应状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
}
