import{r as h,X as O,h as Q,y as S,Q as t,A as u,I as s,al as n,J as W,ar as X,H as j,az as G,z as C,M as p,K as Y,O as m}from"./vendor-DmFBDimT.js";import{T as Z,L as v}from"./LbButton-BtU4V_Gr.js";import{L as ee}from"./LbImage-CnNh5Udj.js";import{L as te}from"./LbPage-DnbiQ0Ct.js";import{_ as le,a as T}from"./index-C9Xz1oqp.js";import{E as d,q as ae}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const oe={class:"service-list"},se={class:"content-container"},re={class:"search-form-container"},ie={class:"table-container"},ne={style:{margin:"0","font-weight":"500"}},ce={key:0,style:{margin:"0",color:"#999","font-size":"18px"}},ue={style:{color:"#f56c6c","font-weight":"500"}},de={class:"time-column"},pe={__name:"ServiceList",setup(me){const z=G(),y=h(!1),w=h([]),b=h(0),l=O({pageNum:1,pageSize:10,title:"",status:null,serviceCateName:"",servicePriceType:null}),N=h(),f=async o=>{o&&(l.pageNum=1),y.value=!0;try{const e={pageNum:l.pageNum,pageSize:l.pageSize};l.title&&(e.title=l.title),l.status!==null&&l.status!==""&&(e.status=l.status),l.serviceCateName&&(e.serviceCateName=l.serviceCateName),l.servicePriceType!==null&&l.servicePriceType!==""&&(e.servicePriceType=l.servicePriceType);const c=await T.service.serviceList(e);if(console.log("📋 服务列表数据 (API-V2):",c),c.code==="200"){const r=c.data;w.value=r.list||[],b.value=r.totalCount||r.total||0,console.log("📊 处理后的数据:",{list:w.value,total:b.value,pageNum:r.pageNum,pageSize:r.pageSize})}else console.error("❌ API响应错误:",c),d.error(c.message||c.msg||"获取数据失败")}catch(e){console.error("获取服务列表失败:",e),d.error("获取数据失败")}finally{y.value=!1}},P=o=>({0:"一口价",1:"报价模式",2:"两者都有"})[o]||"未知",k=()=>{f(1)},L=()=>{l.title="",l.status=null,l.serviceCateName="",l.servicePriceType=null,N.value?.resetFields(),f(1)},D=()=>{z.push({name:"ServiceEdit",query:{type:"add"}})},B=o=>{z.push({name:"ServiceEdit",query:{type:"edit",id:o.id}})},E=async o=>{try{await ae.confirm("确定要删除这个服务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await T.service.serviceDelete({id:o.id});e.code==="200"?(d.success("删除成功"),f()):d.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除服务失败:",e),d.error("删除失败"))}},I=async o=>{try{const e=await T.service.serviceStatus({id:o.id});e.code==="200"?d.success("状态修改成功"):(o.status=o.status===1?-1:1,d.error(e.message||"状态修改失败"))}catch(e){o.status=o.status===1?-1:1,console.error("修改状态失败:",e),d.error("状态修改失败")}},M=o=>{l.pageSize=o,V(1)},V=o=>{l.pageNum=o,f()},U=o=>o?o.split(" ")[0]:"-",R=o=>o?o.split(" ")[1]:"-";return Q(()=>{f()}),(o,e)=>{const c=n("el-input"),r=n("el-form-item"),_=n("el-option"),x=n("el-select"),A=n("el-col"),F=n("el-row"),q=n("el-form"),i=n("el-table-column"),H=n("el-tag"),$=n("el-switch"),J=n("el-table"),K=X("loading");return C(),S("div",oe,[t(Z,{title:"服务管理"}),u("div",se,[u("div",re,[t(q,{ref_key:"searchFormRef",ref:N,model:l,inline:!0,class:"search-form"},{default:s(()=>[t(F,{gutter:20},{default:s(()=>[t(A,{span:24},{default:s(()=>[t(r,{label:"服务名称",prop:"title"},{default:s(()=>[t(c,{size:"default",modelValue:l.title,"onUpdate:modelValue":e[0]||(e[0]=a=>l.title=a),placeholder:"请输入服务名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(r,{label:"状态",prop:"status"},{default:s(()=>[t(x,{size:"default",modelValue:l.status,"onUpdate:modelValue":e[1]||(e[1]=a=>l.status=a),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:s(()=>[t(_,{label:"可用",value:1}),t(_,{label:"禁用",value:-1})]),_:1},8,["modelValue"])]),_:1}),t(r,{label:"分类名称",prop:"serviceCateName"},{default:s(()=>[t(c,{size:"default",modelValue:l.serviceCateName,"onUpdate:modelValue":e[2]||(e[2]=a=>l.serviceCateName=a),placeholder:"请输入分类名称",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),t(r,{label:"报价类型",prop:"servicePriceType"},{default:s(()=>[t(x,{size:"default",modelValue:l.servicePriceType,"onUpdate:modelValue":e[3]||(e[3]=a=>l.servicePriceType=a),placeholder:"请选择报价类型",clearable:"",style:{width:"150px"}},{default:s(()=>[t(_,{label:"一口价模式",value:0}),t(_,{label:"报价模式",value:1}),t(_,{label:"两者都有",value:2})]),_:1},8,["modelValue"])]),_:1}),t(r,null,{default:s(()=>[t(v,{size:"default",type:"primary",icon:"Search",onClick:k},{default:s(()=>e[4]||(e[4]=[p(" 搜索 ")])),_:1,__:[4]}),t(v,{size:"default",icon:"RefreshLeft",onClick:L},{default:s(()=>e[5]||(e[5]=[p(" 重置 ")])),_:1,__:[5]}),t(v,{size:"default",type:"primary",icon:"Plus",onClick:D},{default:s(()=>e[6]||(e[6]=[p(" 新增服务 ")])),_:1,__:[6]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u("div",ie,[W((C(),j(J,{data:w.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:s(()=>[t(i,{prop:"id",label:"ID",width:"80",align:"center"}),t(i,{prop:"cover",label:"封面图",width:"120"},{default:s(a=>[t(ee,{src:a.row.cover,width:"80",height:"50"},null,8,["src"])]),_:1}),t(i,{prop:"title",label:"项目名称","min-width":"150"},{default:s(a=>[u("div",null,[u("p",ne,m(a.row.title),1),a.row.subTitle?(C(),S("p",ce,m(a.row.subTitle),1)):Y("",!0)])]),_:1}),t(i,{prop:"serviceCateName",label:"服务类型",width:"120"}),t(i,{prop:"price",label:"价格",width:"100",align:"center"},{default:s(a=>[u("span",ue," ¥"+m(a.row.price),1)]),_:1}),t(i,{prop:"servicePriceType",label:"报价类型",width:"120",align:"center"},{default:s(a=>[t(H,{type:a.row.servicePriceType===0?"success":a.row.servicePriceType===1?"warning":"info"},{default:s(()=>[p(m(P(a.row.servicePriceType)),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"totalSale",label:"销量",width:"80",align:"center"}),t(i,{prop:"top",label:"排序值",width:"80",align:"center"}),t(i,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(a=>[t($,{modelValue:a.row.status,"onUpdate:modelValue":g=>a.row.status=g,"active-value":1,"inactive-value":-1,onChange:g=>I(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(i,{label:"时间",width:"160"},{default:s(a=>[u("div",de,[u("p",null,m(U(a.row.createTime)),1),u("p",null,m(R(a.row.createTime)),1)])]),_:1}),t(i,{label:"操作",width:"200",fixed:"right"},{default:s(a=>[t(v,{size:"default",type:"primary",onClick:g=>B(a.row)},{default:s(()=>e[7]||(e[7]=[p(" 编辑 ")])),_:2,__:[7]},1032,["onClick"]),t(v,{size:"default",type:"danger",onClick:g=>E(a.row)},{default:s(()=>e[8]||(e[8]=[p(" 删除 ")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[K,y.value]])]),t(te,{page:l.pageNum,"page-size":l.pageSize,total:b.value,onHandleSizeChange:M,onHandleCurrentChange:V},null,8,["page","page-size","total"])])])}}},be=le(pe,[["__scopeId","data-v-ca87f271"]]);export{be as default};
