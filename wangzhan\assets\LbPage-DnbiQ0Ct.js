import{L as y}from"./LbButton-BtU4V_Gr.js";import{_ as z}from"./index-C9Xz1oqp.js";import{c as r,y as u,K as C,A as i,B as S,O as v,Q as g,al as B,z as p,I as k,M as N}from"./vendor-DmFBDimT.js";const x={class:"lb-page"},L={key:0,class:"batch-operations"},P={class:"batch-info"},D={class:"pagination-wrapper"},V={__name:"LbPage",props:{page:{type:Number,default:1},pageSize:{type:Number,default:10},total:{type:Number,default:0},pageSizes:{type:Array,default:()=>[10,20,50,100]},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},small:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},batch:{type:Boolean,default:!1},selectedCount:{type:Number,default:0}},emits:["handleSizeChange","handleCurrentChange","batchDelete","update:page","update:pageSize"],setup(e,{emit:c}){const o=e,n=c,s=r({get:()=>o.page,set:a=>n("update:page",a)}),d=r({get:()=>o.pageSize,set:a=>n("update:pageSize",a)}),m=a=>{n("handleSizeChange",a)},b=a=>{n("handleCurrentChange",a)},h=()=>{n("batchDelete")};return(a,t)=>{const f=B("el-pagination");return p(),u("div",x,[e.batch&&e.selectedCount>0?(p(),u("div",L,[i("span",P,"已选择 "+v(e.selectedCount)+" 项",1),S(a.$slots,"batch-operations",{},()=>[g(y,{size:"small",type:"danger",onClick:h},{default:k(()=>t[2]||(t[2]=[N(" 批量删除 ")])),_:1,__:[2]})],!0)])):C("",!0),i("div",D,[g(f,{"current-page":s.value,"onUpdate:currentPage":t[0]||(t[0]=l=>s.value=l),"page-size":d.value,"onUpdate:pageSize":t[1]||(t[1]=l=>d.value=l),"page-sizes":e.pageSizes,small:e.small,disabled:e.disabled,background:e.background,total:e.total,layout:e.layout,onSizeChange:m,onCurrentChange:b},null,8,["current-page","page-size","page-sizes","small","disabled","background","total","layout"])])])}}},U=z(V,[["__scopeId","data-v-ec619d36"]]);export{U as L};
