import{n as z,o as H,S as J,R as O,E as T}from"./element-fdzwdDuf.js";import{_ as U}from"./index-C9Xz1oqp.js";import{r as d,h as j,al as u,ar as G,y as i,z as c,A as e,P as h,a6 as v,C as f,Q as a,I as l,H as b,L as K,O as n,D as x,M as _,u as m,J as W}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const X={class:"content-analysis"},Y={class:"metrics-grid"},Z={class:"metric-content"},ee={class:"metric-value"},te={class:"metric-label"},se={class:"performance-section"},ae={class:"chart-row"},oe={class:"chart-card"},le={class:"chart-header"},ne={class:"chart-content"},ce={class:"mock-chart"},ie={class:"chart-placeholder"},re={class:"publish-data"},de={class:"publish-date"},ue={class:"publish-count"},_e={class:"chart-card"},pe={class:"chart-content"},he={class:"category-stats"},ve={class:"category-info"},me={class:"category-name"},ye={class:"category-stats-detail"},ge={class:"category-count"},fe={class:"category-percentage"},be={class:"popular-section"},ke={class:"table-card"},we={class:"table-header"},Ce={class:"header-controls"},De={class:"table-content"},Me={class:"interaction-section"},Ve={class:"chart-row"},ze={class:"chart-card"},Te={class:"chart-content"},xe={class:"interaction-stats"},qe={class:"interaction-header"},Ee={class:"interaction-label"},Be={class:"interaction-total"},Pe={class:"interaction-chart"},Se={class:"interaction-bar"},Ne={class:"chart-card"},Ae={class:"chart-content"},Fe={class:"quality-stats"},Ie={class:"quality-overview"},Le={class:"score-trend"},Qe={class:"quality-details"},Re={class:"quality-name"},$e={class:"quality-bar"},He={class:"quality-value"},Je={__name:"ContentAnalysisView",setup(Oe){const k=d(!1),y=d("month"),D=d("views"),q=d([{key:"totalContent",label:"总内容数",value:2345,change:"+15.3%",trend:"up",color:"#409eff",icon:"Document"},{key:"totalViews",label:"总阅读量",value:456789,change:"+22.1%",trend:"up",color:"#67c23a",icon:"View"},{key:"totalInteractions",label:"总互动数",value:89012,change:"+8.7%",trend:"up",color:"#e6a23c",icon:"ChatDotRound"},{key:"avgEngagement",label:"平均参与度",value:67,change:"-1.2%",trend:"down",color:"#f56c6c",icon:"Share"}]),E=d([{date:"01-07",count:12},{date:"01-08",count:15},{date:"01-09",count:8},{date:"01-10",count:18},{date:"01-11",count:22}]),B=d([{name:"技术分享",count:456,percentage:35,color:"#409eff"},{name:"产品动态",count:312,percentage:24,color:"#67c23a"},{name:"行业资讯",count:234,percentage:18,color:"#e6a23c"},{name:"公司新闻",count:189,percentage:15,color:"#f56c6c"},{name:"其他",count:109,percentage:8,color:"#909399"}]),M=d([{rank:1,title:"Vue3 + Vite 项目搭建最佳实践",category:"技术分享",author:"张三",views:12345,likes:567,comments:89,shares:123,publishTime:"2025-01-10 10:00"},{rank:2,title:"Element Plus 组件库使用指南",category:"技术分享",author:"李四",views:9876,likes:432,comments:67,shares:98,publishTime:"2025-01-09 14:30"},{rank:3,title:"2025年前端技术趋势展望",category:"行业资讯",author:"王五",views:8765,likes:398,comments:56,shares:87,publishTime:"2025-01-08 16:45"},{rank:4,title:"公司年度技术大会圆满举办",category:"公司新闻",author:"赵六",views:7654,likes:234,comments:45,shares:67,publishTime:"2025-01-07 09:00"},{rank:5,title:"新产品功能发布说明",category:"产品动态",author:"钱七",views:6543,likes:189,comments:34,shares:56,publishTime:"2025-01-06 11:20"}]),P=d([{type:"views",label:"阅读量",total:456789,percentage:85,color:"#409eff",trend:"up",change:"+12.5%"},{type:"likes",label:"点赞数",total:23456,percentage:65,color:"#67c23a",trend:"up",change:"+8.3%"},{type:"comments",label:"评论数",total:12345,percentage:45,color:"#e6a23c",trend:"down",change:"-2.1%"},{type:"shares",label:"分享数",total:6789,percentage:35,color:"#f56c6c",trend:"up",change:"+15.7%"}]),S=d([{type:"readability",name:"可读性",score:8.8},{type:"originality",name:"原创性",score:9.2},{type:"engagement",name:"互动性",score:7.9},{type:"timeliness",name:"时效性",score:8.5},{type:"accuracy",name:"准确性",score:9}]),w=o=>o>=1e4?(o/1e4).toFixed(1)+"w":o>=1e3?(o/1e3).toFixed(1)+"k":o.toString(),N=o=>o>=9?"#67c23a":o>=8?"#409eff":o>=7?"#e6a23c":"#f56c6c",A=async()=>{k.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),M.value.forEach(o=>{o.views+=Math.floor(Math.random()*100),o.likes+=Math.floor(Math.random()*10),o.comments+=Math.floor(Math.random()*5),o.shares+=Math.floor(Math.random()*8)}),T.success("数据已刷新")}catch{T.error("刷新数据失败")}finally{k.value=!1}};return j(()=>{console.log("内容分析页面已加载")}),(o,s)=>{const p=u("el-icon"),C=u("el-button"),F=u("el-button-group"),g=u("el-option"),I=u("el-select"),V=u("el-tag"),r=u("el-table-column"),L=u("el-link"),Q=u("el-table"),R=G("loading");return c(),i("div",X,[s[14]||(s[14]=e("div",{class:"page-title"},[e("h1",null,"内容分析"),e("p",{class:"page-subtitle"},"分析内容表现和用户互动数据")],-1)),e("div",Y,[(c(!0),i(h,null,v(q.value,t=>(c(),i("div",{class:"metric-card",key:t.key},[e("div",{class:"metric-icon",style:f({backgroundColor:t.color})},[a(p,{size:28},{default:l(()=>[(c(),b(K(t.icon)))]),_:2},1024)],4),e("div",Z,[e("div",ee,n(w(t.value)),1),e("div",te,n(t.label),1),e("div",{class:x(["metric-change",t.trend])},[a(p,null,{default:l(()=>[t.trend==="up"?(c(),b(m(z),{key:0})):(c(),b(m(H),{key:1}))]),_:2},1024),_(" "+n(t.change),1)],2)])]))),128))]),e("div",se,[e("div",ae,[e("div",oe,[e("div",le,[s[5]||(s[5]=e("h3",null,"内容发布趋势",-1)),a(F,{size:"small"},{default:l(()=>[a(C,{type:y.value==="week"?"primary":"",onClick:s[0]||(s[0]=t=>y.value="week")},{default:l(()=>s[3]||(s[3]=[_(" 最近一周 ")])),_:1,__:[3]},8,["type"]),a(C,{type:y.value==="month"?"primary":"",onClick:s[1]||(s[1]=t=>y.value="month")},{default:l(()=>s[4]||(s[4]=[_(" 最近一月 ")])),_:1,__:[4]},8,["type"])]),_:1})]),e("div",ne,[e("div",ce,[e("div",ie,[a(p,{size:64},{default:l(()=>[a(m(J))]),_:1}),s[6]||(s[6]=e("p",null,"内容发布趋势图",-1)),e("div",re,[(c(!0),i(h,null,v(E.value,(t,$)=>(c(),i("div",{class:"publish-item",key:$},[e("span",de,n(t.date),1),e("span",ue,n(t.count)+"篇",1)]))),128))])])])])]),e("div",_e,[s[7]||(s[7]=e("div",{class:"chart-header"},[e("h3",null,"内容分类分布")],-1)),e("div",pe,[e("div",he,[(c(!0),i(h,null,v(B.value,t=>(c(),i("div",{class:"category-item",key:t.name},[e("div",ve,[e("div",{class:"category-color",style:f({backgroundColor:t.color})},null,4),e("span",me,n(t.name),1)]),e("div",ye,[e("span",ge,n(t.count)+"篇",1),e("span",fe,n(t.percentage)+"%",1)])]))),128))])])])])]),e("div",be,[e("div",ke,[e("div",we,[s[9]||(s[9]=e("h3",null,"热门内容排行",-1)),e("div",Ce,[a(I,{modelValue:D.value,"onUpdate:modelValue":s[2]||(s[2]=t=>D.value=t),size:"small",style:{width:"120px"}},{default:l(()=>[a(g,{label:"按阅读量",value:"views"}),a(g,{label:"按点赞数",value:"likes"}),a(g,{label:"按评论数",value:"comments"}),a(g,{label:"按分享数",value:"shares"})]),_:1},8,["modelValue"]),a(C,{size:"small",onClick:A},{default:l(()=>[a(p,null,{default:l(()=>[a(m(O))]),_:1}),s[8]||(s[8]=_(" 刷新 "))]),_:1,__:[8]})])]),e("div",De,[W((c(),b(Q,{data:M.value,stripe:"",style:{width:"100%"}},{default:l(()=>[a(r,{prop:"rank",label:"排名",width:"80",align:"center"},{default:l(({row:t})=>[a(V,{type:t.rank<=3?"warning":"info",size:"small"},{default:l(()=>[_(n(t.rank),1)]),_:2},1032,["type"])]),_:1}),a(r,{prop:"title",label:"标题","min-width":"200","show-overflow-tooltip":""},{default:l(({row:t})=>[a(L,{type:"primary"},{default:l(()=>[_(n(t.title),1)]),_:2},1024)]),_:1}),a(r,{prop:"category",label:"分类",width:"100"},{default:l(({row:t})=>[a(V,{size:"small"},{default:l(()=>[_(n(t.category),1)]),_:2},1024)]),_:1}),a(r,{prop:"author",label:"作者",width:"100"}),a(r,{prop:"views",label:"阅读量",width:"100",align:"center"},{default:l(({row:t})=>[e("strong",null,n(w(t.views)),1)]),_:1}),a(r,{prop:"likes",label:"点赞数",width:"100",align:"center"}),a(r,{prop:"comments",label:"评论数",width:"100",align:"center"}),a(r,{prop:"shares",label:"分享数",width:"100",align:"center"}),a(r,{prop:"publishTime",label:"发布时间",width:"160"})]),_:1},8,["data"])),[[R,k.value]])])])]),e("div",Me,[e("div",Ve,[e("div",ze,[s[10]||(s[10]=e("div",{class:"chart-header"},[e("h3",null,"互动数据趋势")],-1)),e("div",Te,[e("div",xe,[(c(!0),i(h,null,v(P.value,t=>(c(),i("div",{class:"interaction-item",key:t.type},[e("div",qe,[e("span",Ee,n(t.label),1),e("span",Be,n(w(t.total)),1)]),e("div",Pe,[e("div",Se,[e("div",{class:"interaction-progress",style:f({width:t.percentage+"%",backgroundColor:t.color})},null,4)]),e("span",{class:x(["interaction-change",t.trend])},n(t.change),3)])]))),128))])])]),e("div",Ne,[s[13]||(s[13]=e("div",{class:"chart-header"},[e("h3",null,"内容质量评分")],-1)),e("div",Ae,[e("div",Fe,[e("div",Ie,[s[12]||(s[12]=e("div",{class:"quality-score"},[e("span",{class:"score-value"},"8.6"),e("span",{class:"score-label"},"综合评分")],-1)),e("div",Le,[a(p,{class:"trend-up"},{default:l(()=>[a(m(z))]),_:1}),s[11]||(s[11]=e("span",null,"+0.3",-1))])]),e("div",Qe,[(c(!0),i(h,null,v(S.value,t=>(c(),i("div",{class:"quality-item",key:t.type},[e("span",Re,n(t.name),1),e("div",$e,[e("div",{class:"quality-fill",style:f({width:t.score*10+"%",backgroundColor:N(t.score)})},null,4)]),e("span",He,n(t.score),1)]))),128))])])])])])])])}}},We=U(Je,[["__scopeId","data-v-fee8b5d0"]]);export{We as default};
