import{T as P,L as c}from"./LbButton-BtU4V_Gr.js";import{_ as T}from"./index-C9Xz1oqp.js";import{E as l}from"./element-fdzwdDuf.js";import{r as f,X as j,h as N,y as k,Q as t,A as m,I as o,al as a,z as S,M as p}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const U={class:"lb-system-print"},B={class:"page-main"},I={__name:"SystemPrint",setup(O){const d=f(!1),y=f(),n=j({printer_name:"",printer_ip:"",printer_port:9100,status:1}),g=async()=>{try{const e=await(await fetch("/api/system/print/config")).json();e.code===200&&Object.assign(n,e.data||{})}catch(s){console.error("获取配置失败:",s)}},v=async()=>{try{d.value=!0;const e=await(await fetch("/api/system/print/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})).json();e.code===200?l.success("配置保存成功"):l.error(e.message||"保存失败")}catch{l.error("保存失败")}finally{d.value=!1}},V=async()=>{try{const e=await(await fetch("/api/system/print/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})).json();e.code===200?l.success("打印测试成功"):l.error(e.message||"打印测试失败")}catch{l.error("打印测试失败")}};return N(()=>{g()}),(s,e)=>{const u=a("el-input"),i=a("el-form-item"),b=a("el-input-number"),_=a("el-radio"),w=a("el-radio-group"),x=a("el-form"),C=a("el-card");return S(),k("div",U,[t(P),m("div",B,[t(C,{class:"config-card",shadow:"never"},{header:o(()=>e[4]||(e[4]=[m("div",{class:"card-header"},[m("span",null,"打印机设置")],-1)])),default:o(()=>[t(x,{model:n,ref_key:"configFormRef",ref:y,"label-width":"140px",class:"config-form"},{default:o(()=>[t(i,{label:"打印机名称"},{default:o(()=>[t(u,{modelValue:n.printer_name,"onUpdate:modelValue":e[0]||(e[0]=r=>n.printer_name=r),placeholder:"请输入打印机名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"打印机IP"},{default:o(()=>[t(u,{modelValue:n.printer_ip,"onUpdate:modelValue":e[1]||(e[1]=r=>n.printer_ip=r),placeholder:"请输入打印机IP地址"},null,8,["modelValue"])]),_:1}),t(i,{label:"打印机端口"},{default:o(()=>[t(b,{modelValue:n.printer_port,"onUpdate:modelValue":e[2]||(e[2]=r=>n.printer_port=r),min:1,max:65535},null,8,["modelValue"])]),_:1}),t(i,{label:"启用状态"},{default:o(()=>[t(w,{modelValue:n.status,"onUpdate:modelValue":e[3]||(e[3]=r=>n.status=r)},{default:o(()=>[t(_,{value:1},{default:o(()=>e[5]||(e[5]=[p("启用")])),_:1,__:[5]}),t(_,{value:0},{default:o(()=>e[6]||(e[6]=[p("禁用")])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(c,{type:"primary",onClick:v,loading:d.value},{default:o(()=>e[7]||(e[7]=[p("保存配置")])),_:1,__:[7]},8,["loading"]),t(c,{onClick:V,style:{"margin-left":"10px"}},{default:o(()=>e[8]||(e[8]=[p("测试打印")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"])]),_:1})])])}}},J=T(I,[["__scopeId","data-v-a636b186"]]);export{J as default};
