<!--
  分销商列表页面
  管理已通过审核的分销商信息
-->

<template>
  <div class="lb-distribution-list">
    <TopNav />
    <div class="page-main">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.total_distributors || 0 }}</div>
              <div class="stats-label">总分销商</div>
            </div>
            <div class="stats-icon total">
              <i class="el-icon-user"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.active_distributors || 0 }}</div>
              <div class="stats-label">活跃分销商</div>
            </div>
            <div class="stats-icon active">
              <i class="el-icon-star-on"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ stats.total_commission || 0 }}</div>
              <div class="stats-label">总佣金</div>
            </div>
            <div class="stats-icon commission">
              <i class="el-icon-money"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.total_invites || 0 }}</div>
              <div class="stats-label">总邀请数</div>
            </div>
            <div class="stats-icon invites">
              <i class="el-icon-share"></i>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 搜索表单 -->
      <el-row class="page-search-form">
        <el-form @submit.prevent :inline="true" :model="searchForm" ref="searchFormRef">
          <el-form-item label="分销商姓名" prop="user_name">
            <el-input v-model="searchForm.user_name" placeholder="请输入分销商姓名" style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="searchForm.mobile" placeholder="请输入手机号" style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="等级" prop="level">
            <el-select @change="getTableDataList(1)" v-model="searchForm.level" placeholder="请选择">
              <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select @change="getTableDataList(1)" v-model="searchForm.status" placeholder="请选择">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <LbButton size="default" type="primary" style="margin-right: 5px" @click="getTableDataList(1)">
              搜索
            </LbButton>
            <LbButton size="default" style="margin-right: 5px" @click="resetForm">
              重置
            </LbButton>
          </el-form-item>
        </el-form>
      </el-row>
      
      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" fixed />
        <el-table-column prop="avatarUrl" label="头像" width="80">
          <template #default="scope">
            <img :src="scope.row.avatarUrl" alt="头像" style="width: 40px; height: 40px; border-radius: 50%;" />
          </template>
        </el-table-column>
        <el-table-column prop="user_name" label="分销商姓名" width="120" />
        <el-table-column prop="mobile" label="手机号" width="130" />
        <el-table-column prop="level" label="等级" width="100">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.level)" size="small">
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="commission_total" label="累计佣金" width="120">
          <template #default="scope">
            <span style="color: #e6a23c; font-weight: 600;">¥{{ scope.row.commission_total || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invite_count" label="邀请人数" width="100" />
        <el-table-column prop="order_count" label="订单数量" width="100" />
        <el-table-column prop="last_active_time" label="最后活跃" width="170">
          <template #default="scope">
            <div>{{ formatDate(scope.row.last_active_time, 1) }}</div>
            <div>{{ formatDate(scope.row.last_active_time, 2) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="mini"
                type="primary"
                @click="viewDetail(scope.row)"
              >
                查看详情
              </LbButton>
              <LbButton
                size="mini"
                type="success"
                @click="viewCommission(scope.row)"
              >
                佣金记录
              </LbButton>
              <LbButton
                size="mini"
                :type="scope.row.status === 1 ? 'danger' : 'success'"
                @click="toggleStatus(scope.row)"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 分销商详情对话框 -->
    <el-dialog v-model="detailVisible" title="分销商详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="分销商ID">{{ distributorDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ distributorDetail.user_name }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ distributorDetail.mobile }}</el-descriptions-item>
        <el-descriptions-item label="等级">{{ getLevelText(distributorDetail.level) }}</el-descriptions-item>
        <el-descriptions-item label="累计佣金">¥{{ distributorDetail.commission_total || 0 }}</el-descriptions-item>
        <el-descriptions-item label="邀请人数">{{ distributorDetail.invite_count || 0 }}</el-descriptions-item>
        <el-descriptions-item label="订单数量">{{ distributorDetail.order_count || 0 }}</el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ distributorDetail.create_time }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchFormRef = ref()
const detailVisible = ref(false)

// 统计数据
const stats = reactive({
  total_distributors: 0,
  active_distributors: 0,
  total_commission: 0,
  total_invites: 0
})

// 搜索表单
const searchForm = reactive({
  user_name: '',
  mobile: '',
  level: 0,
  status: 0
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 分销商详情
const distributorDetail = reactive({
  id: '',
  user_name: '',
  mobile: '',
  level: 1,
  commission_total: 0,
  invite_count: 0,
  order_count: 0,
  create_time: ''
})

// 等级选项
const levelOptions = [
  { label: '全部', value: 0 },
  { label: '普通分销商', value: 1 },
  { label: '高级分销商', value: 2 },
  { label: '金牌分销商', value: 3 }
]

// 状态选项
const statusOptions = [
  { label: '全部', value: 0 },
  { label: '正常', value: 1 },
  { label: '禁用', value: 2 }
]

// 方法
const getTableDataList = async (page = 1) => {
  loading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize,
      user_name: searchForm.user_name,
      mobile: searchForm.mobile,
      level: searchForm.level,
      status: searchForm.status
    })
    
    const response = await fetch(`/api/distribution/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
      // 更新统计数据
      Object.assign(stats, result.data.stats || {})
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取分销商列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  searchForm.user_name = ''
  searchForm.mobile = ''
  searchForm.level = 0
  searchForm.status = 0
  getTableDataList(1)
}

const getLevelType = (level) => {
  const levelMap = {
    1: 'info',
    2: 'success',
    3: 'warning'
  }
  return levelMap[level] || 'info'
}

const getLevelText = (level) => {
  const levelMap = {
    1: '普通分销商',
    2: '高级分销商',
    3: '金牌分销商'
  }
  return levelMap[level] || '普通分销商'
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    2: '禁用'
  }
  return statusMap[status] || '未知'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const viewDetail = async (row) => {
  try {
    const response = await fetch(`/api/distribution/detail/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(distributorDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.message || '获取分销商详情失败')
    }
  } catch (error) {
    console.error('获取分销商详情失败:', error)
    ElMessage.error('获取分销商详情失败')
  }
}

const viewCommission = (row) => {
  router.push(`/distribution/commission?distributor_id=${row.id}`)
}

const toggleStatus = async (row) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}分销商 "${row.user_name}" 吗？`,
      `${action}分销商确认`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/distribution/status/${row.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        status: row.status === 1 ? 2 : 1 
      })
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改分销商状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getTableDataList(1)
}

const handleCurrentChange = (page) => {
  getTableDataList(page)
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.lb-distribution-list {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  padding: 20px;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
}

.stats-icon.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stats-icon.active {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stats-icon.commission {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.stats-icon.invites {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.page-search-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-distribution-list {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
