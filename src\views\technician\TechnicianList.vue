<!--
  师傅管理列表页面
  根据API接口文档开发的师傅管理功能
  支持师傅列表查询、状态管理、审核等功能
-->

<template>
  <div class="technician-list">
    <!-- 顶部导航 -->
    <TopNav title="师傅管理" />

    <div class="content-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card clickable" :class="{ active: selectedStatType === 'all' }"
            @click="handleStatClick('all')">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.all || 0 }}</div>
              <div class="stat-label">全部</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card clickable" :class="{ active: selectedStatType === 'pending' }"
            @click="handleStatClick('pending')">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.ing || 0 }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card clickable" :class="{ active: selectedStatType === 'approved' }"
            @click="handleStatClick('approved')">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.pass || 0 }}</div>
              <div class="stat-label">审核通过</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card clickable" :class="{ active: selectedStatType === 'rejected' }"
            @click="handleStatClick('rejected')">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.noPass || 0 }}</div>
              <div class="stat-label">审核驳回</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="师傅姓名" prop="coachName">
                <el-input size="default" v-model="searchForm.coachName" placeholder="请输入师傅姓名" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="手机号" prop="mobile">
                <el-input size="default" v-model="searchForm.mobile" placeholder="请输入手机号" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="审核时间" prop="auditTime">
                <el-date-picker size="default" v-model="auditTimeRange" type="datetimerange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px" @change="handleAuditTimeChange" />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select size="default" v-model="searchForm.status" placeholder="请选择状态" clearable
                  style="width: 150px">
                  <el-option label="待审核" :value="1" />
                  <el-option label="审核通过" :value="2" />
                  <el-option label="审核驳回" :value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="开启状态" prop="isEnable">
                <el-select size="default" v-model="searchForm.isEnable" placeholder="请选择开启状态" clearable
                  style="width: 150px">
                  <el-option label="开启" :value="1" />
                  <el-option label="关闭" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
                <LbButton size="default" type="primary" icon="Plus"
                  @click="() => { console.log('🔄 点击新增师傅按钮'); router.push('/technician/edit?mode=add'); }">
                  新增师傅
                </LbButton>
                <LbButton size="default" type="warning" icon="Warning"
                  @click="() => { router.push('/technician/blacklist'); }">
                  黑名单管理
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>


      <!-- 数据表格 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData" :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontSize: '16px',
          fontWeight: '600'
        }" :cell-style="{
          fontSize: '14px',
          padding: '12px 8px'
        }" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="selfImg" label="师傅头像" width="100" align="center">
            <template #default="scope">
              <el-avatar v-if="scope.row.selfImg" :src="getFirstImage(scope.row.selfImg)" :size="50" shape="circle" />
              <el-avatar v-else :size="50" shape="circle">
                <el-icon>
                  <User />
                </el-icon>
              </el-avatar>
            </template>
          </el-table-column>

          <el-table-column prop="coachName" label="师傅姓名" width="100" />
          <el-table-column prop="sex" label="性别" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.sex === 0 ? 'primary' : 'success'" size="default">
                {{ scope.row.sex === 0 ? '男' : '女' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="mobile" label="手机号" width="150" />
          <el-table-column prop="idCode" label="身份证号" width="180" />

          <!-- 认证状态列 -->


          <!-- <el-table-column prop="workTime" label="从业年份" width="60" align="center" /> -->

          <el-table-column prop="address" label="详细地址" min-width="150" show-overflow-tooltip />

          <el-table-column prop="createTime" label="申请时间" width="180" />
          <el-table-column prop="userId" label="认证状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.userId === 0 ? 'warning' : 'success'" size="default">
                {{ scope.row.userId === 0 ? '未认证' : '已认证' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="审核状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="default">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="isEnable" label="开启状态" width="100" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.isEnable" :active-value="1" :inactive-value="2"
                @change="handleStatusChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column prop="count" label="订单数" width="80" align="center" />
          <el-table-column prop="cashPledge" label="保证金金额" width="120" align="center">
            <template #default="scope">
              <span>￥{{ scope.row.cashPledge || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="cashPledgeFreeze" label="冻结保证金" width="120" align="center">
            <template #default="scope">
              <span>￥{{ scope.row.cashPledgeFreeze || 0 }}</span>
            </template>
          </el-table-column>



          <el-table-column prop="credit" label="信誉分" width="80" align="center" />

          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="scope">
              <div class="operation-buttons">
                <div class="button-row">
                  <LbButton size="default" type="primary" @click="handleView(scope.row)">
                    查看
                  </LbButton>
                  <!-- 只有当userId为0（未认证）时才显示编辑按钮 -->
                  <LbButton v-if="scope.row.userId === 0" size="default" @click="handleEdit(scope.row)">
                    编辑
                  </LbButton>
                  <LbButton v-if="scope.row.status === 1" size="default" type="success" @click="handleApprove(scope.row)">
                    审核通过
                  </LbButton>
                  <LbButton v-if="scope.row.status === 1" size="default" type="danger" @click="handleReject(scope.row)">
                    审核驳回
                  </LbButton>
                </div>
                <div class="button-row">
                  <LbButton size="default" type="warning" @click="handleLevelAdjust(scope.row)">
                    等级调整
                  </LbButton>
                  <LbButton size="default" type="danger" @click="handleDelete(scope.row)">
                    删除
                  </LbButton>
                  <LbButton size="default" type="warning" @click="handleBlacklist(scope.row)">
                    黑名单
                  </LbButton>
                </div>
                <div class="button-row">
                  <LbButton size="default" type="danger" @click="handleMarginFreeze(scope.row)">
                    冻结保证金
                  </LbButton>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <!-- 审核驳回对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="审核驳回" width="500px">
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="驳回原因" required>
          <el-input v-model="rejectForm.shText" type="textarea" :rows="4" placeholder="请输入驳回原因" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="rejectDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmReject" :loading="rejectLoading">
          确认驳回
        </LbButton>
      </template>
    </el-dialog>

    <!-- 等级调整对话框 -->
    <el-dialog v-model="levelDialogVisible" title="等级调整" width="500px">
      <el-form :model="levelForm" label-width="100px">
        <el-form-item label="当前等级">
          <span>{{ currentCoach.labelName || '暂无等级' }}</span>
        </el-form-item>
        <el-form-item label="调整等级" required>
          <el-select v-model="levelForm.labelId" placeholder="请选择等级" style="width: 100%" @change="handleLevelChange">
            <el-option v-for="level in levelOptions" :key="level.id" :label="level.labelName" :value="level.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="levelDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmLevelAdjust" :loading="levelLoading">
          确认调整
        </LbButton>
      </template>
    </el-dialog>

    <!-- 关联用户选择对话框 -->
    <el-dialog v-model="userDialogVisible" title="选择关联用户" width="800px">
      <el-table :data="userList" @row-click="selectUser" style="cursor: pointer;">
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column prop="avatarUrl" label="头像" width="80">
          <template #default="scope">
            <el-avatar v-if="scope.row.avatarUrl" :src="scope.row.avatarUrl" :size="40" />
            <el-avatar v-else :size="40">
              <el-icon>
                <User />
              </el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="昵称" />
        <el-table-column prop="phone" label="手机号" />
      </el-table>

      <!-- 用户列表分页 -->
      <div class="user-pagination" style="margin-top: 20px; text-align: center;">
        <el-pagination v-model:current-page="userPagination.pageNum" v-model:page-size="userPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="userPagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleUserSizeChange" @current-change="handleUserCurrentChange" />
      </div>

      <template #footer>
        <LbButton @click="userDialogVisible = false">取消</LbButton>
      </template>
    </el-dialog>

    <!-- 黑名单操作对话框 -->
    <el-dialog v-model="blacklistDialogVisible" title="黑名单操作" width="500px">
      <el-form :model="blacklistForm" label-width="100px">
        <el-form-item label="操作类型" required>
          <el-radio-group v-model="blacklistForm.status">
            <el-radio :label="1">加入黑名单</el-radio>
            <el-radio :label="0">移除黑名单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" required>
          <el-input v-model="blacklistForm.text" type="textarea" :rows="4" placeholder="请输入审核备注" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="blacklistDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmBlacklist" :loading="blacklistLoading">
          确认操作
        </LbButton>
      </template>
    </el-dialog>

    <!-- 冻结保证金对话框 -->
    <el-dialog v-model="marginFreezeDialogVisible" title="冻结保证金" width="500px">
      <el-form :model="marginFreezeForm" label-width="100px" :rules="marginFreezeRules" ref="marginFreezeFormRef">
        <el-form-item label="师傅姓名">
          <span>{{ currentCoach.coachName }}</span>
        </el-form-item>
        <el-form-item label="当前保证金">
          <span>￥{{ currentCoach.cashPledge || 0 }}</span>
        </el-form-item>
        <el-form-item label="已冻结金额">
          <span>￥{{ currentCoach.cashPledgeFreeze || 0 }}</span>
        </el-form-item>
        <el-form-item label="冻结金额" prop="price" required>
          <el-input-number v-model="marginFreezeForm.price" :min="0.01"
            :max="currentCoach.cashPledge - (currentCoach.cashPledgeFreeze || 0)" :precision="2" placeholder="请输入冻结金额"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="冻结原因" prop="text" required>
          <el-input v-model="marginFreezeForm.text" type="textarea" :rows="4" placeholder="请输入冻结原因" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="marginFreezeDialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="confirmMarginFreeze" :loading="marginFreezeLoading">
          确认冻结
        </LbButton>
      </template>
    </el-dialog>

    <!-- 师傅详情查看对话框 -->
    <el-dialog v-model="detailDialogVisible" title="师傅详情" width="900px">
      <div v-loading="detailLoading" class="coach-detail">
        <el-row :gutter="20" v-if="coachDetail">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-item">
                <label>师傅姓名：</label>
                <span>{{ coachDetail.coachName }}</span>
              </div>
              <div class="detail-item">
                <label>手机号：</label>
                <span>{{ coachDetail.mobile }}</span>
              </div>
              <div class="detail-item">
                <label>性别：</label>
                <span>{{ coachDetail.sex === 0 ? '男' : '女' }}</span>
              </div>
              <div class="detail-item">
                <label>从业年份：</label>
                <span>{{ coachDetail.workTime }}年</span>
              </div>
              <div class="detail-item">
                <label>身份证号：</label>
                <span>{{ coachDetail.idCode }}</span>
              </div>
              <div class="detail-item">
                <label>地址：</label>
                <span>{{ coachDetail.address }}</span>
              </div>
              <div class="detail-item">
                <label>经纬度：</label>
                <span>{{ coachDetail.lng }}, {{ coachDetail.lat }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-section">
              <h4>状态信息</h4>
              <div class="detail-item">
                <label>审核状态：</label>
                <el-tag :type="getStatusType(coachDetail.status)">
                  {{ getStatusText(coachDetail.status) }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>开启状态：</label>
                <el-tag :type="coachDetail.isEnable === 1 ? 'success' : 'danger'">
                  {{ coachDetail.isEnable === 1 ? '开启' : '关闭' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>工作状态：</label>
                <el-tag :type="coachDetail.isWork === 1 ? 'success' : 'info'">
                  {{ coachDetail.isWork === 1 ? '工作中' : '休息中' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>师傅等级：</label>
                <span>{{ coachDetail.labelName || '暂无等级' }}</span>
              </div>
              <div class="detail-item">
                <label>服务次数：</label>
                <span>{{ coachDetail.count }}次</span>
              </div>
              <div class="detail-item">
                <label>信誉分：</label>
                <span>{{ coachDetail.credit }}分</span>
              </div>
              <div class="detail-item">
                <label>星级评分：</label>
                <span>{{ coachDetail.star }}星</span>
              </div>
              <div class="detail-item">
                <label>服务收入：</label>
                <span>￥{{ coachDetail.servicePrice }}</span>
              </div>
              <div class="detail-item">
                <label>上门费：</label>
                <span>￥{{ coachDetail.carPrice }}</span>
              </div>
              <div class="detail-item">
                <label>入驻时间：</label>
                <span>{{ coachDetail.createTime }}</span>
              </div>
              <div class="detail-item">
                <label>审核时间：</label>
                <span>{{ coachDetail.shTime || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ coachDetail.updateTime || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>是否后台添加：</label>
                <el-tag :type="coachDetail.adminAdd === 1 ? 'warning' : 'info'" size="default">
                  {{ coachDetail.adminAdd === 1 ? '后台添加' : '用户注册' }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="detail-section" v-if="coachDetail">
          <h4>图片信息</h4>
          <el-row :gutter="20">
            <el-col :span="8" v-if="coachDetail.selfImg">
              <div class="image-section">
                <label>个人照片：</label>
                <div class="image-list">
                  <el-image v-for="(img, index) in getImageList(coachDetail.selfImg)" :key="index" :src="img"
                    :preview-src-list="getImageList(coachDetail.selfImg)" fit="cover"
                    style="width: 80px; height: 80px; margin: 5px;" />
                </div>
              </div>
            </el-col>
            <el-col :span="8" v-if="coachDetail.idCard">
              <div class="image-section">
                <label>身份证照片：</label>
                <div class="image-list">
                  <el-image v-for="(img, index) in getImageList(coachDetail.idCard)" :key="index" :src="img"
                    :preview-src-list="getImageList(coachDetail.idCard)" fit="cover"
                    style="width: 80px; height: 80px; margin: 5px;" />
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 技能证书展示 -->
        <div class="detail-section" v-if="coachDetail">
          <h4>技能证书</h4>
          <el-row :gutter="20">
            <el-col :span="8" v-for="skill in getSkillCertificates(coachDetail)" :key="skill.name"
              class="skill-certificate-col">
              <div class="image-section">
                <label>{{ skill.name }}：</label>
                <div class="image-list">
                  <el-image v-for="(img, index) in getImageList(skill.images)" :key="index" :src="img"
                    :preview-src-list="getImageList(skill.images)" fit="cover"
                    style="width: 80px; height: 80px; margin: 5px;" />
                </div>
              </div>
            </el-col>
          </el-row>
          <div v-if="getSkillCertificates(coachDetail).length === 0" class="no-certificates">
            <el-empty description="暂无技能证书" :image-size="60" />
          </div>
        </div>

        <div class="detail-section" v-if="coachDetail && coachDetail.text">
          <h4>师傅简介</h4>
          <p>{{ coachDetail.text }}</p>
        </div>

        <div class="detail-section" v-if="coachDetail && coachDetail.shText">
          <h4>审核备注</h4>
          <p>{{ coachDetail.shText }}</p>
        </div>
      </div>

      <template #footer>
        <LbButton @click="detailDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'
import { useListRefresh, useRouteBackRefresh } from '@/composables/useDataRefresh'

const { proxy } = getCurrentInstance()
const router = useRouter()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const auditTimeRange = ref([])
const rejectDialogVisible = ref(false)
const levelDialogVisible = ref(false)
const userDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const blacklistDialogVisible = ref(false)
const marginFreezeDialogVisible = ref(false)
const rejectLoading = ref(false)
const levelLoading = ref(false)
const detailLoading = ref(false)
const blacklistLoading = ref(false)
const marginFreezeLoading = ref(false)
const marginFreezeFormRef = ref()
const userList = ref([])
const levelOptions = ref([])
const currentCoach = ref({})
const coachDetail = ref({})
const selectedStatType = ref('all')

// 统计数据
const statsData = ref({
  all: 0,
  ing: 0,
  pass: 0,
  noPass: 0
})

// 用户分页数据
const userPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  coachName: '',
  mobile: '',
  beginTime: '',
  endTime: '',
  status: '',
  isEnable: '',
  orderCount: null,
  creatTime: null,
  pageNum: 1,
  pageSize: 10
})

// 驳回表单
const rejectForm = reactive({
  coachId: '',
  shText: ''
})

// 等级调整表单
const levelForm = reactive({
  id: '',
  labelId: '',
  labelName: ''
})

// 黑名单操作表单
const blacklistForm = reactive({
  coachId: '',
  text: '',
  status: 1 // 默认加入黑名单
})

// 冻结保证金表单
const marginFreezeForm = reactive({
  coachId: '',
  price: 0,
  text: ''
})

// 冻结保证金表单验证规则
const marginFreezeRules = reactive({
  price: [
    { required: true, message: '请输入冻结金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '冻结金额必须大于0', trigger: 'blur' }
  ],
  text: [
    { required: true, message: '请输入冻结原因', trigger: 'blur' },
    { min: 1, max: 200, message: '冻结原因长度在1到200个字符', trigger: 'blur' }
  ]
})

// 方法
const handleAuditTimeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.beginTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.beginTime = ''
    searchForm.endTime = ''
  }
}

const getFirstImage = (images) => {
  if (typeof images === 'string') {
    const imageArray = images.split(',')
    return imageArray[0]
  }
  return images
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 待审核
    2: 'success',  // 审核通过
    4: 'danger'    // 审核驳回
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    4: '审核驳回'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  searchForm.pageNum = 1
  getCoachList()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  auditTimeRange.value = []
  searchForm.beginTime = ''
  searchForm.endTime = ''
  searchForm.pageNum = 1
  getCoachList()
}

const handleAdd = () => {
  console.log('🔄 点击新增师傅按钮')
  console.log('🔍 当前路由:', router.currentRoute.value.path)
  console.log('🔍 目标路由:', '/technician/edit?mode=add')

  // 直接跳转到新增页面
  router.push('/technician/edit?mode=add').then(() => {
    console.log('✅ 路由跳转成功')
  }).catch((error) => {
    console.error('❌ 路由跳转失败:', error)
  })
}

// 将方法暴露到全局，方便调试
window.handleAdd = handleAdd

const handleView = async (row) => {
  try {
    detailLoading.value = true
    detailDialogVisible.value = true

    const result = await proxy.$api.technician.coachDetail(row.id)
    if (result.code === '200') {
      coachDetail.value = result.data
    } else {
      ElMessage.error(result.msg || '获取师傅详情失败')
    }
  } catch (error) {
    console.error('获取师傅详情失败:', error)
    ElMessage.error('获取师傅详情失败')
  } finally {
    detailLoading.value = false
  }
}

const handleEdit = (row) => {
  console.log('🔄 点击编辑师傅按钮，师傅信息:', row)
  console.log('🔍 师傅ID:', row.id, '用户ID:', row.userId)

  // 跳转到编辑页面，传递师傅ID
  router.push(`/technician/edit?id=${row.id}&mode=edit`)
}

const handleStatusChange = async (row) => {
  try {
    // isEnable: 1开启 2关闭
    // status: 1开启 0关闭 -1删除
    const result = await proxy.$api.technician.coachStatus({
      id: row.id,
      status: row.isEnable === 1 ? 1 : 0
    })

    if (result.code === '200') {
      ElMessage.success('状态更新成功')
      getCoachList()
    } else {
      ElMessage.error(result.msg || '状态更新失败')
      // 恢复原状态
      row.isEnable = row.isEnable === 1 ? 2 : 1
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.isEnable = row.isEnable === 1 ? 2 : 1
  }
}

// 删除师傅
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该师傅吗？删除后无法恢复！', '危险操作', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })

    const result = await proxy.$api.technician.coachStatus({
      id: row.id,
      status: -1  // -1表示删除
    })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getCoachList()
    } else {
      ElMessage.error(result.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm('确认审核通过该师傅吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const result = await proxy.$api.technician.coachApprove({
      id: row.id
    })

    if (result.code === '200') {
      ElMessage.success('审核通过成功')
      getCoachList()
    } else {
      ElMessage.error(result.msg || '审核通过失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核通过失败:', error)
      ElMessage.error('审核通过失败')
    }
  }
}

const handleReject = (row) => {
  rejectForm.coachId = row.id
  rejectForm.shText = ''
  rejectDialogVisible.value = true
}

const confirmReject = async () => {
  if (!rejectForm.shText.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  try {
    rejectLoading.value = true
    const result = await proxy.$api.technician.coachReject({
      coachId: rejectForm.coachId,
      shText: rejectForm.shText
    })

    if (result.code === '200') {
      ElMessage.success('审核驳回成功')
      rejectDialogVisible.value = false
      getCoachList()
    } else {
      ElMessage.error(result.msg || '审核驳回失败')
    }
  } catch (error) {
    console.error('审核驳回失败:', error)
    ElMessage.error('审核驳回失败')
  } finally {
    rejectLoading.value = false
  }
}

const handleLevelAdjust = (row) => {
  currentCoach.value = row
  levelForm.id = row.id
  levelForm.labelId = ''
  levelForm.labelName = ''
  getLevelOptions()
  levelDialogVisible.value = true
}

const handleLevelChange = (labelId) => {
  const selectedLevel = levelOptions.value.find(level => level.id === labelId)
  if (selectedLevel) {
    levelForm.labelName = selectedLevel.labelName
  }
}

const confirmLevelAdjust = async () => {
  if (!levelForm.labelId) {
    ElMessage.warning('请选择等级')
    return
  }

  try {
    levelLoading.value = true
    const result = await proxy.$api.technician.coachUpDateLevel({
      coachId: levelForm.id,
      labelId: levelForm.labelId,
      labelName: levelForm.labelName
    })

    if (result.code === '200') {
      ElMessage.success('等级调整成功')
      levelDialogVisible.value = false
      getCoachList()
    } else {
      ElMessage.error(result.msg || '等级调整失败')
    }
  } catch (error) {
    console.error('等级调整失败:', error)
    ElMessage.error('等级调整失败')
  } finally {
    levelLoading.value = false
  }
}

const handleBlacklist = (row) => {
  blacklistForm.coachId = row.id
  blacklistForm.text = ''
  blacklistForm.status = 1 // 默认加入黑名单
  blacklistDialogVisible.value = true
}

// 冻结保证金处理
const handleMarginFreeze = (row) => {
  currentCoach.value = row
  marginFreezeForm.coachId = row.id
  marginFreezeForm.price = 0
  marginFreezeForm.text = ''
  marginFreezeDialogVisible.value = true
}

const confirmMarginFreeze = async () => {
  try {
    // 表单验证
    await marginFreezeFormRef.value?.validate()

    if (!marginFreezeForm.price || marginFreezeForm.price <= 0) {
      ElMessage.warning('请输入有效的冻结金额')
      return
    }

    if (!marginFreezeForm.text.trim()) {
      ElMessage.warning('请输入冻结原因')
      return
    }

    // 检查冻结金额是否超过可用保证金
    const availableAmount = (currentCoach.value.cashPledge || 0) - (currentCoach.value.cashPledgeFreeze || 0)
    if (marginFreezeForm.price > availableAmount) {
      ElMessage.warning(`冻结金额不能超过可用保证金 ￥${availableAmount}`)
      return
    }

    marginFreezeLoading.value = true
    const result = await proxy.$api.technician.marginFreeze({
      coachId: marginFreezeForm.coachId,
      price: marginFreezeForm.price,
      text: marginFreezeForm.text
    })

    if (result.code === '200') {
      ElMessage.success('保证金冻结成功')
      marginFreezeDialogVisible.value = false
      getCoachList() // 刷新列表
    } else {
      ElMessage.error(result.msg || '保证金冻结失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保证金冻结失败:', error)
      ElMessage.error('保证金冻结失败')
    }
  } finally {
    marginFreezeLoading.value = false
  }
}

const confirmBlacklist = async () => {
  if (!blacklistForm.text.trim()) {
    ElMessage.warning('请输入审核备注')
    return
  }

  try {
    blacklistLoading.value = true
    const result = await proxy.$api.technician.addBlack({
      coachId: blacklistForm.coachId,
      text: blacklistForm.text,
      status: blacklistForm.status
    })

    if (result.code === '200') {
      ElMessage.success(blacklistForm.status === 1 ? '加入黑名单成功' : '移除黑名单成功')
      blacklistDialogVisible.value = false
      getCoachList()
    } else {
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    console.error('黑名单操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    blacklistLoading.value = false
  }
}

const selectUser = (user) => {
  // 这里可以处理用户选择逻辑
  ElMessage.success(`已选择用户：${user.nickName}`)
  userDialogVisible.value = false
}

// 统计卡片点击处理
const handleStatClick = (type) => {
  selectedStatType.value = type

  // 根据点击的统计类型设置搜索条件
  switch (type) {
    case 'all':
      searchForm.status = ''
      break
    case 'pending':
      searchForm.status = 1
      break
    case 'approved':
      searchForm.status = 2
      break
    case 'rejected':
      searchForm.status = 4
      break
  }

  // 重置页码并搜索
  searchForm.pageNum = 1
  getCoachList()
}

// 用户分页处理
const handleUserSizeChange = (size) => {
  userPagination.pageSize = size
  userPagination.pageNum = 1
  getUserList()
}

const handleUserCurrentChange = (page) => {
  userPagination.pageNum = page
  getUserList()
}

// 获取图片列表
const getImageList = (images) => {
  if (!images) return []
  if (typeof images === 'string') {
    return images.split(',').filter(img => img.trim())
  }
  return Array.isArray(images) ? images : []
}

// 获取技能证书列表
const getSkillCertificates = (coachDetail) => {
  if (!coachDetail) return []

  const skillFields = [
    { name: '电工证', imgField: 'electricianImg' },
    { name: '驾驶证', imgField: 'driverLicenseImg' },
    { name: '制冷与空调作业证', imgField: 'workPermitImg' },
    { name: '燃气具安装维修资质', imgField: 'maintenanceCertificateImg' },
    { name: '高空作业A类证', imgField: 'gkzyAImg' },
    { name: '高空作业B类证', imgField: 'gkzyBImg' },
    { name: '开锁备案', imgField: 'ksbaImg' },
    { name: '焊工证', imgField: 'hgzImg' },
    { name: '弱电A类证', imgField: 'rdAImg' },
    { name: '弱电B类证', imgField: 'rdBImg' },
    { name: '特种行业许可证', imgField: 'tzhyhkzImg' },
    { name: '其他', imgField: 'otherImg' }
  ]

  return skillFields.filter(skill => {
    const images = coachDetail[skill.imgField]
    return images && images.trim() !== ''
  }).map(skill => ({
    name: skill.name,
    images: coachDetail[skill.imgField]
  }))
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getCoachList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getCoachList()
}

// API调用方法
const getCoachList = async () => {
  try {
    loading.value = true
    const result = await proxy.$api.technician.coachList(searchForm)

    if (result.code === '200') {
      tableData.value = result.data.coachList.list || []
      total.value = result.data.coachList.totalCount || 0

      // 更新统计数据
      statsData.value = {
        all: result.data.all || 0,
        ing: result.data.ing || 0,
        pass: result.data.pass || 0,
        noPass: result.data.noPass || 0
      }
    } else {
      ElMessage.error(result.msg || '获取师傅列表失败')
    }
  } catch (error) {
    console.error('获取师傅列表失败:', error)
    ElMessage.error('获取师傅列表失败')
  } finally {
    loading.value = false
  }
}

const getLevelOptions = async () => {
  try {
    const result = await proxy.$api.technician.labelCoachList()

    if (result.code === '200') {
      levelOptions.value = result.data || []
    } else {
      ElMessage.error(result.msg || '获取等级列表失败')
    }
  } catch (error) {
    console.error('获取等级列表失败:', error)
    ElMessage.error('获取等级列表失败')
  }
}

const getUserList = async () => {
  try {
    const result = await proxy.$api.technician.getAssociateUser({
      pageNum: userPagination.pageNum,
      pageSize: userPagination.pageSize
    })

    if (result.code === '200') {
      userList.value = result.data.list || []
      userPagination.total = result.data.totalCount || 0
    } else {
      ElMessage.error(result.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 使用列表刷新 Composable
const { refresh } = useListRefresh({
  module: 'technician',
  loadDataFn: getCoachList,
  autoRefresh: true
})

// 使用路由返回刷新 Composable
const { handleRouteBack } = useRouteBackRefresh({
  loadDataFn: getCoachList
})

// 生命周期
onMounted(() => {
  getCoachList()
  getUserList()
})
</script>

<style scoped>
.technician-list {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 统计卡片样式 - 使用统一规范 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable.active {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border: none;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa !important;
}

:deep(.el-table th) {
  border-bottom: 2px solid #e4e7ed;
}

/* 按钮样式 */
.el-button+.el-button {
  margin-left: 8px;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  background: #f5f7fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

/* 师傅详情样式 */
.coach-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.detail-item label {
  min-width: 100px;
  font-weight: 500;
  color: #666;
  margin-right: 12px;
}

.detail-item span {
  color: #333;
  flex: 1;
}

.image-section {
  margin-bottom: 16px;
}

.image-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #666;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-pagination {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

/* 技能证书样式 */
.skill-certificate-col {
  margin-bottom: 16px;
}

.no-certificates {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.no-certificates .el-empty {
  padding: 20px 0;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}

.button-row {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.button-row .el-button {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  height: 28px;
  line-height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    padding: 15px;
  }

  .search-form-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .stats-cards {
    margin-bottom: 15px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .content-container {
    margin: 5px;
    padding: 10px;
  }

  .search-form-container {
    padding: 10px;
  }

  .stats-cards {
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 20px;
  }
}
</style>