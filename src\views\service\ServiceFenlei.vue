<template>
  <div class="service-fenlei">
    <!-- 顶部导航 -->
    <TopNav title="服务分类管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="分类名称" prop="name">
                <el-input
                  size="default"
                  v-model="searchForm.name"
                  placeholder="请输入分类名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="启用" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="推荐状态" prop="isRecommend">
                <el-select
                  size="default"
                  v-model="searchForm.isRecommend"
                  placeholder="请选择推荐状态"
                  clearable
                  style="width: 140px"
                >
                  <el-option label="推荐" :value="1" />
                  <el-option label="不推荐" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增分类
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Plus"
                  @click="handleAddChild"
                >
                  添加子分类
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="displayData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
          row-key="id"
          :row-class-name="getRowClassName"
        >
        <el-table-column prop="id" label="ID" width="100" align="center">
  <template #default="scope">
    <span :style="{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }">
      {{ scope.row.id }}
    </span>
  </template>
</el-table-column>


        <el-table-column prop="name" label="分类名称" min-width="150">
          <template #default="scope">
            <span
              :style="{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }"
              class="category-name-wrapper"
            >
              <i
                v-if="scope.row.hasChildren"
                :class="[
                  'arrow-icon',
                  'el-icon-arrow-right',
                  { 'expanded': expandedRows.has(scope.row.id) }
                ]"
                @click="toggleExpand(scope.row)"
              ></i>
              <span v-else class="child-indent"></span>
              {{ scope.row.name }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="parentId" label="父级分类" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.parentId === 0" type="primary">顶级分类</el-tag>
            <span v-else>{{ getParentName(scope.row.parentId) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="isRecommend" label="推荐" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isRecommend"
              :active-value="1"
              :inactive-value="2"
              @change="handleRecommendChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" min-width="120">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <!-- 添加子分类/同级分类功能已抽离到页面顶部 -->
              <!-- <LbButton
                size="default"
                type="success"
                @click="handleAddChild(scope.row)"
              >
                {{ scope.row.parentId === 0 ? '添加子分类' : '添加同级分类' }}
              </LbButton> -->
              <LbButton
                size="default"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="default"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="父级分类" prop="parentId">
          <el-select
            v-model="form.parentId"
            placeholder="请选择父级分类（可选）"
            clearable
            style="width: 100%"
          >
            <el-option label="顶级分类" :value="0"></el-option>
            <el-option
              v-for="parent in parentOptions"
              :key="parent.id"
              :label="parent.name"
              :value="parent.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分类图片" prop="img">
          <el-input v-model="form.img" placeholder="请输入图片URL（可选）" />
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入分类描述（可选）" />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="推荐" prop="isRecommend">
          <el-radio-group v-model="form.isRecommend">
            <el-radio :value="1">推荐</el-radio>
            <el-radio :value="2">不推荐</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const displayData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)
const parentOptions = ref([])
const expandedRows = ref(new Set())

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  status: null,
  isRecommend: null,
  parentId: null
})

// 编辑表单
const form = reactive({
  id: null,
  name: '',
  parentId: 0,
  img: '',
  description: '',
  sort: 0,
  isRecommend: 2,
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑分类' : '新增分类')

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.name) params.name = searchForm.name
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.isRecommend !== null && searchForm.isRecommend !== '') params.isRecommend = searchForm.isRecommend
    if (searchForm.parentId !== null && searchForm.parentId !== '') params.parentId = searchForm.parentId

    // 使用API-V2调用方式
    const result = await api.service.serviceCateList(params)
    console.log('📋 服务分类列表数据 (API-V2):', result)

    // 处理真实API的响应格式
    if (result.code === 200 || result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      const rawList = data || [] // Assuming data.list holds the array
      tableData.value = rawList
      total.value = data.totalCount || data.total || 0

      // 构建树形结构显示数据
      buildTreeData()

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        displayData: displayData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取服务分类列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 构建树形结构显示数据
const buildTreeData = () => {
  const result = []
  const parentMap = new Map()

  // Process all items to determine hierarchy and collect parent IDs
  const categorizedData = tableData.value.map(item => {
    return {
      ...item,
      children: [],
      hasChildren: false, // Will be updated later
      level: item.parentId === 0 ? 0 : -1 // -1 means not a top-level parent yet
    }
  }).reduce((acc, item) => {
    acc[item.id] = item
    return acc
  }, {})

  // Populate children arrays and identify true parent nodes
  for (const id in categorizedData) {
    const item = categorizedData[id]
    if (item.parentId !== 0 && categorizedData[item.parentId]) {
      categorizedData[item.parentId].children.push(item)
      categorizedData[item.parentId].hasChildren = true
    }
  }

  // Build the flat display list, maintaining hierarchy and expansion state
  const appendChildren = (node, level) => {
    node.level = level
    result.push(node)
    if (node.hasChildren && expandedRows.value.has(node.id)) {
      node.children.sort((a, b) => a.sort - b.sort) // Sort children by sort field
      node.children.forEach(child => appendChildren(child, level + 1))
    }
  }

  // Start with top-level parents (parentId === 0)
  Object.values(categorizedData)
    .filter(item => item.parentId === 0)
    .sort((a, b) => a.sort - b.sort) // Sort top-level parents by sort field
    .forEach(parent => appendChildren(parent, 0))

  displayData.value = result
}

// 切换展开/收起
const toggleExpand = (row) => {
  if (expandedRows.value.has(row.id)) {
    expandedRows.value.delete(row.id)
  } else {
    expandedRows.value.add(row.id)
  }
  buildTreeData()
}

// 获取行样式类名
const getRowClassName = ({ row }) => {
  if (row.parentId === 0) {
    return 'parent-row'
  } else {
    return 'child-row'
  }
}

// 获取父级分类名称
const getParentName = (parentId) => {
  const parent = tableData.value.find(item => item.id === parentId)
  return parent ? parent.name : '未知'
}

// 加载父级分类选项
const loadParentOptions = async () => {
  try {
    const result = await api.service.serviceCateParentList()
    if (result.code === '200') {
      parentOptions.value = result.data || []
    }
  } catch (error) {
    console.error('获取父级分类列表失败:', error)
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.status = null
  searchForm.isRecommend = null
  searchForm.parentId = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  loadParentOptions()
  dialogVisible.value = true
}

const handleAddChild = (row) => {
  resetForm()
  if (row.parentId === 0) {
    // Adding a child category
    form.parentId = row.id
  } else {
    // Adding a sibling category
    form.parentId = row.parentId
  }
  loadParentOptions()
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  resetForm()
  try {
    // Get category details
    const result = await api.service.serviceCateInfo({ id: row.id })
    if (result.code === '200') {
      const data = result.data
      form.id = data.id
      form.name = data.name || ''
      form.parentId = data.parentId || 0
      form.img = data.img || ''
      form.description = data.description || ''
      form.sort = data.sort || 0
      form.isRecommend = data.isRecommend || 2
      form.status = data.status || 1
    } else {
      ElMessage.error(result.message || '获取分类详情失败')
      return
    }
  } catch (error) {
    console.error('获取分类详情失败:', error)
    ElMessage.error('获取分类详情失败')
    return
  }

  loadParentOptions()
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个分类吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.service.serviceCateDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    const result = await api.service.serviceCateStatus({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
    } else {
      // Revert to original status
      row.status = row.status === 1 ? 0 : 1
      ElMessage.error(result.message || '状态修改失败')
    }
  } catch (error) {
    // Revert to original status
    row.status = row.status === 1 ? 0 : 1
    console.error('修改状态失败:', error)
    ElMessage.error('状态修改失败')
  }
}

const handleRecommendChange = async (row) => {
  try {
    const result = await api.service.serviceCateRecommend({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('推荐状态修改成功')
    } else {
      // Revert to original status
      row.isRecommend = row.isRecommend === 1 ? 2 : 1
      ElMessage.error(result.message || '推荐状态修改失败')
    }
  } catch (error) {
    // Revert to original status
    row.isRecommend = row.isRecommend === 1 ? 2 : 1
    console.error('修改推荐状态失败:', error)
    ElMessage.error('推荐状态修改失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // Edit category
      result = await api.service.serviceCateUpdate(form)
    } else {
      // Add category
      result = await api.service.serviceCateAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.name = ''
  form.parentId = 0
  form.img = ''
  form.description = ''
  form.sort = 0
  form.isRecommend = 2
  form.status = 1
}

// Format date and time
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// Lifecycle hook
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* Page main styles */
.lb-examine-goods {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  margin-top: 0;
}

/* Spacing styles */
.space-lg {
  height: 16px;
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
} 

/* Table styles */
.el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* Time column styles */
.time-column p {
  margin: 0;
  line-height: 1.6;
  font-size: 16px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Operation button styles */
.table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Tree structure styles */
.expandable-id {
  cursor: pointer;
  color: #409eff;
  font-weight: bold;
}

.expandable-id:hover {
  text-decoration: underline;
}

/* Row styles */
:deep(.parent-row) {
  /* background-color: #f8f9fa; */
  font-weight: 500;
}

:deep(.child-row) {
  background-color: #fafbfc;
}

/* Dialog styles */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* Pagination styles */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: right;
}

/* Category name specific styles */
.category-name-wrapper {
  display: flex;
  align-items: center;
}

.arrow-icon {
  margin-right: 5px;
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
}

.arrow-icon.expanded {
  transform: rotate(90deg);
}

.child-indent {
  /* This creates the visual indent for child categories without an arrow */
  display: inline-block;
  width: 19px; /* Adjust this value to align with the arrow icon's width + margin */
  height: 1px; /* Minimal height */
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}
</style>