import{T as b,L as w}from"./LbButton-BtU4V_Gr.js";import{_ as k}from"./index-C9Xz1oqp.js";import{E as i}from"./element-fdzwdDuf.js";import{r as u,X as h,h as x,y as C,Q as o,A as p,I as a,al as t,z as M,M as N}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const S={class:"lb-system-message"},T={class:"page-main"},U={__name:"SystemMessage",setup(j){const c=u(!1),f=u(),s=h({platform:"",access_key:"",secret_key:"",signature:""}),_=async()=>{try{const e=await(await fetch("/api/system/message/config")).json();e.code===200&&Object.assign(s,e.data||{})}catch(n){console.error("获取配置失败:",n)}},y=async()=>{try{c.value=!0;const e=await(await fetch("/api/system/message/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();e.code===200?i.success("配置保存成功"):i.error(e.message||"保存失败")}catch{i.error("保存失败")}finally{c.value=!1}};return x(()=>{_()}),(n,e)=>{const d=t("el-option"),g=t("el-select"),r=t("el-form-item"),m=t("el-input"),v=t("el-form"),V=t("el-card");return M(),C("div",S,[o(b),p("div",T,[o(V,{class:"config-card",shadow:"never"},{header:a(()=>e[4]||(e[4]=[p("div",{class:"card-header"},[p("span",null,"短信通知设置")],-1)])),default:a(()=>[o(v,{model:s,ref_key:"configFormRef",ref:f,"label-width":"140px",class:"config-form"},{default:a(()=>[o(r,{label:"短信平台"},{default:a(()=>[o(g,{modelValue:s.platform,"onUpdate:modelValue":e[0]||(e[0]=l=>s.platform=l),placeholder:"请选择短信平台"},{default:a(()=>[o(d,{label:"阿里云",value:"aliyun"}),o(d,{label:"腾讯云",value:"tencent"}),o(d,{label:"华为云",value:"huawei"})]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"AccessKey"},{default:a(()=>[o(m,{modelValue:s.access_key,"onUpdate:modelValue":e[1]||(e[1]=l=>s.access_key=l),placeholder:"请输入AccessKey"},null,8,["modelValue"])]),_:1}),o(r,{label:"SecretKey"},{default:a(()=>[o(m,{modelValue:s.secret_key,"onUpdate:modelValue":e[2]||(e[2]=l=>s.secret_key=l),placeholder:"请输入SecretKey",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(r,{label:"签名"},{default:a(()=>[o(m,{modelValue:s.signature,"onUpdate:modelValue":e[3]||(e[3]=l=>s.signature=l),placeholder:"请输入短信签名"},null,8,["modelValue"])]),_:1}),o(r,null,{default:a(()=>[o(w,{type:"primary",onClick:y,loading:c.value},{default:a(()=>e[5]||(e[5]=[N("保存配置")])),_:1,__:[5]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},L=k(U,[["__scopeId","data-v-5d4fa4dc"]]);export{L as default};
