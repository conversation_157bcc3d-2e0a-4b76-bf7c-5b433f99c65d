import{T as G,L as p}from"./LbButton-BtU4V_Gr.js";import{_ as K}from"./index-C9Xz1oqp.js";import{E as r,q as S}from"./element-fdzwdDuf.js";import{r as f,X as A,h as W,y as Y,Q as t,A as s,al as i,I as a,J as Z,ar as ee,H as te,z as D,M as d,O as g}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const se={class:"page"},ae={class:"page-main"},oe={class:"distance-setting"},le={class:"setting-item"},ne={class:"setting-actions"},re={class:"distance-statistics"},ie={class:"stat-card"},de={class:"stat-number"},ue={class:"stat-card"},ce={class:"stat-number"},me={class:"stat-card"},pe={class:"stat-number"},_e={class:"stat-card"},fe={class:"stat-number"},ge={class:"area-management"},ve={class:"area-actions"},ye={class:"dialog-footer"},we={__name:"TechnicianDistance",setup(be){const _=f(1),k=f(!1),h=f(!1),V=f(!1),v=f(!1),C=f(),T=f([]),y=A({total:0,within_5km:0,within_10km:0,beyond_10km:0}),n=A({id:null,name:"",center_address:"",radius:5,status:1}),L={name:[{required:!0,message:"请输入区域名称",trigger:"blur"}],center_address:[{required:!0,message:"请输入中心地址",trigger:"blur"}],radius:[{required:!0,message:"请输入服务半径",trigger:"blur"},{type:"number",min:1,max:50,message:"服务半径必须在1-50km之间",trigger:"blur"}]},O=async()=>{if(_.value===""||_.value<1)return r.error("请填写正确的距离");k.value=!0;try{const e=await(await fetch("/api/technician/distance/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({distance:_.value})})).json();e.code===200?r.success("保存成功"):r.error(e.message||"保存失败")}catch(o){console.error("保存失败:",o),r.error("保存失败")}finally{k.value=!1}},U=()=>{_.value=1},E=async()=>{try{const e=await(await fetch("/api/technician/distance/config")).json();e.code===200?_.value=e.data.distance||1:r.error(e.message||"获取配置失败")}catch(o){console.error("获取配置失败:",o),r.error("获取配置失败")}},N=async()=>{try{const e=await(await fetch("/api/technician/distance/statistics")).json();e.code===200?Object.assign(y,e.data):r.error(e.message||"获取统计数据失败")}catch(o){console.error("获取统计数据失败:",o),r.error("获取统计数据失败")}},x=async()=>{h.value=!0;try{const e=await(await fetch("/api/technician/area/list")).json();e.code===200?T.value=e.data.list||[]:r.error(e.message||"获取区域列表失败")}catch(o){console.error("获取区域列表失败:",o),r.error("获取区域列表失败")}finally{h.value=!1}},z=o=>{Object.assign(n,o),v.value=!0},q=async()=>{try{await C.value.validate(),V.value=!0;const o=n.id?`/api/technician/area/update/${n.id}`:"/api/technician/area/add",e=n.id?"PUT":"POST",c=await(await fetch(o,{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})).json();c.code===200?(r.success(n.id?"更新成功":"新增成功"),v.value=!1,J(),x()):r.error(c.message||"操作失败")}catch(o){console.error("保存区域失败:",o),r.error("操作失败")}finally{V.value=!1}},F=async o=>{try{const e=o.status===1?0:1,u=e===1?"启用":"禁用";await S.confirm(`确定要${u}区域 "${o.name}" 吗？`,"状态切换确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const w=await(await fetch(`/api/technician/area/toggle/${o.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:e})})).json();w.code===200?(r.success(`${u}成功`),o.status=e):r.error(w.message||`${u}失败`)}catch(e){e!=="cancel"&&(console.error("状态切换失败:",e),r.error("操作失败"))}},I=async o=>{try{await S.confirm(`确定要删除区域 "${o.name}" 吗？`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const u=await(await fetch(`/api/technician/area/delete/${o.id}`,{method:"DELETE"})).json();u.code===200?(r.success("删除成功"),x()):r.error(u.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除失败:",e),r.error("删除失败"))}},J=()=>{n.id=null,n.name="",n.center_address="",n.radius=5,n.status=1};return W(()=>{E(),N(),x()}),(o,e)=>{const u=i("el-input-number"),c=i("el-col"),w=i("el-row"),m=i("el-table-column"),M=i("el-tag"),P=i("el-table"),$=i("el-input"),b=i("el-form-item"),j=i("el-radio"),R=i("el-radio-group"),H=i("el-form"),Q=i("el-dialog"),X=ee("loading");return D(),Y("div",se,[t(G),s("div",ae,[s("div",oe,[s("div",le,[e[8]||(e[8]=s("label",{class:"setting-label"},"师傅接单距离：",-1)),t(u,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=l=>_.value=l),placeholder:"请输入接单距离",min:1,max:100,style:{width:"200px"}},null,8,["modelValue"]),e[9]||(e[9]=s("span",{class:"unit"},"km",-1))]),s("div",ne,[t(p,{type:"primary",onClick:O,loading:k.value,size:"default"},{default:a(()=>e[10]||(e[10]=[d(" 保存设置 ")])),_:1,__:[10]},8,["loading"]),t(p,{onClick:U,style:{"margin-left":"12px"},size:"default"},{default:a(()=>e[11]||(e[11]=[d(" 重置 ")])),_:1,__:[11]})])]),e[26]||(e[26]=s("div",{class:"setting-description"},[s("h3",null,"设置说明"),s("ul",null,[s("li",null,"师傅接单距离是指师傅可以接收订单的最大距离范围"),s("li",null,"距离以师傅当前位置为中心，向四周扩散的半径距离"),s("li",null,"设置范围：1-100公里"),s("li",null,"建议根据实际业务需求和师傅分布情况合理设置"),s("li",null,"距离过小可能导致师傅接单困难，距离过大可能影响服务质量")])],-1)),s("div",re,[e[16]||(e[16]=s("h3",null,"师傅分布统计",-1)),t(w,{gutter:20},{default:a(()=>[t(c,{span:6},{default:a(()=>[s("div",ie,[s("div",de,g(y.total),1),e[12]||(e[12]=s("div",{class:"stat-label"},"总师傅数",-1))])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",ue,[s("div",ce,g(y.within_5km),1),e[13]||(e[13]=s("div",{class:"stat-label"},"5km内师傅",-1))])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",me,[s("div",pe,g(y.within_10km),1),e[14]||(e[14]=s("div",{class:"stat-label"},"10km内师傅",-1))])]),_:1}),t(c,{span:6},{default:a(()=>[s("div",_e,[s("div",fe,g(y.beyond_10km),1),e[15]||(e[15]=s("div",{class:"stat-label"},"10km外师傅",-1))])]),_:1})]),_:1})]),s("div",ge,[e[20]||(e[20]=s("h3",null,"区域管理",-1)),s("div",ve,[t(p,{type:"primary",onClick:e[1]||(e[1]=l=>v.value=!0)},{default:a(()=>e[17]||(e[17]=[d(" 新增服务区域 ")])),_:1,__:[17]})]),Z((D(),te(P,{data:T.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%","margin-top":"16px"}},{default:a(()=>[t(m,{prop:"id",label:"区域ID",width:"80"}),t(m,{prop:"name",label:"区域名称",width:"150"}),t(m,{prop:"center_address",label:"中心地址","min-width":"200"}),t(m,{prop:"radius",label:"服务半径",width:"120"},{default:a(l=>[d(g(l.row.radius)+"km ",1)]),_:1}),t(m,{prop:"technician_count",label:"师傅数量",width:"100"}),t(m,{prop:"status",label:"状态",width:"100"},{default:a(l=>[t(M,{type:l.row.status===1?"success":"info"},{default:a(()=>[d(g(l.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"create_time",label:"创建时间",width:"170"}),t(m,{label:"操作",width:"180",fixed:"right"},{default:a(l=>[t(p,{size:"mini",type:"primary",onClick:B=>z(l.row)},{default:a(()=>e[18]||(e[18]=[d(" 编辑 ")])),_:2,__:[18]},1032,["onClick"]),t(p,{size:"mini",type:l.row.status===1?"warning":"success",onClick:B=>F(l.row)},{default:a(()=>[d(g(l.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(p,{size:"mini",type:"danger",onClick:B=>I(l.row)},{default:a(()=>e[19]||(e[19]=[d(" 删除 ")])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,h.value]])]),t(Q,{title:n.id?"编辑服务区域":"新增服务区域",modelValue:v.value,"onUpdate:modelValue":e[7]||(e[7]=l=>v.value=l),width:"600px"},{footer:a(()=>[s("span",ye,[t(p,{onClick:e[6]||(e[6]=l=>v.value=!1)},{default:a(()=>e[24]||(e[24]=[d("取消")])),_:1,__:[24]}),t(p,{type:"primary",onClick:q,loading:V.value},{default:a(()=>e[25]||(e[25]=[d(" 确定 ")])),_:1,__:[25]},8,["loading"])])]),default:a(()=>[t(H,{model:n,ref_key:"areaFormRef",ref:C,rules:L,"label-width":"120px"},{default:a(()=>[t(b,{label:"区域名称",prop:"name"},{default:a(()=>[t($,{modelValue:n.name,"onUpdate:modelValue":e[2]||(e[2]=l=>n.name=l),placeholder:"请输入区域名称"},null,8,["modelValue"])]),_:1}),t(b,{label:"中心地址",prop:"center_address"},{default:a(()=>[t($,{modelValue:n.center_address,"onUpdate:modelValue":e[3]||(e[3]=l=>n.center_address=l),placeholder:"请输入中心地址"},null,8,["modelValue"])]),_:1}),t(b,{label:"服务半径",prop:"radius"},{default:a(()=>[t(u,{modelValue:n.radius,"onUpdate:modelValue":e[4]||(e[4]=l=>n.radius=l),min:1,max:50,placeholder:"请输入服务半径",style:{width:"100%"}},null,8,["modelValue"]),e[21]||(e[21]=s("span",{style:{"margin-left":"8px"}},"km",-1))]),_:1,__:[21]}),t(b,{label:"状态",prop:"status"},{default:a(()=>[t(R,{modelValue:n.status,"onUpdate:modelValue":e[5]||(e[5]=l=>n.status=l)},{default:a(()=>[t(j,{value:1},{default:a(()=>e[22]||(e[22]=[d("启用")])),_:1,__:[22]}),t(j,{value:0},{default:a(()=>e[23]||(e[23]=[d("禁用")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])])}}},Te=K(we,[["__scopeId","data-v-8e7b8d5d"]]);export{Te as default};
