import{T as C,L as c}from"./LbButton-BtU4V_Gr.js";import{_ as A}from"./index-C9Xz1oqp.js";import{E as p}from"./element-fdzwdDuf.js";import{r as v,X as S,h as U,y as j,Q as l,A as t,I as a,al as i,z as T,M as u}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const I={class:"lb-system-wechat"},N={class:"page-main"},O={__name:"SystemWechat",setup(q){const _=v(!1),m=v(),o=S({app_name:"",app_id:"",app_secret:"",mch_id:"",mch_key:"",version:"",status:1,debug:0,remark:""}),V={app_name:[{required:!0,message:"请输入小程序名称",trigger:"blur"}],app_id:[{required:!0,message:"请输入小程序AppID",trigger:"blur"}],app_secret:[{required:!0,message:"请输入小程序AppSecret",trigger:"blur"}]},w=async()=>{try{const e=await(await fetch("/api/system/wechat/config")).json();e.code===200?Object.assign(o,e.data||{}):p.error(e.message||"获取配置失败")}catch(n){console.error("获取小程序配置失败:",n),p.error("获取配置失败")}},b=async()=>{try{await m.value.validate(),_.value=!0;const e=await(await fetch("/api/system/wechat/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?p.success("配置保存成功"):p.error(e.message||"保存失败")}catch(n){console.error("保存配置失败:",n),p.error("保存失败")}finally{_.value=!1}},h=()=>{Object.assign(o,{app_name:"",app_id:"",app_secret:"",mch_id:"",mch_key:"",version:"",status:1,debug:0,remark:""}),m.value&&m.value.clearValidate()},k=async()=>{try{await m.value.validate();const e=await(await fetch("/api/system/wechat/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})).json();e.code===200?p.success("连接测试成功"):p.error(e.message||"连接测试失败")}catch(n){console.error("连接测试失败:",n),p.error("连接测试失败")}};return U(()=>{w()}),(n,e)=>{const d=i("el-input"),r=i("el-form-item"),f=i("el-radio"),g=i("el-radio-group"),x=i("el-form"),y=i("el-card");return T(),j("div",I,[l(C),t("div",N,[l(y,{class:"config-card",shadow:"never"},{header:a(()=>e[9]||(e[9]=[t("div",{class:"card-header"},[t("span",null,"小程序设置")],-1)])),default:a(()=>[l(x,{model:o,rules:V,ref_key:"configFormRef",ref:m,"label-width":"140px",class:"config-form"},{default:a(()=>[l(r,{label:"小程序名称",prop:"app_name"},{default:a(()=>[l(d,{modelValue:o.app_name,"onUpdate:modelValue":e[0]||(e[0]=s=>o.app_name=s),placeholder:"请输入小程序名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(r,{label:"AppID",prop:"app_id"},{default:a(()=>[l(d,{modelValue:o.app_id,"onUpdate:modelValue":e[1]||(e[1]=s=>o.app_id=s),placeholder:"请输入小程序AppID",maxlength:"100"},null,8,["modelValue"])]),_:1}),l(r,{label:"AppSecret",prop:"app_secret"},{default:a(()=>[l(d,{modelValue:o.app_secret,"onUpdate:modelValue":e[2]||(e[2]=s=>o.app_secret=s),placeholder:"请输入小程序AppSecret",type:"password","show-password":"",maxlength:"100"},null,8,["modelValue"])]),_:1}),l(r,{label:"商户号",prop:"mch_id"},{default:a(()=>[l(d,{modelValue:o.mch_id,"onUpdate:modelValue":e[3]||(e[3]=s=>o.mch_id=s),placeholder:"请输入微信支付商户号",maxlength:"50"},null,8,["modelValue"])]),_:1}),l(r,{label:"商户密钥",prop:"mch_key"},{default:a(()=>[l(d,{modelValue:o.mch_key,"onUpdate:modelValue":e[4]||(e[4]=s=>o.mch_key=s),placeholder:"请输入微信支付商户密钥",type:"password","show-password":"",maxlength:"100"},null,8,["modelValue"])]),_:1}),l(r,{label:"小程序版本",prop:"version"},{default:a(()=>[l(d,{modelValue:o.version,"onUpdate:modelValue":e[5]||(e[5]=s=>o.version=s),placeholder:"请输入小程序版本号",maxlength:"20"},null,8,["modelValue"])]),_:1}),l(r,{label:"启用状态",prop:"status"},{default:a(()=>[l(g,{modelValue:o.status,"onUpdate:modelValue":e[6]||(e[6]=s=>o.status=s)},{default:a(()=>[l(f,{value:1},{default:a(()=>e[10]||(e[10]=[u("启用")])),_:1,__:[10]}),l(f,{value:0},{default:a(()=>e[11]||(e[11]=[u("禁用")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"调试模式",prop:"debug"},{default:a(()=>[l(g,{modelValue:o.debug,"onUpdate:modelValue":e[7]||(e[7]=s=>o.debug=s)},{default:a(()=>[l(f,{value:1},{default:a(()=>e[12]||(e[12]=[u("开启")])),_:1,__:[12]}),l(f,{value:0},{default:a(()=>e[13]||(e[13]=[u("关闭")])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"备注说明",prop:"remark"},{default:a(()=>[l(d,{modelValue:o.remark,"onUpdate:modelValue":e[8]||(e[8]=s=>o.remark=s),type:"textarea",rows:4,placeholder:"请输入备注说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(r,null,{default:a(()=>[l(c,{type:"primary",onClick:b,loading:_.value},{default:a(()=>e[14]||(e[14]=[u(" 保存配置 ")])),_:1,__:[14]},8,["loading"]),l(c,{onClick:h,style:{"margin-left":"10px"}},{default:a(()=>e[15]||(e[15]=[u(" 重置 ")])),_:1,__:[15]}),l(c,{type:"success",onClick:k,style:{"margin-left":"10px"}},{default:a(()=>e[16]||(e[16]=[u(" 测试连接 ")])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),l(y,{class:"help-card",shadow:"never"},{header:a(()=>e[17]||(e[17]=[t("div",{class:"card-header"},[t("span",null,"配置说明")],-1)])),default:a(()=>[e[18]||(e[18]=t("div",{class:"help-content"},[t("h4",null,"小程序配置步骤："),t("ol",null,[t("li",null,"登录微信公众平台，进入小程序管理后台"),t("li",null,'在"开发"-"开发设置"中获取AppID和AppSecret'),t("li",null,'在"微信支付"中获取商户号和商户密钥'),t("li",null,"配置服务器域名，将本站域名添加到request合法域名中"),t("li",null,"上传小程序代码并提交审核")]),t("h4",null,"注意事项："),t("ul",null,[t("li",null,"AppSecret请妥善保管，不要泄露给他人"),t("li",null,"商户密钥用于微信支付，请确保安全"),t("li",null,"调试模式仅在开发环境使用，生产环境请关闭"),t("li",null,"修改配置后需要重新发布小程序")])],-1))]),_:1,__:[18]})])])}}},M=A(O,[["__scopeId","data-v-ef9e8b1f"]]);export{M as default};
