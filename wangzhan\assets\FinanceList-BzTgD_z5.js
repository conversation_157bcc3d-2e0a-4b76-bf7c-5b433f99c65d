import{T as Q,L as z}from"./LbButton-BtU4V_Gr.js";import{L as X}from"./LbPage-DnbiQ0Ct.js";import{_ as G}from"./index-C9Xz1oqp.js";import{E as _}from"./element-fdzwdDuf.js";import{g as K,r as m,X as Z,h as ee,y as te,Q as o,A as i,I as l,al as d,J as ae,ar as oe,H as le,z as M,O as g,M as v,D as se}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ne={class:"page-container"},re={class:"content-container"},ie={class:"stat-content"},ce={class:"stat-value"},de={class:"stat-content"},ue={class:"stat-value"},pe={class:"stat-content"},me={class:"stat-value"},fe={class:"stat-content"},_e={class:"stat-value"},ge={class:"search-form-container"},he={class:"table-container"},ye={__name:"FinanceList",setup(ve){const{proxy:N}=K(),w=m(!1),x=m(!1),S=m([]),b=m(0),C=m([]),k=m(0),F=m(0),V=m(0),e=Z({pageNum:1,pageSize:10,type:null,userId:"",coachId:"",startTime:"",endTime:""}),E=m(null),$=a=>({0:"服务收入",1:"分销",2:"服务提现",3:"佣金提现"})[a]||"未知",L=a=>({0:"success",1:"primary",2:"warning",3:"info"})[a]||"info",R=a=>{const t=parseFloat(a)||0;return t>=0?`+¥${t.toFixed(2)}`:`¥${t.toFixed(2)}`},H=(a,t)=>{const s=parseFloat(t)||0;return a===2||a===3?"price-negative":s>0?"price-positive":"price-neutral"},P=a=>{a&&a.length===2?(e.startTime=a[0],e.endTime=a[1]):(e.startTime="",e.endTime="")},O=a=>{k.value=a.reduce((n,c)=>n+(parseFloat(c.price)||0),0).toFixed(2);const t=new Date().toISOString().split("T")[0],s=a.filter(n=>n.createTime&&n.createTime.startsWith(t));F.value=s.length,V.value=s.reduce((n,c)=>n+(parseFloat(c.price)||0),0).toFixed(2)},T=async()=>{try{w.value=!0,console.log("🔍 开始加载财务流水列表，参数:",e);const a={pageNum:e.pageNum,pageSize:e.pageSize};e.type!==null&&(a.type=e.type),e.userId&&(a.userId=parseInt(e.userId)),e.coachId&&(a.coachId=parseInt(e.coachId)),e.startTime&&(a.startTime=e.startTime),e.endTime&&(a.endTime=e.endTime);const t=await N.$api.finance.list(a);console.log("📋 财务流水列表响应:",t),t.code==="200"?(S.value=t.data.list||[],b.value=t.data.totalCount||0,O(t.data.list||[]),console.log(`✅ 财务流水列表加载成功，共 ${b.value} 条数据`)):_.error(t.msg||"获取财务流水列表失败")}catch(a){console.error("❌ 加载财务流水列表失败:",a),_.error("获取财务流水列表失败")}finally{w.value=!1}},U=()=>{e.pageNum=1,T()},Y=()=>{E.value?.resetFields(),Object.assign(e,{pageNum:1,pageSize:10,type:null,userId:"",coachId:"",startTime:"",endTime:""}),C.value=[],T()},j=async()=>{try{x.value=!0,console.log("📤 开始导出财务流水Excel...");const a={};e.type!==""&&e.type!==null&&e.type!==void 0&&(a.type=parseInt(e.type)),e.userId!==""&&e.userId!==null&&e.userId!==void 0&&(a.userId=parseInt(e.userId)),e.coachId!==""&&e.coachId!==null&&e.coachId!==void 0&&(a.coachId=parseInt(e.coachId)),e.startTime!==""&&e.startTime!==null&&e.startTime!==void 0&&(a.startTime=e.startTime),e.endTime!==""&&e.endTime!==null&&e.endTime!==void 0&&(a.endTime=e.endTime),console.log("📤 导出参数:",a);const t=sessionStorage.getItem("minitk"),s=await fetch("/api/admin/finance/export",{method:"POST",headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}},body:JSON.stringify(a)});if(s.ok){const n=s.headers.get("Content-Type");if(n&&n.includes("application/json")){const u=await s.json();if(console.error("❌ 导出返回错误:",u),u.code==="-1"||u.code===-1){const I=u.msg||"导出失败";_.error(`导出失败: ${I}`),(I.includes("ResultMapException")||I.includes("column"))&&_.warning("后端数据库字段映射异常，请联系技术人员修复")}else _.error(u.msg||"导出失败");return}const c=s.headers.get("Content-Disposition");let h=`财务流水导出_${new Date().toISOString().slice(0,10)}.xlsx`;if(c){const u=c.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);u&&u[1]&&(h=u[1].replace(/['"]/g,""))}const D=await s.blob(),f=window.URL.createObjectURL(D),p=document.createElement("a");p.href=f,p.download=h,p.style.display="none",document.body.appendChild(p),p.click(),document.body.removeChild(p),window.URL.revokeObjectURL(f),_.success("导出成功，请查看浏览器下载"),console.log("✅ 导出财务流水Excel成功")}else try{const n=await s.text();console.error("❌ 导出HTTP错误:",s.status,s.statusText,n);try{const c=JSON.parse(n);if(c.msg)_.error(`导出失败: ${c.msg}`);else throw new Error(`HTTP ${s.status}: ${s.statusText}`)}catch{throw new Error(`导出失败: HTTP ${s.status} ${s.statusText}`)}}catch{throw new Error(`导出失败: HTTP ${s.status} ${s.statusText}`)}}catch(a){console.error("❌ 导出财务流水Excel异常:",a),_.error("导出失败，请稍 后重试")}finally{x.value=!1}},B=a=>{e.pageSize=a,e.pageNum=1,T()},A=a=>{e.pageNum=a,T()};return ee(()=>{console.log("🚀 财务流水管理页面初始化"),T()}),(a,t)=>{const s=d("el-card"),n=d("el-col"),c=d("el-row"),h=d("el-option"),D=d("el-select"),f=d("el-form-item"),p=d("el-input"),u=d("el-date-picker"),I=d("el-form"),y=d("el-table-column"),J=d("el-tag"),W=d("el-table"),q=oe("loading");return M(),te("div",ne,[o(Q,{title:"财务流水管理"}),i("div",re,[o(c,{gutter:20,class:"stats-cards"},{default:l(()=>[o(n,{span:6},{default:l(()=>[o(s,{class:"stat-card"},{default:l(()=>[i("div",ie,[i("div",ce,g(b.value||0),1),t[4]||(t[4]=i("div",{class:"stat-label"},"流水总数",-1))])]),_:1})]),_:1}),o(n,{span:6},{default:l(()=>[o(s,{class:"stat-card"},{default:l(()=>[i("div",de,[i("div",ue,"¥"+g(k.value||"0.00"),1),t[5]||(t[5]=i("div",{class:"stat-label"},"流水总金额",-1))])]),_:1})]),_:1}),o(n,{span:6},{default:l(()=>[o(s,{class:"stat-card"},{default:l(()=>[i("div",pe,[i("div",me,g(F.value||0),1),t[6]||(t[6]=i("div",{class:"stat-label"},"今日流水",-1))])]),_:1})]),_:1}),o(n,{span:6},{default:l(()=>[o(s,{class:"stat-card"},{default:l(()=>[i("div",fe,[i("div",_e,"¥"+g(V.value||"0.00"),1),t[7]||(t[7]=i("div",{class:"stat-label"},"今日金额",-1))])]),_:1})]),_:1})]),_:1}),i("div",ge,[o(I,{ref_key:"searchFormRef",ref:E,model:e,inline:!0,class:"search-form"},{default:l(()=>[o(c,{gutter:20},{default:l(()=>[o(n,{span:24},{default:l(()=>[o(f,{label:"流水类型",prop:"type"},{default:l(()=>[o(D,{size:"default",modelValue:e.type,"onUpdate:modelValue":t[0]||(t[0]=r=>e.type=r),placeholder:"请选择流水类型",clearable:"",style:{width:"150px"}},{default:l(()=>[o(h,{label:"服务收入",value:0}),o(h,{label:"分销",value:1}),o(h,{label:"服务提现",value:2}),o(h,{label:"佣金提现",value:3})]),_:1},8,["modelValue"])]),_:1}),o(f,{label:"用户ID",prop:"userId"},{default:l(()=>[o(p,{size:"default",modelValue:e.userId,"onUpdate:modelValue":t[1]||(t[1]=r=>e.userId=r),placeholder:"请输入用户ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),o(f,{label:"师傅ID",prop:"coachId"},{default:l(()=>[o(p,{size:"default",modelValue:e.coachId,"onUpdate:modelValue":t[2]||(t[2]=r=>e.coachId=r),placeholder:"请输入师傅ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),o(f,{label:"时间范围",prop:"timeRange"},{default:l(()=>[o(u,{size:"default",modelValue:C.value,"onUpdate:modelValue":t[3]||(t[3]=r=>C.value=r),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:P},null,8,["modelValue"])]),_:1}),o(f,null,{default:l(()=>[o(z,{size:"default",type:"primary",icon:"Search",onClick:U,loading:w.value},{default:l(()=>t[8]||(t[8]=[v(" 搜索 ")])),_:1,__:[8]},8,["loading"]),o(z,{size:"default",icon:"RefreshLeft",onClick:Y},{default:l(()=>t[9]||(t[9]=[v(" 重置 ")])),_:1,__:[9]}),o(z,{size:"default",type:"success",icon:"Download",onClick:j,loading:x.value},{default:l(()=>t[10]||(t[10]=[v(" 导出 ")])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),i("div",he,[ae((M(),le(W,{data:S.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"},fit:!0},{default:l(()=>[o(y,{prop:"id",label:"ID",width:"80",align:"center"}),o(y,{prop:"type",label:"流水类型","min-width":"120",align:"center"},{default:l(r=>[o(J,{type:L(r.row.type)},{default:l(()=>[v(g($(r.row.type)),1)]),_:2},1032,["type"])]),_:1}),o(y,{prop:"price",label:"金额","min-width":"150",align:"center"},{default:l(r=>[i("span",{class:se(H(r.row.type,r.row.price))},g(R(r.row.price)),3)]),_:1}),o(y,{prop:"userId",label:"用户ID","min-width":"120",align:"center"},{default:l(r=>[v(g(r.row.userId||"-"),1)]),_:1}),o(y,{prop:"coachId",label:"师傅ID","min-width":"120",align:"center"},{default:l(r=>[v(g(r.row.coachId||"-"),1)]),_:1}),o(y,{prop:"createTime",label:"创建时间","min-width":"180"})]),_:1},8,["data"])),[[q,w.value]])]),o(X,{page:e.pageNum,"page-size":e.pageSize,total:b.value,onHandleSizeChange:B,onHandleCurrentChange:A},null,8,["page","page-size","total"])])])}}},De=G(ye,[["__scopeId","data-v-cb347426"]]);export{De as default};
