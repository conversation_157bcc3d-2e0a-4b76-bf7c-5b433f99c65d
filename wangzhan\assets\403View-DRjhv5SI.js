import{y as d,A as e,Q as t,I as s,al as a,az as p,z as m,u,M as _}from"./vendor-DmFBDimT.js";import{l as f}from"./element-fdzwdDuf.js";import{_ as k}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const v={class:"error-container"},g={class:"error-content"},x={class:"error-image"},V={class:"error-actions"},B={__name:"403View",setup(C){const r=p(),c=()=>{r.push("/")},i=()=>{r.go(-1)};return(w,o)=>{const l=a("el-icon"),n=a("el-button");return m(),d("div",v,[e("div",g,[e("div",x,[t(l,{size:120},{default:s(()=>[t(u(f))]),_:1})]),o[2]||(o[2]=e("h1",{class:"error-title"},"403",-1)),o[3]||(o[3]=e("p",{class:"error-message"},"抱歉，您没有权限访问此页面",-1)),e("div",V,[t(n,{type:"primary",onClick:c},{default:s(()=>o[0]||(o[0]=[_("返回首页")])),_:1,__:[0]}),t(n,{onClick:i},{default:s(()=>o[1]||(o[1]=[_("返回上页")])),_:1,__:[1]})])])])}}},I=k(B,[["__scopeId","data-v-9c4045c7"]]);export{I as default};
