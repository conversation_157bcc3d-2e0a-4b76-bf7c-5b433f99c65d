import{T as b,L as v}from"./LbButton-BtU4V_Gr.js";import{_ as V}from"./index-C9Xz1oqp.js";import{E as d}from"./element-fdzwdDuf.js";import{r as f,X as x,h as C,y as k,Q as o,A as t,I as a,al as i,z as w,M as F}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const N={class:"lb-system-car-fee"},T={class:"page-main"},j={__name:"SystemCarFee",setup(B){const m=f(!1),c=f(),s=x({base_price:0,price_per_km:0,free_distance:0}),_=async()=>{try{const e=await(await fetch("/api/system/car-fee/config")).json();e.code===200&&Object.assign(s,e.data||{})}catch(n){console.error("获取配置失败:",n)}},u=async()=>{try{m.value=!0;const e=await(await fetch("/api/system/car-fee/config",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();e.code===200?d.success("配置保存成功"):d.error(e.message||"保存失败")}catch{d.error("保存失败")}finally{m.value=!1}};return C(()=>{_()}),(n,e)=>{const p=i("el-input-number"),l=i("el-form-item"),g=i("el-form"),y=i("el-card");return w(),k("div",N,[o(b),t("div",T,[o(y,{class:"config-card",shadow:"never"},{header:a(()=>e[3]||(e[3]=[t("div",{class:"card-header"},[t("span",null,"车费设置")],-1)])),default:a(()=>[o(g,{model:s,ref_key:"configFormRef",ref:c,"label-width":"140px",class:"config-form"},{default:a(()=>[o(l,{label:"起步价"},{default:a(()=>[o(p,{modelValue:s.base_price,"onUpdate:modelValue":e[0]||(e[0]=r=>s.base_price=r),min:0,precision:2},null,8,["modelValue"]),e[4]||(e[4]=t("span",{style:{"margin-left":"10px"}},"元",-1))]),_:1,__:[4]}),o(l,{label:"每公里价格"},{default:a(()=>[o(p,{modelValue:s.price_per_km,"onUpdate:modelValue":e[1]||(e[1]=r=>s.price_per_km=r),min:0,precision:2},null,8,["modelValue"]),e[5]||(e[5]=t("span",{style:{"margin-left":"10px"}},"元/公里",-1))]),_:1,__:[5]}),o(l,{label:"免费里程"},{default:a(()=>[o(p,{modelValue:s.free_distance,"onUpdate:modelValue":e[2]||(e[2]=r=>s.free_distance=r),min:0,precision:1},null,8,["modelValue"]),e[6]||(e[6]=t("span",{style:{"margin-left":"10px"}},"公里",-1))]),_:1,__:[6]}),o(l,null,{default:a(()=>[o(v,{type:"primary",onClick:u,loading:m.value},{default:a(()=>e[7]||(e[7]=[F("保存配置")])),_:1,__:[7]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])])}}},I=V(j,[["__scopeId","data-v-06ba68da"]]);export{I as default};
