import{T as O,L as y}from"./LbButton-BtU4V_Gr.js";import{_ as q}from"./index-C9Xz1oqp.js";import{E as _,q as A}from"./element-fdzwdDuf.js";import{r as v,X as B,h as D,y as H,Q as t,A as a,H as K,K as Q,I as s,al as d,z as M,M as o,O as r}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const X={class:"lb-system-upgrade"},$={class:"page-main"},F={class:"card-header"},G={class:"upgrade-actions"},J={class:"upgrade-progress"},R={class:"progress-content"},W={class:"progress-text"},Y={__name:"SystemUpgrade",setup(Z){const C=v(!1),f=v(!1),c=v(0),p=v(0),b=v(""),m=v(""),n=B({version:"v3.0.0",release_time:"2024-01-01",type:"stable",description:"系统初始版本，包含基础功能模块",has_update:!1}),u=B({version:"v3.1.0",release_time:"2024-01-15",type:"stable",size:"25.6MB",description:"新增用户管理功能，修复已知问题，优化系统性能"}),x=async()=>{try{const e=await(await fetch("/api/system/upgrade/check-update")).json();e.code===200?e.data.has_update?(n.has_update=!0,Object.assign(u,e.data.latest_version),_.success("发现新版本")):_.info("当前已是最新版本"):_.error(e.message||"检查更新失败")}catch(l){console.error("检查更新失败:",l),_.error("检查更新失败")}},U=async()=>{try{await A.confirm("升级过程中系统将暂时不可用，确定要开始升级吗？","升级确认",{confirmButtonText:"开始升级",cancelButtonText:"取消",type:"warning"}),f.value=!0,c.value=0,p.value=0,b.value="",m.value="准备下载升级包...",await z()}catch(l){l!=="cancel"&&(console.error("升级失败:",l),_.error("升级失败"))}},z=async()=>{c.value=1,m.value="正在下载升级包...";for(let l=0;l<=100;l+=10)p.value=l,await new Promise(e=>setTimeout(e,200));c.value=2,m.value="正在备份数据...",p.value=0;for(let l=0;l<=100;l+=20)p.value=l,await new Promise(e=>setTimeout(e,300));c.value=3,m.value="正在安装更新...",p.value=0;for(let l=0;l<=100;l+=15)p.value=l,await new Promise(e=>setTimeout(e,250));c.value=4,m.value="升级完成，正在重启系统...",p.value=100,b.value="success",setTimeout(()=>{f.value=!1,_.success("系统升级成功"),n.version=u.version,n.has_update=!1},2e3)},N=()=>{_.info("开始下载升级包...")},S=()=>{f.value=!1},T=l=>({stable:"success",beta:"warning",alpha:"danger"})[l]||"info",k=l=>({stable:"稳定版",beta:"测试版",alpha:"内测版"})[l]||"未知";return D(()=>{x()}),(l,e)=>{const i=d("el-descriptions-item"),h=d("el-tag"),V=d("el-descriptions"),w=d("el-card"),E=d("el-alert"),g=d("el-step"),P=d("el-steps"),L=d("el-progress"),j=d("el-dialog");return M(),H("div",X,[t(O),a("div",$,[t(w,{class:"current-version-card",shadow:"never"},{header:s(()=>[a("div",F,[e[2]||(e[2]=a("span",null,"当前版本信息",-1)),t(y,{type:"primary",onClick:x},{default:s(()=>e[1]||(e[1]=[o("检查更新")])),_:1,__:[1]})])]),default:s(()=>[t(V,{column:2,border:""},{default:s(()=>[t(i,{label:"系统版本"},{default:s(()=>[o(r(n.version),1)]),_:1}),t(i,{label:"发布时间"},{default:s(()=>[o(r(n.release_time),1)]),_:1}),t(i,{label:"版本类型"},{default:s(()=>[t(h,{type:T(n.type),size:"small"},{default:s(()=>[o(r(k(n.type)),1)]),_:1},8,["type"])]),_:1}),t(i,{label:"更新状态"},{default:s(()=>[t(h,{type:n.has_update?"warning":"success",size:"small"},{default:s(()=>[o(r(n.has_update?"有新版本":"已是最新"),1)]),_:1},8,["type"])]),_:1}),t(i,{label:"版本描述",span:"2"},{default:s(()=>[o(r(n.description),1)]),_:1})]),_:1})]),_:1}),n.has_update?(M(),K(w,{key:0,class:"upgrade-card",shadow:"never"},{header:s(()=>e[3]||(e[3]=[a("div",{class:"card-header"},[a("span",null,"版本升级")],-1)])),default:s(()=>[t(E,{title:"发现新版本",type:"warning",description:`新版本 ${u.version} 已发布，建议及时升级以获得最新功能和安全修复。`,"show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["description"]),t(V,{column:2,border:"",style:{"margin-bottom":"20px"}},{default:s(()=>[t(i,{label:"最新版本"},{default:s(()=>[o(r(u.version),1)]),_:1}),t(i,{label:"发布时间"},{default:s(()=>[o(r(u.release_time),1)]),_:1}),t(i,{label:"版本类型"},{default:s(()=>[t(h,{type:T(u.type),size:"small"},{default:s(()=>[o(r(k(u.type)),1)]),_:1},8,["type"])]),_:1}),t(i,{label:"升级大小"},{default:s(()=>[o(r(u.size),1)]),_:1}),t(i,{label:"更新内容",span:"2"},{default:s(()=>[o(r(u.description),1)]),_:1})]),_:1}),a("div",G,[t(y,{type:"primary",onClick:U,loading:C.value},{default:s(()=>e[4]||(e[4]=[o(" 立即升级 ")])),_:1,__:[4]},8,["loading"]),t(y,{onClick:N},{default:s(()=>e[5]||(e[5]=[o(" 下载升级包 ")])),_:1,__:[5]})])]),_:1})):Q("",!0),t(w,{class:"help-card",shadow:"never"},{header:s(()=>e[6]||(e[6]=[a("div",{class:"card-header"},[a("span",null,"升级说明")],-1)])),default:s(()=>[e[7]||(e[7]=a("div",{class:"help-content"},[a("h4",null,"升级前准备："),a("ol",null,[a("li",null,"备份重要数据和配置文件"),a("li",null,"确保服务器磁盘空间充足"),a("li",null,"建议在业务低峰期进行升级"),a("li",null,"通知相关用户系统维护时间")]),a("h4",null,"升级注意事项："),a("ul",null,[a("li",null,"升级过程中请勿关闭浏览器或断开网络"),a("li",null,"升级失败时系统会自动回滚到原版本"),a("li",null,"升级完成后请清除浏览器缓存"),a("li",null,"如遇问题请联系技术支持")])],-1))]),_:1,__:[7]})]),t(j,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=I=>f.value=I),title:"系统升级",width:"50%","close-on-click-modal":!1},{footer:s(()=>[t(y,{onClick:S,disabled:c.value>0},{default:s(()=>e[8]||(e[8]=[o("取消升级")])),_:1,__:[8]},8,["disabled"])]),default:s(()=>[a("div",J,[t(P,{active:c.value,"finish-status":"success"},{default:s(()=>[t(g,{title:"下载升级包"}),t(g,{title:"备份数据"}),t(g,{title:"安装更新"}),t(g,{title:"重启系统"})]),_:1},8,["active"]),a("div",R,[t(L,{percentage:p.value,status:b.value,"stroke-width":20},null,8,["percentage","status"]),a("p",W,r(m.value),1)])])]),_:1},8,["modelValue"])])}}},oe=q(Y,[["__scopeId","data-v-9b9b1881"]]);export{oe as default};
