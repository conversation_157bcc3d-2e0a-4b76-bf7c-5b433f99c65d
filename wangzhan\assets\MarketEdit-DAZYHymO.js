import{g as ie,r as v,X as H,c as A,ay as re,h as se,y as x,Q as l,A as n,I as o,V as de,al as p,az as ne,ar as ue,z as g,M as s,K as T,P as M,H as L,O as P,J as me}from"./vendor-DmFBDimT.js";import{T as pe,L as _}from"./LbButton-BtU4V_Gr.js";import{L as z}from"./LbToolTips-Bbf_ZQSC.js";import{_ as ge}from"./index-C9Xz1oqp.js";import{E as y}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const fe={class:"lb-market-edit"},ve={class:"page-main"},ye={class:"form-section"},_e={class:"form-section"},be={class:"form-section"},xe={style:{display:"flex","align-items":"center"}},Ve={class:"form-section"},we={key:0,style:{"margin-top":"16px"}},ke={style:{"margin-bottom":"8px","font-weight":"500",color:"#606266"}},Te={key:1,style:{"margin-top":"8px",color:"#909399","font-size":"14px"}},Le={class:"form-section"},Se={style:{"margin-bottom":"16px"}},ce={style:{"margin-top":"16px","text-align":"right"}},Ce={__name:"MarketEdit",setup(ze){const U=re(),j=ne(),{proxy:S}=ie(),$=v(),I=v(),D=v(!1),V=v(!1),N=v(!1),c=v([]),q=v(0),m=v([]),R=v([]),u=H({pageNum:1,pageSize:10,name:""}),w=A(()=>!!U.query.id),J=A(()=>w.value?"编辑卡券":"新增卡券"),t=H({id:null,title:"",type:0,full:"",discount:"",rule:"",text:"",sendType:0,userLimit:1,stock:0,timeLimit:0,startTime:"",endTime:"",day:1,top:0,status:1,goodsIds:[]}),K={title:[{required:!0,message:"请输入卡券名称",trigger:"blur"}],discount:[{required:!0,message:"请输入优惠金额",trigger:"blur"}],full:[{validator:(r,e,a)=>{t.type===0&&(!e||e<=0)?a(new Error("请输入消费金额")):a()},trigger:"blur"}],rule:[{required:!0,message:"请输入使用规则",trigger:"blur"}],text:[{required:!0,message:"请输入优惠详情",trigger:"blur"}],sendType:[{required:!0,message:"请选择派发方式",trigger:"change"}],stock:[{required:!0,message:"请输入卡券库存",trigger:"blur"}],timeLimit:[{required:!0,message:"请选择使用时间类型",trigger:"change"}],startTime:[{validator:(r,e,a)=>{t.timeLimit===0&&!e?a(new Error("请选择开始时间")):a()},trigger:"blur"}],endTime:[{validator:(r,e,a)=>{t.timeLimit===0&&!e?a(new Error("请选择结束时间")):a()},trigger:"blur"}],day:[{validator:(r,e,a)=>{t.timeLimit===1&&(!e||e<=0)?a(new Error("请输入有效天数")):a()},trigger:"blur"}],top:[{required:!0,message:"请输入排序值",trigger:"blur"}]},k=async r=>{r&&(u.pageNum=1),N.value=!0;try{const e={pageNum:u.pageNum,pageSize:u.pageSize};u.name&&(e.name=u.name);const a=await S.$api.service.serviceCateList(e);if(console.log("🔍 服务分类列表数据:",a),a.code===200||a.code==="200"){const d=a.data;c.value=d.list||d||[],q.value=d.totalCount||d.total||0,console.log("📊 处理后的服务分类数据:",{list:c.value,total:q.value})}else console.error("❌ 服务分类API响应错误:",a),y.error(a.message||a.msg||"获取服务分类失败")}catch(e){console.error("获取服务分类列表失败:",e),y.error("获取服务分类失败")}finally{N.value=!1}},O=async r=>{try{const e=await S.$api.market.couponInfo({id:r});if(console.log("🔍 优惠券详情数据:",e),e.code===200||e.code==="200"){const a=e.data;t.id=a.id,t.title=a.title||"",t.type=a.type||0,t.full=a.full||"",t.discount=a.discount||"",t.rule=a.rule||"",t.text=a.text||"",t.sendType=a.sendType||0,t.userLimit=a.userLimit||1,t.stock=a.stock||0,t.timeLimit=a.timeLimit||0,t.startTime=a.startTime||"",t.endTime=a.endTime||"",t.day=a.day||1,t.top=a.top||0,t.status=a.status||1,m.value=a.services||[],t.goodsIds=m.value.map(d=>d.id)}else y.error(e.message||e.msg||"获取优惠券详情失败")}catch(e){console.error("获取优惠券详情失败:",e),y.error("获取优惠券详情失败")}},Q=()=>{u.name="",k(1)},X=r=>{u.pageSize=r,k(1)},G=r=>{u.pageNum=r,k()},W=r=>{R.value=r},Z=()=>{m.value=[...R.value],t.goodsIds=m.value.map(r=>r.id),V.value=!1,y.success(`已选择 ${m.value.length} 个项目`)},h=r=>{m.value=m.value.filter(e=>e.id!==r),t.goodsIds=m.value.map(e=>e.id)},ee=async()=>{try{await $.value.validate(),D.value=!0;const r={...t,goodsIds:t.goodsIds||[]};console.log("📤 提交优惠券数据:",r);let e;w.value?e=await S.$api.market.couponUpdate(r):e=await S.$api.market.couponAdd(r),console.log("📥 优惠券提交结果:",e),e.code===200||e.code==="200"?(y.success(w.value?"更新成功":"新增成功"),j.push("/market/list")):y.error(e.message||e.msg||"操作失败")}catch(r){console.error("提交失败:",r),y.error("操作失败")}finally{D.value=!1}};return se(()=>{k(),w.value&&U.query.id&&O(U.query.id),I.value&&m.value.forEach(r=>{const e=c.value.find(a=>a.id===r.id);e&&I.value.toggleRowSelection(e,!0)})}),(r,e)=>{const a=p("el-input"),d=p("el-form-item"),f=p("el-radio"),C=p("el-radio-group"),E=p("el-input-number"),Y=p("el-date-picker"),b=p("el-table-column"),te=p("el-button"),F=p("el-table"),B=p("el-form"),le=p("el-pagination"),oe=p("el-dialog"),ae=ue("loading");return g(),x("div",fe,[l(pe,{title:J.value,isBack:!0},null,8,["title"]),n("div",ve,[l(B,{onSubmit:e[16]||(e[16]=de(()=>{},["prevent"])),model:t,rules:K,ref_key:"subFormRef",ref:$,"label-width":"140px"},{default:o(()=>[n("div",ye,[e[26]||(e[26]=n("div",{class:"form-section-title"},"基本信息",-1)),l(d,{label:"卡券名称",prop:"title"},{default:o(()=>[l(a,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=i=>t.title=i),maxlength:"20","show-word-limit":"",placeholder:"请输入卡券名称",style:{width:"300px"}},null,8,["modelValue"])]),_:1}),l(d,{label:"使用条件",prop:"type"},{default:o(()=>[l(C,{modelValue:t.type,"onUpdate:modelValue":e[1]||(e[1]=i=>t.type=i)},{default:o(()=>[l(f,{value:0},{default:o(()=>e[22]||(e[22]=[s("消费满")])),_:1,__:[22]}),l(f,{value:1},{default:o(()=>e[23]||(e[23]=[s("无门槛")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1}),l(d,{prop:"full"},{default:o(()=>[t.type===0?(g(),x(M,{key:0},[e[24]||(e[24]=s(" 消费满 ")),l(a,{modelValue:t.full,"onUpdate:modelValue":e[2]||(e[2]=i=>t.full=i),placeholder:"请输入消费金额",style:{width:"130px",margin:"0 8px"}},null,8,["modelValue"]),e[25]||(e[25]=s(" 元可用 "))],64)):(g(),x(M,{key:1},[s("立减")],64)),l(a,{modelValue:t.discount,"onUpdate:modelValue":e[3]||(e[3]=i=>t.discount=i),placeholder:"请输入优惠金额",style:{width:"130px",margin:"0 8px"}},null,8,["modelValue"]),t.type===1?(g(),x(M,{key:2},[s("元")],64)):T("",!0)]),_:1}),l(d,{label:"使用规则",prop:"rule"},{default:o(()=>[l(a,{type:"textarea",rows:4,maxlength:"1000",resize:"none","show-word-limit":"",placeholder:"请输入使用规则",modelValue:t.rule,"onUpdate:modelValue":e[4]||(e[4]=i=>t.rule=i),style:{width:"500px"}},null,8,["modelValue"])]),_:1}),l(d,{label:"优惠详情",prop:"text"},{default:o(()=>[l(a,{type:"textarea",rows:4,maxlength:"1000",resize:"none","show-word-limit":"",placeholder:"请输入优惠详情",modelValue:t.text,"onUpdate:modelValue":e[5]||(e[5]=i=>t.text=i),style:{width:"500px"}},null,8,["modelValue"])]),_:1})]),n("div",_e,[e[33]||(e[33]=n("div",{class:"form-section-title"},"派发设置",-1)),l(d,{label:"派发方式",prop:"sendType"},{default:o(()=>[l(C,{modelValue:t.sendType,"onUpdate:modelValue":e[6]||(e[6]=i=>t.sendType=i),disabled:!!t.id},{default:o(()=>[l(f,{value:0},{default:o(()=>e[27]||(e[27]=[s("活动派发")])),_:1,__:[27]}),l(f,{value:2},{default:o(()=>e[28]||(e[28]=[s("用户领取")])),_:1,__:[28]}),l(f,{value:1},{default:o(()=>e[29]||(e[29]=[s("平台定向派发")])),_:1,__:[29]})]),_:1},8,["modelValue","disabled"]),l(z,null,{default:o(()=>e[30]||(e[30]=[n("div",null,"活动派发：需要参与活动，活动参与成功之后系统自动派发到用户的卡包",-1),n("div",{style:{"margin-top":"8px"}},"平台定向派发：平台指定派发到用户的卡包",-1)])),_:1,__:[30]})]),_:1}),t.sendType==2?(g(),L(d,{key:0,label:"领取身份",prop:"userLimit"},{default:o(()=>[l(C,{modelValue:t.userLimit,"onUpdate:modelValue":e[7]||(e[7]=i=>t.userLimit=i)},{default:o(()=>[l(f,{value:1},{default:o(()=>e[31]||(e[31]=[s("不限制，任何人可领取")])),_:1,__:[31]}),l(f,{value:2},{default:o(()=>e[32]||(e[32]=[s("仅限新用户")])),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1})):T("",!0),l(d,{label:"卡券库存",prop:"stock"},{default:o(()=>[l(E,{modelValue:t.stock,"onUpdate:modelValue":e[8]||(e[8]=i=>t.stock=i),min:0,controls:!1,placeholder:"请输入卡券数量",style:{width:"200px"}},null,8,["modelValue"])]),_:1})]),n("div",be,[e[40]||(e[40]=n("div",{class:"form-section-title"},"时间设置",-1)),l(d,{label:"使用时间",prop:"timeLimit"},{default:o(()=>[l(C,{modelValue:t.timeLimit,"onUpdate:modelValue":e[9]||(e[9]=i=>t.timeLimit=i)},{default:o(()=>[l(f,{value:0},{default:o(()=>e[34]||(e[34]=[s("指定日期")])),_:1,__:[34]}),l(f,{value:1},{default:o(()=>e[35]||(e[35]=[s("有效天数")])),_:1,__:[35]})]),_:1},8,["modelValue"])]),_:1}),t.timeLimit===0?(g(),L(d,{key:0,label:"开始时间",prop:"startTime"},{default:o(()=>[l(Y,{modelValue:t.startTime,"onUpdate:modelValue":e[10]||(e[10]=i=>t.startTime=i),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"选择开始日期时间",teleported:!1,style:{width:"300px"}},null,8,["modelValue"])]),_:1})):T("",!0),t.timeLimit===0?(g(),L(d,{key:1,label:"结束时间",prop:"endTime"},{default:o(()=>[l(Y,{modelValue:t.endTime,"onUpdate:modelValue":e[11]||(e[11]=i=>t.endTime=i),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"选择结束日期时间",teleported:!1,style:{width:"300px"}},null,8,["modelValue"]),l(z,null,{default:o(()=>e[36]||(e[36]=[s("领取的卡券必须在该时间前使用")])),_:1,__:[36]})]),_:1})):T("",!0),t.timeLimit===1?(g(),L(d,{key:2,label:"有效天数",prop:"day"},{default:o(()=>[n("div",xe,[e[38]||(e[38]=n("span",{style:{"margin-right":"8px"}},"自领券当日起",-1)),l(E,{modelValue:t.day,"onUpdate:modelValue":e[12]||(e[12]=i=>t.day=i),controls:!1,min:1,precision:0,style:{width:"120px",margin:"0 8px"}},null,8,["modelValue"]),e[39]||(e[39]=n("span",{style:{"margin-right":"8px"}},"天内可用",-1)),l(z,null,{default:o(()=>e[37]||(e[37]=[s(" 有效期按自然天计算。 "),n("div",{style:{margin:"8px 0"}}," 举例：如设置领券当日起30天内可用，用户在5月18日14:00时领取卡券，则该卡券的可用时间为5月18日的14:00:00至6月18日的14:00 ",-1),s(" 注意：时间按自然天来算，不是月 ")])),_:1,__:[37]})])]),_:1})):T("",!0)]),n("div",Ve,[e[44]||(e[44]=n("div",{class:"form-section-title"},"其他设置",-1)),l(d,{label:"排序值",prop:"top"},{default:o(()=>[l(E,{modelValue:t.top,"onUpdate:modelValue":e[13]||(e[13]=i=>t.top=i),min:0,controls:!1,placeholder:"请输入排序值",style:{width:"200px"}},null,8,["modelValue"]),l(z,null,{default:o(()=>e[41]||(e[41]=[s("值越大, 排序越靠前")])),_:1,__:[41]})]),_:1}),l(d,{label:"限用项目",prop:"goodsIds"},{default:o(()=>[l(_,{type:"primary",onClick:e[14]||(e[14]=i=>V.value=!0)},{default:o(()=>e[42]||(e[42]=[s(" 选择项目 ")])),_:1,__:[42]}),m.value.length>0?(g(),x("div",we,[n("div",ke," 已选择项目 ("+P(m.value.length)+"个): ",1),l(F,{data:m.value,size:"small",border:"",style:{width:"100%"},"max-height":"200",class:"selected-services-table"},{default:o(()=>[l(b,{prop:"id",label:"项目ID",width:"100",align:"center"}),l(b,{prop:"name",label:"项目名称","min-width":"150"}),l(b,{label:"操作",width:"80",align:"center"},{default:o(i=>[l(te,{type:"danger",size:"small",onClick:Ue=>h(i.row.id),link:""},{default:o(()=>e[43]||(e[43]=[s(" 移除 ")])),_:2,__:[43]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):(g(),x("div",Te," 暂未选择任何项目 "))]),_:1})]),n("div",Le,[l(d,null,{default:o(()=>[l(_,{type:"primary",onClick:ee,loading:D.value,size:"default",style:{width:"120px"}},{default:o(()=>[s(P(w.value?"更新卡券":"新增卡券"),1)]),_:1},8,["loading"]),l(_,{onClick:e[15]||(e[15]=i=>r.$router.go(-1)),style:{"margin-left":"12px",width:"80px"},size:"default"},{default:o(()=>e[45]||(e[45]=[s(" 取消 ")])),_:1,__:[45]})]),_:1})])]),_:1},8,["model"])]),l(oe,{modelValue:V.value,"onUpdate:modelValue":e[21]||(e[21]=i=>V.value=i),title:"选择限用项目",width:"70%"},{footer:o(()=>[l(_,{onClick:e[20]||(e[20]=i=>V.value=!1)},{default:o(()=>e[48]||(e[48]=[s("取消")])),_:1,__:[48]}),l(_,{type:"primary",onClick:Z},{default:o(()=>e[49]||(e[49]=[s("确定")])),_:1,__:[49]})]),default:o(()=>[n("div",Se,[l(B,{inline:!0},{default:o(()=>[l(d,{label:"项目名称"},{default:o(()=>[l(a,{modelValue:u.name,"onUpdate:modelValue":e[17]||(e[17]=i=>u.name=i),placeholder:"请输入项目名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(d,null,{default:o(()=>[l(_,{type:"primary",onClick:k},{default:o(()=>e[46]||(e[46]=[s("搜索")])),_:1,__:[46]}),l(_,{onClick:Q},{default:o(()=>e[47]||(e[47]=[s("重置")])),_:1,__:[47]})]),_:1})]),_:1})]),me((g(),L(F,{data:c.value,onSelectionChange:W,ref_key:"serviceTableRef",ref:I,"max-height":"400"},{default:o(()=>[l(b,{type:"selection",width:"55"}),l(b,{prop:"id",label:"项目ID",width:"100",align:"center"}),l(b,{prop:"name",label:"项目名称","min-width":"200"})]),_:1},8,["data"])),[[ae,N.value]]),n("div",ce,[l(le,{"current-page":u.pageNum,"onUpdate:currentPage":e[18]||(e[18]=i=>u.pageNum=i),"page-size":u.pageSize,"onUpdate:pageSize":e[19]||(e[19]=i=>u.pageSize=i),"page-sizes":[5,10,20,50],total:q.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:G,"pager-count":5,"prev-text":"上一页","next-text":"下一页",small:!1,background:""},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"])])}}},$e=ge(Ce,[["__scopeId","data-v-229dc9a4"]]);export{$e as default};
