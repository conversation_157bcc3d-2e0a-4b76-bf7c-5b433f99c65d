/**
 * 用户管理模块 - V2版本
 * 按照API封装规范文档实现用户管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取用户列表
   * @param {Object} querys 查询参数
   * @param {string} querys.nickName 用户昵称（支持模糊查询）
   * @param {string} querys.beginTime 加入时间---开始日期（与结束时间一起传  要传都传要不传都不传）
   * @param {string} querys.endTime 加入时间---结束日期（与结束时间一起传  要传都传要不传都不传）
   * @param {number} querys.status 状态（0查所有  1正常用户  2黑名单用户）
   * @param {number} querys.sortOrderCount 根据订单总量排序（-1不根据此项排序   0降序  1升序）
   * @param {number} querys.pageNum 页码
   * @param {number} querys.pageSize 数量
   * @returns {Promise} 返回用户列表数据
   */
  userList(querys) {
    console.log('👥 用户列表API-V2请求参数:', querys)
    return get('/api/admin/user/list', querys)
  },

  /**
   * 获取用户详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 用户ID
   * @returns {Promise} 返回用户详情数据
   */
  userInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('用户ID不能为空'))
    }
    console.log('🔍 获取用户详情API-V2请求:', querys)
    return get(`/api/admin/user/info/${querys.id}`)
  },

  /**
   * 新增用户
   * @param {Object} querys 用户数据
   * @param {string} querys.nickname 用户昵称
   * @param {string} querys.phone 手机号
   * @param {string} querys.avatar 头像URL
   * @param {number} querys.gender 性别，1男，2女，0未知
   * @param {string} querys.birthday 生日，格式：YYYY-MM-DD
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  userAdd(querys) {
    if (!querys || !querys.nickname || !querys.phone) {
      return Promise.reject(new Error('用户昵称和手机号不能为空'))
    }

    const apiData = {
      nickname: querys.nickname,
      phone: querys.phone,
      avatar: querys.avatar || '',
      gender: querys.gender || 0,
      birthday: querys.birthday || '',
      status: querys.status || 1
    }

    console.log('➕ 新增用户API-V2请求数据:', apiData)
    return post('/api/admin/user/add', apiData)
  },

  /**
   * 编辑用户
   * @param {Object} querys 编辑数据
   * @param {number} querys.id 用户id主键
   * @param {string} querys.nickName 用户昵称
   * @param {string} querys.avatarUrl 用户上传的头像url
   * @returns {Promise} 返回编辑结果
   */
  userUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('用户ID不能为空'))
    }
    if (!querys.nickName) {
      return Promise.reject(new Error('用户昵称不能为空'))
    }

    const apiData = {
      id: querys.id,
      nickName: querys.nickName,
      avatarUrl: querys.avatarUrl || ''
    }

    console.log('✏️ 编辑用户API-V2请求:', apiData)
    return post('/api/admin/user/update', apiData)
  },

  /**
   * 加入/移除黑名单
   * @param {Object} querys 黑名单操作参数
   * @param {number} querys.id 用户id
   * @param {string} querys.text 审核备注（此操作原因）
   * @param {number} querys.status 操作状态（0移除黑名单 1加入黑名单）
   * @returns {Promise} 返回操作结果
   */
  userAddBlack(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('用户ID不能为空'))
    }
    if (!querys.text) {
      return Promise.reject(new Error('审核备注不能为空'))
    }
    if (![0, 1].includes(querys.status)) {
      return Promise.reject(new Error('操作状态无效'))
    }

    const apiData = {
      id: querys.id,
      text: querys.text,
      status: querys.status
    }

    console.log('🚫 加入/移除黑名单API-V2请求:', apiData)
    return post('/api/admin/user/addBlack', apiData)
  },

  /**
   * 用户状态变更
   * @param {Object} querys 状态变更参数
   * @returns {Promise} 返回变更结果
   */
  userStatus(querys) {
    console.log('🔄 用户状态变更API-V2请求:', querys)
    return post('/api/admin/user/status', querys)
  },

  /**
   * 操作日志查询
   * @param {Object} querys 查询参数
   * @param {string} querys.nickName 昵称（可选）
   * @param {number} querys.status 状态（可选）（0查全部 -1删除 1解除黑名单 2拉入黑名单 3修改操作）
   * @param {string} querys.pageNum 页码（可选）
   * @param {string} querys.pageSize 数量（可选）
   * @returns {Promise} 返回日志列表
   */
  userLog(querys) {
    console.log('📋 用户操作日志API-V2请求参数:', querys)
    return get('/api/admin/user/log', querys)
  },

  /**
   * 删除用户
   * @param {Object} querys 删除参数
   * @param {number} querys.id 用户ID
   * @returns {Promise} 返回删除结果
   */
  userDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('🗑️ 删除用户API-V2请求:', querys)
    return post(`/api/admin/user/delete/${querys.id}`)
  },



  /**
   * 重置用户密码
   * @param {Object} querys 重置参数
   * @param {number} querys.id 用户ID
   * @param {string} querys.newPassword 新密码
   * @returns {Promise} 返回重置结果
   */
  userResetPassword(querys) {
    if (!querys || !querys.id || !querys.newPassword) {
      return Promise.reject(new Error('用户ID和新密码不能为空'))
    }

    console.log('🔑 重置用户密码API-V2请求:', { id: querys.id })
    return post(`/api/admin/user/resetPassword/${querys.id}`, { newPassword: querys.newPassword })
  },

  /**
   * 获取用户订单列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID
   * @param {number} querys.status 订单状态，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回用户订单列表
   */
  userOrderList(querys) {
    if (!querys || !querys.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('📋 用户订单列表API-V2请求参数:', querys)
    return get('/api/admin/user/orders', querys)
  },

  /**
   * 获取用户消费统计
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @returns {Promise} 返回用户消费统计
   */
  userConsumptionStats(querys) {
    if (!querys || !querys.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('📊 用户消费统计API-V2请求参数:', querys)
    return get('/api/admin/user/consumption/stats', querys)
  },

  /**
   * 获取用户余额记录
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID
   * @param {number} querys.type 记录类型，1充值，2消费，3退款
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回用户余额记录
   */
  userBalanceRecord(querys) {
    if (!querys || !querys.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('💰 用户余额记录API-V2请求参数:', querys)
    return get('/api/admin/user/balance/record', querys)
  },

  /**
   * 获取用户积分记录
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID
   * @param {number} querys.type 记录类型，1获得，2消费
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回用户积分记录
   */
  userPointRecord(querys) {
    if (!querys || !querys.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('⭐ 用户积分记录API-V2请求参数:', querys)
    return get('/api/admin/user/point/record', querys)
  },

  /**
   * 手动调整用户积分
   * @param {Object} querys 调整参数
   * @param {number} querys.userId 用户ID
   * @param {number} querys.points 积分数量（正数为增加，负数为扣除）
   * @param {string} querys.reason 调整原因
   * @returns {Promise} 返回调整结果
   */
  userPointAdjust(querys) {
    if (!querys || !querys.userId || querys.points === undefined) {
      return Promise.reject(new Error('用户ID和积分数量不能为空'))
    }

    console.log('⭐ 调整用户积分API-V2请求:', querys)
    return post('/api/admin/user/point/adjust', querys)
  },

  /**
   * 导出用户列表
   * @param {Object} querys 导出参数
   * @param {number} querys.status 用户状态，非必填
   * @param {string} querys.registerStartTime 注册开始时间，非必填
   * @param {string} querys.registerEndTime 注册结束时间，非必填
   * @returns {Promise} 返回导出结果
   */
  userExport(querys) {
    console.log('📤 导出用户列表API-V2请求参数:', querys)
    return post('/api/admin/user/export', querys)
  }
}
