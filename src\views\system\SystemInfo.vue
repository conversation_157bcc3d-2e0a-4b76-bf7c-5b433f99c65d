<!--
  隐私协议配置页面
  支持8种不同类型的协议配置，使用富文本编辑器
-->

<template>
  <div class="lb-system-info">
    <TopNav />
    <div class="page-main">
      <!-- 用户隐私协议 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>用户隐私协议</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.loginProtocol"
            :height="'90vh'"
            placeholder="请输入用户隐私协议内容..."
          />
        </div>
      </el-card>

      <!-- 个人信息保护指引 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>个人信息保护指引</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.informationProtection"
            :height="'90vh'"
            placeholder="请输入个人信息保护指引内容..."
          />
        </div>
      </el-card>

      <!-- 师傅端隐私协议 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>师傅端隐私协议</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.content"
            :height="'90vh'"
            placeholder="请输入师傅端隐私协议内容..."
          />
        </div>
      </el-card>

      <!-- 师傅端服务协议 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>师傅端服务协议</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.shifuServiceAgreement"
            :height="'90vh'"
            placeholder="请输入师傅端服务协议内容..."
          />
        </div>
      </el-card>

      <!-- 师傅端安全协议 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>师傅端安全协议</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.shifuSecurityProtocol"
            :height="'90vh'"
            placeholder="请输入师傅端安全协议内容..."
          />
        </div>
      </el-card>

      <!-- 服务质量保障承诺 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>服务质量保障承诺</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.shifuQualityCommitment"
            :height="'90vh'"
            placeholder="请输入服务质量保障承诺内容..."
          />
        </div>
      </el-card>

      <!-- 用户须知 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>用户须知</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.userNotice"
            :height="'90vh'"
            placeholder="请输入用户须知内容..."
          />
        </div>
      </el-card>

      <!-- 师傅须知 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>师傅须知</span>
          </div>
        </template>

        <div class="editor-container">
          <LbUeditor
            v-model="configForm.masterNotice"
            :height="'90vh'"
            placeholder="请输入师傅须知内容..."
          />
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <el-card class="config-card" shadow="never">
        <div class="action-buttons">
          <LbButton type="primary" @click="saveConfig" :loading="saveLoading" size="large">
            <el-icon><DocumentAdd /></el-icon>
            保存所有配置
          </LbButton>
          <LbButton @click="resetConfig" size="large" style="margin-left: 15px;">
            <el-icon><RefreshLeft /></el-icon>
            重置配置
          </LbButton>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentAdd, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbUeditor from '@/components/common/LbUeditor.vue'
import { api } from '@/api-v2'

// 响应式数据
const saveLoading = ref(false)

// 配置表单数据
const configForm = reactive({
  loginProtocol: '',              // 用户隐私协议
  informationProtection: '',     // 个人信息保护指引
  content: '',                   // 师傅端隐私协议
  shifuServiceAgreement: '',     // 师傅端服务协议
  shifuSecurityProtocol: '',     // 师傅端安全协议
  shifuQualityCommitment: '',    // 服务质量保障承诺
  userNotice: '',                // 用户须知
  masterNotice: ''               // 师傅须知
})

// 获取配置数据
const getConfig = async () => {
  try {
    console.log('📄 开始获取隐私协议配置...')
    const result = await api.sys.getPrivacyAgreement()

    if (result.code === '200') {
      // 更新表单数据
      Object.assign(configForm, result.data || {})
      console.log('✅ 隐私协议配置获取成功')
    } else {
      ElMessage.error(result.message || '获取配置失败')
    }
  } catch (error) {
    console.error('❌ 获取隐私协议配置失败:', error)
    ElMessage.error('获取配置失败，请稍后重试')
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    // 确认保存操作
    await ElMessageBox.confirm(
      '确定要保存所有隐私协议配置吗？',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    saveLoading.value = true
    console.log('💾 开始保存隐私协议配置...')

    const result = await api.sys.updatePrivacyAgreement(configForm)

    if (result.code === '200') {
      ElMessage.success('隐私协议配置保存成功')
      console.log('✅ 隐私协议配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 保存隐私协议配置失败:', error)
      ElMessage.error('保存失败，请稍后重试')
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置配置
const resetConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置吗？此操作将清空所有内容！',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重置表单数据
    Object.assign(configForm, {
      loginProtocol: '',
      informationProtection: '',
      content: '',
      shifuServiceAgreement: '',
      shifuSecurityProtocol: '',
      shifuQualityCommitment: '',
      userNotice: '',
      masterNotice: ''
    })

    ElMessage.success('配置已重置')
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-info {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-main {
  /* max-width: 1200px; */
  /* margin: 0 auto; */
}

.config-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.editor-container {
  padding: 16px 0;
  width: 100%;
  height: 90vh;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  gap: 16px;
}

/* 富文本编辑器样式优化 */
:deep(.lb-ueditor) {
  border-radius: 6px;
  overflow: hidden;
  width: 100%;
  height: 90vh;
}

:deep(.ql-toolbar) {
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.ql-container) {
  font-size: 16px;
  line-height: 1.6;
  width: 100%;
  height: calc(90vh - 60px); /* 减去工具栏高度 */
}

:deep(.ql-editor) {
  min-height: calc(90vh - 100px); /* 减去工具栏和边距 */
  height: calc(90vh - 100px);
  padding: 16px;
  color: #606266;
  width: 100%;
  box-sizing: border-box;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

/* 卡片标题样式 */
:deep(.el-card__header) {
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: white;
  border-bottom: none;
}

:deep(.el-card__header .card-header span) {
  color: rgb(0, 0, 0);
  font-weight: 600;
}

/* 按钮样式优化 */
.action-buttons .lb-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-buttons .lb-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lb-system-info {
    padding: 12px;
  }

  .page-main {
    max-width: 100%;
  }

  .config-card {
    margin-bottom: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons .lb-button {
    width: 100%;
    max-width: 300px;
  }

  .editor-container {
    height: 80vh;
  }

  :deep(.lb-ueditor) {
    height: 80vh;
  }

  :deep(.ql-container) {
    height: calc(80vh - 60px);
  }

  :deep(.ql-editor) {
    min-height: calc(80vh - 100px);
    height: calc(80vh - 100px);
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .lb-system-info {
    padding: 8px;
  }

  .card-header {
    font-size: 14px;
  }

  :deep(.ql-toolbar) {
    padding: 8px;
  }

  .editor-container {
    height: 70vh;
  }

  :deep(.lb-ueditor) {
    height: 70vh;
  }

  :deep(.ql-container) {
    height: calc(70vh - 60px);
  }

  :deep(.ql-editor) {
    min-height: calc(70vh - 100px);
    height: calc(70vh - 100px);
    padding: 10px;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 滚动条样式 */
:deep(.ql-editor)::-webkit-scrollbar {
  width: 6px;
}

:deep(.ql-editor)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.ql-editor)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.ql-editor)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>