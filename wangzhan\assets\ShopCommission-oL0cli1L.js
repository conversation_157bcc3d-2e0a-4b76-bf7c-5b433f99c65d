import{E as k,M as Q,N as X}from"./element-fdzwdDuf.js";import{T as q,L as w}from"./LbButton-BtU4V_Gr.js";import{L as G}from"./LbPage-DnbiQ0Ct.js";import{_ as W}from"./index-C9Xz1oqp.js";import{g as Y,r as _,X as Z,c as x,h as ee,y as P,Q as e,A as o,I as t,al as r,J as te,ar as ae,H as le,z as C,M as u,u as L,O as d,K as oe}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const se={class:"shop-commission"},ne={class:"content-container"},de={class:"search-form-container"},ie={class:"stats-container"},re={class:"stat-card"},ue={class:"stat-value"},ce={class:"stat-card"},pe={class:"stat-value"},_e={class:"stat-card"},fe={class:"stat-value"},me={class:"stat-card"},ve={class:"stat-value"},ge={class:"table-container"},Ie={style:{"font-family":"monospace","font-size":"12px"}},he={style:{color:"#409eff","font-weight":"bold"}},be={style:{color:"#67c23a","font-weight":"bold"}},ye={class:"pagination-container"},we={key:0,class:"detail-content"},De={style:{"font-family":"monospace"}},xe={style:{color:"#409eff","font-weight":"bold"}},Ce={style:{color:"#67c23a","font-weight":"bold"}},Ve={class:"dialog-footer"},ze={__name:"ShopCommission",setup(Ne){const{proxy:T}=Y(),I=_(!1),h=_([]),b=_(0),f=_(!1),i=_(null),V=_(),l=Z({userId:"",topId:"",orderId:"",transactionId:"",pageNum:1,pageSize:10}),D=x(()=>h.value.reduce((n,a)=>n+a.payPrice,0)),z=x(()=>h.value.reduce((n,a)=>n+a.cash,0)),B=x(()=>D.value===0?"0.00":(z.value/D.value*100).toFixed(2)),m=async()=>{try{I.value=!0,console.log("🔍 获取佣金列表，参数:",l);const n={pageNum:l.pageNum,pageSize:l.pageSize};l.userId&&(n.userId=l.userId),l.topId&&(n.topId=l.topId),l.orderId&&(n.orderId=l.orderId),l.transactionId&&(n.transactionId=l.transactionId);const a=await T.$api.shop.commissionList(n);console.log("💰 佣金列表响应:",a),a.code==="200"?(h.value=a.data.list||[],b.value=a.data.totalCount||0,console.log(`✅ 佣金列表加载成功，共 ${b.value} 条数据`)):k.error(a.msg||"获取佣金列表失败")}catch(n){console.error("❌ 获取佣金列表失败:",n),k.error("获取佣金列表失败")}finally{I.value=!1}},U=()=>{l.pageNum=1,m()},M=()=>{V.value?.resetFields(),Object.assign(l,{userId:"",topId:"",orderId:"",transactionId:"",pageNum:1,pageSize:10}),m()},R=n=>{l.pageSize=n,l.pageNum=1,m()},$=n=>{l.pageNum=n,m()},E=n=>{i.value=n,f.value=!0},O=()=>{f.value=!1,i.value=null};return ee(()=>{console.log("🚀 分销佣金页面初始化"),m()}),(n,a)=>{const y=r("el-input"),v=r("el-form-item"),N=r("el-icon"),g=r("el-col"),F=r("el-row"),j=r("el-form"),c=r("el-table-column"),S=r("el-tag"),A=r("el-table"),p=r("el-descriptions-item"),H=r("el-descriptions"),J=r("el-dialog"),K=ae("loading");return C(),P("div",se,[e(q,{title:"分销佣金"}),o("div",ne,[o("div",de,[e(j,{ref_key:"searchFormRef",ref:V,model:l,inline:!0,class:"search-form"},{default:t(()=>[e(F,{gutter:20},{default:t(()=>[e(g,{span:24},{default:t(()=>[e(v,{label:"用户ID",prop:"userId"},{default:t(()=>[e(y,{size:"default",modelValue:l.userId,"onUpdate:modelValue":a[0]||(a[0]=s=>l.userId=s),placeholder:"请输入用户ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(v,{label:"上级ID",prop:"topId"},{default:t(()=>[e(y,{size:"default",modelValue:l.topId,"onUpdate:modelValue":a[1]||(a[1]=s=>l.topId=s),placeholder:"请输入上级ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(v,{label:"订单ID",prop:"orderId"},{default:t(()=>[e(y,{size:"default",modelValue:l.orderId,"onUpdate:modelValue":a[2]||(a[2]=s=>l.orderId=s),placeholder:"请输入订单ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(v,{label:"交易单号",prop:"transactionId"},{default:t(()=>[e(y,{size:"default",modelValue:l.transactionId,"onUpdate:modelValue":a[3]||(a[3]=s=>l.transactionId=s),placeholder:"请输入交易单号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(w,{type:"primary",onClick:U,loading:I.value},{default:t(()=>[e(N,null,{default:t(()=>[e(L(Q))]),_:1}),a[6]||(a[6]=u(" 搜索 "))]),_:1,__:[6]},8,["loading"]),e(w,{onClick:M},{default:t(()=>[e(N,null,{default:t(()=>[e(L(X))]),_:1}),a[7]||(a[7]=u(" 重置 "))]),_:1,__:[7]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),o("div",ie,[e(F,{gutter:20},{default:t(()=>[e(g,{span:6},{default:t(()=>[o("div",re,[a[8]||(a[8]=o("div",{class:"stat-title"},"总佣金记录",-1)),o("div",ue,d(b.value),1)])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",ce,[a[9]||(a[9]=o("div",{class:"stat-title"},"总支付金额",-1)),o("div",pe,"¥"+d(D.value.toFixed(2)),1)])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",_e,[a[10]||(a[10]=o("div",{class:"stat-title"},"总佣金金额",-1)),o("div",fe,"¥"+d(z.value.toFixed(2)),1)])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",me,[a[11]||(a[11]=o("div",{class:"stat-title"},"平均佣金率",-1)),o("div",ve,d(B.value)+"%",1)])]),_:1})]),_:1})]),o("div",ge,[te((C(),le(A,{data:h.value,stripe:"",border:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[e(c,{prop:"id",label:"ID",width:"80",align:"center"}),e(c,{prop:"userId",label:"用户ID",width:"100",align:"center"}),e(c,{prop:"topId",label:"上级ID",width:"100",align:"center"}),e(c,{prop:"orderId",label:"订单ID",width:"100",align:"center"}),e(c,{prop:"transactionId",label:"交易单号",width:"250",align:"center"},{default:t(({row:s})=>[o("span",Ie,d(s.transactionId),1)]),_:1}),e(c,{label:"支付金额",width:"120",align:"center"},{default:t(({row:s})=>[o("span",he,"¥"+d(s.payPrice.toFixed(2)),1)]),_:1}),e(c,{label:"佣金金额",width:"120",align:"center"},{default:t(({row:s})=>[o("span",be,"¥"+d(s.cash.toFixed(2)),1)]),_:1}),e(c,{label:"佣金率",width:"100",align:"center"},{default:t(({row:s})=>[e(S,{type:"info"},{default:t(()=>[u(d((s.cash/s.payPrice*100).toFixed(2))+"%",1)]),_:2},1024)]),_:1}),e(c,{prop:"createTime",label:"创建时间",width:"180",align:"center"}),e(c,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(({row:s})=>[e(w,{type:"primary",size:"small",onClick:Fe=>E(s)},{default:t(()=>a[12]||(a[12]=[u(" 详情 ")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[K,I.value]]),o("div",ye,[e(G,{"current-page":l.pageNum,"page-size":l.pageSize,total:b.value,onSizeChange:R,onCurrentChange:$},null,8,["current-page","page-size","total"])])])]),e(J,{modelValue:f.value,"onUpdate:modelValue":a[5]||(a[5]=s=>f.value=s),title:"佣金详情",width:"600px","before-close":O},{footer:t(()=>[o("span",Ve,[e(w,{onClick:a[4]||(a[4]=s=>f.value=!1)},{default:t(()=>a[13]||(a[13]=[u("关闭")])),_:1,__:[13]})])]),default:t(()=>[i.value?(C(),P("div",we,[e(H,{column:2,border:""},{default:t(()=>[e(p,{label:"佣金ID"},{default:t(()=>[u(d(i.value.id),1)]),_:1}),e(p,{label:"用户ID"},{default:t(()=>[u(d(i.value.userId),1)]),_:1}),e(p,{label:"上级ID"},{default:t(()=>[u(d(i.value.topId),1)]),_:1}),e(p,{label:"订单ID"},{default:t(()=>[u(d(i.value.orderId),1)]),_:1}),e(p,{label:"交易单号",span:2},{default:t(()=>[o("span",De,d(i.value.transactionId),1)]),_:1}),e(p,{label:"支付金额"},{default:t(()=>[o("span",xe,"¥"+d(i.value.payPrice.toFixed(2)),1)]),_:1}),e(p,{label:"佣金金额"},{default:t(()=>[o("span",Ce,"¥"+d(i.value.cash.toFixed(2)),1)]),_:1}),e(p,{label:"佣金率"},{default:t(()=>[e(S,{type:"info"},{default:t(()=>[u(d((i.value.cash/i.value.payPrice*100).toFixed(2))+"%",1)]),_:1})]),_:1}),e(p,{label:"创建时间"},{default:t(()=>[u(d(i.value.createTime),1)]),_:1})]),_:1})])):oe("",!0)]),_:1},8,["modelValue"])])}}},Ue=W(ze,[["__scopeId","data-v-4feb6121"]]);export{Ue as default};
