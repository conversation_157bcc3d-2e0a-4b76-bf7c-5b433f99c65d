import{E as d,x as xe,q as he}from"./element-fdzwdDuf.js";import{T as Ce,L as f}from"./LbButton-BtU4V_Gr.js";import{L as Ue}from"./LbPage-DnbiQ0Ct.js";import{_ as we}from"./index-C9Xz1oqp.js";import{g as Ve,r as u,X as E,h as Ne,y as J,Q as a,A as g,I as t,al as i,J as ze,ar as Te,H as N,z as x,M as p,O as z,K as A,u as Fe}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const Me={class:"page-container"},Se={class:"content-container"},De={class:"search-form-container"},$e={class:"table-container"},Be={class:"table-operate-buttons"},Le={key:0,class:"upload-progress"},Re={class:"dialog-footer"},Ee={class:"dialog-footer"},Ae={__name:"UserList",setup(Oe){const{proxy:U}=Ve(),T=u(!1),S=u(!1),D=u(!1),O=u([]),$=u(0),b=u([]),v=u([]),m=u(0),F=u(!1),s=E({pageNum:1,pageSize:10,nickName:"",status:0,beginTime:"",endTime:"",sortOrderCount:-1}),Y=u(null),w=u(!1),B=u(null),r=E({id:null,nickName:"",avatarUrl:""}),K={nickName:[{required:!0,message:"请输入用户昵称",trigger:"blur"},{min:1,max:50,message:"昵称长度在 1 到 50 个字符",trigger:"blur"}]},V=u(!1),I=u(null),k=u(1),M=u(null),h=E({text:""}),Q={text:[{required:!0,message:"请输入操作原因",trigger:"blur"},{min:1,max:200,message:"原因长度在 1 到 200 个字符",trigger:"blur"}]},W=l=>({1:"正常用户",2:"黑名单用户"})[l]||"未知",X=l=>({1:"success",2:"danger"})[l]||"info",G=()=>{console.log("头像加载失败")},_=async()=>{try{T.value=!0,console.log("🔍 开始加载用户列表，参数:",s),b.value&&b.value.length===2?(s.beginTime=b.value[0],s.endTime=b.value[1]):(s.beginTime="",s.endTime="");const l=await U.$api.user.userList(s);console.log("📋 用户列表响应:",l),l.code==="200"?(O.value=l.data.list||[],$.value=l.data.totalCount||0,console.log(`✅ 用户列表加载成功，共 ${$.value} 条数据`)):d.error(l.msg||"获取用户列表失败")}catch(l){console.error("❌ 加载用户列表失败:",l),d.error("获取用户列表失败")}finally{T.value=!1}},Z=()=>{s.pageNum=1,_()},ee=()=>{Y.value?.resetFields(),b.value=[],Object.assign(s,{pageNum:1,pageSize:10,nickName:"",status:0,beginTime:"",endTime:"",sortOrderCount:-1}),_()},le=l=>{s.pageSize=l,s.pageNum=1,_()},ae=l=>{s.pageNum=l,_()},te=l=>{M.value=l,r.id=l.id,r.nickName=l.nickName,r.avatarUrl=l.avatarUrl||"",l.avatarUrl?v.value=[{name:"avatar",url:l.avatarUrl}]:v.value=[],w.value=!0},oe=async()=>{try{await B.value?.validate(),S.value=!0,console.log("✏️ 提交编辑用户:",r);const l=await U.$api.user.userUpdate(r);console.log("📝 编辑用户响应:",l),l.code==="200"?(d.success("编辑用户成功"),w.value=!1,_()):d.error(l.msg||"编辑用户失败")}catch(l){console.error("❌ 编辑用户失败:",l),d.error("编辑用户失败")}finally{S.value=!1}},q=(l,e)=>{M.value=l,k.value=e,h.text="",V.value=!0},se=async()=>{try{await I.value?.validate(),D.value=!0;const l={id:M.value.id,text:h.text,status:k.value};console.log("🚫 提交黑名单操作:",l);const e=await U.$api.user.userAddBlack(l);if(console.log("📝 黑名单操作响应:",e),e.code==="200"){const n=k.value===1?"加入黑名单":"移除黑名单";d.success(`${n}成功`),V.value=!1,_()}else d.error(e.msg||"操作失败")}catch(l){console.error("❌ 黑名单操作失败:",l),d.error("操作失败")}finally{D.value=!1}},ne=async l=>{try{await he.confirm(`确定要删除用户"${l.nickName}"吗？删除后无法恢复！`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),console.log("🗑️ 开始删除用户:",l);const e={id:l.id,status:-1};console.log("🗑️ 删除用户参数:",e);const n=await U.$api.user.userStatus(e);console.log("📝 删除用户响应:",n),n.code==="200"?(d.success("删除用户成功"),_()):d.error(n.msg||"删除用户失败")}catch(e){if(e==="cancel"){console.log("👤 用户取消删除操作");return}console.error("❌ 删除用户失败:",e),d.error("删除用户失败")}},re=l=>(console.log("📋 头像上传前验证:",l),l.type.indexOf("image/")===0?l.size/1024/1024<2?(console.log("✅ 头像验证通过"),!0):(d.error("上传头像图片大小不能超过 2MB!"),!1):(d.error("只能上传图片文件!"),!1)),ue=async(l,e)=>{console.log("🖼️ 头像文件变更:",l,e),e.length>1&&(v.value=[l]),l.status==="ready"&&!F.value&&await de(l)},ie=l=>{console.log("🗑️ 移除头像:",l),r.avatarUrl="",m.value=0,v.value=[]},de=async l=>{console.log("📤 开始上传头像:",l);try{F.value=!0,m.value=0;const e=new FormData;e.append("multipartFile",l.raw),console.log("📦 FormData创建完成:",e);const n=await U.$api.upload.uploadFile(e,c=>{m.value=Math.round(c.loaded*100/c.total),console.log("📊 上传进度:",m.value+"%")});if(console.log("✅ 头像上传成功:",n),n.code===200||n.code==="200")r.avatarUrl=n.data.url||n.data.fileUrl||n.data,d.success("头像上传成功"),v.value=[{name:l.name,url:r.avatarUrl,status:"success"}],console.log("💾 头像URL已保存到表单:",r.avatarUrl);else throw new Error(n.message||n.msg||"上传失败")}catch(e){console.error("❌ 头像上传失败:",e),d.error("头像上传失败: "+(e.message||"未知错误")),v.value=[],r.avatarUrl=""}finally{F.value=!1,m.value=0}},ce=()=>{B.value?.resetFields(),r.id=null,r.nickName="",r.avatarUrl="",v.value=[],m.value=0,F.value=!1};return Ne(()=>{console.log("🚀 用户管理页面初始化"),_()}),(l,e)=>{const n=i("el-input"),c=i("el-form-item"),C=i("el-option"),H=i("el-select"),pe=i("el-date-picker"),me=i("el-col"),fe=i("el-row"),L=i("el-form"),y=i("el-table-column"),j=i("el-avatar"),ge=i("el-tag"),ve=i("el-table"),_e=i("el-icon"),be=i("el-upload"),ke=i("el-progress"),P=i("el-dialog"),ye=Te("loading");return x(),J("div",Me,[a(Ce,{title:"用户管理"}),g("div",Se,[g("div",De,[a(L,{ref_key:"searchFormRef",ref:Y,model:s,inline:!0,class:"search-form"},{default:t(()=>[a(fe,{gutter:20},{default:t(()=>[a(me,{span:24},{default:t(()=>[a(c,{label:"用户昵称",prop:"nickName"},{default:t(()=>[a(n,{size:"default",modelValue:s.nickName,"onUpdate:modelValue":e[0]||(e[0]=o=>s.nickName=o),placeholder:"请输入用户昵称，支持模糊查询",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(c,{label:"用户状态",prop:"status"},{default:t(()=>[a(H,{size:"default",modelValue:s.status,"onUpdate:modelValue":e[1]||(e[1]=o=>s.status=o),placeholder:"请选择用户状态",clearable:"",style:{width:"180px"}},{default:t(()=>[a(C,{label:"查看所有",value:0}),a(C,{label:"正常用户",value:1}),a(C,{label:"黑名单用户",value:2})]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"加入时间",prop:"timeRange"},{default:t(()=>[a(pe,{size:"default",modelValue:b.value,"onUpdate:modelValue":e[2]||(e[2]=o=>b.value=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),a(c,{label:"订单排序",prop:"sortOrderCount"},{default:t(()=>[a(H,{size:"default",modelValue:s.sortOrderCount,"onUpdate:modelValue":e[3]||(e[3]=o=>s.sortOrderCount=o),placeholder:"请选择排序方式",clearable:"",style:{width:"150px"}},{default:t(()=>[a(C,{label:"不排序",value:-1}),a(C,{label:"降序",value:0}),a(C,{label:"升序",value:1})]),_:1},8,["modelValue"])]),_:1}),a(c,null,{default:t(()=>[a(f,{size:"default",type:"primary",icon:"Search",onClick:Z,loading:T.value},{default:t(()=>e[11]||(e[11]=[p(" 搜索 ")])),_:1,__:[11]},8,["loading"]),a(f,{size:"default",icon:"RefreshLeft",onClick:ee},{default:t(()=>e[12]||(e[12]=[p(" 重置 ")])),_:1,__:[12]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),g("div",$e,[ze((x(),N(ve,{data:O.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"},"default-expand-all":!1,"table-layout":"auto"},{default:t(()=>[a(y,{prop:"id",label:"用户ID",width:"100",align:"center"}),a(y,{prop:"avatarUrl",label:"头像",width:"200",align:"center"},{default:t(({row:o})=>[o.avatarUrl?(x(),N(j,{key:0,src:o.avatarUrl,size:40,onError:G},null,8,["src"])):(x(),N(j,{key:1,size:40,icon:"User"}))]),_:1}),a(y,{prop:"nickName",label:"用户昵称",width:"150","show-overflow-tooltip":""}),a(y,{prop:"status",label:"用户状态","min-width":"320",align:"center"},{default:t(({row:o})=>[a(ge,{type:X(o.status)},{default:t(()=>[p(z(W(o.status)),1)]),_:2},1032,["type"])]),_:1}),a(y,{prop:"createTime",label:"加入时间",width:"180","show-overflow-tooltip":""}),a(y,{prop:"orderCount",label:"订单总量",width:"100",align:"center"},{default:t(({row:o})=>[p(z(o.orderCount||0),1)]),_:1}),a(y,{label:"操作",width:"280",align:"center",fixed:"right"},{default:t(({row:o})=>[g("div",Be,[a(f,{size:"default",type:"primary",onClick:R=>te(o)},{default:t(()=>e[13]||(e[13]=[p(" 编辑 ")])),_:2,__:[13]},1032,["onClick"]),o.status===1?(x(),N(f,{key:0,size:"default",type:"danger",onClick:R=>q(o,1)},{default:t(()=>e[14]||(e[14]=[p(" 拉黑 ")])),_:2,__:[14]},1032,["onClick"])):A("",!0),o.status===2?(x(),N(f,{key:1,size:"default",type:"success",onClick:R=>q(o,0)},{default:t(()=>e[15]||(e[15]=[p(" 解除拉黑 ")])),_:2,__:[15]},1032,["onClick"])):A("",!0),a(f,{size:"default",type:"danger",onClick:R=>ne(o)},{default:t(()=>e[16]||(e[16]=[p(" 删除 ")])),_:2,__:[16]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ye,T.value]])]),a(Ue,{page:s.pageNum,"page-size":s.pageSize,total:$.value,onHandleSizeChange:le,onHandleCurrentChange:ae},null,8,["page","page-size","total"])]),a(P,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=o=>w.value=o),title:"编辑用户",width:"600px","close-on-click-modal":!1,onClose:ce},{footer:t(()=>[g("span",Re,[a(f,{onClick:e[5]||(e[5]=o=>w.value=!1)},{default:t(()=>e[18]||(e[18]=[p("取消")])),_:1,__:[18]}),a(f,{type:"primary",onClick:oe,loading:S.value},{default:t(()=>e[19]||(e[19]=[p(" 确定 ")])),_:1,__:[19]},8,["loading"])])]),default:t(()=>[a(L,{ref_key:"editFormRef",ref:B,model:r,rules:K,"label-width":"100px"},{default:t(()=>[a(c,{label:"用户昵称",prop:"nickName"},{default:t(()=>[a(n,{modelValue:r.nickName,"onUpdate:modelValue":e[4]||(e[4]=o=>r.nickName=o),placeholder:"请输入用户昵称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(c,{label:"用户头像",prop:"avatarUrl"},{default:t(()=>[a(be,{class:"avatar-upload",action:"#","auto-upload":!1,"on-change":ue,"on-remove":ie,"before-upload":re,"file-list":v.value,"list-type":"picture-card",limit:1,accept:"image/*"},{tip:t(()=>e[17]||(e[17]=[g("div",{class:"el-upload__tip"}," 只能上传jpg/png等图片文件，且不超过2MB ",-1)])),default:t(()=>[a(_e,null,{default:t(()=>[a(Fe(xe))]),_:1})]),_:1},8,["file-list"]),m.value>0&&m.value<100?(x(),J("div",Le,[a(ke,{percentage:m.value,"show-text":!0},null,8,["percentage"]),g("p",null,"上传中... "+z(m.value)+"%",1)])):A("",!0)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(P,{modelValue:V.value,"onUpdate:modelValue":e[9]||(e[9]=o=>V.value=o),title:k.value===1?"加入黑名单":"移除黑名单",width:"500px","close-on-click-modal":!1,onClose:e[10]||(e[10]=o=>h.text="")},{footer:t(()=>[g("span",Ee,[a(f,{onClick:e[8]||(e[8]=o=>V.value=!1)},{default:t(()=>e[20]||(e[20]=[p("取消")])),_:1,__:[20]}),a(f,{type:k.value===1?"danger":"success",onClick:se,loading:D.value},{default:t(()=>[p(z(k.value===1?"确认拉黑":"确认解除"),1)]),_:1},8,["type","loading"])])]),default:t(()=>[a(L,{ref_key:"blacklistFormRef",ref:I,model:h,rules:Q,"label-width":"100px"},{default:t(()=>[a(c,{label:"用户昵称"},{default:t(()=>[g("span",null,z(M.value?.nickName),1)]),_:1}),a(c,{label:"操作原因",prop:"text"},{default:t(()=>[a(n,{modelValue:h.text,"onUpdate:modelValue":e[7]||(e[7]=o=>h.text=o),placeholder:k.value===1?"请输入拉黑原因":"请输入解除拉黑原因",type:"textarea",rows:4,maxlength:"200","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Je=we(Ae,[["__scopeId","data-v-3e20f668"]]);export{Je as default};
