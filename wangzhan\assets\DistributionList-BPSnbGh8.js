import{r as C,X as V,h as le,y as S,Q as t,A as a,J as ae,I as l,al as u,ar as oe,H as ne,az as ie,z as h,O as n,V as re,P as O,a6 as F,M as r}from"./vendor-DmFBDimT.js";import{T as ue,L as g}from"./LbButton-BtU4V_Gr.js";import{_ as de}from"./index-C9Xz1oqp.js";import{E as b,q as pe}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const ce={class:"lb-distribution-list"},_e={class:"page-main"},me={class:"stats-content"},ve={class:"stats-value"},fe={class:"stats-content"},be={class:"stats-value"},ge={class:"stats-content"},we={class:"stats-value"},ye={class:"stats-content"},ze={class:"stats-value"},Ce=["src"],Ve={style:{color:"#e6a23c","font-weight":"600"}},he={class:"table-operate"},xe={class:"pagination-section"},Se={__name:"DistributionList",setup(ke){const N=ie(),x=C(!1),k=C([]),E=C(),z=C(!1),w=V({total_distributors:0,active_distributors:0,total_commission:0,total_invites:0}),i=V({user_name:"",mobile:"",level:0,status:0}),_=V({page:1,pageSize:10,total:0}),m=V({id:"",user_name:"",mobile:"",level:1,commission_total:0,invite_count:0,order_count:0,create_time:""}),I=[{label:"全部",value:0},{label:"普通分销商",value:1},{label:"高级分销商",value:2},{label:"金牌分销商",value:3}],P=[{label:"全部",value:0},{label:"正常",value:1},{label:"禁用",value:2}],v=async(o=1)=>{x.value=!0,_.page=o;try{const e=new URLSearchParams({page:_.page,pageSize:_.pageSize,user_name:i.user_name,mobile:i.mobile,level:i.level,status:i.status}),p=await(await fetch(`/api/distribution/list?${e}`)).json();p.code===200?(k.value=p.data.list||[],_.total=p.data.total||0,Object.assign(w,p.data.stats||{})):b.error(p.message||"获取数据失败")}catch(e){console.error("获取分销商列表失败:",e),b.error("获取数据失败")}finally{x.value=!1}},R=()=>{i.user_name="",i.mobile="",i.level=0,i.status=0,v(1)},J=o=>({1:"info",2:"success",3:"warning"})[o]||"info",$=o=>({1:"普通分销商",2:"高级分销商",3:"金牌分销商"})[o]||"普通分销商",q=o=>({1:"success",2:"danger"})[o]||"info",A=o=>({1:"正常",2:"禁用"})[o]||"未知",D=(o,e)=>{if(!o)return"";const d=new Date(o);return e===1?d.toLocaleDateString():d.toLocaleTimeString()},H=async o=>{try{const d=await(await fetch(`/api/distribution/detail/${o.id}`)).json();d.code===200?(Object.assign(m,d.data),z.value=!0):b.error(d.message||"获取分销商详情失败")}catch(e){console.error("获取分销商详情失败:",e),b.error("获取分销商详情失败")}},Q=o=>{N.push(`/distribution/commission?distributor_id=${o.id}`)},X=async o=>{try{const e=o.status===1?"禁用":"启用";await pe.confirm(`确定要${e}分销商 "${o.user_name}" 吗？`,`${e}分销商确认`,{confirmButtonText:`确定${e}`,cancelButtonText:"取消",type:"warning"});const p=await(await fetch(`/api/distribution/status/${o.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:o.status===1?2:1})})).json();p.code===200?(b.success(`${e}成功`),v()):b.error(p.message||"操作失败")}catch(e){e!=="cancel"&&(console.error("修改分销商状态失败:",e),b.error("操作失败"))}},G=o=>{_.pageSize=o,v(1)},K=o=>{v(o)};return le(()=>{v()}),(o,e)=>{const d=u("el-card"),p=u("el-col"),M=u("el-row"),T=u("el-input"),y=u("el-form-item"),L=u("el-option"),U=u("el-select"),W=u("el-form"),c=u("el-table-column"),B=u("el-tag"),Y=u("el-table"),Z=u("el-pagination"),f=u("el-descriptions-item"),ee=u("el-descriptions"),te=u("el-dialog"),se=oe("loading");return h(),S("div",ce,[t(ue),a("div",_e,[t(M,{gutter:20,class:"stats-cards"},{default:l(()=>[t(p,{span:6},{default:l(()=>[t(d,{class:"stats-card"},{default:l(()=>[a("div",me,[a("div",ve,n(w.total_distributors||0),1),e[12]||(e[12]=a("div",{class:"stats-label"},"总分销商",-1))]),e[13]||(e[13]=a("div",{class:"stats-icon total"},[a("i",{class:"el-icon-user"})],-1))]),_:1,__:[13]})]),_:1}),t(p,{span:6},{default:l(()=>[t(d,{class:"stats-card"},{default:l(()=>[a("div",fe,[a("div",be,n(w.active_distributors||0),1),e[14]||(e[14]=a("div",{class:"stats-label"},"活跃分销商",-1))]),e[15]||(e[15]=a("div",{class:"stats-icon active"},[a("i",{class:"el-icon-star-on"})],-1))]),_:1,__:[15]})]),_:1}),t(p,{span:6},{default:l(()=>[t(d,{class:"stats-card"},{default:l(()=>[a("div",ge,[a("div",we,"¥"+n(w.total_commission||0),1),e[16]||(e[16]=a("div",{class:"stats-label"},"总佣金",-1))]),e[17]||(e[17]=a("div",{class:"stats-icon commission"},[a("i",{class:"el-icon-money"})],-1))]),_:1,__:[17]})]),_:1}),t(p,{span:6},{default:l(()=>[t(d,{class:"stats-card"},{default:l(()=>[a("div",ye,[a("div",ze,n(w.total_invites||0),1),e[18]||(e[18]=a("div",{class:"stats-label"},"总邀请数",-1))]),e[19]||(e[19]=a("div",{class:"stats-icon invites"},[a("i",{class:"el-icon-share"})],-1))]),_:1,__:[19]})]),_:1})]),_:1}),t(M,{class:"page-search-form"},{default:l(()=>[t(W,{onSubmit:e[7]||(e[7]=re(()=>{},["prevent"])),inline:!0,model:i,ref_key:"searchFormRef",ref:E},{default:l(()=>[t(y,{label:"分销商姓名",prop:"user_name"},{default:l(()=>[t(T,{modelValue:i.user_name,"onUpdate:modelValue":e[0]||(e[0]=s=>i.user_name=s),placeholder:"请输入分销商姓名",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(y,{label:"手机号",prop:"mobile"},{default:l(()=>[t(T,{modelValue:i.mobile,"onUpdate:modelValue":e[1]||(e[1]=s=>i.mobile=s),placeholder:"请输入手机号",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(y,{label:"等级",prop:"level"},{default:l(()=>[t(U,{onChange:e[2]||(e[2]=s=>v(1)),modelValue:i.level,"onUpdate:modelValue":e[3]||(e[3]=s=>i.level=s),placeholder:"请选择"},{default:l(()=>[(h(),S(O,null,F(I,s=>t(L,{key:s.value,label:s.label,value:s.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(U,{onChange:e[4]||(e[4]=s=>v(1)),modelValue:i.status,"onUpdate:modelValue":e[5]||(e[5]=s=>i.status=s),placeholder:"请选择"},{default:l(()=>[(h(),S(O,null,F(P,s=>t(L,{key:s.value,label:s.label,value:s.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:l(()=>[t(g,{size:"default",type:"primary",style:{"margin-right":"5px"},onClick:e[6]||(e[6]=s=>v(1))},{default:l(()=>e[20]||(e[20]=[r(" 搜索 ")])),_:1,__:[20]}),t(g,{size:"default",style:{"margin-right":"5px"},onClick:R},{default:l(()=>e[21]||(e[21]=[r(" 重置 ")])),_:1,__:[21]})]),_:1})]),_:1},8,["model"])]),_:1}),ae((h(),ne(Y,{data:k.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:l(()=>[t(c,{prop:"id",label:"ID",width:"80",fixed:""}),t(c,{prop:"avatarUrl",label:"头像",width:"80"},{default:l(s=>[a("img",{src:s.row.avatarUrl,alt:"头像",style:{width:"40px",height:"40px","border-radius":"50%"}},null,8,Ce)]),_:1}),t(c,{prop:"user_name",label:"分销商姓名",width:"120"}),t(c,{prop:"mobile",label:"手机号",width:"130"}),t(c,{prop:"level",label:"等级",width:"100"},{default:l(s=>[t(B,{type:J(s.row.level),size:"small"},{default:l(()=>[r(n($(s.row.level)),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"commission_total",label:"累计佣金",width:"120"},{default:l(s=>[a("span",Ve,"¥"+n(s.row.commission_total||0),1)]),_:1}),t(c,{prop:"invite_count",label:"邀请人数",width:"100"}),t(c,{prop:"order_count",label:"订单数量",width:"100"}),t(c,{prop:"last_active_time",label:"最后活跃",width:"170"},{default:l(s=>[a("div",null,n(D(s.row.last_active_time,1)),1),a("div",null,n(D(s.row.last_active_time,2)),1)]),_:1}),t(c,{prop:"status",label:"状态",width:"100"},{default:l(s=>[t(B,{type:q(s.row.status),size:"small"},{default:l(()=>[r(n(A(s.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(c,{label:"操作",width:"250",fixed:"right"},{default:l(s=>[a("div",he,[t(g,{size:"mini",type:"primary",onClick:j=>H(s.row)},{default:l(()=>e[22]||(e[22]=[r(" 查看详情 ")])),_:2,__:[22]},1032,["onClick"]),t(g,{size:"mini",type:"success",onClick:j=>Q(s.row)},{default:l(()=>e[23]||(e[23]=[r(" 佣金记录 ")])),_:2,__:[23]},1032,["onClick"]),t(g,{size:"mini",type:s.row.status===1?"danger":"success",onClick:j=>X(s.row)},{default:l(()=>[r(n(s.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])])]),_:1})]),_:1},8,["data"])),[[se,x.value]]),a("div",xe,[t(Z,{"current-page":_.page,"onUpdate:currentPage":e[8]||(e[8]=s=>_.page=s),"page-size":_.pageSize,"onUpdate:pageSize":e[9]||(e[9]=s=>_.pageSize=s),"page-sizes":[10,20,50,100],total:_.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:K},null,8,["current-page","page-size","total"])])]),t(te,{modelValue:z.value,"onUpdate:modelValue":e[11]||(e[11]=s=>z.value=s),title:"分销商详情",width:"60%"},{footer:l(()=>[t(g,{onClick:e[10]||(e[10]=s=>z.value=!1)},{default:l(()=>e[24]||(e[24]=[r("关闭")])),_:1,__:[24]})]),default:l(()=>[t(ee,{column:2,border:""},{default:l(()=>[t(f,{label:"分销商ID"},{default:l(()=>[r(n(m.id),1)]),_:1}),t(f,{label:"姓名"},{default:l(()=>[r(n(m.user_name),1)]),_:1}),t(f,{label:"手机号"},{default:l(()=>[r(n(m.mobile),1)]),_:1}),t(f,{label:"等级"},{default:l(()=>[r(n($(m.level)),1)]),_:1}),t(f,{label:"累计佣金"},{default:l(()=>[r("¥"+n(m.commission_total||0),1)]),_:1}),t(f,{label:"邀请人数"},{default:l(()=>[r(n(m.invite_count||0),1)]),_:1}),t(f,{label:"订单数量"},{default:l(()=>[r(n(m.order_count||0),1)]),_:1}),t(f,{label:"注册时间"},{default:l(()=>[r(n(m.create_time),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},Ue=de(Se,[["__scopeId","data-v-964d6601"]]);export{Ue as default};
