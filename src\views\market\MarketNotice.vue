<!--
  公告管理页面
  参考ServiceFenlei.vue和MarketList.vue的实现模式，按照快速开发指南重构
  对应API: /api/admin/notice/*
-->

<template>
  <div class="market-notice">
    <!-- 顶部导航 -->
    <TopNav title="公告管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- <el-form-item label="公告内容" prop="content">
                <el-input
                  size="default"
                  v-model="searchForm.content"
                  placeholder="请输入公告内容"
                  clearable
                  style="width: 200px"
                />
              </el-form-item> -->
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="启用" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="用户端" :value="1" />
                  <el-option label="师傅端" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增公告
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
        >
        <el-table-column prop="id" label="ID" width="100" align="center" />

        <el-table-column prop="content" label="公告内容" min-width="300" align="center">
          <template #default="scope">
            <div class="content-column">
              <p>{{ scope.row.content }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'primary' : 'warning'">
              {{ scope.row.type === 1 ? '用户端' : '师傅端' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" min-width="120" align="center">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="default"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="default"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
          />
        </el-form-item>

        <el-form-item label="公告类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :value="1">用户端</el-radio>
            <el-radio :value="2">师傅端</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  content: '',
  status: null,
  type: null
})

// 编辑表单
const form = reactive({
  id: null,
  content: '',
  type: 1,
  status: 1
})

// 表单验证规则
const rules = {
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑公告' : '新增公告')

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.content) params.content = searchForm.content
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    // 使用API-V2调用方式
    const result = await api.market.noticeList(params)
    console.log('📢 公告列表数据 (API-V2):', result)

    // 处理真实API的响应格式
    if (result.code === 200 || result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      const rawList = data.list || []
      tableData.value = rawList
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.content = ''
  searchForm.status = null
  searchForm.type = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  resetForm()
  try {
    // 获取公告详情
    const result = await api.market.noticeInfo({ id: row.id })
    if (result.code === '200') {
      const data = result.data
      form.id = data.id
      form.content = data.content || ''
      form.type = data.type || 1
      form.status = data.status || 1
    } else {
      ElMessage.error(result.message || '获取公告详情失败')
      return
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
    return
  }

  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条公告吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.market.noticeDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公告失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    const result = await api.market.noticeStatus({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
      ElMessage.error(result.message || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    console.error('修改状态失败:', error)
    ElMessage.error('状态修改失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑公告
      result = await api.market.noticeUpdate(form)
    } else {
      // 新增公告
      result = await api.market.noticeAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.content = ''
  form.type = 1
  form.status = 1
}

// 格式化日期时间
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 生命周期
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* 页面主体样式 */
.market-notice {
  padding: 0px;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格边框增强 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 内容列样式 */
.content-column p {
  margin: 0;
  line-height: 1.6;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 时间列样式 */
.time-column p {
  margin: 0;
  line-height: 1.4;
  font-size: 12px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: right;
}

/* 修复下拉选择框显示问题 */
:deep(.el-select-dropdown) {
  z-index: 9999 !important;
}

:deep(.el-popper) {
  z-index: 9999 !important;
}

/* 确保下拉选择框正常显示 */
.page-search-form :deep(.el-select) {
  position: relative;
}

.page-search-form :deep(.el-select .el-input) {
  cursor: pointer;
}

.page-search-form :deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #409eff;
}

/* 对话框中的下拉选择框 */
.el-dialog :deep(.el-select) {
  position: relative;
}

.el-dialog :deep(.el-select .el-input) {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lb-examine-goods {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
    gap: 4px;
  }

  :deep(.el-pagination) {
    text-align: center;
  }
}
</style>