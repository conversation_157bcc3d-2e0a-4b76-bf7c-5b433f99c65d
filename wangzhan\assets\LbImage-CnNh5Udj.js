import{F as b,G as I}from"./element-fdzwdDuf.js";import{_ as L}from"./index-C9Xz1oqp.js";import{r as f,c as o,al as p,y as B,z as N,Q as a,I as n,A as s,u as m,C as h}from"./vendor-DmFBDimT.js";const C={class:"image-placeholder"},E={class:"image-error"},k={__name:"LbImage",props:{src:{type:String,default:""},width:{type:[String,Number],default:"auto"},height:{type:[String,Number],default:"auto"},fit:{type:String,default:"cover",validator:r=>["fill","contain","cover","none","scale-down"].includes(r)},lazy:{type:Boolean,default:!0},preview:{type:Boolean,default:!0},zIndex:{type:Number,default:2e3},defaultSrc:{type:String,default:""}},emits:["load","error"],setup(r,{emit:g}){const e=r,i=g,u=f(!1),c=f(!1),y=o(()=>e.src?e.src:e.defaultSrc?e.defaultSrc:""),v=o(()=>e.preview&&e.src?[e.src]:[]),_=o(()=>{const t={};return e.width!=="auto"&&(t.width=typeof e.width=="number"?`${e.width}px`:e.width),e.height!=="auto"&&(t.height=typeof e.height=="number"?`${e.height}px`:e.height),t}),w=o(()=>({width:"100%",height:"100%"})),S=t=>{u.value=!1,c.value=!1,i("load",t)},x=t=>{u.value=!1,c.value=!0,i("error",t)};return(t,l)=>{const d=p("el-icon"),z=p("el-image");return N(),B("div",{class:"lb-image",style:h(_.value)},[a(z,{src:y.value,fit:r.fit,lazy:r.lazy,"preview-src-list":v.value,"z-index":r.zIndex,style:h(w.value),onLoad:S,onError:x},{placeholder:n(()=>[s("div",C,[a(d,null,{default:n(()=>[a(m(I))]),_:1}),l[0]||(l[0]=s("span",null,"加载中...",-1))])]),error:n(()=>[s("div",E,[a(d,null,{default:n(()=>[a(m(b))]),_:1}),l[1]||(l[1]=s("span",null,"加载失败",-1))])]),_:1},8,["src","fit","lazy","preview-src-list","z-index","style"])],4)}}},F=L(k,[["__scopeId","data-v-88727fb0"]]);export{F as L};
