<template>
  <div class="lb-system-transaction">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>交易基础设置</span>
          </div>
        </template>

        <el-form
          :model="configForm"
          ref="configFormRef"
          label-width="140px"
          class="config-form"
          :rules="formRules"
        >
          <el-form-item label="转账方式" prop="companyPay">
            <el-radio-group v-model="configForm.companyPay" class="payment-radio-group">
              <el-radio :label="1" class="payment-radio">
                <span class="radio-label">企业转账</span>
              </el-radio>
              <el-radio :label="2" class="payment-radio">
                <span class="radio-label">商家转账</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="overTime">
            <template #label>
              <div class="label-with-tooltip">
                <span>订单超时时间</span>
                <el-tooltip
                  content="订单未支付超时时间，超时将自动取消订单，单位：分钟"
                  placement="top"
                  effect="dark"
                >
                  <el-icon class="tooltip-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="input-with-unit">
              <el-input-number
                v-model="configForm.overTime"
                :min="1"
                :max="10080"
                :step="1"
                controls-position="right"
                class="number-input"
              />
              <span class="unit-label">分钟</span>
            </div>
          </el-form-item>

          <el-form-item prop="maxDay">
            <template #label>
              <div class="label-with-tooltip">
                <span>最长预约时间</span>
                <el-tooltip
                  content="客户预约项目选择时间时可选择的时间期限"
                  placement="top"
                  effect="dark"
                >
                  <el-icon class="tooltip-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select v-model="configForm.maxDay" placeholder="请选择最长预约时间" class="select-input">
              <el-option :value="3" label="3天">
                <div class="option-content">
                  <span class="option-label">3天</span>
                  <span class="option-desc">用户可提前3天预约</span>
                </div>
              </el-option>
              <el-option :value="5" label="5天">
                <div class="option-content">
                  <span class="option-label">5天</span>
                  <span class="option-desc">用户可提前5天预约</span>
                </div>
              </el-option>
              <el-option :value="7" label="7天">
                <div class="option-content">
                  <span class="option-label">7天</span>
                  <span class="option-desc">用户可提前7天预约</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="timeUnit">
            <template #label>
              <div class="label-with-tooltip">
                <span>服务时长单位</span>
                <el-tooltip
                  content="划分工作时间的时间单位"
                  placement="top"
                  effect="dark"
                >
                  <el-icon class="tooltip-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select v-model="configForm.timeUnit" placeholder="请选择服务时长单位" class="select-input">
              <el-option :value="30" label="半小时">
                <div class="option-content">
                  <span class="option-label">半小时</span>
                  <span class="option-desc">30分钟为一个单位</span>
                </div>
              </el-option>
              <el-option :value="60" label="一小时">
                <div class="option-content">
                  <span class="option-label">一小时</span>
                  <span class="option-desc">60分钟为一个单位</span>
                </div>
              </el-option>
              <el-option :value="120" label="两小时">
                <div class="option-content">
                  <span class="option-label">两小时</span>
                  <span class="option-desc">120分钟为一个单位</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <div class="rules-section">
          <div class="section-title">
            <span>交易规则设置</span>
          </div>

          <div class="editor-container">
            <LbUeditor
              v-model="configForm.tradingRules"
              :height="'50vh'"
              placeholder="请输入交易规则内容..."
            />
          </div>
        </div>
      </el-card>

      <el-card class="config-card" shadow="never">
        <div class="action-buttons">
          <LbButton type="primary" @click="saveConfig" :loading="saveLoading" size="large">
            <el-icon><DocumentAdd /></el-icon>
            保存配置
          </LbButton>
          <LbButton @click="resetConfig" size="large" style="margin-left: 15px;">
            <el-icon><RefreshLeft /></el-icon>
            重置配置
          </LbButton>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentAdd, RefreshLeft, QuestionFilled } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbUeditor from '@/components/common/LbUeditor.vue'
import { api } from '@/api-v2'

// 响应式数据
const saveLoading = ref(false)
const configFormRef = ref()

// 配置表单数据
const configForm = reactive({
  companyPay: 2,           // 转账方式 1企业转账 2商家转账
  overTime: 1440,          // 订单超时时间（分钟）
  maxDay: 7,               // 最长预约时间（天）
  timeUnit: 30,            // 时长单位（分钟）
  tradingRules: ''         // 交易规则（富文本）
})

// 表单验证规则
const formRules = {
  companyPay: [
    { required: true, message: '请选择转账方式', trigger: 'change' }
  ],
  overTime: [
    { required: true, message: '请输入订单超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '订单超时时间必须在1-10080分钟之间', trigger: 'blur' }
  ],
  maxDay: [
    { required: true, message: '请选择最长预约时间', trigger: 'change' }
  ],
  timeUnit: [
    { required: true, message: '请选择时长单位', trigger: 'change' }
  ]
}

// 获取配置数据
const getConfig = async () => {
  try {
    console.log('💰 开始获取交易设置配置...')
    const result = await api.sys.getTradeSettings()

    if (result.code === '200') {
      // 更新表单数据
      Object.assign(configForm, result.data || {})
      console.log('✅ 交易设置配置获取成功')
    } else {
      ElMessage.error(result.message || '获取配置失败')
    }
  } catch (error) {
    console.error('❌ 获取交易设置配置失败:', error)
    ElMessage.error('获取配置失败，请稍后重试')
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    // 表单验证
    await configFormRef.value.validate()

    // 确认保存操作
    await ElMessageBox.confirm(
      '确定要保存交易设置配置吗？',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    saveLoading.value = true
    console.log('💾 开始保存交易设置配置...')

    const result = await api.sys.updateTradeSettings(configForm)

    if (result.code === '200') {
      ElMessage.success('交易设置配置保存成功')
      console.log('✅ 交易设置配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 保存交易设置配置失败:', error)
      ElMessage.error('保存失败，请稍后重试')
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置配置
const resetConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置吗？此操作将恢复到默认设置！',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重置表单数据
    Object.assign(configForm, {
      companyPay: 2,
      overTime: 1440,
      maxDay: 7,
      timeUnit: 30,
      tradingRules: ''
    })

    // 清除表单验证
    configFormRef.value.clearValidate()

    ElMessage.success('配置已重置')
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-transaction {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-main {
  /* max-width: 1200px;
  margin: 0 auto; */
}

.config-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.config-form {
  padding: 24px;
}

/* 标签和提示样式 */
.label-with-tooltip {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tooltip-icon {
  color: #909399;
  font-size: 14px;
  cursor: help;
  transition: color 0.3s ease;
}

.tooltip-icon:hover {
  color: #409eff;
}

/* --- Global form item content alignment --- */
/* This is the key change to left-align all form item content by default */
:deep(.el-form-item__content) {
  display: flex;
  justify-content: flex-start; /* Changed from center to flex-start */
  align-items: center;
}

/* --- Transfer Method specific styles --- */
.payment-radio-group {
  display: flex;
  flex-direction: row;
  gap: 20px;
  /* Removed justify-content: center; to allow parent .el-form-item__content to control alignment */
  align-items: center;
}

.payment-radio {
  display: flex;
  align-items: center;
  justify-content: center; /* Keep this to center content within each radio button */
  margin-right: 0;
  padding: 20px 40px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: #fafbfc;
  min-width: 150px;
  cursor: pointer;
}

.payment-radio:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.payment-radio.is-checked {
  border-color: #409eff;
  background: #e6f7ff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

:deep(.payment-radio .el-radio__input) {
  margin-right: 8px;
}

:deep(.payment-radio .el-radio__label) {
  margin-left: 0;
  padding-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Other radio group styles (e.g., for timeUnit options) */
:deep(.el-radio-group:not(.payment-radio-group)) {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

:deep(.el-radio:not(.payment-radio)) {
  display: flex;
  align-items: flex-start;
  margin-right: 0;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fafbfc;
}

:deep(.el-radio:not(.payment-radio):hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

:deep(.el-radio:not(.payment-radio).is-checked) {
  border-color: #409eff;
  background: #e6f7ff;
}

:deep(.el-radio:not(.payment-radio) .el-radio__input) {
  margin-top: 2px;
}

:deep(.el-radio:not(.payment-radio) .el-radio__label) {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
  padding-left: 0;
}

.radio-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
}

/* --- Number input with unit styles --- */
.input-with-unit {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Already flex-start, so no change needed here */
  gap: 12px;
}

.number-input {
  width: 200px;
}

.unit-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* --- Select input styles --- */
.select-input {
  width: 300px;
  /* Removed margin: 0 auto; to allow parent .el-form-item__content to control alignment */
  display: block;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 0;
}

.option-content {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  gap: 4px;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.option-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 交易规则区域样式 */
.rules-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.section-title {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-right: 8px;
  border-radius: 2px;
}

/* 富文本编辑器容器 */
.editor-container {
  width: 100%;
  height: 50vh;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  gap: 16px;
}

.action-buttons .lb-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-buttons .lb-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片标题样式 */
:deep(.el-card__header) {
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: rgb(0, 0, 0);
  border-bottom: none;
}

:deep(.el-card__header .card-header span) {
  color: rgb(0, 0, 0);
  font-weight: 600;
}

/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-form-item) {
  margin-bottom: 32px;
}


/* 数字输入框样式优化 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
  padding-left: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lb-system-transaction {
    padding: 12px;
  }

  .page-main {
    max-width: 100%;
  }

  .config-card {
    margin-bottom: 16px;
  }

  .config-form {
    padding: 16px;
  }

  /* Ensure radio groups also align left on smaller screens */
  .payment-radio-group {
    flex-direction: column; /* Stack radios vertically */
    align-items: flex-start; /* Align radios to the start (left) */
    gap: 12px;
  }

  .payment-radio {
    width: 100%; /* Make radios take full width */
    max-width: 250px; /* Limit max width for better appearance */
    justify-content: flex-start; /* Align content inside radio to the left */
    padding: 16px 20px;
  }


  :deep(.el-radio-group) {
    gap: 12px;
  }

  :deep(.el-radio) {
    padding: 12px;
  }

  .radio-label {
    font-size: 14px;
  }

  .radio-desc {
    font-size: 12px;
  }

  .number-input {
    width: 150px;
  }

  .select-input {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons .lb-button {
    width: 100%;
    max-width: 300px;
  }

  .editor-container {
    height: 40vh;
  }
}

@media (max-width: 480px) {
  .lb-system-transaction {
    padding: 8px;
  }

  .card-header {
    font-size: 14px;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
  }

  .input-with-unit {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .number-input {
    width: 100%;
  }

  .editor-container {
    height: 35vh;
  }

  .select-input {
    width: 100%;
  }
}
</style>