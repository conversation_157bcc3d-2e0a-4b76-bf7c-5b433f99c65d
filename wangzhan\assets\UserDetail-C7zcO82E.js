import{r as b,X as $,h as ee,ay as te,y as se,Q as t,A as o,I as s,al as d,az as ae,z as oe,M as r,O as i}from"./vendor-DmFBDimT.js";import{T as le,L as _}from"./LbButton-BtU4V_Gr.js";import{_ as re}from"./index-C9Xz1oqp.js";import{E as f,q as ne}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const ie={class:"lb-user-detail"},de={class:"page-main"},ue={class:"user-avatar"},ce=["src"],pe={class:"stats-content"},_e={class:"stats-value"},fe={class:"stats-content"},me={class:"stats-value"},ve={class:"stats-content"},ge={class:"stats-value"},ye={class:"stats-content"},be={class:"stats-value"},we={class:"card-header"},ke={style:{color:"#e6a23c","font-weight":"600"}},Me={__name:"UserDetail",setup(Te){const x=te(),w=ae(),S=b(!1),g=b(!1),U=b(),k=b(!1),O=b([]),l=$({id:"",nickName:"",avatarUrl:"",mobile:"",gender:1,create_time:"",last_login_time:"",status:1,type:1,address:""}),y=$({order_count:0,total_amount:0,balance:0,coupon_count:0}),c=$({service_type:1,service_info:"",remark:""}),j={service_type:[{required:!0,message:"请选择客服类型",trigger:"change"}],service_info:[{required:!0,message:"请输入客服信息",trigger:"blur"}]},D=async()=>{S.value=!0;try{const e=await(await fetch(`/api/user/detail/${x.params.id}`)).json();e.code===200?(Object.assign(l,e.data.info||{}),Object.assign(y,e.data.stats||{}),O.value=e.data.recent_orders||[]):f.error(e.message||"获取用户详情失败")}catch(a){console.error("获取用户详情失败:",a),f.error("获取用户详情失败")}finally{S.value=!1}},z=a=>({0:"未知",1:"男",2:"女"})[a]||"未知",I=a=>({1:"success",2:"danger"})[a]||"info",L=a=>({1:"正常",2:"禁用"})[a]||"未知",R=a=>({1:"primary",2:"warning"})[a]||"info",E=a=>({1:"普通用户",2:"VIP用户"})[a]||"普通用户",q=a=>({1:"warning",2:"info",3:"primary",4:"success",5:"danger"})[a]||"info",F=a=>({1:"待付款",2:"待服务",3:"服务中",4:"已完成",5:"已取消"})[a]||"未知",N=(a,e)=>{if(!a)return"";const u=new Date(a);return e===1?u.toLocaleDateString():u.toLocaleTimeString()},P=()=>{w.push(`/user/edit/${l.id}`)},A=async()=>{try{const a=l.status===1?"禁用":"启用";await ne.confirm(`确定要${a}用户 "${l.nickName}" 吗？`,`${a}用户确认`,{confirmButtonText:`确定${a}`,cancelButtonText:"取消",type:"warning"});const u=await(await fetch(`/api/user/status/${l.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:l.status===1?2:1})})).json();u.code===200?(f.success(`${a}成功`),D()):f.error(u.message||"操作失败")}catch(a){a!=="cancel"&&(console.error("修改用户状态失败:",a),f.error("操作失败"))}},J=()=>{c.service_type=1,c.service_info="",c.remark="",g.value=!0},G=async()=>{try{await U.value.validate(),k.value=!0;const e=await(await fetch(`/api/user/customer-service/${l.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)})).json();e.code===200?(f.success("客服设置成功"),g.value=!1):f.error(e.message||"设置失败")}catch(a){console.error("客服设置失败:",a),f.error("设置失败")}finally{k.value=!1}},Q=()=>{w.push(`/shop/order?user_id=${l.id}`)},X=()=>{w.push(`/shop/order?user_id=${l.id}`)},H=a=>{w.push(`/shop/order/detail/${a.id}`)};return ee(()=>{x.params.id&&D()}),(a,e)=>{const u=d("el-col"),p=d("el-descriptions-item"),M=d("el-tag"),K=d("el-descriptions"),T=d("el-row"),m=d("el-card"),v=d("el-table-column"),W=d("el-table"),V=d("el-radio"),Y=d("el-radio-group"),C=d("el-form-item"),B=d("el-input"),Z=d("el-form"),h=d("el-dialog");return oe(),se("div",ie,[t(le,{title:"用户详情",isBack:!0}),o("div",de,[t(m,{class:"user-info-card",shadow:"never"},{header:s(()=>e[6]||(e[6]=[o("div",{class:"card-header"},[o("span",null,"用户基本信息")],-1)])),default:s(()=>[t(T,{gutter:20},{default:s(()=>[t(u,{span:6},{default:s(()=>[o("div",ue,[o("img",{src:l.avatarUrl,alt:"用户头像"},null,8,ce)])]),_:1}),t(u,{span:18},{default:s(()=>[t(K,{column:2,border:""},{default:s(()=>[t(p,{label:"用户ID"},{default:s(()=>[r(i(l.id),1)]),_:1}),t(p,{label:"微信昵称"},{default:s(()=>[r(i(l.nickName),1)]),_:1}),t(p,{label:"手机号"},{default:s(()=>[r(i(l.mobile||"未绑定"),1)]),_:1}),t(p,{label:"性别"},{default:s(()=>[r(i(z(l.gender)),1)]),_:1}),t(p,{label:"注册时间"},{default:s(()=>[r(i(l.create_time),1)]),_:1}),t(p,{label:"最后登录"},{default:s(()=>[r(i(l.last_login_time||"未知"),1)]),_:1}),t(p,{label:"用户状态"},{default:s(()=>[t(M,{type:I(l.status),size:"small"},{default:s(()=>[r(i(L(l.status)),1)]),_:1},8,["type"])]),_:1}),t(p,{label:"用户类型"},{default:s(()=>[t(M,{type:R(l.type),size:"small"},{default:s(()=>[r(i(E(l.type)),1)]),_:1},8,["type"])]),_:1}),t(p,{label:"地址",span:"2"},{default:s(()=>[r(i(l.address||"未填写"),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(T,{gutter:20,class:"stats-cards"},{default:s(()=>[t(u,{span:6},{default:s(()=>[t(m,{class:"stats-card"},{default:s(()=>[o("div",pe,[o("div",_e,i(y.order_count||0),1),e[7]||(e[7]=o("div",{class:"stats-label"},"订单数量",-1))]),e[8]||(e[8]=o("div",{class:"stats-icon orders"},[o("i",{class:"el-icon-shopping-cart-2"})],-1))]),_:1,__:[8]})]),_:1}),t(u,{span:6},{default:s(()=>[t(m,{class:"stats-card"},{default:s(()=>[o("div",fe,[o("div",me,"¥"+i(y.total_amount||0),1),e[9]||(e[9]=o("div",{class:"stats-label"},"消费金额",-1))]),e[10]||(e[10]=o("div",{class:"stats-icon amount"},[o("i",{class:"el-icon-money"})],-1))]),_:1,__:[10]})]),_:1}),t(u,{span:6},{default:s(()=>[t(m,{class:"stats-card"},{default:s(()=>[o("div",ve,[o("div",ge,"¥"+i(y.balance||0),1),e[11]||(e[11]=o("div",{class:"stats-label"},"账户余额",-1))]),e[12]||(e[12]=o("div",{class:"stats-icon balance"},[o("i",{class:"el-icon-wallet"})],-1))]),_:1,__:[12]})]),_:1}),t(u,{span:6},{default:s(()=>[t(m,{class:"stats-card"},{default:s(()=>[o("div",ye,[o("div",be,i(y.coupon_count||0),1),e[13]||(e[13]=o("div",{class:"stats-label"},"优惠券",-1))]),e[14]||(e[14]=o("div",{class:"stats-icon coupons"},[o("i",{class:"el-icon-ticket"})],-1))]),_:1,__:[14]})]),_:1})]),_:1}),t(T,{class:"action-buttons"},{default:s(()=>[t(_,{type:"primary",onClick:P},{default:s(()=>e[15]||(e[15]=[r(" 编辑用户 ")])),_:1,__:[15]}),t(_,{type:l.status===1?"danger":"success",onClick:A},{default:s(()=>[r(i(l.status===1?"禁用用户":"启用用户"),1)]),_:1},8,["type"]),t(_,{type:"warning",onClick:J},{default:s(()=>e[16]||(e[16]=[r(" 设置客服 ")])),_:1,__:[16]}),t(_,{type:"info",onClick:Q},{default:s(()=>e[17]||(e[17]=[r(" 查看订单 ")])),_:1,__:[17]})]),_:1}),t(m,{class:"recent-orders",shadow:"never"},{header:s(()=>[o("div",we,[e[19]||(e[19]=o("span",null,"最近订单",-1)),t(_,{type:"text",onClick:X},{default:s(()=>e[18]||(e[18]=[r("查看全部")])),_:1,__:[18]})])]),default:s(()=>[t(W,{data:O.value,"header-cell-style":{background:"#f5f7fa",color:"#606266"},style:{width:"100%"}},{default:s(()=>[t(v,{prop:"order_code",label:"订单号",width:"180"}),t(v,{prop:"service_name",label:"服务项目",width:"200"}),t(v,{prop:"total_price",label:"订单金额",width:"120"},{default:s(n=>[o("span",ke,"¥"+i(n.row.total_price),1)]),_:1}),t(v,{prop:"status",label:"订单状态",width:"120"},{default:s(n=>[t(M,{type:q(n.row.status),size:"small"},{default:s(()=>[r(i(F(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(v,{prop:"create_time",label:"下单时间",width:"170"},{default:s(n=>[o("div",null,i(N(n.row.create_time,1)),1),o("div",null,i(N(n.row.create_time,2)),1)]),_:1}),t(v,{label:"操作",width:"150"},{default:s(n=>[t(_,{size:"mini",type:"primary",onClick:Ve=>H(n.row)},{default:s(()=>e[20]||(e[20]=[r(" 查看详情 ")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e[22]||(e[22]=o("div",{class:"space-lg mt-lg mb-lg"},null,-1)),t(_,{type:"primary",onClick:e[0]||(e[0]=n=>a.$router.go(-1))},{default:s(()=>e[21]||(e[21]=[r(" 返回 ")])),_:1,__:[21]})]),t(h,{modelValue:g.value,"onUpdate:modelValue":e[5]||(e[5]=n=>g.value=n),title:"设置客服",width:"40%"},{footer:s(()=>[t(_,{onClick:e[4]||(e[4]=n=>g.value=!1)},{default:s(()=>e[26]||(e[26]=[r("取消")])),_:1,__:[26]}),t(_,{type:"primary",onClick:G,loading:k.value},{default:s(()=>e[27]||(e[27]=[r("确定设置")])),_:1,__:[27]},8,["loading"])]),default:s(()=>[t(Z,{model:c,rules:j,ref_key:"serviceFormRef",ref:U},{default:s(()=>[t(C,{label:"客服类型",prop:"service_type"},{default:s(()=>[t(Y,{modelValue:c.service_type,"onUpdate:modelValue":e[1]||(e[1]=n=>c.service_type=n)},{default:s(()=>[t(V,{value:1},{default:s(()=>e[23]||(e[23]=[r("在线客服")])),_:1,__:[23]}),t(V,{value:2},{default:s(()=>e[24]||(e[24]=[r("电话客服")])),_:1,__:[24]}),t(V,{value:3},{default:s(()=>e[25]||(e[25]=[r("微信客服")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"客服信息",prop:"service_info"},{default:s(()=>[t(B,{modelValue:c.service_info,"onUpdate:modelValue":e[2]||(e[2]=n=>c.service_info=n),placeholder:"请输入客服信息"},null,8,["modelValue"])]),_:1}),t(C,{label:"备注",prop:"remark"},{default:s(()=>[t(B,{modelValue:c.remark,"onUpdate:modelValue":e[3]||(e[3]=n=>c.remark=n),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Oe=re(Me,[["__scopeId","data-v-f8ac8db4"]]);export{Oe as default};
