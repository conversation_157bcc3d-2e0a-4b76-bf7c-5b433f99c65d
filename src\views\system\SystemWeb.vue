<!--
  公众号设置页面
-->

<template>
  <div class="lb-system-web">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>公众号设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          :rules="configRules" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="公众号名称" prop="name">
            <el-input v-model="configForm.name" placeholder="请输入公众号名称" />
          </el-form-item>
          
          <el-form-item label="AppID" prop="app_id">
            <el-input v-model="configForm.app_id" placeholder="请输入公众号AppID" />
          </el-form-item>
          
          <el-form-item label="AppSecret" prop="app_secret">
            <el-input v-model="configForm.app_secret" placeholder="请输入公众号AppSecret" type="password" show-password />
          </el-form-item>
          
          <el-form-item label="Token" prop="token">
            <el-input v-model="configForm.token" placeholder="请输入Token" />
          </el-form-item>
          
          <el-form-item label="启用状态" prop="status">
            <el-radio-group v-model="configForm.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
            <LbButton @click="resetConfig" style="margin-left: 10px;">重置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  name: '',
  app_id: '',
  app_secret: '',
  token: '',
  status: 1
})

const configRules = {
  name: [{ required: true, message: '请输入公众号名称', trigger: 'blur' }],
  app_id: [{ required: true, message: '请输入AppID', trigger: 'blur' }]
}

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/web/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    saveLoading.value = true
    
    const response = await fetch('/api/system/web/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const resetConfig = () => {
  Object.assign(configForm, {
    name: '',
    app_id: '',
    app_secret: '',
    token: '',
    status: 1
  })
  configFormRef.value?.clearValidate()
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-web {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}

@media (max-width: 768px) {
  .lb-system-web {
    padding: 10px;
  }
  
  .config-form {
    max-width: 100%;
  }
}
</style>
