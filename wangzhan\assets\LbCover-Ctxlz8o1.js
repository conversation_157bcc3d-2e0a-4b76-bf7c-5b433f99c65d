import{x as I,K as N,L as U,E as _}from"./element-fdzwdDuf.js";import{_ as $}from"./index-C9Xz1oqp.js";import{r as v,c as A,j as E,al as g,y as u,z as r,Q as o,K as M,I as d,H as S,u as h,P as K,a6 as R,A as y}from"./vendor-DmFBDimT.js";const j={class:"lb-cover"},D=["src"],H={key:0,class:"uploaded-list"},P=["src"],Q={class:"uploaded-actions"},W=["src"],q={__name:"LbCover",props:{fileList:{type:Array,default:()=>[]},limit:{type:Number,default:1},maxSize:{type:Number,default:5}},emits:["selectedFiles"],setup(m,{expose:k,emit:F}){const s=m,f=F,i=v(""),p=v(!1),L=v(""),w=A(()=>s.fileList||[]);E(()=>s.fileList,e=>{e&&e.length>0?i.value=e[0].url||e[0].src:i.value=""},{immediate:!0});const b=e=>{const l=e.type.startsWith("image/"),t=e.size/1024/1024<s.maxSize;return l?t?!0:(_.error(`图片大小不能超过 ${s.maxSize}MB!`),!1):(_.error("只能上传图片文件!"),!1)},x=(e,l)=>{if(e.status==="ready"){const t=new FileReader;t.onload=c=>{const n={name:e.name,url:c.target.result,raw:e.raw,size:e.size,type:e.raw.type};if(s.limit===1)i.value=n.url,f("selectedFiles",[n]);else{const a=[...w.value,n];a.length<=s.limit?f("selectedFiles",a):_.warning(`最多只能上传 ${s.limit} 张图片`)}},t.readAsDataURL(e.raw)}},z=e=>{L.value=e.url||e.src,p.value=!0},V=e=>{const l=w.value.filter((t,c)=>c!==e);f("selectedFiles",l),s.limit===1&&(i.value="")};return k({getCover:e=>{f("selectedFiles",e)}}),(e,l)=>{const t=g("el-icon"),c=g("el-upload"),n=g("el-dialog");return r(),u("div",j,[o(c,{class:"cover-uploader",action:"#","show-file-list":!1,"on-change":x,"before-upload":b,"auto-upload":!1,accept:"image/*"},{default:d(()=>[i.value?(r(),u("img",{key:0,src:i.value,class:"cover-image"},null,8,D)):(r(),S(t,{key:1,class:"cover-uploader-icon"},{default:d(()=>[o(h(I))]),_:1}))]),_:1}),m.fileList&&m.fileList.length>0?(r(),u("div",H,[(r(!0),u(K,null,R(m.fileList,(a,C)=>(r(),u("div",{key:C,class:"uploaded-item"},[y("img",{src:a.url||a.src,class:"uploaded-image"},null,8,P),y("div",Q,[o(t,{onClick:B=>z(a),class:"action-icon"},{default:d(()=>[o(h(N))]),_:2},1032,["onClick"]),o(t,{onClick:B=>V(C),class:"action-icon"},{default:d(()=>[o(h(U))]),_:2},1032,["onClick"])])]))),128))])):M("",!0),o(n,{modelValue:p.value,"onUpdate:modelValue":l[0]||(l[0]=a=>p.value=a),title:"图片预览",width:"60%"},{default:d(()=>[y("img",{src:L.value,style:{width:"100%"}},null,8,W)]),_:1},8,["modelValue"])])}}},X=$(q,[["__scopeId","data-v-d4d9f2f2"]]);export{X as L};
