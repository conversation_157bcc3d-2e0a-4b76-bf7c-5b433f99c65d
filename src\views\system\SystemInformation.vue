<!--
  备案信息页面
-->

<template>
  <div class="lb-system-information">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>备案信息设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="网站名称">
            <el-input v-model="configForm.site_name" placeholder="请输入网站名称" />
          </el-form-item>
          
          <el-form-item label="备案号">
            <el-input v-model="configForm.icp_number" placeholder="请输入ICP备案号" />
          </el-form-item>
          
          <el-form-item label="公司名称">
            <el-input v-model="configForm.company_name" placeholder="请输入公司名称" />
          </el-form-item>
          
          <el-form-item label="联系地址">
            <el-input v-model="configForm.address" placeholder="请输入联系地址" />
          </el-form-item>
          
          <el-form-item label="联系电话">
            <el-input v-model="configForm.phone" placeholder="请输入联系电话" />
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  site_name: '',
  icp_number: '',
  company_name: '',
  address: '',
  phone: ''
})

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/information/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    saveLoading.value = true
    
    const response = await fetch('/api/system/information/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-information {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}
</style>
