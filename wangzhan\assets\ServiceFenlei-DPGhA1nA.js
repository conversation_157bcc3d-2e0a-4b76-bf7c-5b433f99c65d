import{T as ve,L as v}from"./LbButton-BtU4V_Gr.js";import{L as ge}from"./LbPage-DnbiQ0Ct.js";import{_ as he,a as g}from"./index-C9Xz1oqp.js";import{E as u,q as ye}from"./element-fdzwdDuf.js";import{r as p,X as q,c as we,h as be,y as R,Q as l,A as f,I as o,al as m,J as Ce,ar as Ve,H as P,z as h,M as c,C as j,O as I,D as Re,P as Ie,a6 as ze}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ke={class:"service-fenlei"},xe={class:"content-container"},Se={class:"search-form-container"},De={class:"table-container"},Ue=["onClick"],Le={key:1,class:"child-indent"},Ne={key:1},Te={class:"time-column"},Pe={class:"table-operate"},Be={class:"dialog-footer"},Ee={__name:"ServiceFenlei",setup(Fe){const S=p(!1),D=p(!1),z=p([]),U=p([]),L=p(0),B=p(),E=p(),y=p(!1),F=p([]),V=p(new Set),r=q({pageNum:1,pageSize:10,name:"",status:null,isRecommend:null,parentId:null}),n=q({id:null,name:"",parentId:0,img:"",description:"",sort:0,isRecommend:2,status:1}),J={name:[{required:!0,message:"请输入分类名称",trigger:"blur"}]},Q=we(()=>n.id?"编辑分类":"新增分类"),C=async t=>{t&&(r.pageNum=1),S.value=!0;try{const e={pageNum:r.pageNum,pageSize:r.pageSize};r.name&&(e.name=r.name),r.status!==null&&r.status!==""&&(e.status=r.status),r.isRecommend!==null&&r.isRecommend!==""&&(e.isRecommend=r.isRecommend),r.parentId!==null&&r.parentId!==""&&(e.parentId=r.parentId);const d=await g.service.serviceCateList(e);if(console.log("📋 服务分类列表数据 (API-V2):",d),d.code===200||d.code==="200"){const s=d.data,i=s||[];z.value=i,L.value=s.totalCount||s.total||0,A(),console.log("📊 处理后的数据:",{list:z.value,displayData:U.value,total:L.value,pageNum:s.pageNum,pageSize:s.pageSize})}else console.error("❌ API响应错误:",d),u.error(d.message||d.msg||"获取数据失败")}catch(e){console.error("获取服务分类列表失败:",e),u.error("获取数据失败")}finally{S.value=!1}},A=()=>{const t=[],e=z.value.map(s=>({...s,children:[],hasChildren:!1,level:s.parentId===0?0:-1})).reduce((s,i)=>(s[i.id]=i,s),{});for(const s in e){const i=e[s];i.parentId!==0&&e[i.parentId]&&(e[i.parentId].children.push(i),e[i.parentId].hasChildren=!0)}const d=(s,i)=>{s.level=i,t.push(s),s.hasChildren&&V.value.has(s.id)&&(s.children.sort((w,T)=>w.sort-T.sort),s.children.forEach(w=>d(w,i+1)))};Object.values(e).filter(s=>s.parentId===0).sort((s,i)=>s.sort-i.sort).forEach(s=>d(s,0)),U.value=t},W=t=>{V.value.has(t.id)?V.value.delete(t.id):V.value.add(t.id),A()},X=({row:t})=>t.parentId===0?"parent-row":"child-row",G=t=>{const e=z.value.find(d=>d.id===t);return e?e.name:"未知"},N=async()=>{try{const t=await g.service.serviceCateParentList();t.code==="200"&&(F.value=t.data||[])}catch(t){console.error("获取父级分类列表失败:",t)}},K=()=>{C(1)},Y=()=>{r.name="",r.status=null,r.isRecommend=null,r.parentId=null,B.value?.resetFields(),C(1)},Z=()=>{k(),N(),y.value=!0},ee=t=>{k(),t.parentId===0?n.parentId=t.id:n.parentId=t.parentId,N(),y.value=!0},te=async t=>{k();try{const e=await g.service.serviceCateInfo({id:t.id});if(e.code==="200"){const d=e.data;n.id=d.id,n.name=d.name||"",n.parentId=d.parentId||0,n.img=d.img||"",n.description=d.description||"",n.sort=d.sort||0,n.isRecommend=d.isRecommend||2,n.status=d.status||1}else{u.error(e.message||"获取分类详情失败");return}}catch(e){console.error("获取分类详情失败:",e),u.error("获取分类详情失败");return}N(),y.value=!0},le=async t=>{try{await ye.confirm("确定要删除这个分类吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await g.service.serviceCateDelete({id:t.id});e.code==="200"?(u.success("删除成功"),C()):u.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除分类失败:",e),u.error("删除失败"))}},ae=async t=>{try{const e=await g.service.serviceCateStatus({id:t.id});e.code==="200"?u.success("状态修改成功"):(t.status=t.status===1?0:1,u.error(e.message||"状态修改失败"))}catch(e){t.status=t.status===1?0:1,console.error("修改状态失败:",e),u.error("状态修改失败")}},oe=async t=>{try{const e=await g.service.serviceCateRecommend({id:t.id});e.code==="200"?u.success("推荐状态修改成功"):(t.isRecommend=t.isRecommend===1?2:1,u.error(e.message||"推荐状态修改失败"))}catch(e){t.isRecommend=t.isRecommend===1?2:1,console.error("修改推荐状态失败:",e),u.error("推荐状态修改失败")}},ne=async()=>{try{await E.value.validate(),D.value=!0;let t;n.id?t=await g.service.serviceCateUpdate(n):t=await g.service.serviceCateAdd(n),t.code==="200"?(u.success(n.id?"更新成功":"新增成功"),y.value=!1,C()):u.error(t.message||"操作失败")}catch(t){console.error("提交失败:",t),u.error("操作失败")}finally{D.value=!1}},se=t=>{r.pageSize=t,$(1)},$=t=>{r.pageNum=t,C()},re=()=>{k()},k=()=>{n.id=null,n.name="",n.parentId=0,n.img="",n.description="",n.sort=0,n.isRecommend=2,n.status=1},de=t=>t?new Date(t).toLocaleDateString("zh-CN"):"",ie=t=>t?new Date(t).toLocaleTimeString("zh-CN",{hour12:!1}):"";return be(()=>{C(1)}),(t,e)=>{const d=m("el-input"),s=m("el-form-item"),i=m("el-option"),w=m("el-select"),T=m("el-col"),ue=m("el-row"),M=m("el-form"),b=m("el-table-column"),me=m("el-tag"),O=m("el-switch"),ce=m("el-table"),pe=m("el-input-number"),x=m("el-radio"),H=m("el-radio-group"),fe=m("el-dialog"),_e=Ve("loading");return h(),R("div",ke,[l(ve,{title:"服务分类管理"}),f("div",xe,[f("div",Se,[l(M,{ref_key:"searchFormRef",ref:B,model:r,inline:!0,class:"search-form"},{default:o(()=>[l(ue,{gutter:20},{default:o(()=>[l(T,{span:24},{default:o(()=>[l(s,{label:"分类名称",prop:"name"},{default:o(()=>[l(d,{size:"default",modelValue:r.name,"onUpdate:modelValue":e[0]||(e[0]=a=>r.name=a),placeholder:"请输入分类名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),l(s,{label:"状态",prop:"status"},{default:o(()=>[l(w,{size:"default",modelValue:r.status,"onUpdate:modelValue":e[1]||(e[1]=a=>r.status=a),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:o(()=>[l(i,{label:"启用",value:1}),l(i,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"推荐状态",prop:"isRecommend"},{default:o(()=>[l(w,{size:"default",modelValue:r.isRecommend,"onUpdate:modelValue":e[2]||(e[2]=a=>r.isRecommend=a),placeholder:"请选择推荐状态",clearable:"",style:{width:"140px"}},{default:o(()=>[l(i,{label:"推荐",value:1}),l(i,{label:"不推荐",value:2})]),_:1},8,["modelValue"])]),_:1}),l(s,null,{default:o(()=>[l(v,{size:"default",type:"primary",icon:"Search",onClick:K},{default:o(()=>e[12]||(e[12]=[c(" 搜索 ")])),_:1,__:[12]}),l(v,{size:"default",icon:"RefreshLeft",onClick:Y},{default:o(()=>e[13]||(e[13]=[c(" 重置 ")])),_:1,__:[13]}),l(v,{size:"default",type:"primary",icon:"Plus",onClick:Z},{default:o(()=>e[14]||(e[14]=[c(" 新增分类 ")])),_:1,__:[14]}),l(v,{size:"default",type:"success",icon:"Plus",onClick:ee},{default:o(()=>e[15]||(e[15]=[c(" 添加子分类 ")])),_:1,__:[15]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),f("div",De,[Ce((h(),P(ce,{data:U.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"},"row-key":"id","row-class-name":X},{default:o(()=>[l(b,{prop:"id",label:"ID",width:"100",align:"center"},{default:o(a=>[f("span",{style:j({paddingLeft:(a.row.level||0)*20+"px"})},I(a.row.id),5)]),_:1}),l(b,{prop:"name",label:"分类名称","min-width":"150"},{default:o(a=>[f("span",{style:j({paddingLeft:(a.row.level||0)*20+"px"}),class:"category-name-wrapper"},[a.row.hasChildren?(h(),R("i",{key:0,class:Re(["arrow-icon","el-icon-arrow-right",{expanded:V.value.has(a.row.id)}]),onClick:_=>W(a.row)},null,10,Ue)):(h(),R("span",Le)),c(" "+I(a.row.name),1)],4)]),_:1}),l(b,{prop:"parentId",label:"父级分类",width:"120"},{default:o(a=>[a.row.parentId===0?(h(),P(me,{key:0,type:"primary"},{default:o(()=>e[16]||(e[16]=[c("顶级分类")])),_:1,__:[16]})):(h(),R("span",Ne,I(G(a.row.parentId)),1))]),_:1}),l(b,{prop:"status",label:"状态",width:"100"},{default:o(a=>[l(O,{modelValue:a.row.status,"onUpdate:modelValue":_=>a.row.status=_,"active-value":1,"inactive-value":0,onChange:_=>ae(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(b,{prop:"isRecommend",label:"推荐",width:"100"},{default:o(a=>[l(O,{modelValue:a.row.isRecommend,"onUpdate:modelValue":_=>a.row.isRecommend=_,"active-value":1,"inactive-value":2,onChange:_=>oe(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(b,{prop:"createTime",label:"创建时间","min-width":"120"},{default:o(a=>[f("div",Te,[f("p",null,I(de(a.row.createTime)),1),f("p",null,I(ie(a.row.createTime)),1)])]),_:1}),l(b,{label:"操作",width:"200",fixed:"right"},{default:o(a=>[f("div",Pe,[l(v,{size:"default",type:"primary",onClick:_=>te(a.row)},{default:o(()=>e[17]||(e[17]=[c(" 编辑 ")])),_:2,__:[17]},1032,["onClick"]),l(v,{size:"default",type:"danger",onClick:_=>le(a.row)},{default:o(()=>e[18]||(e[18]=[c(" 删除 ")])),_:2,__:[18]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[_e,S.value]])]),l(ge,{page:r.pageNum,"page-size":r.pageSize,total:L.value,onHandleSizeChange:se,onHandleCurrentChange:$},null,8,["page","page-size","total"])]),l(fe,{title:Q.value,modelValue:y.value,"onUpdate:modelValue":e[11]||(e[11]=a=>y.value=a),width:"600px",onClose:re},{footer:o(()=>[f("span",Be,[l(v,{onClick:e[10]||(e[10]=a=>y.value=!1)},{default:o(()=>e[23]||(e[23]=[c("取消")])),_:1,__:[23]}),l(v,{type:"primary",onClick:ne,loading:D.value},{default:o(()=>e[24]||(e[24]=[c(" 确定 ")])),_:1,__:[24]},8,["loading"])])]),default:o(()=>[l(M,{ref_key:"formRef",ref:E,model:n,rules:J,"label-width":"100px"},{default:o(()=>[l(s,{label:"分类名称",prop:"name"},{default:o(()=>[l(d,{modelValue:n.name,"onUpdate:modelValue":e[3]||(e[3]=a=>n.name=a),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),l(s,{label:"父级分类",prop:"parentId"},{default:o(()=>[l(w,{modelValue:n.parentId,"onUpdate:modelValue":e[4]||(e[4]=a=>n.parentId=a),placeholder:"请选择父级分类（可选）",clearable:"",style:{width:"100%"}},{default:o(()=>[l(i,{label:"顶级分类",value:0}),(h(!0),R(Ie,null,ze(F.value,a=>(h(),P(i,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"分类图片",prop:"img"},{default:o(()=>[l(d,{modelValue:n.img,"onUpdate:modelValue":e[5]||(e[5]=a=>n.img=a),placeholder:"请输入图片URL（可选）"},null,8,["modelValue"])]),_:1}),l(s,{label:"分类描述",prop:"description"},{default:o(()=>[l(d,{modelValue:n.description,"onUpdate:modelValue":e[6]||(e[6]=a=>n.description=a),type:"textarea",placeholder:"请输入分类描述（可选）"},null,8,["modelValue"])]),_:1}),l(s,{label:"排序",prop:"sort"},{default:o(()=>[l(pe,{modelValue:n.sort,"onUpdate:modelValue":e[7]||(e[7]=a=>n.sort=a),min:0,placeholder:"请输入排序值",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(s,{label:"推荐",prop:"isRecommend"},{default:o(()=>[l(H,{modelValue:n.isRecommend,"onUpdate:modelValue":e[8]||(e[8]=a=>n.isRecommend=a)},{default:o(()=>[l(x,{value:1},{default:o(()=>e[19]||(e[19]=[c("推荐")])),_:1,__:[19]}),l(x,{value:2},{default:o(()=>e[20]||(e[20]=[c("不推荐")])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"状态",prop:"status"},{default:o(()=>[l(H,{modelValue:n.status,"onUpdate:modelValue":e[9]||(e[9]=a=>n.status=a)},{default:o(()=>[l(x,{value:1},{default:o(()=>e[21]||(e[21]=[c("启用")])),_:1,__:[21]}),l(x,{value:0},{default:o(()=>e[22]||(e[22]=[c("禁用")])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},je=he(Ee,[["__scopeId","data-v-c21ec477"]]);export{je as default};
