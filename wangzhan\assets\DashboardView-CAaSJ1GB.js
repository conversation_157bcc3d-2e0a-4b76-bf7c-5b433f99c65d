import{r as h,h as B,ax as E,y as c,A as t,P as y,a6 as f,Q as a,I as o,al as r,az as P,z as i,C as S,H as p,L as k,O as n,D as $,M as d,u}from"./vendor-DmFBDimT.js";import{n as g,o as M,p as N,u as q,E as I}from"./element-fdzwdDuf.js";import{_ as L}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const Q={class:"dashboard-container"},F={class:"stats-grid"},H={class:"stat-content"},O={class:"stat-value"},R={class:"stat-label"},U={class:"charts-section"},j={class:"chart-row"},G={class:"chart-card"},J={class:"card-header"},K={class:"chart-content"},W={class:"mock-chart"},X={class:"chart-placeholder"},Y={class:"chart-card"},Z={class:"chart-content"},tt={class:"mock-chart"},st={class:"chart-placeholder"},et={class:"quick-actions"},at={class:"card"},ot={class:"card-body"},it={class:"action-grid"},nt=["onClick"],ct={class:"action-icon"},lt={class:"action-content"},dt={class:"action-title"},rt={class:"action-desc"},ut={class:"recent-activities"},_t={class:"card"},vt={class:"card-header"},ht={class:"card-body"},pt={class:"activity-list"},mt={class:"activity-avatar"},yt={class:"activity-content"},ft={class:"activity-text"},kt={class:"activity-time"},gt={class:"activity-status"},bt={__name:"DashboardView",setup(wt){const b=E(),w=P(),_=h("week"),x=h([{key:"users",label:"总用户数",value:"12,345",change:"+12.5%",trend:"up",color:"#409eff",icon:"User"},{key:"orders",label:"订单总数",value:"8,765",change:"+8.2%",trend:"up",color:"#67c23a",icon:"Document"},{key:"revenue",label:"总收入",value:"¥234,567",change:"-2.1%",trend:"down",color:"#e6a23c",icon:"DataAnalysis"},{key:"conversion",label:"转化率",value:"3.45%",change:"+0.8%",trend:"up",color:"#f56c6c",icon:"TrendCharts"}]),z=h([{key:"add-user",title:"添加用户",description:"创建新的系统用户",icon:"Plus",route:"/system/user"},{key:"add-content",title:"发布内容",description:"创建新的文章内容",icon:"Edit",route:"/content/article"},{key:"view-stats",title:"查看统计",description:"查看详细数据统计",icon:"View",route:"/statistics/overview"},{key:"system-settings",title:"系统设置",description:"配置系统参数",icon:"Setting",route:"/system/config"}]),C=h([{id:1,user:"张三",action:"创建了新用户",time:"2分钟前",avatar:"https://avatars.githubusercontent.com/u/1?v=4",status:"success",statusText:"成功"},{id:2,user:"李四",action:"更新了文章内容",time:"5分钟前",avatar:"https://avatars.githubusercontent.com/u/2?v=4",status:"warning",statusText:"待审核"},{id:3,user:"王五",action:"删除了过期数据",time:"10分钟前",avatar:"https://avatars.githubusercontent.com/u/3?v=4",status:"info",statusText:"已完成"},{id:4,user:"赵六",action:"修改了系统配置",time:"15分钟前",avatar:"https://avatars.githubusercontent.com/u/4?v=4",status:"danger",statusText:"需要确认"}]),D=v=>{v.route?w.push(v.route):I.info(`${v.title} 功能开发中...`)};return B(()=>{b.dispatch("user/recordPageVisit",{path:"/dashboard",meta:{title:"仪表盘"}})}),(v,s)=>{const l=r("el-icon"),m=r("el-button"),T=r("el-button-group"),V=r("el-avatar"),A=r("el-tag");return i(),c("div",Q,[s[13]||(s[13]=t("div",{class:"page-title"},[t("h1",null,"仪表盘"),t("p",{class:"page-subtitle"},"欢迎使用 今师傅后台管理系统")],-1)),t("div",F,[(i(!0),c(y,null,f(x.value,e=>(i(),c("div",{class:"stat-card",key:e.key},[t("div",{class:"stat-icon",style:S({color:e.color})},[a(l,{size:48},{default:o(()=>[(i(),p(k(e.icon)))]),_:2},1024)],4),t("div",H,[t("div",O,n(e.value),1),t("div",R,n(e.label),1),t("div",{class:$(["stat-change",e.trend])},[a(l,null,{default:o(()=>[e.trend==="up"?(i(),p(u(g),{key:0})):(i(),p(u(M),{key:1}))]),_:2},1024),d(" "+n(e.change),1)],2)])]))),128))]),t("div",U,[t("div",j,[t("div",G,[t("div",J,[s[4]||(s[4]=t("h3",null,"访问量趋势",-1)),a(T,{size:"small"},{default:o(()=>[a(m,{type:_.value==="week"?"primary":"",onClick:s[0]||(s[0]=e=>_.value="week")},{default:o(()=>s[2]||(s[2]=[d(" 最近一周 ")])),_:1,__:[2]},8,["type"]),a(m,{type:_.value==="month"?"primary":"",onClick:s[1]||(s[1]=e=>_.value="month")},{default:o(()=>s[3]||(s[3]=[d(" 最近一月 ")])),_:1,__:[3]},8,["type"])]),_:1})]),t("div",K,[t("div",W,[t("div",X,[a(l,{size:64},{default:o(()=>[a(u(g))]),_:1}),s[5]||(s[5]=t("p",null,"访问量趋势图",-1)),s[6]||(s[6]=t("p",{class:"chart-desc"},"这里将显示网站访问量的时间趋势",-1))])])])]),t("div",Y,[s[9]||(s[9]=t("div",{class:"card-header"},[t("h3",null,"用户分布")],-1)),t("div",Z,[t("div",tt,[t("div",st,[a(l,{size:64},{default:o(()=>[a(u(N))]),_:1}),s[7]||(s[7]=t("p",null,"用户分布图",-1)),s[8]||(s[8]=t("p",{class:"chart-desc"},"这里将显示用户的地域分布情况",-1))])])])])])]),t("div",et,[t("div",at,[s[10]||(s[10]=t("div",{class:"card-header"},[t("h3",null,"快速操作")],-1)),t("div",ot,[t("div",it,[(i(!0),c(y,null,f(z.value,e=>(i(),c("div",{class:"action-item",key:e.key,onClick:xt=>D(e)},[t("div",ct,[a(l,{size:32},{default:o(()=>[(i(),p(k(e.icon)))]),_:2},1024)]),t("div",lt,[t("div",dt,n(e.title),1),t("div",rt,n(e.description),1)])],8,nt))),128))])])])]),t("div",ut,[t("div",_t,[t("div",vt,[s[12]||(s[12]=t("h3",null,"最近活动",-1)),a(m,{size:"small"},{default:o(()=>s[11]||(s[11]=[d("查看全部")])),_:1,__:[11]})]),t("div",ht,[t("div",pt,[(i(!0),c(y,null,f(C.value,e=>(i(),c("div",{class:"activity-item",key:e.id},[t("div",mt,[a(V,{size:32,src:e.avatar},{default:o(()=>[a(l,null,{default:o(()=>[a(u(q))]),_:1})]),_:2},1032,["src"])]),t("div",yt,[t("div",ft,[t("strong",null,n(e.user),1),d(" "+n(e.action),1)]),t("div",kt,n(e.time),1)]),t("div",gt,[a(A,{type:e.status,size:"small"},{default:o(()=>[d(n(e.statusText),1)]),_:2},1032,["type"])])]))),128))])])])])])}}},Vt=L(bt,[["__scopeId","data-v-8d793ead"]]);export{Vt as default};
