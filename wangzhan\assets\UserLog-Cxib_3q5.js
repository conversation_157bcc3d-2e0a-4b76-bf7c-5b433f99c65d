import{T as E,L as v}from"./LbButton-BtU4V_Gr.js";import{L as H}from"./LbPage-DnbiQ0Ct.js";import{_ as O}from"./index-C9Xz1oqp.js";import{E as y}from"./element-fdzwdDuf.js";import{g as P,r as u,X as $,h as j,y as A,Q as e,A as _,I as a,al as n,J,ar as Q,H as W,z as x,M as p,O as m}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const X={class:"page-container"},q={class:"content-container"},G={class:"search-form-container"},K={class:"table-container"},Y={__name:"UserLog",setup(Z){const{proxy:N}=P(),d=u(!1),h=u([]),g=u(0),o=$({pageNum:1,pageSize:10,nickName:"",status:""}),b=u(null),z=t=>({0:"查全部","-1":"删除",1:"解除黑名单",2:"拉入黑名单",3:"修改操作"})[t]||"未知",k=t=>({0:"info","-1":"danger",1:"success",2:"danger",3:"warning"})[t]||"info",c=async()=>{try{d.value=!0,console.log("🔍 开始加载用户操作日志列表，参数:",o);const t=await N.$api.user.userLog(o);console.log("📋 用户操作日志列表响应:",t),t.code==="200"?(h.value=t.data.list||[],g.value=t.data.totalCount||0,console.log(`✅ 用户操作日志列表加载成功，共 ${g.value} 条数据`)):y.error(t.msg||"获取用户操作日志列表失败")}catch(t){console.error("❌ 加载用户操作日志列表失败:",t),y.error("获取用户操作日志列表失败")}finally{d.value=!1}},w=()=>{o.pageNum=1,c()},S=()=>{b.value?.resetFields(),Object.assign(o,{pageNum:1,pageSize:10,nickName:"",status:""}),c()},C=t=>{o.pageSize=t,o.pageNum=1,c()},I=t=>{o.pageNum=t,c()};return j(()=>{console.log("🚀 用户操作日志页面初始化"),c()}),(t,s)=>{const L=n("el-input"),f=n("el-form-item"),i=n("el-option"),V=n("el-select"),D=n("el-col"),M=n("el-row"),T=n("el-form"),r=n("el-table-column"),B=n("el-tag"),F=n("el-tooltip"),R=n("el-table"),U=Q("loading");return x(),A("div",X,[e(E,{title:"用户操作日志"}),_("div",q,[_("div",G,[e(T,{ref_key:"searchFormRef",ref:b,model:o,inline:!0,class:"search-form"},{default:a(()=>[e(M,{gutter:20},{default:a(()=>[e(D,{span:24},{default:a(()=>[e(f,{label:"用户昵称",prop:"nickName"},{default:a(()=>[e(L,{size:"default",modelValue:o.nickName,"onUpdate:modelValue":s[0]||(s[0]=l=>o.nickName=l),placeholder:"请输入用户昵称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(f,{label:"操作状态",prop:"status"},{default:a(()=>[e(V,{size:"default",modelValue:o.status,"onUpdate:modelValue":s[1]||(s[1]=l=>o.status=l),placeholder:"请选择操作状态",clearable:"",style:{width:"180px"}},{default:a(()=>[e(i,{label:"查全部",value:0}),e(i,{label:"删除",value:-1}),e(i,{label:"解除黑名单",value:1}),e(i,{label:"拉入黑名单",value:2}),e(i,{label:"修改操作",value:3})]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(v,{size:"default",type:"primary",icon:"Search",onClick:w,loading:d.value},{default:a(()=>s[2]||(s[2]=[p(" 搜索 ")])),_:1,__:[2]},8,["loading"]),e(v,{size:"default",icon:"RefreshLeft",onClick:S},{default:a(()=>s[3]||(s[3]=[p(" 重置 ")])),_:1,__:[3]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_("div",K,[J((x(),W(R,{data:h.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:a(()=>[e(r,{prop:"id",label:"日志ID",width:"100",align:"center"}),e(r,{prop:"userId",label:"用户ID",width:"100",align:"center"}),e(r,{prop:"nickName",label:"用户昵称",width:"150"}),e(r,{prop:"status",label:"操作状态",width:"120",align:"center"},{default:a(({row:l})=>[e(B,{type:k(l.status)},{default:a(()=>[p(m(z(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"adminId",label:"管理员ID",width:"100",align:"center"},{default:a(({row:l})=>[p(m(l.adminId||"-"),1)]),_:1}),e(r,{prop:"ipv4",label:"IP地址",width:"130"},{default:a(({row:l})=>[p(m(l.ipv4||"-"),1)]),_:1}),e(r,{prop:"text",label:"操作描述","min-width":"200"},{default:a(({row:l})=>[e(F,{content:l.text,placement:"top",disabled:!l.text||l.text.length<=30},{default:a(()=>[_("span",null,m(l.text||"-"),1)]),_:2},1032,["content","disabled"])]),_:1}),e(r,{prop:"createTime",label:"操作时间",width:"160"})]),_:1},8,["data"])),[[U,d.value]])]),e(H,{page:o.pageNum,"page-size":o.pageSize,total:g.value,onHandleSizeChange:C,onHandleCurrentChange:I},null,8,["page","page-size","total"])])])}}},se=O(Y,[["__scopeId","data-v-496b116a"]]);export{se as default};
