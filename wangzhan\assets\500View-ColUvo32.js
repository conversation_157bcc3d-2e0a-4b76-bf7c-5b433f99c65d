import{y as d,A as e,Q as t,I as s,al as a,az as m,z as p,u,M as _}from"./vendor-DmFBDimT.js";import{m as f}from"./element-fdzwdDuf.js";import{_ as v}from"./index-C9Xz1oqp.js";import"./utils-DCVfloi1.js";const g={class:"error-container"},k={class:"error-content"},x={class:"error-image"},V={class:"error-actions"},B={__name:"500View",setup(C){const r=m(),i=()=>{r.push("/")},c=()=>{r.go(-1)};return(b,o)=>{const l=a("el-icon"),n=a("el-button");return p(),d("div",g,[e("div",k,[e("div",x,[t(l,{size:120},{default:s(()=>[t(u(f))]),_:1})]),o[2]||(o[2]=e("h1",{class:"error-title"},"500",-1)),o[3]||(o[3]=e("p",{class:"error-message"},"服务器内部错误",-1)),e("div",V,[t(n,{type:"primary",onClick:i},{default:s(()=>o[0]||(o[0]=[_("返回首页")])),_:1,__:[0]}),t(n,{onClick:c},{default:s(()=>o[1]||(o[1]=[_("返回上页")])),_:1,__:[1]})])])])}}},I=v(B,[["__scopeId","data-v-4e81b835"]]);export{I as default};
