import{_ as n}from"./index-C9Xz1oqp.js";import{y as r,A as t,Q as a,I as d,al as l,z as i,M as p}from"./vendor-DmFBDimT.js";import"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const _={},f={class:"forgot-password-container"},m={class:"forgot-password-content"};function u(e,o){const s=l("el-button");return i(),r("div",f,[t("div",m,[o[2]||(o[2]=t("h1",null,"忘记密码",-1)),o[3]||(o[3]=t("p",null,"忘记密码功能开发中...",-1)),a(s,{onClick:o[0]||(o[0]=c=>e.$router.push("/login"))},{default:d(()=>o[1]||(o[1]=[p("返回登录")])),_:1,__:[1]})])])}const V=n(_,[["render",u],["__scopeId","data-v-9ab1238d"]]);export{V as default};
