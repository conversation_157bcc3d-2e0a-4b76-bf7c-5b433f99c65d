import{n as F,o as de,h as ue,p as ce,Q as ve,R as _e,E as M}from"./element-fdzwdDuf.js";import{_ as pe}from"./index-C9Xz1oqp.js";import{r,c as me,h as he,S as fe,al as d,ar as ge,y as u,z as n,A as e,Q as s,I as l,M as c,P as C,a6 as R,D as P,C as O,H as _,L as be,O as i,K as $,u as f,J as ye}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ke={class:"statistics-overview"},we={class:"time-range-selector"},De={class:"metrics-grid"},Ve={class:"metric-content"},Me={class:"metric-value"},Ce={key:1},Re={class:"metric-label"},Te={key:1},qe={class:"charts-section"},ze={class:"chart-row"},xe={class:"chart-card"},Ue={class:"chart-header"},Ye={class:"chart-controls"},Pe={class:"chart-content"},Be={key:1,class:"mock-chart"},Ee={class:"chart-placeholder"},Le={class:"chart-data"},Se={class:"data-label"},Ne={class:"data-value"},Ie={class:"chart-card"},Fe={class:"chart-header"},Oe={class:"chart-content"},$e={key:1,class:"mock-chart"},Ae={class:"chart-placeholder"},Qe={class:"distribution-data"},je={class:"distribution-label"},He={class:"distribution-value"},Je={class:"realtime-section"},Ke={class:"chart-card"},Ge={class:"chart-header"},We={class:"realtime-status"},Xe={class:"chart-content"},Ze={class:"realtime-metrics"},et={class:"realtime-label"},tt={class:"realtime-value"},at={class:"realtime-unit"},st={class:"data-table-section"},ot={class:"table-card"},lt={class:"table-header"},nt={class:"table-content"},it={__name:"OverviewView",setup(rt){const g=r(!0),k=r(!0),T=r(!1),p=r("today"),q=r([]),y=r("line"),w=r("region"),D=r(null),B=r([{key:"totalUsers",label:"总用户数",value:12345,change:"+12.5%",trend:"up",color:"#409eff",icon:"User"},{key:"totalViews",label:"总访问量",value:234567,change:"+8.2%",trend:"up",color:"#67c23a",icon:"View"},{key:"totalArticles",label:"文章总数",value:1234,change:"+15.3%",trend:"up",color:"#e6a23c",icon:"Document"},{key:"avgDuration",label:"平均停留时间",value:245,change:"-2.1%",trend:"down",color:"#f56c6c",icon:"Timer"}]),E=r([{date:"01-07",visits:1200},{date:"01-08",visits:1350},{date:"01-09",visits:1180},{date:"01-10",visits:1420},{date:"01-11",visits:1680}]),A=r([{name:"北京",value:25,color:"#409eff"},{name:"上海",value:20,color:"#67c23a"},{name:"广州",value:15,color:"#e6a23c"},{name:"深圳",value:12,color:"#f56c6c"},{name:"其他",value:28,color:"#909399"}]),Q=r([{name:"桌面端",value:45,color:"#409eff"},{name:"移动端",value:40,color:"#67c23a"},{name:"平板端",value:15,color:"#e6a23c"}]),L=r([{key:"onlineUsers",label:"在线用户",value:1234,unit:"人"},{key:"todayViews",label:"今日访问",value:5678,unit:"次"},{key:"todayRegisters",label:"今日注册",value:89,unit:"人"},{key:"serverLoad",label:"服务器负载",value:45,unit:"%"}]),S=r([{rank:1,page:"/dashboard",visits:12345,uniqueVisitors:8901,avgDuration:"3:45",bounceRate:25},{rank:2,page:"/articles/vue3-guide",visits:9876,uniqueVisitors:7654,avgDuration:"5:20",bounceRate:35},{rank:3,page:"/user/profile",visits:7654,uniqueVisitors:5432,avgDuration:"2:15",bounceRate:45},{rank:4,page:"/articles/javascript-tips",visits:6543,uniqueVisitors:4321,avgDuration:"4:30",bounceRate:40},{rank:5,page:"/about",visits:4321,uniqueVisitors:3210,avgDuration:"1:50",bounceRate:65}]),j=me(()=>w.value==="region"?A.value:Q.value),z=async()=>{g.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),J()}catch{M.error("加载指标数据失败")}finally{g.value=!1}},x=async()=>{k.value=!0;try{await new Promise(o=>setTimeout(o,1200)),K()}catch{M.error("加载图表数据失败")}finally{k.value=!1}},H=async()=>{T.value=!0;try{await new Promise(o=>setTimeout(o,800)),S.value.forEach(o=>{o.visits+=Math.floor(Math.random()*100),o.uniqueVisitors+=Math.floor(Math.random()*50)}),M.success("数据已刷新")}catch{M.error("刷新数据失败")}finally{T.value=!1}},J=()=>{const o={today:1,week:7,month:30,quarter:90,year:365},t=o[p.value]||1;B.value.forEach(v=>{const m=v.value/(o[p.value]||1);v.value=Math.floor(m*t)})},K=()=>{const t={today:24,week:7,month:30,quarter:90,year:12}[p.value]||7,v=[];for(let m=0;m<t;m++)v.push({date:`${String(m+1).padStart(2,"0")}`,visits:Math.floor(Math.random()*1e3)+800});E.value=v},G=o=>{p.value=o,q.value=[],z(),x()},W=o=>{o&&o.length===2&&(p.value="custom",z(),x())},X=o=>{w.value=o},N=o=>o>=1e4?(o/1e4).toFixed(1)+"w":o>=1e3?(o/1e3).toFixed(1)+"k":o.toString(),Z=()=>{L.value.forEach(o=>{const t=Math.floor(Math.random()*20)-10;o.value=Math.max(0,o.value+t)})},ee=()=>{D.value=setInterval(Z,5e3)},te=()=>{D.value&&(clearInterval(D.value),D.value=null)};return he(()=>{z(),x(),ee()}),fe(()=>{te()}),(o,t)=>{const v=d("el-radio-button"),m=d("el-radio-group"),ae=d("el-date-picker"),h=d("el-icon"),U=d("el-skeleton"),V=d("el-button"),se=d("el-button-group"),I=d("el-dropdown-item"),oe=d("el-dropdown-menu"),le=d("el-dropdown"),ne=d("el-tag"),b=d("el-table-column"),ie=d("el-table"),re=ge("loading");return n(),u("div",ke,[t[19]||(t[19]=e("div",{class:"page-title"},[e("h1",null,"数据概览"),e("p",{class:"page-subtitle"},"实时监控系统关键指标和数据趋势")],-1)),e("div",we,[s(m,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=a=>p.value=a),onChange:G},{default:l(()=>[s(v,{label:"today"},{default:l(()=>t[4]||(t[4]=[c("今日")])),_:1,__:[4]}),s(v,{label:"week"},{default:l(()=>t[5]||(t[5]=[c("本周")])),_:1,__:[5]}),s(v,{label:"month"},{default:l(()=>t[6]||(t[6]=[c("本月")])),_:1,__:[6]}),s(v,{label:"quarter"},{default:l(()=>t[7]||(t[7]=[c("本季度")])),_:1,__:[7]}),s(v,{label:"year"},{default:l(()=>t[8]||(t[8]=[c("本年")])),_:1,__:[8]})]),_:1},8,["modelValue"]),s(ae,{modelValue:q.value,"onUpdate:modelValue":t[1]||(t[1]=a=>q.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:W,style:{"margin-left":"16px"}},null,8,["modelValue"])]),e("div",De,[(n(!0),u(C,null,R(B.value,a=>(n(),u("div",{key:a.key,class:P(["metric-card",{"metric-card-loading":g.value}])},[e("div",{class:"metric-icon",style:O({backgroundColor:a.color})},[s(h,{size:32},{default:l(()=>[(n(),_(be(a.icon)))]),_:2},1024)],4),e("div",Ve,[e("div",Me,[g.value?(n(),_(U,{key:0,rows:1,animated:""})):(n(),u("span",Ce,i(N(a.value)),1))]),e("div",Re,i(a.label),1),e("div",{class:P(["metric-change",a.trend])},[g.value?$("",!0):(n(),_(h,{key:0},{default:l(()=>[a.trend==="up"?(n(),_(f(F),{key:0})):(n(),_(f(de),{key:1}))]),_:2},1024)),g.value?$("",!0):(n(),u("span",Te,i(a.change),1))],2)])],2))),128))]),e("div",qe,[e("div",ze,[e("div",xe,[e("div",Ue,[t[11]||(t[11]=e("h3",null,"访问量趋势",-1)),e("div",Ye,[s(se,{size:"small"},{default:l(()=>[s(V,{type:y.value==="line"?"primary":"",onClick:t[2]||(t[2]=a=>y.value="line")},{default:l(()=>t[9]||(t[9]=[c(" 线图 ")])),_:1,__:[9]},8,["type"]),s(V,{type:y.value==="bar"?"primary":"",onClick:t[3]||(t[3]=a=>y.value="bar")},{default:l(()=>t[10]||(t[10]=[c(" 柱图 ")])),_:1,__:[10]},8,["type"])]),_:1})])]),e("div",Pe,[k.value?(n(),_(U,{key:0,rows:8,animated:""})):(n(),u("div",Be,[e("div",Ee,[s(h,{size:64},{default:l(()=>[s(f(F))]),_:1}),e("p",null,i(y.value==="line"?"线性":"柱状")+"访问量趋势图",1),e("div",Le,[(n(!0),u(C,null,R(E.value,(a,Y)=>(n(),u("div",{class:"data-point",key:Y},[e("span",Se,i(a.date),1),e("span",Ne,i(a.visits),1)]))),128))])])]))])]),e("div",Ie,[e("div",Fe,[t[14]||(t[14]=e("h3",null,"用户分布",-1)),s(le,{onCommand:X},{dropdown:l(()=>[s(oe,null,{default:l(()=>[s(I,{command:"region"},{default:l(()=>t[12]||(t[12]=[c("地域分布")])),_:1,__:[12]}),s(I,{command:"device"},{default:l(()=>t[13]||(t[13]=[c("设备分布")])),_:1,__:[13]})]),_:1})]),default:l(()=>[s(V,{size:"small"},{default:l(()=>[c(i(w.value==="region"?"地域分布":"设备分布")+" ",1),s(h,null,{default:l(()=>[s(f(ue))]),_:1})]),_:1})]),_:1})]),e("div",Oe,[k.value?(n(),_(U,{key:0,rows:6,animated:""})):(n(),u("div",$e,[e("div",Ae,[s(h,{size:64},{default:l(()=>[s(f(ce))]),_:1}),e("p",null,i(w.value==="region"?"地域":"设备")+"分布图",1),e("div",Qe,[(n(!0),u(C,null,R(j.value,(a,Y)=>(n(),u("div",{class:"distribution-item",key:Y},[e("div",{class:"distribution-color",style:O({backgroundColor:a.color})},null,4),e("span",je,i(a.name),1),e("span",He,i(a.value)+"%",1)]))),128))])])]))])])]),e("div",Je,[e("div",Ke,[e("div",Ge,[t[16]||(t[16]=e("h3",null,"实时数据监控",-1)),e("div",We,[s(h,{class:"status-icon online"},{default:l(()=>[s(f(ve))]),_:1}),t[15]||(t[15]=e("span",null,"实时更新中",-1))])]),e("div",Xe,[e("div",Ze,[(n(!0),u(C,null,R(L.value,a=>(n(),u("div",{class:"realtime-metric",key:a.key},[e("div",et,i(a.label),1),e("div",tt,i(a.value),1),e("div",at,i(a.unit),1)]))),128))])])])])]),e("div",st,[e("div",ot,[e("div",lt,[t[18]||(t[18]=e("h3",null,"热门页面排行",-1)),s(V,{size:"small",onClick:H},{default:l(()=>[s(h,null,{default:l(()=>[s(f(_e))]),_:1}),t[17]||(t[17]=c(" 刷新 "))]),_:1,__:[17]})]),e("div",nt,[ye((n(),_(ie,{data:S.value,stripe:"",style:{width:"100%"}},{default:l(()=>[s(b,{prop:"rank",label:"排名",width:"80",align:"center"},{default:l(({row:a})=>[s(ne,{type:a.rank<=3?"warning":"info",size:"small"},{default:l(()=>[c(i(a.rank),1)]),_:2},1032,["type"])]),_:1}),s(b,{prop:"page",label:"页面","min-width":"200","show-overflow-tooltip":""}),s(b,{prop:"visits",label:"访问量",width:"120",align:"center"},{default:l(({row:a})=>[e("strong",null,i(N(a.visits)),1)]),_:1}),s(b,{prop:"uniqueVisitors",label:"独立访客",width:"120",align:"center"}),s(b,{prop:"avgDuration",label:"平均停留时间",width:"140",align:"center"}),s(b,{prop:"bounceRate",label:"跳出率",width:"100",align:"center"},{default:l(({row:a})=>[e("span",{class:P({"high-bounce":a.bounceRate>60})},i(a.bounceRate)+"% ",3)]),_:1})]),_:1},8,["data"])),[[re,T.value]])])])])])}}},_t=pe(it,[["__scopeId","data-v-9fb5fd4f"]]);export{_t as default};
