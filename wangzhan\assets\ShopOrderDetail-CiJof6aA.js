import{r as F,X as J,h as R,ay as U,y as m,Q as s,A as d,J as A,ar as H,H as f,I as t,al as _,az as K,z as c,K as p,M as a,O as r,P as T,a6 as B}from"./vendor-DmFBDimT.js";import{T as Q,L as h}from"./LbButton-BtU4V_Gr.js";import{_ as X}from"./index-C9Xz1oqp.js";import{E as u,q as M}from"./element-fdzwdDuf.js";import"./utils-DCVfloi1.js";const G={class:"lb-order-detail"},W={class:"page-main"},Y={class:"order-header"},Z={class:"order-status"},ee=["src"],te={style:{color:"#e6a23c","font-weight":"600","font-size":"16px"}},oe={key:0,class:"section-title"},se={key:2,class:"section-title"},ae={key:0,class:"service-images"},re=["src"],le={class:"action-buttons"},ne={__name:"ShopOrderDetail",setup(ie){const x=U(),C=K(),b=F(!1),e=J({id:null,order_code:"",user_name:"",user_mobile:"",address_info:"",appointment_time:"",create_time:"",pay_type:1,pay_method:1,goods_price:0,discount_price:0,pay_price:0,order_goods:[],coach_info:null,service_logs:[]}),v=async l=>{b.value=!0;try{const y=await(await fetch(`/api/shop/order/detail/${l}`)).json();y.code===200?Object.assign(e,y.data):u.error(y.message||"获取订单详情失败")}catch(o){console.error("获取订单详情失败:",o),u.error("获取订单详情失败")}finally{b.value=!1}},O=l=>({1:"warning",2:"info",3:"primary",4:"success",5:"warning",6:"success",7:"danger"})[l]||"info",$=l=>({1:"待付款",2:"待接单",3:"待服务",4:"服务中",5:"待评价",6:"已完成",7:"已取消"})[l]||"未知",D=l=>({1:"微信支付",2:"支付宝",3:"余额支付"})[l]||"未知",N=async()=>{try{await M.confirm(`确定要取消订单 "${e.order_code}" 吗？`,"取消订单确认",{confirmButtonText:"确定取消",cancelButtonText:"取消",type:"warning"});const o=await(await fetch(`/api/shop/order/cancel/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:"管理员取消"})})).json();o.code===200?(u.success("订单取消成功"),v(e.id)):u.error(o.message||"取消失败")}catch(l){l!=="cancel"&&(console.error("取消订单失败:",l),u.error("取消失败"))}},S=()=>{C.push(`/shop/order/assign?id=${e.id}`)},j=async()=>{try{await M.confirm(`确定要完成订单 "${e.order_code}" 吗？`,"完成订单确认",{confirmButtonText:"确定完成",cancelButtonText:"取消",type:"success"});const o=await(await fetch(`/api/shop/order/complete/${e.id}`,{method:"PUT"})).json();o.code===200?(u.success("订单完成成功"),v(e.id)):u.error(o.message||"完成失败")}catch(l){l!=="cancel"&&(console.error("完成订单失败:",l),u.error("完成失败"))}};return R(()=>{x.query.id&&v(x.query.id)}),(l,o)=>{const y=_("el-tag"),n=_("el-descriptions-item"),w=_("el-descriptions"),g=_("el-table-column"),z=_("el-table"),k=_("el-card"),E=_("el-timeline-item"),P=_("el-timeline"),V=H("loading");return c(),m("div",G,[s(Q,{title:"订单详情",isBack:!0}),d("div",W,[A((c(),f(k,null,{default:t(()=>[d("div",Y,[o[1]||(o[1]=d("h3",null,"订单信息",-1)),d("div",Z,[s(y,{type:O(e.pay_type),size:"large"},{default:t(()=>[a(r($(e.pay_type)),1)]),_:1},8,["type"])])]),s(w,{column:2,border:""},{default:t(()=>[s(n,{label:"订单号"},{default:t(()=>[a(r(e.order_code),1)]),_:1}),s(n,{label:"下单时间"},{default:t(()=>[a(r(e.create_time),1)]),_:1}),s(n,{label:"下单人"},{default:t(()=>[a(r(e.user_name),1)]),_:1}),s(n,{label:"联系电话"},{default:t(()=>[a(r(e.user_mobile),1)]),_:1}),s(n,{label:"服务地址"},{default:t(()=>[a(r(e.address_info),1)]),_:1}),s(n,{label:"预约时间"},{default:t(()=>[a(r(e.appointment_time),1)]),_:1})]),_:1}),o[6]||(o[6]=d("div",{class:"section-title"},"项目信息",-1)),s(z,{data:e.order_goods,style:{width:"100%"}},{default:t(()=>[s(g,{label:"项目图片",width:"100"},{default:t(i=>[d("img",{src:i.row.goods_cover,alt:"项目图片",style:{width:"60px",height:"60px","border-radius":"4px"}},null,8,ee)]),_:1}),s(g,{prop:"goods_name",label:"项目名称"}),s(g,{prop:"goods_price",label:"单价",width:"120"},{default:t(i=>[a(" ¥"+r(i.row.goods_price),1)]),_:1}),s(g,{prop:"num",label:"数量",width:"80"}),s(g,{label:"小计",width:"120"},{default:t(i=>[a(" ¥"+r((i.row.goods_price*i.row.num).toFixed(2)),1)]),_:1})]),_:1},8,["data"]),o[7]||(o[7]=d("div",{class:"section-title"},"费用明细",-1)),s(w,{column:2,border:""},{default:t(()=>[s(n,{label:"商品总额"},{default:t(()=>[a("¥"+r(e.goods_price),1)]),_:1}),s(n,{label:"优惠金额"},{default:t(()=>[a("-¥"+r(e.discount_price||0),1)]),_:1}),s(n,{label:"实付金额"},{default:t(()=>[d("span",te,"¥"+r(e.pay_price),1)]),_:1}),s(n,{label:"支付方式"},{default:t(()=>[a(r(D(e.pay_method)),1)]),_:1})]),_:1}),e.coach_info?(c(),m("div",oe,"师傅信息")):p("",!0),e.coach_info?(c(),f(w,{key:1,column:2,border:""},{default:t(()=>[s(n,{label:"师傅姓名"},{default:t(()=>[a(r(e.coach_info.coach_name),1)]),_:1}),s(n,{label:"联系电话"},{default:t(()=>[a(r(e.coach_info.mobile),1)]),_:1}),s(n,{label:"师傅等级"},{default:t(()=>[a(r(e.coach_info.level_name),1)]),_:1}),s(n,{label:"从业年份"},{default:t(()=>[a(r(e.coach_info.work_time)+"年",1)]),_:1})]),_:1})):p("",!0),e.service_logs&&e.service_logs.length>0?(c(),m("div",se,"服务记录")):p("",!0),e.service_logs&&e.service_logs.length>0?(c(),f(P,{key:3},{default:t(()=>[(c(!0),m(T,null,B(e.service_logs,(i,q)=>(c(),f(E,{key:q,timestamp:i.create_time,placement:"top"},{default:t(()=>[s(k,null,{default:t(()=>[d("h4",null,r(i.title),1),d("p",null,r(i.content),1),i.images&&i.images.length>0?(c(),m("div",ae,[(c(!0),m(T,null,B(i.images,(I,L)=>(c(),m("img",{key:L,src:I,alt:"服务图片",style:{width:"100px",height:"100px","margin-right":"8px","border-radius":"4px"}},null,8,re))),128))])):p("",!0)]),_:2},1024)]),_:2},1032,["timestamp"]))),128))]),_:1})):p("",!0),d("div",le,[e.pay_type===1?(c(),f(h,{key:0,type:"warning",onClick:N},{default:t(()=>o[2]||(o[2]=[a(" 取消订单 ")])),_:1,__:[2]})):p("",!0),e.pay_type===2?(c(),f(h,{key:1,type:"primary",onClick:S},{default:t(()=>o[3]||(o[3]=[a(" 分配师傅 ")])),_:1,__:[3]})):p("",!0),e.pay_type===4?(c(),f(h,{key:2,type:"success",onClick:j},{default:t(()=>o[4]||(o[4]=[a(" 完成订单 ")])),_:1,__:[4]})):p("",!0),s(h,{onClick:o[0]||(o[0]=i=>l.$router.go(-1))},{default:t(()=>o[5]||(o[5]=[a(" 返回列表 ")])),_:1,__:[5]})])]),_:1,__:[6,7]})),[[V,b.value]])])])}}},me=X(ne,[["__scopeId","data-v-db7efbfc"]]);export{me as default};
