import{T as W,L as T}from"./LbButton-BtU4V_Gr.js";import{L as j}from"./LbPage-DnbiQ0Ct.js";import{_ as q}from"./index-C9Xz1oqp.js";import{E as x}from"./element-fdzwdDuf.js";import{g as A,r as m,X as J,h as Q,y as X,Q as e,A as i,I as l,al as d,J as G,ar as K,H as Z,z as k,O as _,M as g}from"./vendor-DmFBDimT.js";import"./utils-DCVfloi1.js";const ee={class:"page-container"},te={class:"content-container"},ae={class:"stat-content"},le={class:"stat-value"},oe={class:"stat-content"},se={class:"stat-value"},ne={class:"stat-content"},re={class:"stat-value"},ie={class:"stat-content"},de={class:"stat-value"},ce={class:"search-form-container"},ue={class:"table-container"},pe={__name:"FinanceWithdrawHistory",setup(fe){const{proxy:N}=A(),C=m(),y=m(!1),z=m(!1),w=m([]),S=m(0),I=m([]),p=m({total:0,pending:0,success:0,failed:0}),o=J({userId:"",status:"",type:"",beginTime:"",endTime:"",pageNum:1,pageSize:10}),R=a=>{a&&a.length===2?(o.beginTime=a[0],o.endTime=a[1]):(o.beginTime="",o.endTime="")},H=a=>({1:"primary",2:"success",3:"warning",4:"info"})[a]||"info",M=a=>({"-1":"danger",1:"warning",2:"success",3:"danger",4:"info"})[a]||"info",E=()=>{o.pageNum=1,v()},L=()=>{C.value?.resetFields(),I.value=[],o.beginTime="",o.endTime="",o.pageNum=1,v()},U=()=>{try{z.value=!0,console.log("📤 开始导出历史提现Excel...");const a=new URLSearchParams;Object.keys(o).forEach(c=>{o[c]!==""&&o[c]!==null&&o[c]!==void 0&&c!=="pageNum"&&c!=="pageSize"&&a.append(c,o[c])});const t="http://localhost:3004/api/admin/wallet/history/export",s=document.createElement("a"),r=a.toString()?`${t}?${a.toString()}`:t;s.href=r,s.download=`历史提现导出_${new Date().toISOString().slice(0,10)}.xlsx`,s.target="_blank";const h=sessionStorage.getItem("minitk");if(h){const c=r.includes("?")?"&":"?";s.href=`${r}${c}token=${encodeURIComponent(h)}`}document.body.appendChild(s),s.click(),document.body.removeChild(s),x.success("导出开始，请查看浏览器下载"),console.log("✅ 导出历史提现Excel成功")}catch(a){console.error("❌ 导出历史提现Excel异常:",a),x.error("导出失败，请稍后重试")}finally{z.value=!1}},Y=a=>{o.pageSize=a,o.pageNum=1,v()},$=a=>{o.pageNum=a,v()},v=async()=>{try{y.value=!0;const a={...o};a.userId&&(a.userId=parseInt(a.userId)),a.status&&(a.status=parseInt(a.status)),a.type&&(a.type=parseInt(a.type));const t=await N.$api.finance.withdrawHistoryList(a);if(console.log("📋 历史提现列表响应:",t),t.code===200||t.code==="200"){w.value=t.data.list||[],S.value=t.data.totalCount||0;const s=t.data.list||[];p.value={total:t.data.totalCount||0,pending:s.filter(r=>r.status===1).length,success:s.filter(r=>r.status===2).length,failed:s.filter(r=>[3,4,-1].includes(r.status)).length},console.log("📊 统计数据更新:",p.value),console.log("📋 表格数据更新:",w.value.length,"条记录")}else x.error(t.msg||"获取历史提现列表失败")}catch(a){console.error("❌ 获取历史提现列表失败:",a),x.error("获取历史提现列表失败")}finally{y.value=!1}};return Q(()=>{console.log("🚀 历史提现管理页面初始化"),console.log("📊 初始统计数据:",p.value),console.log("📋 初始表格数据:",w.value),v()}),(a,t)=>{const s=d("el-card"),r=d("el-col"),h=d("el-row"),c=d("el-input"),b=d("el-form-item"),u=d("el-option"),V=d("el-select"),F=d("el-date-picker"),B=d("el-form"),f=d("el-table-column"),D=d("el-tag"),O=d("el-table"),P=K("loading");return k(),X("div",ee,[e(W,{title:"历史提现管理"}),i("div",te,[e(h,{gutter:20,class:"stats-cards"},{default:l(()=>[e(r,{span:6},{default:l(()=>[e(s,{class:"stat-card"},{default:l(()=>[i("div",ae,[i("div",le,_(p.value.total||0),1),t[4]||(t[4]=i("div",{class:"stat-label"},"总记录数",-1))])]),_:1})]),_:1}),e(r,{span:6},{default:l(()=>[e(s,{class:"stat-card"},{default:l(()=>[i("div",oe,[i("div",se,_(p.value.pending||0),1),t[5]||(t[5]=i("div",{class:"stat-label"},"已提现未领取",-1))])]),_:1})]),_:1}),e(r,{span:6},{default:l(()=>[e(s,{class:"stat-card"},{default:l(()=>[i("div",ne,[i("div",re,_(p.value.success||0),1),t[6]||(t[6]=i("div",{class:"stat-label"},"到账成功",-1))])]),_:1})]),_:1}),e(r,{span:6},{default:l(()=>[e(s,{class:"stat-card"},{default:l(()=>[i("div",ie,[i("div",de,_(p.value.failed||0),1),t[7]||(t[7]=i("div",{class:"stat-label"},"失败/关闭",-1))])]),_:1})]),_:1})]),_:1}),i("div",ce,[e(B,{ref_key:"searchFormRef",ref:C,model:o,inline:!0,class:"search-form"},{default:l(()=>[e(h,{gutter:20},{default:l(()=>[e(r,{span:24},{default:l(()=>[e(b,{label:"用户ID",prop:"userId"},{default:l(()=>[e(c,{size:"default",modelValue:o.userId,"onUpdate:modelValue":t[0]||(t[0]=n=>o.userId=n),placeholder:"请输入用户ID",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(b,{label:"提现类型",prop:"type"},{default:l(()=>[e(V,{size:"default",modelValue:o.type,"onUpdate:modelValue":t[1]||(t[1]=n=>o.type=n),placeholder:"请选择提现类型",clearable:"",style:{width:"150px"}},{default:l(()=>[e(u,{label:"车费",value:1}),e(u,{label:"服务费",value:2}),e(u,{label:"加盟",value:3}),e(u,{label:"用户分销",value:4})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"状态",prop:"status"},{default:l(()=>[e(V,{size:"default",modelValue:o.status,"onUpdate:modelValue":t[2]||(t[2]=n=>o.status=n),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:l(()=>[e(u,{label:"内部错误",value:-1}),e(u,{label:"已提现，未领取",value:1}),e(u,{label:"到账",value:2}),e(u,{label:"失败",value:3}),e(u,{label:"关闭",value:4})]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"时间范围",prop:"timeRange"},{default:l(()=>[e(F,{size:"default",modelValue:I.value,"onUpdate:modelValue":t[3]||(t[3]=n=>I.value=n),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:R},null,8,["modelValue"])]),_:1}),e(b,null,{default:l(()=>[e(T,{size:"default",type:"primary",icon:"Search",onClick:E,loading:y.value},{default:l(()=>t[8]||(t[8]=[g(" 搜索 ")])),_:1,__:[8]},8,["loading"]),e(T,{size:"default",icon:"RefreshLeft",onClick:L},{default:l(()=>t[9]||(t[9]=[g(" 重置 ")])),_:1,__:[9]}),e(T,{size:"default",type:"success",icon:"Download",onClick:U,loading:z.value},{default:l(()=>t[10]||(t[10]=[g(" 导出 ")])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),i("div",ue,[G((k(),Z(O,{data:w.value,"header-cell-style":{background:"#f5f7fa",color:"#606266",fontSize:"16px",fontWeight:"600"},"cell-style":{fontSize:"14px",padding:"12px 8px"},style:{width:"100%"}},{default:l(()=>[e(f,{prop:"outBillNo",label:"订单号","min-width":"200","show-overflow-tooltip":""}),e(f,{prop:"userId",label:"用户ID",width:"100",align:"center"}),e(f,{prop:"type",label:"提现类型",width:"120",align:"center"},{default:l(n=>[e(D,{type:H(n.row.type),size:"default"},{default:l(()=>[g(_(n.row.typeText),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"status",label:"状态",width:"120",align:"center"},{default:l(n=>[e(D,{type:M(n.row.status),size:"default"},{default:l(()=>[g(_(n.row.statusText),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"changeTime",label:"变更时间",width:"180"}),e(f,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""}),e(f,{prop:"failReason",label:"失败原因","min-width":"150","show-overflow-tooltip":""},{default:l(n=>[g(_(n.row.failReason||n.row.failFriendlyReason||"-"),1)]),_:1})]),_:1},8,["data"])),[[P,y.value]])]),e(j,{page:o.pageNum,"page-size":o.pageSize,total:S.value,onHandleSizeChange:Y,onHandleCurrentChange:$},null,8,["page","page-size","total"])])])}}},ye=q(pe,[["__scopeId","data-v-ac4b25a3"]]);export{ye as default};
